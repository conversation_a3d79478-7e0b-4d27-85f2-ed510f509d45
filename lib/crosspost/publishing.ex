defmodule Crosspost.Publishing do
  @moduledoc """
  The Publishing context.
  """

  require Logger

  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Publishing.{Post, PostContent, Attachment, Schedule}
  alias Crosspost.Accounts.User
  alias Crosspost.Publishing.PostStatus
  alias Crosspost.Workers.ContentPublisher
  alias Crosspost.Publishing.PostNetworkStatus
  alias Crosspost.Features

  def publish_post_now(%Post{} = post, %{timezone: timezone} = user) do
    :ok = cancel_scheduled_jobs(post.id)

    # Reset any final failed statuses so they can be retried
    reset_final_failed_statuses(post.id)

    # Get current post statuses to determine which networks need publishing
    post_statuses = get_post_statuses(post.id)

    # A network should not be republished if it has a final published status
    published_networks = post_statuses |> Enum.filter(&(&1.status == :published and &1.is_final))

    # Filter out networks that:
    # 1. Are already published (have a final published status)
    # 2. Have a non-final status (in progress)
    networks_to_publish =
      post.social_networks
      |> Enum.reject(fn network ->
        network in published_networks or
          Enum.any?(post_statuses, fn status ->
            status.network == network and
              status.status == :published and
              status.is_final
          end)
      end)

    if Enum.empty?(networks_to_publish) do
      {:error, "All networks have already been published"}
    else
      # Create schedules for immediate publishing (5 seconds from now)
      scheduled_time = DateTime.utc_now() |> DateTime.add(5, :second)

      # Create schedules only for unpublished networks
      Logger.info("Creating schedules for networks", %{
        event: "publishing.publish_post_now.creating_schedules",
        networks: networks_to_publish,
        post_id: post.id
      })

      schedules =
        Enum.map(networks_to_publish, fn network ->
          %{
            network: network,
            scheduled_at: scheduled_time,
            timezone: timezone || "Etc/UTC",
            workspace_id: post.workspace_id
          }
        end)

      case update_post(post, %{schedules: schedules, status: "publishing"}) do
        {:ok, updated_post} ->
          case schedule_job(updated_post, user, publish_now: true) do
            {:ok, _job} ->
              Logger.info("Scheduled post for publishing", %{
                event: "publishing.publish_post_now.scheduled",
                post_id: updated_post.id,
                networks: networks_to_publish,
                scheduled_at: scheduled_time
              })

              {:ok, Repo.preload(updated_post, :schedules)}

            {:error, reason} ->
              Logger.error("Failed to schedule post for publishing", %{
                event: "publishing.publish_post_now.schedule_failed",
                scheduled_at: scheduled_time,
                post_id: updated_post.id,
                reason: inspect(reason)
              })

              {:error, "Failed to schedule post for publishing"}
          end

        {:error, changeset} ->
          Logger.error("Failed to queue post for publishing", %{
            event: "publishing.publish_post_now.update_failed",
            changeset: changeset
          })

          {:error, "Failed to queue post for publishing"}

        _other ->
          {:error, "Failed to queue post for publishing"}
      end
    end
  end

  def schedule_job(%Post{} = post, user, opts \\ []) do
    cond do
      not Features.enabled?(user, "scheduled_posts") ->
        Logger.error("User does not have access to scheduled posts feature", %{
          event: "publishing.schedule_job.feature_access_denied",
          user_id: user.id,
          post_id: post.id,
          reason: "feature_not_enabled"
        })

        {:error, :feature_not_enabled}

      Features.limit_reached?(user, "scheduled_posts") ->
        Logger.error("User has exceeded scheduled posts limit", %{
          event: "publishing.schedule_job.feature_limit_exceeded",
          user_id: user.id,
          post_id: post.id
        })

        {:error, :feature_limit_exceeded}

      true ->
        :ok = cancel_scheduled_jobs(post.id)

        schedules = get_schedules_for_post(post.id)

        Logger.info("Scheduling jobs for networks", %{
          event: "publishing.schedule_job.scheduling",
          schedules: Enum.map(schedules, &{&1.network, &1.scheduled_at})
        })

        results =
          Enum.map(schedules, fn schedule ->
            Logger.info("Scheduling job for network", %{
              event: "publishing.schedule_job.network",
              network: schedule.network,
              scheduled_at: schedule.scheduled_at
            })

            payload =
              Map.merge(
                %{
                  post_id: post.id,
                  user_id: user.id,
                  network: schedule.network
                },
                Enum.into(opts, %{})
              )

            payload
            |> publish_post_worker().new(scheduled_at: schedule.scheduled_at)
            |> Oban.insert()
          end)

        case Enum.find(results, &(elem(&1, 0) == :error)) do
          nil ->
            {:ok, results}

          {:error, error} ->
            Logger.error("Failed to schedule network jobs", %{
              post_id: post.id,
              error: inspect(error),
              event: "publishing.schedule_job.network_job_scheduling_failed"
            })

            {:error, :scheduling_failed}
        end
    end
  end

  defp publish_post_worker() do
    Application.get_env(:crosspost, :publish_post_worker)
  end

  def schedule_workflow(%Post{} = post, network, opts \\ []) do
    :ok = cancel_scheduled_workflow(post.workflow_id)

    user = Repo.get(User, post.user_id)
    post = Repo.preload(post, content: [:media, :links, :attachments])

    network_content =
      post.content
      |> Enum.filter(&(&1.network == network))
      |> Enum.sort_by(& &1.order)

    content_job_names =
      Enum.map(network_content, fn content ->
        ContentPublisher.job_name(post.id, network, content.order)
      end)

    workflow =
      Oban.Pro.Workflow.new()
      |> Oban.Pro.Workflow.add(
        "initialize_#{post.id}_#{network}",
        Crosspost.Workers.InitializePublishing.new(%{
          user_id: user.id,
          post_id: post.id,
          network: network
        })
      )
      |> fan_out_media_uploads(network_content, post, network, user)
      |> fan_out_content_publishers(network_content, post, network, user, content_job_names)
      |> add_status_updater(post, content_job_names, network, opts)

    jobs = Oban.insert_all(workflow)

    case jobs do
      [] ->
        Logger.error("No jobs were created for workflow")
        {:error, :scheduling_failed}

      [_ | _] = _jobs ->
        {:ok, _post} = post |> Post.changeset(%{workflow_id: workflow.id}) |> Repo.update()
        {:ok, workflow}
    end
  end

  def create_post(%{content: _content} = attrs, user) do
    attrs =
      attrs
      |> Map.put(:user_id, user.id)
      |> maybe_add_schedules_timezone(user.timezone)
      |> prepare_attachments()

    %Post{}
    |> Post.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, post} ->
        {:ok,
         Repo.preload(post, [:schedules, :user, content: [:attachments, :content_attachments]])}

      error ->
        error
    end
  end

  def create_post(attrs, user) do
    %Post{}
    |> Post.changeset(attrs)
    |> Ecto.Changeset.put_assoc(:user, user)
    |> Repo.insert()
  end

  @doc """
  Updates a post.
  Returns {:error, :already_published} if trying to reschedule a fully published post.
  """
  def update_post(%Post{user: user} = post, %{content: _content} = attrs) do
    attrs =
      attrs
      |> maybe_add_schedules_timezone(user.timezone)
      |> prepare_attachments()

    post
    |> Post.changeset(attrs)
    |> Repo.update()
    |> case do
      {:ok, updated_post} ->
        {:ok,
         Repo.preload(updated_post, [:schedules, content: [:attachments, :content_attachments]])}

      error ->
        error
    end
  end

  def update_post(%Post{} = post, attrs) do
    post
    |> Post.changeset(attrs)
    |> Repo.update()
  end

  defp prepare_attachments(%{content: content} = attrs) do
    updated_content =
      content
      |> Enum.map(fn content_attrs ->
        case content_attrs do
          %{attachments: attachments} when is_list(attachments) ->
            attachments =
              attachments
              |> Enum.with_index()
              |> Enum.map(fn {%{id: id, order: order}, index} ->
                %{
                  post_content_id: Map.get(content_attrs, :id),
                  attachment_id: id,
                  order: order || index
                }
              end)

            Map.put(content_attrs, :attachments, attachments)

          _ ->
            content_attrs
        end
      end)

    Map.put(attrs, :content, updated_content)
  end

  @doc """
  Creates an attachment with the given attributes.
  """
  def create_attachment(attrs \\ %{}) do
    %Attachment{id: attrs[:id] || Ecto.UUID.generate()}
    |> Attachment.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a new post status entry.
  """
  def create_post_status(post_id, network, params) do
    %PostStatus{}
    |> PostStatus.changeset(%{
      post_id: post_id,
      content_id: Map.get(params, :content_id),
      network: network,
      status: params.status,
      is_final: Map.get(params, :is_final, false),
      outcome: Map.get(params, :outcome, %{})
    })
    |> Repo.insert()
  end

  def backend_module(network) do
    normalized_network = normalize_network(network)

    case normalized_network do
      :x ->
        Application.get_env(:crosspost, :x_module, Crosspost.Publishing.X)

      :linkedin ->
        Application.get_env(:crosspost, :linkedin_module, Crosspost.Publishing.LinkedIn)

      :bsky ->
        Application.get_env(:crosspost, :bsky_module, Crosspost.Publishing.Bsky)

      :mastodon ->
        Application.get_env(:crosspost, :mastodon_module, Crosspost.Publishing.Mastodon)
    end
  end

  # Fan out media uploads for each content piece
  defp fan_out_media_uploads(workflow, content_items, post, network, user) do
    Enum.reduce(content_items, workflow, fn content, workflow ->
      Enum.reduce(content.attachments, workflow, fn attachment, workflow ->
        job_name = "media_#{content.id}_#{network}_#{attachment.id}"

        Oban.Pro.Workflow.add(
          workflow,
          job_name,
          Crosspost.Workers.MediaUploader.new(%{
            attachment_id: attachment.id,
            content_id: content.id,
            user_id: user.id,
            network: network,
            workspace_id: post.workspace_id
          })
        )
      end)
    end)
  end

  defp fan_out_content_publishers(workflow, content_items, post, network, user, content_job_names) do
    Enum.zip(content_items, content_job_names)
    |> Enum.reduce(workflow, fn {content, _job_name}, workflow ->
      media_deps =
        content.attachments
        |> Enum.map(&"media_#{content.id}_#{network}_#{&1.id}")

      previous_deps =
        if content.order > 0 do
          [ContentPublisher.job_name(post.id, network, content.order - 1)]
        else
          []
        end

      all_deps = ["initialize_#{post.id}_#{network}" | previous_deps ++ media_deps]
      job_name = ContentPublisher.job_name(post.id, network, content.order)

      Oban.Pro.Workflow.add(
        workflow,
        job_name,
        ContentPublisher.new(%{
          post_id: post.id,
          content_id: content.id,
          user_id: user.id,
          network: network
        }),
        deps: all_deps
      )
    end)
  end

  defp add_status_updater(workflow, post, content_jobs, network, opts) do
    payload =
      Map.merge(
        %{
          post_id: post.id,
          user_id: post.user_id,
          network: network,
          publish_now: Keyword.get(opts, :publish_now, false)
        },
        Enum.into(opts, %{})
      )

    Oban.Pro.Workflow.add(
      workflow,
      "finalize_#{post.id}_#{network}",
      Crosspost.Workers.FinalizePublishing.new(payload),
      deps: content_jobs,
      ignore_cancelled: true,
      ignore_deleted: true,
      ignore_discarded: true
    )
  end

  defp cancel_scheduled_workflow(nil), do: :ok

  defp cancel_scheduled_workflow(workflow_id) when is_binary(workflow_id) do
    Oban.cancel_job(workflow_id)
  end

  @doc """
  Gets a single post.

  Raises `Ecto.NoResultsError` if the Post does not exist.

  ## Examples

      iex> get_post!(123)
      %Post{}

      iex> get_post!(456)
      ** (Ecto.NoResultsError)

  """
  def get_post!(id) do
    Post
    |> Repo.get!(id)
    |> Repo.preload([
      :post_statuses,
      :schedules,
      content: {from(c in PostContent, order_by: [asc: c.order]), [:attachments]}
    ])
  end

  @doc """
  Returns the list of posts.

  ## Examples

      iex> list_posts()
      [%Post{}, ...]

  """
  def list_posts(user, filter \\ "all") do
    Post
    |> where(user_id: ^user.id)
    |> filter_query(filter)
    |> order_by([p], desc: p.inserted_at)
    |> Repo.all()
    |> Repo.preload([
      :post_statuses,
      :schedules,
      content: {from(c in PostContent, order_by: [asc: c.order]), [:attachments]}
    ])
  end

  defp filter_query(query, "scheduled"), do: where(query, status: "scheduled")
  defp filter_query(query, "draft"), do: where(query, status: "draft")

  defp filter_query(query, "published"),
    do: where(query, [p], p.status in ["published", "partially_published"])

  defp filter_query(query, _), do: query

  @doc """
  Deletes a post.

  ## Examples

      iex> delete_post(post)
      {:ok, %Post{}}

      iex> delete_post(post)
      {:error, %Ecto.Changeset{}}

  """
  def delete_post(%Post{} = post) do
    post = Repo.preload(post, [:content, :post_content_attachments])

    Repo.transaction(fn ->
      # First delete all post_content_attachments
      Enum.each(post.post_content_attachments, &Repo.delete!/1)

      # Then delete the post (which will cascade to content and other associations)
      cancel_job(post)
      Repo.delete!(post)
    end)
  end

  defp cancel_job(%{job_id: nil} = post), do: {:ok, post}

  defp cancel_job(%Post{} = post) do
    case Oban.cancel_job(post.job_id) do
      :ok ->
        Logger.info("Cancelled existing publish job for post #{post.id}")
        {:ok, post}

      {:error, reason} ->
        Logger.error(
          "Failed to cancel existing publish job for post #{post.id}: #{inspect(reason)}"
        )

        {:error, reason}
    end
  end

  def change_post(%Post{} = post, attrs \\ %{}) do
    Post.changeset(post, attrs)
  end

  @doc """
  Gets an attachment by ID.
  """
  def get_attachment(id, user) do
    Attachment
    |> where([a], a.id == ^id and a.user_id == ^user.id)
    |> Repo.one()
  end

  def get_attachment(id) do
    Repo.get(Attachment, id)
  end

  def get_attachment!(id) do
    Repo.get!(Attachment, id)
  end

  def get_post_content(id) do
    case PostContent
         |> Repo.get(id)
         |> Repo.preload([:attachments, post: :user]) do
      nil -> {:error, :not_found}
      post_content -> {:ok, post_content}
    end
  end

  def update_post_content(%PostContent{} = post_content, attrs) do
    post_content
    |> PostContent.changeset(attrs)
    |> Repo.update()
  end

  def add_attachments_to_post_content(%PostContent{} = post_content, attachments) do
    post_content
    |> Repo.preload(:attachments)
    |> PostContent.changeset(%{
      attachments: post_content.attachments ++ attachments
    })
    |> Repo.update()
  end

  @doc """
  Gets a single post belonging to the user.
  Returns {:ok, post} if found, {:error, :not_found} otherwise.
  """
  def get_post(id, user) do
    Post
    |> where(id: ^id, user_id: ^user.id)
    |> Repo.one()
    |> case do
      nil ->
        {:error, :not_found}

      post ->
        {:ok,
         Repo.preload(post, [
           :user,
           :post_statuses,
           :schedules,
           content:
             {from(c in PostContent, order_by: [asc: c.order]),
              [:attachments, :content_attachments, :media, :links]}
         ])}
    end
  end

  def delete_attachment(%Attachment{} = attachment) do
    Repo.delete(attachment)
  end

  def get_attachment_with_user(id) do
    case Attachment
         |> Repo.get(id)
         |> Repo.preload(post_content: [post: :user]) do
      nil -> {:error, :not_found}
      attachment -> {:ok, attachment}
    end
  end

  def create_temporary_attachment(attrs) do
    %Attachment{}
    |> Attachment.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a post's publishing status.
  This is used specifically by the PublishToNetworkWorker to update the post's status.
  """
  def update_publish_status(%Post{} = post, network_status, outcome) do
    post = Repo.preload(post, :post_statuses)

    case Repo.get_by(PostStatus, post_id: post.id, network: network_status.network) do
      nil ->
        %PostStatus{}
        |> PostStatus.changeset(%{
          post_id: post.id,
          network: network_status.network,
          status: network_status.status,
          outcome: outcome
        })
        |> Repo.insert()

      existing_status ->
        existing_status
        |> PostStatus.changeset(%{
          status: network_status.status,
          outcome: outcome
        })
        |> Repo.update()
    end
  end

  @doc """
  Gets all post statuses for a post.
  """
  def get_post_statuses(post_id) do
    PostStatus
    |> where(post_id: ^post_id)
    |> Repo.all()
  end

  @doc """
  Gets the latest post status for a specific network.
  """
  def get_post_status(post_id, network) do
    from(ps in PostStatus,
      where: ps.post_id == ^post_id and ps.network == ^network,
      order_by: [desc: ps.inserted_at],
      limit: 1
    )
    |> Repo.one()
  end

  @doc """
  Returns whether a post has been fully published to all networks.
  """
  def fully_published?(post) do
    statuses = get_post_statuses(post.id)

    Enum.all?(post.social_networks, fn network ->
      Enum.find(statuses, &(&1.network == network))
      |> case do
        %PostStatus{status: :published} -> true
        _ -> false
      end
    end)
  end

  # Add this helper function in the Publishing context
  @doc """
  Creates a new post status entry, preserving the history of status transitions.
  """
  def update_post_status(post_id, network, params) do
    outcome = Map.get(params, :outcome, %{})
    is_final = Map.get(params, :is_final, false)

    %PostStatus{}
    |> PostStatus.changeset(%{
      post_id: post_id,
      network: network,
      status: params.status,
      is_final: is_final,
      outcome: outcome
    })
    |> Repo.insert()
  end

  @doc """
  Updates an attachment.
  """
  def update_attachment(%Attachment{} = attachment, attrs) do
    attachment
    |> Attachment.changeset(attrs)
    |> Repo.update()
  end

  # Add this function to handle job cancellation
  def cancel_scheduled_jobs(post_id) do
    # Find and cancel any existing jobs for this post that aren't currently executing
    query =
      from j in Oban.Job,
        where:
          fragment("? @> ?", j.args, ^%{"post_id" => post_id}) and
            j.state not in ["executing", "completed", "discarded"]

    case Repo.all(query) do
      [] ->
        Logger.debug("No existing jobs found for post #{post_id}")
        :ok

      jobs ->
        Enum.each(jobs, fn job ->
          case Oban.cancel_job(job.id) do
            :ok ->
              Logger.debug("Cancelled scheduled job", job_id: job.id, post_id: post_id)

            {:error, reason} ->
              Logger.error(
                "Failed to cancel job",
                job_id: job.id,
                post_id: post_id,
                reason: inspect(reason)
              )
          end
        end)

        :ok
    end
  end

  # Add this function to the Publishing context
  @doc """
  Gets a post for publishing, ensuring it hasn't been fully published yet.
  Raises if the post doesn't exist or is already published.
  """
  def get_post_for_publishing!(post_id) do
    import Ecto.Query

    Post
    |> where(
      [p],
      p.id == ^post_id and
        p.status in ["draft", "scheduled", "pending", "partially_published", "failed"]
    )
    |> preload([:post_statuses, :schedules, content: :attachments])
    |> Repo.one!()
  end

  # Add this function to the Publishing context
  def list_scheduled_posts(user) do
    content_query = from(c in PostContent, order_by: [asc: c.order])

    from(p in Post,
      as: :post,
      where: p.user_id == ^user.id and p.status in ["scheduled", "pending"],
      left_join: s in assoc(p, :schedules),
      group_by: p.id,
      order_by: [asc: fragment("MIN(?)", s.scheduled_at)],
      preload: [
        :schedules,
        content: ^{content_query, [:attachments]}
      ]
    )
    |> Repo.all()
  end

  def list_published_posts(user) do
    content_query = from(c in PostContent, order_by: [asc: c.order])

    from(p in Post,
      join: ps in PostStatus,
      on: ps.post_id == p.id,
      where: p.user_id == ^user.id,
      where: p.status in ["published", "partially_published"],
      where: ps.is_final == true,
      group_by: p.id,
      select_merge: %{
        published_at: fragment("? AT TIME ZONE 'UTC'", max(ps.inserted_at))
      },
      order_by: [desc: max(ps.inserted_at)],
      preload: [
        :post_statuses,
        content: ^{content_query, [:attachments]}
      ]
    )
    |> Repo.all()
  end

  @doc """
  Auto-schedules a post across optimal posting times for each selected network.
  Returns {:ok, post} if successful, or {:error, reason} if scheduling fails.
  """
  def auto_schedule(%Post{} = post, user) do
    cond do
      post.status == "published" ->
        {:error, :already_published}

      post.status == "scheduled" ->
        {:error, :already_scheduled}

      Enum.empty?(post.social_networks) ->
        {:error, :no_networks}

      not Features.enabled?(user, "scheduled_posts") ->
        Logger.error("User does not have access to scheduled posts feature", %{
          event: "publishing.auto_schedule.feature_access_denied",
          user_id: user.id,
          post_id: post.id,
          reason: "feature_not_enabled"
        })

        {:error, :feature_not_enabled}

      Features.limit_reached?(user, "scheduled_posts") ->
        Logger.error("User has exceeded scheduled posts limit", %{
          event: "publishing.auto_schedule.feature_limit_exceeded",
          user_id: user.id,
          post_id: post.id
        })

        {:error, :feature_limit_exceeded}

      true ->
        # Cancel any existing jobs first
        :ok = cancel_scheduled_jobs(post.id)

        # Get optimal posting times based on number of networks
        base_time = get_next_base_time()
        network_times = generate_network_times(base_time, length(post.social_networks))

        # Create schedules in a transaction
        case Repo.transaction(fn ->
               # First delete any existing schedules
               Schedule |> where(post_id: ^post.id) |> Repo.delete_all()

               # Create schedules for each network with its corresponding time
               results =
                 post.social_networks
                 |> Enum.zip(network_times)
                 |> Enum.map(fn {network, scheduled_time} ->
                   %Schedule{}
                   |> Schedule.changeset(%{
                     post_id: post.id,
                     network: network,
                     scheduled_at: scheduled_time,
                     timezone: user.timezone
                   })
                   |> Repo.insert()
                 end)

               case Enum.find(results, &(elem(&1, 0) == :error)) do
                 nil ->
                   # All schedules created successfully
                   # Update post status to scheduled
                   case update_post(post, %{status: "scheduled"}) do
                     {:ok, updated_post} ->
                       # Schedule the main publish job
                       case schedule_job(updated_post, user) do
                         {:ok, _job} ->
                           # Explicitly reload post with schedules before returning
                           Repo.get(Post, post.id)
                           |> Repo.preload([:schedules, :user, content: [:attachments]])

                         {:error, reason} ->
                           Repo.rollback(reason)
                       end

                     {:error, reason} ->
                       Repo.rollback(reason)
                   end

                 {:error, reason} ->
                   Repo.rollback(reason)
               end
             end) do
          {:ok, updated_post} -> {:ok, updated_post}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  # Gets the next base time that falls within high-activity hours
  defp get_next_base_time do
    now = DateTime.utc_now()
    tomorrow = DateTime.add(now, 1, :day)

    # Start with 9 AM tomorrow
    base = %{tomorrow | hour: 9, minute: 0, second: 0}

    # If it's early enough today, we can still schedule for today
    if now.hour < 9 do
      %{now | hour: 9, minute: 0, second: 0}
    else
      base
    end
  end

  # Generates optimal posting times based on number of networks
  defp generate_network_times(base_time, network_count) do
    # High activity hours in UTC
    peak_hours = [9, 10, 11, 12, 13, 14, 19, 20, 21]

    # For multiple networks, ensure minimum spread
    min_hours_between = 2
    # Increase total hours to account for potential peak hour adjustments
    total_hours = max(4, (network_count - 1) * min_hours_between + 2)

    # Generate times spread evenly across the interval
    times =
      case network_count do
        1 ->
          [base_time]

        _ ->
          hours_between = total_hours / (network_count - 1)

          0..(network_count - 1)
          |> Enum.map(fn i ->
            base_time
            |> DateTime.add(trunc(i * hours_between * 3600), :second)
            |> DateTime.truncate(:second)
          end)
      end

    # Ensure all times fall within peak hours and sort them
    times
    |> Enum.map(&ensure_peak_hour(&1, peak_hours))
    |> Enum.sort_by(&DateTime.to_unix/1)
  end

  # Ensures the given time falls within peak hours
  defp ensure_peak_hour(time, peak_hours) do
    hour = time.hour

    cond do
      hour in peak_hours ->
        time

      hour < 9 ->
        %{time | hour: 9, minute: 0, second: 0}

      hour > 21 ->
        # Move to next day at 9 AM if we're past the last peak hour
        time
        |> DateTime.add(1, :day)
        |> then(fn next_day -> %{next_day | hour: 9, minute: 0, second: 0} end)

      true ->
        # Find nearest peak hour
        nearest_peak = Enum.min_by(peak_hours, &abs(&1 - hour))
        %{time | hour: nearest_peak}
    end
  end

  @doc """
  Gets all schedules for a post.
  """
  def get_schedules_for_post(post_id) do
    Schedule
    |> where(post_id: ^post_id)
    |> order_by(asc: :scheduled_at)
    |> Repo.all()
  end

  # Helper to add timezone to schedules
  defp maybe_add_schedules_timezone(%{schedules: schedules} = attrs, timezone)
       when is_list(schedules) do
    updated_schedules = Enum.map(schedules, &Map.put(&1, :timezone, timezone))
    %{attrs | schedules: updated_schedules}
  end

  defp maybe_add_schedules_timezone(attrs, _timezone), do: attrs

  def format_schedules(schedules, timezone) when is_list(schedules) do
    Enum.map(schedules, fn schedule ->
      %{
        network: normalize_network(schedule["network"]),
        scheduled_at: parse_schedule_time(schedule["scheduled_at"]),
        timezone: timezone
      }
    end)
  end

  def format_schedules(nil, _timezone), do: []

  defp parse_schedule_time(time) when is_binary(time) do
    {:ok, datetime, _} = DateTime.from_iso8601(time)

    datetime
  end

  defp parse_schedule_time(%DateTime{} = time), do: time
  defp parse_schedule_time(_), do: DateTime.utc_now() |> DateTime.add(3600)

  defp normalize_network(network) when is_binary(network) do
    String.to_existing_atom(network)
  end

  defp normalize_network(network) when is_atom(network) do
    network
  end

  # Add this function to handle resetting final statuses:
  defp reset_final_failed_statuses(post_id) do
    from(ps in PostStatus,
      where:
        ps.post_id == ^post_id and
          ps.status == :failed and
          ps.is_final == true
    )
    |> Repo.update_all(set: [is_final: false])
  end

  def list_posts_for_calendar(user_id, workspace_id) do
    from(pns in PostNetworkStatus,
      join: p in Post,
      on: p.id == pns.post_id,
      where: p.user_id == ^user_id and p.workspace_id == ^workspace_id,
      preload: [post: [content: :attachments]]
    )
    |> Repo.all()
    |> Enum.map(fn status ->
      display_time = status.published_at || status.scheduled_at

      if display_time do
        status
        |> Map.put(:display_time, display_time)
      end
    end)
    # Remove any nil entries
    |> Enum.reject(&is_nil/1)
  end

  def list_past_schedules(user) do
    Schedule
    |> join(:inner, [s], p in Post, on: s.post_id == p.id)
    |> where([s, p], p.user_id == ^user.id)
    |> where([s], s.scheduled_at < ^DateTime.utc_now())
    |> order_by([s], desc: s.scheduled_at)
    |> limit(100)
    |> Repo.all()
  end

  @doc """
  Lists all future schedules for a workspace.
  Returns a list of schedules with their network, scheduled time, and post preview.
  """
  def list_workspace_future_schedules(workspace_id, current_post_id \\ nil) do
    query =
      Schedule
      |> join(:inner, [s], p in Post, on: s.post_id == p.id)
      |> where([s, p], p.workspace_id == ^workspace_id)
      |> where([s], s.scheduled_at > ^DateTime.utc_now())
      |> order_by([s], asc: s.scheduled_at)
      |> join(:left, [s, p], c in assoc(p, :content))
      |> preload([s, p, c], post: {p, content: c})

    query =
      if current_post_id do
        where(query, [s], s.post_id != ^current_post_id)
      else
        query
      end

    query
    |> Repo.all()
    |> Enum.map(fn schedule ->
      preview_text = get_preview_text(schedule.post)

      %{
        network: schedule.network,
        scheduled_at: schedule.scheduled_at,
        content: preview_text,
        post_id: schedule.post_id
      }
    end)
  end

  # Helper function to get preview text from post content
  defp get_preview_text(post) do
    post.content
    |> Enum.sort_by(& &1.order)
    |> Enum.find_value("", fn content ->
      case {content.network, content.text} do
        {:canonical, text} when is_binary(text) -> text
        {_, text} when is_binary(text) -> text
        _ -> nil
      end
    end)
    |> String.trim()
    |> then(fn text ->
      if String.length(text) > 50 do
        String.slice(text, 0, 50) <> "..."
      else
        text
      end
    end)
  end
end
