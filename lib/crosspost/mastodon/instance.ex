defmodule Crosspost.Mastodon.Instance do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false

  schema "mastodon_instances" do
    field :domain, :string, primary_key: true
    field :info, :map, default: %{}

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(instance, attrs) do
    instance
    |> cast(attrs, [:domain, :info])
    |> validate_required([:domain])
  end
end
