defmodule Crosspost.Mastodon.Instances do
  require Logger

  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Mastodon.Instance

  use Crosspost.HTTP

  @api_url "https://instances.social/api/1.0"

  def list_instances(query) when is_binary(query) and byte_size(query) > 0 do
    db_instances =
      Instance
      |> where([i], ilike(i.domain, ^"#{query}%"))
      |> limit(20)
      |> Repo.all()
      |> Enum.map(& &1.domain)

    if length(db_instances) >= 5 do
      {:ok, %{"instances" => db_instances}}
    else
      case fetch_instances_from_api(query) do
        {:ok, api_instances} ->
          store_instances(api_instances)
          {:ok, %{"instances" => api_instances}}

        error ->
          error
      end
    end
  end

  def list_instances(_), do: {:ok, %{"instances" => []}}

  def update_instance_info(domain) when is_binary(domain) do
    case mastodon_client().fetch_instance_info(domain) do
      {:ok, info} ->
        %Instance{domain: domain}
        |> Instance.changeset(%{info: info})
        |> Repo.insert(on_conflict: {:replace, [:info]}, conflict_target: :domain)

      error ->
        error
    end
  end

  defp fetch_instances_from_api(query) do
    api_key = Application.get_env(:crosspost, :instances_social)[:api_key]

    client = http(@api_url, access_token: api_key)

    params = %{
      "q" => query,
      "count" => 20,
      "name" => true
    }

    case get(client, "/instances/search", params: params) do
      {:ok, %{"instances" => instances}} ->
        domains =
          instances
          |> Enum.map(& &1["name"])
          |> Enum.filter(&is_binary/1)
          |> Enum.uniq()

        {:ok, domains}

      {:error, reason} ->
        Logger.error("Failed to fetch Mastodon instances", %{
          event: "mastodon.instances.fetch_failed",
          reason: reason
        })

        {:error, "Failed to fetch instances list"}
    end
  end

  defp store_instances(instances) do
    Enum.each(instances, fn domain ->
      Repo.insert(%Instance{domain: domain}, on_conflict: :nothing)
    end)
  end

  defp mastodon_client do
    Application.get_env(:crosspost, :mastodon_client, Crosspost.Mastodon.Client)
  end
end
