defmodule Crosspost.Features.StripeSync do
  @moduledoc """
  Syncs plan and feature definitions with Stripe Products and Features.
  Only pushes changes from database to Stripe, never modifies local data.
  """

  alias Crosspost.Admin
  alias Crosspost.Admin.Resources.{Feature, Plan}
  require Logger

  @doc """
  Syncs all features and plans with Stripe.
  Returns {:ok, results} or {:error, reason}
  """
  def sync_all do
    with {:ok, features} <- sync_features(),
         {:ok, products} <- sync_products(features) do
      {:ok, %{features: features, products: products}}
    end
  end

  @doc """
  Syncs feature definitions from database to Stripe Features API.
  Only updates Stripe data, never modifies local database data.
  """
  def sync_features do
    {:ok, {features, _meta}} = Admin.list_features(%{})

    # First, get all existing features from Stripe (with limit 100)
    case Stripe.API.request(
           %{limit: 100},
           :get,
           "/v1/entitlements/features",
           %{},
           []
         ) do
      {:ok, %{"data" => stripe_features}} ->
        Logger.info("Found #{length(stripe_features)} existing Stripe features")
        Logger.debug("Stripe features: #{inspect(stripe_features, pretty: true)}")

        results =
          features
          |> Enum.map(fn feature ->
            # Try to find existing feature by lookup_key
            existing_feature = Enum.find(stripe_features, &(&1["lookup_key"] == feature.key))

            Logger.info("""
            Processing feature:
              Key: #{feature.key}
              Name: #{feature.name}
              Exists in Stripe: #{if existing_feature, do: "Yes (#{existing_feature["id"]})", else: "No"}
            """)

            case existing_feature do
              nil ->
                Logger.info("Creating new feature in Stripe: #{feature.key}")
                create_stripe_feature(feature)

              existing ->
                if should_update_stripe_feature?(existing, feature) do
                  Logger.info(
                    "Updating existing feature in Stripe: #{existing["id"]} (#{feature.key})"
                  )

                  update_stripe_feature(existing["id"], feature)
                else
                  Logger.info("No changes needed for feature: #{existing["id"]} (#{feature.key})")
                  {:ok, existing}
                end
            end
          end)

        # Log results summary
        success_count = Enum.count(results, &match?({:ok, _}, &1))
        error_count = Enum.count(results, &match?({:error, _}, &1))

        Logger.info("""
        Feature sync completed:
          Successful: #{success_count}
          Failed: #{error_count}
        """)

        if Enum.any?(results, &match?({:error, _}, &1)) do
          {:error, results}
        else
          {:ok, results}
        end

      {:error, error} ->
        Logger.error("Failed to fetch Stripe features: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  Syncs product definitions from database to Stripe Products API.
  Only updates Stripe data, never modifies local database data.
  """
  def sync_products(_features) do
    plans = Admin.list_plans()

    results =
      Enum.map(plans, fn plan ->
        with {:ok, product} <- ensure_product_exists(plan),
             {:ok, _price} <- ensure_price_exists(product, plan),
             {:ok, _features} <- sync_product_features(product.id, plan.features) do
          {:ok, product}
        else
          {:error, error} -> {:error, {plan.key, error}}
        end
      end)

    if Enum.any?(results, &match?({:error, _}, &1)) do
      {:error, results}
    else
      {:ok, results}
    end
  end

  # Sync features for a specific product
  defp sync_product_features(product_id, feature_keys) do
    # First, get current features attached to the product
    case Stripe.API.request(
           %{limit: 100},
           :get,
           "/v1/products/#{product_id}/features",
           %{},
           []
         ) do
      {:ok, %{"data" => current_features}} ->
        Logger.info(
          "Found #{length(current_features)} features attached to product #{product_id}"
        )

        # Get all available features from Stripe
        case Stripe.API.request(
               %{limit: 100},
               :get,
               "/v1/entitlements/features",
               %{},
               []
             ) do
          {:ok, %{"data" => all_features}} ->
            # Map feature keys to Stripe feature IDs
            feature_ids =
              feature_keys
              |> Enum.map(fn key ->
                case Enum.find(all_features, &(&1["lookup_key"] == key)) do
                  nil ->
                    Logger.warning("Feature #{key} not found in Stripe")
                    nil

                  feature ->
                    feature["id"]
                end
              end)
              |> Enum.reject(&is_nil/1)

            # Remove features from Stripe that aren't in our database
            current_features
            |> Enum.each(fn feature ->
              feature_id = feature["entitlement_feature"]["id"]

              unless feature_id in feature_ids do
                Logger.info("Removing feature #{feature_id} from product #{product_id} in Stripe")

                Stripe.API.request(
                  %{},
                  :delete,
                  "/v1/products/#{product_id}/features/#{feature["id"]}",
                  %{},
                  []
                )
              end
            end)

            # Add new features to Stripe
            current_feature_ids = Enum.map(current_features, & &1["entitlement_feature"]["id"])

            results =
              feature_ids
              |> Enum.reject(&(&1 in current_feature_ids))
              |> Enum.map(fn feature_id ->
                Logger.info("Adding feature #{feature_id} to product #{product_id} in Stripe")

                Stripe.API.request(
                  %{entitlement_feature: feature_id},
                  :post,
                  "/v1/products/#{product_id}/features",
                  %{},
                  []
                )
              end)

            if Enum.any?(results, &match?({:error, _}, &1)) do
              {:error, results}
            else
              {:ok, results}
            end

          {:error, error} ->
            {:error, error}
        end

      {:error, error} ->
        {:error, error}
    end
  end

  defp create_stripe_feature(%Feature{} = feature) do
    Logger.debug("Creating feature with params: #{inspect(feature)}")

    result =
      Stripe.API.request(
        %{
          lookup_key: feature.key,
          name: format_feature_name(feature),
          metadata: %{
            description: feature.description,
            type: to_string(feature.type),
            value: to_string(feature.limit || ""),
            reset_period: to_string(feature.reset_period || "")
          }
        },
        :post,
        "/v1/entitlements/features",
        %{},
        []
      )

    case result do
      {:ok, response} ->
        Logger.info("Successfully created feature: #{feature.key} (#{response["id"]})")

      {:error, error} ->
        Logger.error("Failed to create feature #{feature.key}: #{inspect(error)}")
    end

    result
  end

  defp update_stripe_feature(feature_id, %Feature{} = feature) do
    Logger.debug("Updating feature #{feature_id} with params: #{inspect(feature)}")

    result =
      Stripe.API.request(
        %{
          name: format_feature_name(feature),
          metadata: %{
            description: feature.description,
            type: to_string(feature.type),
            value: to_string(feature.limit || ""),
            reset_period: to_string(feature.reset_period || "")
          }
        },
        :post,
        "/v1/entitlements/features/#{feature_id}",
        %{},
        []
      )

    case result do
      {:ok, _response} ->
        Logger.info("Successfully updated feature: #{feature.key} (#{feature_id})")

      {:error, error} ->
        Logger.error("Failed to update feature #{feature.key} (#{feature_id}): #{inspect(error)}")
    end

    result
  end

  defp format_feature_name(%Feature{} = feature) do
    base_name = feature.name

    case feature do
      %{type: :limit, limit: limit} when not is_nil(limit) ->
        limit_text =
          case limit do
            -1 ->
              "Unlimited"

            value when is_integer(value) ->
              case feature.reset_period do
                :monthly -> "#{value}/month"
                :yearly -> "#{value}/year"
                :daily -> "#{value}/day"
                _ -> to_string(value)
              end
          end

        "#{base_name} (#{limit_text})"

      _ ->
        base_name
    end
  end

  defp ensure_product_exists(%Plan{} = plan) do
    case Stripe.Product.list(%{active: true}) do
      {:ok, %{data: products}} ->
        existing_product = Enum.find(products, &(&1.id == plan.key))

        case existing_product do
          nil ->
            Logger.info("Creating new product in Stripe: #{plan.key}")
            # Create new product
            Stripe.Product.create(%{
              id: plan.key,
              name: plan.name,
              description: plan.description,
              active: true,
              metadata: %{
                price_cents: plan.price,
                highlight: plan.highlight,
                enabled: plan.enabled,
                cta: plan.cta,
                action: plan.action
              }
            })

          product ->
            if should_update_stripe_product?(product, plan) do
              Logger.info("Updating existing product in Stripe: #{product.id}")
              # Update existing product
              Stripe.Product.update(product.id, %{
                name: plan.name,
                description: plan.description,
                active: true,
                metadata: %{
                  price_cents: plan.price,
                  highlight: plan.highlight,
                  enabled: plan.enabled,
                  cta: plan.cta,
                  action: plan.action
                }
              })
            else
              Logger.info("No changes needed for product: #{product.id}")
              {:ok, product}
            end
        end

      {:error, error} ->
        {:error, error}
    end
  end

  defp ensure_price_exists(product, %Plan{} = plan) do
    case Stripe.Price.list(%{product: product.id, active: true}) do
      {:ok, %{data: [price | _]}} ->
        if price.unit_amount == plan.price do
          Logger.info("Price for product #{product.id} is up to date in Stripe")
          # Update stripe_price_id if it's different
          if plan.stripe_price_id != price.id do
            Logger.info(
              "Updating local stripe_price_id from #{plan.stripe_price_id} to #{price.id}"
            )

            Admin.update_plan(plan, %{stripe_price_id: price.id})
          end

          {:ok, price}
        else
          Logger.info(
            "Creating new price in Stripe for product #{product.id} due to amount change"
          )

          # Create new price if amount changed
          case create_price(product.id, plan) do
            {:ok, price} = result ->
              Logger.info("Updating local stripe_price_id to #{price.id}")
              Admin.update_plan(plan, %{stripe_price_id: price.id})
              result

            error ->
              error
          end
        end

      {:ok, %{data: []}} ->
        Logger.info("Creating new price in Stripe for product #{product.id}")

        case create_price(product.id, plan) do
          {:ok, price} = result ->
            Logger.info("Updating local stripe_price_id to #{price.id}")
            Admin.update_plan(plan, %{stripe_price_id: price.id})
            result

          error ->
            error
        end

      {:error, error} ->
        {:error, error}
    end
  end

  defp create_price(product_id, %Plan{} = plan) do
    Stripe.Price.create(%{
      product: product_id,
      unit_amount: plan.price,
      currency: "usd",
      recurring: %{
        interval: "month"
      }
    })
  end

  # Determines if a Stripe product needs to be updated based on local plan data
  defp should_update_stripe_product?(stripe_product, %Plan{} = local_plan) do
    stripe_metadata = stripe_product.metadata || %{}

    stripe_product.name != local_plan.name ||
      stripe_product.description != local_plan.description ||
      stripe_metadata["price_cents"] != to_string(local_plan.price) ||
      stripe_metadata["highlight"] != to_string(local_plan.highlight) ||
      stripe_metadata["enabled"] != to_string(local_plan.enabled) ||
      stripe_metadata["cta"] != local_plan.cta ||
      stripe_metadata["action"] != local_plan.action
  end

  # Determines if a Stripe feature needs to be updated based on local feature data
  defp should_update_stripe_feature?(stripe_feature, %Feature{} = local_feature) do
    stripe_metadata = stripe_feature["metadata"] || %{}

    stripe_feature["name"] != format_feature_name(local_feature) ||
      stripe_metadata["description"] != local_feature.description ||
      stripe_metadata["type"] != to_string(local_feature.type) ||
      stripe_metadata["value"] != to_string(local_feature.limit || "") ||
      stripe_metadata["reset_period"] != to_string(local_feature.reset_period || "")
  end
end
