defmodule Crosspost.Admin do
  @moduledoc """
  The Admin context.
  """

  import Ecto.Query, warn: false

  alias Crosspost.Repo

  alias Crosspost.Admin.Resources.{
    User,
    Feature,
    Post,
    Plan,
    ObanJob,
    Workspace,
    UserFeature,
    Attachment,
    AdminUser
  }

  alias Crosspost.Publishing.{PostContent, PostStatus, Schedule}
  alias Crosspost.Accounts.Waitlist

  def toggle_plan(plan) do
    plan
    |> Ecto.Changeset.change(%{enabled: !plan.enabled})
    |> Repo.update()
  end

  @doc """
  Lists all active (non-archived) plans.
  """
  def list_active_plans do
    Plan
    |> where([p], p.archived == false)
    |> order_by([p], asc: p.price)
    |> Repo.all()
  end

  @doc """
  Returns a paginated list of users.
  """
  def list_users(params \\ %{}) do
    params =
      params
      |> process_password_filter()
      |> process_confirmation_filter()
      |> process_status_filter()
      |> process_has_posts_filter()
      |> process_date_filter()
      |> clean_empty_filters()
      |> normalize_filters_format()

    case Flop.validate(params, for: User) do
      {:ok, flop} ->
        query = from(u in User, preload: [:plan, :posts])

        # Apply custom filters
        query = apply_custom_filters(query, flop.filters)

        # Store original filters for the meta
        original_filters = flop.filters

        # Remove our custom filters from flop to avoid Flop trying to apply them
        flop = %{
          flop
          | filters:
              Enum.reject(flop.filters, fn filter ->
                filter.field in [:status, :has_posts]
              end)
        }

        # Run the query
        {users, meta} = Flop.run(query, flop)

        # Restore original filters in the meta for the form
        meta = %{meta | flop: %{meta.flop | filters: original_filters}}

        {:ok, {users, meta}}

      {:error, meta} ->
        {:error, meta}
    end
  end

  @doc """
  Returns a paginated list of users with simple email and name filtering.
  If paginate is false, returns all matching users without pagination.
  """
  def list_users_with_filters(
        email_filter,
        name_filter,
        page,
        per_page,
        created_after \\ nil,
        created_before \\ nil,
        status_filter \\ [],
        paginate \\ true
      ) do
    filters =
      []
      |> add_filter(:email, :ilike_and, email_filter)
      |> add_filter(:name, :ilike_and, name_filter)
      |> add_filter(:inserted_at, :greater_than_or, created_after)
      |> add_filter(:inserted_at, :less_than_or, created_before)
      |> add_status_filters(status_filter)
      |> Enum.reject(&is_nil/1)

    flop = %Flop{
      filters: filters,
      page: page,
      page_size: per_page
    }

    query = AdminUser

    if paginate do
      with {:ok, {users, meta}} <- Flop.validate_and_run(query, flop, for: AdminUser) do
        {:ok, %{users: users, total_count: meta.total_count, total_pages: meta.total_pages}}
      end
    else
      with {:ok, flop} <- Flop.validate(flop, for: AdminUser),
           users <- Flop.all(query, flop) do
        {:ok, %{users: users, total_count: length(users), total_pages: 1}}
      end
    end
  end

  defp add_status_filters(filters, status_list)
       when is_list(status_list) and length(status_list) > 0 do
    [%{field: :status, op: :in, value: status_list} | filters]
  end

  defp add_status_filters(filters, _), do: filters

  defp add_filter(filters, field, op, value) when is_binary(value) and value != "" do
    [%{field: field, op: op, value: value} | filters]
  end

  defp add_filter(filters, _field, _op, _value), do: filters

  defp process_password_filter(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn
        %{"field" => "hashed_password", "value" => "true"} = filter ->
          %{filter | "op" => "!=", "value" => nil}

        %{"field" => "hashed_password", "value" => "false"} = filter ->
          %{filter | "op" => "==", "value" => nil}

        filter ->
          filter
      end)

    %{params | "filters" => filters}
  end

  defp process_password_filter(params), do: params

  defp process_confirmation_filter(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn
        %{"field" => "confirmed_at", "value" => "true"} = filter ->
          %{filter | "op" => "!=", "value" => nil}

        %{"field" => "confirmed_at", "value" => "false"} = filter ->
          %{filter | "op" => "==", "value" => nil}

        filter ->
          filter
      end)

    %{params | "filters" => filters}
  end

  defp process_confirmation_filter(params), do: params

  defp process_status_filter(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn
        %{"field" => "status", "value" => value} = filter
        when value in ["trial", "trial_ended", "customer"] ->
          # We'll handle this in apply_custom_filters
          filter

        filter ->
          filter
      end)

    %{params | "filters" => filters}
  end

  defp process_status_filter(params), do: params

  defp process_has_posts_filter(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn
        %{"field" => "has_posts", "value" => value} = filter when value in ["true", "false"] ->
          # We'll handle this in apply_custom_filters
          filter

        filter ->
          filter
      end)

    %{params | "filters" => filters}
  end

  defp process_has_posts_filter(params), do: params

  defp process_date_filter(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn
        %{"field" => "inserted_at", "op" => "<=", "value" => datetime} = filter
        when is_binary(datetime) and datetime != "" ->
          # For datetime-local input, we can use the value directly
          # Just ensure it's in a format Flop can handle
          %{filter | "value" => datetime}

        filter ->
          filter
      end)

    %{params | "filters" => filters}
  end

  defp process_date_filter(%{"filters" => filters} = params) when is_map(filters) do
    filters =
      Enum.map(filters, fn {key, filter} ->
        filter =
          case filter do
            %{"field" => "inserted_at", "op" => "<=", "value" => datetime}
            when is_binary(datetime) and datetime != "" ->
              # For datetime-local input, we can use the value directly
              # Just ensure it's in a format Flop can handle
              %{filter | "value" => datetime}

            _ ->
              filter
          end

        {key, filter}
      end)
      |> Map.new()

    %{params | "filters" => filters}
  end

  defp process_date_filter(params), do: params

  defp apply_custom_filters(query, filters) do
    Enum.reduce(filters, query, fn filter, query ->
      case {filter.field, filter.value} do
        # Status filters
        {:status, "trial"} ->
          from u in query,
            where: not is_nil(u.trial_end) and fragment("? > NOW()", u.trial_end)

        {:status, "trial_ended"} ->
          from u in query,
            where: not is_nil(u.trial_end) and fragment("? <= NOW()", u.trial_end)

        {:status, "customer"} ->
          from u in query,
            where:
              fragment("?.subscription->>'status' = ?", u, "active") and
                fragment("?.subscription->>'stripe_customer_id' IS NOT NULL", u)

        # Posts filters - only consider published posts
        {:has_posts, "true"} ->
          from u in query,
            join: p in assoc(u, :posts),
            where: p.status == "published",
            distinct: true

        {:has_posts, "false"} ->
          # First get all users with published posts
          users_with_published_posts =
            from(u2 in Crosspost.Admin.Resources.User,
              join: p in assoc(u2, :posts),
              where: p.status == "published",
              select: u2.id
            )

          # Then exclude them from the main query
          from u in query,
            where: u.id not in subquery(users_with_published_posts)

        # Default case - no custom filter
        _ ->
          query
      end
    end)
  end

  @doc """
  Returns a paginated list of features.
  """
  def list_features(params \\ %{}) do
    case Flop.validate(params, for: Feature) do
      {:ok, flop} ->
        {:ok, Flop.run(Feature, flop)}

      {:error, meta} ->
        {:error, meta}
    end
  end

  @doc """
  Returns a paginated list of posts with preloaded users.
  """
  def list_posts(params \\ %{}) do
    latest_final_status_query =
      from ps in Crosspost.Publishing.PostStatus,
        where: ps.is_final == true,
        group_by: ps.post_id,
        select: %{
          post_id: ps.post_id,
          published_at: max(ps.inserted_at)
        }

    query =
      from p in Post,
        left_join: fs in subquery(latest_final_status_query),
        on: p.id == fs.post_id,
        left_join: u in assoc(p, :user),
        select: %{
          p
          | published_at: fs.published_at |> selected_as(:published_at)
        },
        preload: [user: u]

    # Process params to ensure published_at uses NULLS LAST
    params = process_published_at_order(params)

    with {:ok, flop} <- Flop.validate(params, for: Post) do
      {posts, meta} = Flop.run(query, flop, for: Post)
      {:ok, {posts, meta}}
    else
      error -> error
    end
  end

  # Helper function to ensure published_at uses NULLS LAST
  defp process_published_at_order(params) do
    order_by = params["order_by"] || params[:order_by] || []
    order_directions = params["order_directions"] || params[:order_directions] || []

    # Check if we're ordering by published_at
    case Enum.find_index(order_by, &(&1 == "published_at" || &1 == :published_at)) do
      nil ->
        # Not ordering by published_at, return params unchanged
        params

      idx ->
        # Get current direction or default to :asc
        current_direction = Enum.at(order_directions, idx)

        # Set the appropriate nulls last direction
        new_direction =
          case current_direction do
            "desc" -> "desc_nulls_last"
            :desc -> :desc_nulls_last
            "asc" -> "asc_nulls_last"
            :asc -> :asc_nulls_last
            "desc_nulls_first" -> "desc_nulls_last"
            :desc_nulls_first -> :desc_nulls_last
            "asc_nulls_first" -> "asc_nulls_last"
            :asc_nulls_first -> :asc_nulls_last
            "desc_nulls_last" -> "desc_nulls_last"
            :desc_nulls_last -> :desc_nulls_last
            "asc_nulls_last" -> "asc_nulls_last"
            :asc_nulls_last -> :asc_nulls_last
            _ -> "asc_nulls_last"
          end

        # Update the order_directions
        new_order_directions = List.replace_at(order_directions, idx, new_direction)

        # Update params with the new order_directions
        params =
          if is_map(params) do
            if Map.has_key?(params, "order_directions") do
              %{params | "order_directions" => new_order_directions}
            else
              Map.put(params, "order_directions", new_order_directions)
            end
          else
            params
          end

        params
    end
  end

  def get_post(id) do
    post_content_query =
      from(pc in PostContent, where: pc.network != :canonical, preload: [:attachments])

    post_query =
      from(p in Post,
        where: p.id == ^id,
        preload: [
          :user,
          :final_statuses,
          post_content: ^post_content_query,
          post_statuses:
            ^from(ps in PostStatus, order_by: [desc: ps.is_final, desc: ps.inserted_at]),
          schedules: ^from(s in Schedule, order_by: [asc: s.scheduled_at])
        ]
      )

    case Repo.one(post_query) do
      nil -> {:error, :not_found}
      post -> {:ok, post}
    end
  end

  @doc """
  Updates a post.
  """
  def update_post(%Post{} = post, attrs) do
    post
    |> Post.changeset(attrs)
    |> Repo.update()
    |> case do
      {:ok, updated_post} -> get_post(updated_post.id)
      error -> error
    end
  end

  def get_user(id) do
    case Repo.get(User, id) |> Repo.preload([:plan, :connections, :enabled_features]) do
      nil ->
        {:error, :not_found}

      user ->
        {:ok, user}
    end
  end

  @doc """
  Updates a user.
  """
  def update_user(%User{} = admin_user, attrs) do
    attrs = maybe_parse_json_fields(attrs)
    old_plan_key = admin_user.plan_key

    if Map.has_key?(attrs, "plan_key") && old_plan_key != attrs["plan_key"] do
      # Plan is changing, use enroll
      with {:ok, admin_plan} <- get_plan(attrs["plan_key"]),
           {:ok, accounts_user} <- get_accounts_user(admin_user.id),
           {:ok, accounts_plan} <- get_accounts_plan(admin_plan.key) do
        # Generate fake subscription info
        now = DateTime.utc_now()
        period_end = DateTime.add(now, 365 * 24 * 60 * 60, :second)

        subscription = %{
          status: "active",
          plan: admin_plan.key,
          period_start: now,
          period_end: period_end,
          features: admin_plan.features,
          stripe_customer_id:
            "admin_cus_#{:crypto.strong_rand_bytes(8) |> Base.url_encode64(padding: false)}",
          stripe_subscription_id:
            "admin_sub_#{:crypto.strong_rand_bytes(8) |> Base.url_encode64(padding: false)}",
          stripe_product_id: admin_plan.key,
          stripe_price_id:
            admin_plan.stripe_price_id ||
              "admin_price_#{:crypto.strong_rand_bytes(8) |> Base.url_encode64(padding: false)}"
        }

        with {:ok, _updated_user} <-
               Crosspost.Accounts.enroll(accounts_user, accounts_plan, subscription),
             # Reload admin user to reflect changes
             {:ok, updated_admin_user} <- get_user(admin_user.id) do
          # Apply any other changes after enrollment
          other_attrs = Map.drop(attrs, ["plan_key"])

          if Enum.empty?(other_attrs) do
            {:ok, updated_admin_user}
          else
            updated_admin_user
            |> User.changeset(other_attrs)
            |> Repo.update()
          end
        end
      end
    else
      # No plan change, proceed with normal update
      admin_user
      |> User.changeset(attrs)
      |> Repo.update()
    end
  end

  defp get_accounts_user(id) do
    case Repo.get(Crosspost.Accounts.User, id) do
      nil -> {:error, :not_found}
      user -> {:ok, user}
    end
  end

  defp get_accounts_plan(key) do
    case Repo.get(Crosspost.Accounts.Plan, key) do
      nil -> {:error, :plan_not_found}
      plan -> {:ok, plan}
    end
  end

  # Helper function to parse JSON fields if they're strings
  defp maybe_parse_json_fields(%{"settings" => settings, "info" => info} = attrs) do
    attrs
    |> Map.put("settings", parse_json_field(settings))
    |> Map.put("info", parse_json_field(info))
  end

  defp maybe_parse_json_fields(attrs), do: attrs

  defp parse_json_field(value) when is_binary(value) do
    case Jason.decode(value) do
      {:ok, parsed} -> parsed
      {:error, _} -> nil
    end
  end

  defp parse_json_field(value), do: value

  def list_waitlist(params \\ %{}) do
    query = from(w in Waitlist)

    case Flop.validate_and_run(query, params, for: Waitlist) do
      {:ok, {waitlist, meta}} ->
        {:ok, {waitlist, meta}}

      {:error, meta} ->
        {:error, meta}
    end
  end

  def get_waitlist_entry(id) do
    case Repo.get(Waitlist, id) do
      nil -> {:error, :not_found}
      waitlist -> {:ok, waitlist}
    end
  end

  @doc """
  Updates a waitlist entry.
  """
  def update_waitlist_entry(%Waitlist{} = entry, attrs) do
    require Logger
    Logger.debug("Updating waitlist entry", entry_id: entry.id, attrs: inspect(attrs))

    attrs =
      if Map.has_key?(attrs, :invitation_code) || Map.has_key?(attrs, "invitation_code") do
        Map.put(attrs, "invited_at", DateTime.utc_now() |> DateTime.truncate(:second))
      else
        attrs
      end

    result =
      entry
      |> Waitlist.changeset(attrs)
      |> Repo.update()

    case result do
      {:ok, updated_entry} ->
        Logger.debug("Successfully updated waitlist entry",
          entry_id: entry.id,
          invitation_code: updated_entry.invitation_code,
          invited_at: updated_entry.invited_at
        )

        {:ok, updated_entry}

      {:error, changeset} = error ->
        Logger.error("Failed to update waitlist entry",
          entry_id: entry.id,
          errors: inspect(changeset.errors)
        )

        error
    end
  end

  @doc """
  Deletes a waitlist entry.
  """
  def delete_waitlist_entry(%Waitlist{} = entry) do
    Repo.delete(entry)
  end

  def get_feature(key) do
    case Repo.get(Feature, key) do
      nil -> {:error, :not_found}
      feature -> {:ok, feature}
    end
  end

  @doc """
  Returns true if the feature is used by any plans.
  """
  def feature_in_use?(%Feature{} = feature) do
    query = from(p in Plan, where: fragment("? = ANY(features)", ^feature.key))
    Repo.exists?(query)
  end

  @doc """
  Returns a map of feature keys to their in-use status for the given features.
  This is more efficient than calling feature_in_use? for each feature individually.
  """
  def features_in_use?(features) when is_list(features) do
    # Get all feature keys that are used in any plan
    used_keys =
      from(p in Plan,
        select: fragment("unnest(features)"),
        distinct: true
      )
      |> Repo.all()
      |> MapSet.new()

    # Create a map of feature key to boolean indicating if it's in use
    features
    |> Enum.map(&{&1.key, MapSet.member?(used_keys, &1.key)})
    |> Map.new()
  end

  @doc """
  Updates a feature.
  """
  def update_feature(%Feature{} = feature, attrs) do
    feature
    |> Feature.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a feature.
  Returns {:error, :feature_in_use} if the feature is used in any plans.
  """
  def delete_feature(%Feature{} = feature) do
    # Check if any plan uses this feature
    query = from(p in Plan, where: fragment("? = ANY(features)", ^feature.key))

    case Repo.exists?(query) do
      true ->
        {:error, :feature_in_use}

      false ->
        Repo.delete(feature)
    end
  end

  def list_jobs(params \\ %{}) do
    query = from(j in ObanJob, order_by: [desc: j.inserted_at])

    case Flop.validate_and_run(query, params, for: ObanJob) do
      {:ok, {jobs, meta}} ->
        {:ok, {jobs, meta}}

      {:error, meta} ->
        {:error, meta}
    end
  end

  def get_job(id) do
    case Repo.get(ObanJob, id) do
      nil -> {:error, :not_found}
      job -> {:ok, job}
    end
  end

  @doc """
  Returns a list of users who have posts, with only id, email, and name fields.
  The list is ordered by email and each user appears only once.
  """
  def users_for_post_filter do
    query =
      from u in User,
        join: p in assoc(u, :posts),
        distinct: true,
        select: %{id: u.id, email: u.email, name: u.name},
        order_by: [asc: u.email]

    Repo.all(query)
  end

  @doc """
  Gets the number of posts created per day for the last n days.
  Returns a list of maps with date and count.
  """
  def get_posts_per_day(days) do
    today = Date.utc_today()
    start_date = Date.add(today, -days + 1)

    query =
      from p in Post,
        where: fragment("date(?)", p.inserted_at) >= ^start_date,
        group_by: fragment("date(?)", p.inserted_at),
        select: %{
          date: fragment("date(?)", p.inserted_at),
          count: count(p.id)
        },
        order_by: [asc: fragment("date(?)", p.inserted_at)]

    dates =
      Date.range(start_date, today)
      |> Enum.map(&%{date: &1, count: 0})
      |> Map.new(&{&1.date, &1})

    query
    |> Repo.all()
    |> Enum.into(dates, &{&1.date, &1})
    |> Map.values()
    |> Enum.sort_by(& &1.date, Date)
  end

  @doc """
  Gets the distribution of posts across different social networks.
  Returns a list of maps with network and count.
  """
  def get_network_distribution do
    query =
      from pc in PostContent,
        where: pc.network != :canonical,
        group_by: pc.network,
        select: %{
          network: pc.network,
          count: count(pc.post_id, :distinct)
        }

    Repo.all(query)
  end

  @doc """
  Gets the publishing status distribution per day for the last n days.
  Returns a list of maps with date and counts per status.
  """
  def get_publishing_status_distribution(days) do
    today = Date.utc_today()
    start_date = Date.add(today, -days + 1)

    # Get the latest final status for each post per day
    query =
      from p in Post,
        join: ps in assoc(p, :final_statuses),
        where: fragment("date(?)", ps.inserted_at) >= ^start_date,
        group_by: [fragment("date(?)", ps.inserted_at), p.status],
        select: %{
          date: fragment("date(?)", ps.inserted_at),
          status: p.status,
          count: count(p.id)
        },
        order_by: [asc: fragment("date(?)", ps.inserted_at)]

    Repo.all(query)
  end

  @doc """
  Gets a plan by key.
  """
  def get_plan(key) when is_binary(key) do
    case Repo.get(Plan, key) do
      nil -> {:error, :not_found}
      plan -> {:ok, plan}
    end
  end

  @doc """
  Creates a plan.
  """
  def create_plan(attrs \\ %{}) do
    %Plan{}
    |> Plan.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a plan.
  """
  def update_plan(%Plan{} = plan, attrs) do
    plan
    |> Plan.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Archives a plan.
  """
  def archive_plan(%Plan{} = plan) do
    update_plan(plan, %{archived: true})
  end

  @doc """
  Lists all plans.
  """
  def list_plans do
    Plan
    |> order_by([p], asc: p.price)
    |> Repo.all()
  end

  @doc """
  Returns a paginated list of attachments.
  """
  def list_attachments(params \\ %{}) do
    query = from(a in Attachment, order_by: [desc: a.inserted_at]) |> preload(:user)

    case Flop.validate_and_run(query, params, for: Attachment) do
      {:ok, {attachments, meta}} ->
        {:ok, {attachments, meta}}

      {:error, meta} ->
        {:error, meta}
    end
  end

  @doc """
  Gets a single attachment.
  Returns {:ok, attachment} if found, {:error, :not_found} otherwise.
  """
  def get_attachment(id) do
    case Repo.get(Attachment, id) |> Repo.preload(:user) do
      nil -> {:error, :not_found}
      attachment -> {:ok, attachment}
    end
  end

  @doc """
  Updates an attachment.
  """
  def update_attachment(%Attachment{} = attachment, attrs) do
    attrs = maybe_parse_metadata(attrs)

    attachment
    |> Attachment.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking attachment changes.
  """
  def change_attachment(%Attachment{} = attachment, attrs \\ %{}) do
    Attachment.changeset(attachment, attrs)
  end

  # Helper function to parse metadata JSON if it's a string
  defp maybe_parse_metadata(%{"metadata" => metadata} = attrs) when is_binary(metadata) do
    case Jason.decode(metadata) do
      {:ok, parsed} -> %{attrs | "metadata" => parsed}
      {:error, _} -> attrs
    end
  end

  defp maybe_parse_metadata(attrs), do: attrs

  @doc """
  Returns a list of all users for select inputs, with only id, email, and name fields.
  The list is ordered by email.
  """
  def list_users_for_select do
    query =
      from u in User,
        select: %{id: u.id, email: u.email, name: u.name},
        order_by: [asc: u.email]

    Repo.all(query)
  end

  @doc """
  Returns a paginated list of workspaces with preloaded owners.
  """
  def list_workspaces(params \\ %{}) do
    case Flop.validate(params, for: Workspace) do
      {:ok, flop} ->
        query = from(w in Workspace, preload: [:owner])
        {:ok, Flop.run(query, flop)}

      {:error, meta} ->
        {:error, meta}
    end
  end

  @doc """
  Gets a workspace by ID.
  """
  def get_workspace(id) do
    case Repo.get(Workspace, id) do
      nil ->
        {:error, :not_found}

      workspace ->
        workspace =
          workspace
          |> Repo.preload([
            :owner,
            workspace_users: [
              user: from(u in User, select: %{id: u.id, email: u.email, name: u.name})
            ],
            connections: [
              user: from(u in User, select: %{id: u.id, email: u.email, name: u.name})
            ]
          ])

        {:ok, workspace}
    end
  end

  @doc """
  Updates a workspace.
  """
  def update_workspace(%Workspace{} = workspace, attrs) do
    attrs = maybe_parse_json_fields(attrs)

    workspace
    |> Workspace.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Adds a feature to a user.
  """
  def add_user_feature(%User{} = user, feature_key) when is_binary(feature_key) do
    case Repo.get(Feature, feature_key) do
      nil ->
        {:error, :feature_not_found}

      _feature ->
        %UserFeature{}
        |> UserFeature.changeset(%{
          user_id: user.id,
          feature_key: feature_key
        })
        |> Repo.insert(
          on_conflict: :nothing,
          conflict_target: [:user_id, :feature_key]
        )

        get_user(user.id)
    end
  end

  @doc """
  Removes a feature from a user.
  """
  def remove_user_feature(%User{} = user, feature_key) when is_binary(feature_key) do
    Repo.delete_all(
      from(uf in UserFeature,
        where: uf.user_id == ^user.id and uf.feature_key == ^feature_key
      )
    )

    get_user(user.id)
  end

  @doc """
  Creates a feature.
  """
  def create_feature(attrs \\ %{}) do
    %Feature{}
    |> Feature.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets the number of user sign-ups per day for the last n days.
  Returns a list of maps with date and count.
  """
  def get_signups_per_day(days) do
    today = Date.utc_today()
    start_date = Date.add(today, -days + 1)

    query =
      from u in User,
        where: fragment("date(?)", u.inserted_at) >= ^start_date,
        group_by: fragment("date(?)", u.inserted_at),
        select: %{
          date: fragment("date(?)", u.inserted_at),
          count: count(u.id)
        },
        order_by: fragment("date(?)", u.inserted_at)

    dates =
      Date.range(start_date, today)
      |> Enum.map(&%{date: &1, count: 0})
      |> Map.new(&{&1.date, &1})

    query
    |> Repo.all()
    |> Enum.into(dates, &{&1.date, &1})
    |> Map.values()
    |> Enum.sort_by(& &1.date)
  end

  @doc """
  Gets the number of new customers (users with active subscriptions) per day for the last n days.
  Returns a list of maps with date and count.
  """
  def get_new_customers_per_day(days) do
    today = Date.utc_today()
    start_date = Date.add(today, -days + 1)

    query =
      from u in User,
        where: fragment("(u0.subscription->>'period_start')::timestamp::date >= ?", ^start_date),
        where: fragment("u0.subscription->>'status' = ?", "active"),
        where: fragment("u0.subscription->>'stripe_customer_id' IS NOT NULL"),
        group_by: fragment("(u0.subscription->>'period_start')::timestamp::date"),
        select: %{
          date: fragment("(u0.subscription->>'period_start')::timestamp::date"),
          count: count(u.id)
        },
        order_by: fragment("(u0.subscription->>'period_start')::timestamp::date")

    dates =
      Date.range(start_date, today)
      |> Enum.map(&%{date: &1, count: 0})
      |> Map.new(&{&1.date, &1})

    query
    |> Repo.all()
    |> Enum.into(dates, &{&1.date, &1})
    |> Map.values()
    |> Enum.sort_by(& &1.date)
  end

  @doc """
  Updates a post content.
  When updating order, it will reorder all content items to maintain order consistency.
  """
  def update_post_content(%PostContent{} = content, attrs) do
    Repo.transaction(fn ->
      if Map.has_key?(attrs, :order) || Map.has_key?(attrs, "order") do
        new_order = attrs[:order] || String.to_integer(attrs["order"])

        # Get all content for the same post and network, ordered by current order
        query =
          from pc in PostContent,
            where:
              pc.post_id == ^content.post_id and
                pc.network == ^content.network,
            order_by: [asc: pc.order]

        all_contents = Repo.all(query)

        # Remove the content being updated from the list
        other_contents = Enum.reject(all_contents, &(&1.id == content.id))

        # Reorder all content items
        other_contents
        |> Enum.with_index()
        |> Enum.each(fn {item, idx} ->
          order = if idx >= new_order, do: idx + 1, else: idx

          item
          |> Ecto.Changeset.change(%{order: order})
          |> Repo.update!()
        end)

        # Update the target content's order
        content
        |> Ecto.Changeset.change(%{order: new_order})
        |> Repo.update!()
      end

      # Update the content's other attributes
      content
      |> PostContent.changeset(attrs)
      |> Repo.update!()

      get_post(content.post_id)
    end)
  end

  @doc """
  Gets all connections for a post based on its social networks and workspace.
  Returns a list of connections that match the post's social networks.
  """
  def connections_for_post(%Post{} = post) do
    # Convert enum values to strings
    networks = Enum.map(post.social_networks, &Atom.to_string/1)

    query =
      from c in Crosspost.Accounts.Connection,
        where: c.workspace_id == ^post.workspace_id,
        where: c.platform in ^networks,
        preload: [:user]

    Repo.all(query)
  end

  @doc """
  Gets the overall post status distribution for the last n days.
  Returns a list of maps with status and count.
  """
  def get_status_distribution(days) do
    today = Date.utc_today()
    start_date = Date.add(today, -days + 1)

    query =
      from p in Post,
        where: fragment("date(?)", p.inserted_at) >= ^start_date,
        group_by: p.status,
        select: %{
          status: p.status,
          count: count(p.id)
        },
        order_by: p.status

    Repo.all(query)
  end

  @doc """
  Gets the distribution of users by status (trial with posts, trial without posts, customers).
  Returns a list of maps with status and count.
  """
  def get_user_status_distribution do
    now = DateTime.utc_now()

    # Get all users with published posts
    users_with_posts_query =
      from(u in User,
        join: p in assoc(u, :posts),
        where: p.status == "published",
        select: u.id,
        distinct: true
      )

    users_with_posts = Repo.all(users_with_posts_query)
    users_with_posts_set = MapSet.new(users_with_posts)

    # Get all users
    users_query =
      from(u in User,
        select: %{
          id: u.id,
          is_customer:
            fragment(
              "?.subscription->>'status' = ? AND ?.subscription->>'stripe_customer_id' IS NOT NULL",
              u,
              "active",
              u
            ),
          is_trial: not is_nil(u.trial_end) and fragment("? > ?", u.trial_end, ^now)
        }
      )

    # Process the results
    users = Repo.all(users_query)

    # Count users in each category
    trial_with_posts =
      users
      |> Enum.count(fn user ->
        user.is_trial && MapSet.member?(users_with_posts_set, user.id)
      end)

    trial_without_posts =
      users
      |> Enum.count(fn user ->
        user.is_trial && !MapSet.member?(users_with_posts_set, user.id)
      end)

    customers =
      users
      |> Enum.count(fn user ->
        user.is_customer
      end)

    [
      %{status: "trial_with_posts", count: trial_with_posts, label: "Trial Users with Posts"},
      %{
        status: "trial_without_posts",
        count: trial_without_posts,
        label: "Trial Users without Posts"
      },
      %{status: "customers", count: customers, label: "Customers"}
    ]
  end

  @doc """
  Gets the distribution of new users in the last 30 days (trials vs customers).
  Returns a list of maps with status and count.
  """
  def get_new_users_last_30_days do
    today = Date.utc_today()
    start_date = Date.add(today, -30)
    now = DateTime.utc_now()

    # Get all users created in the last 30 days
    users_query =
      from(u in User,
        where: fragment("date(?)", u.inserted_at) >= ^start_date,
        select: %{
          id: u.id,
          is_customer:
            fragment(
              "?.subscription->>'status' = ? AND ?.subscription->>'stripe_customer_id' IS NOT NULL",
              u,
              "active",
              u
            ),
          is_trial: not is_nil(u.trial_end) and fragment("? > ?", u.trial_end, ^now),
          inserted_at: u.inserted_at
        }
      )

    # Process the results
    users = Repo.all(users_query)

    # Count users in each category
    new_trials =
      users
      |> Enum.count(fn user ->
        user.is_trial
      end)

    new_customers =
      users
      |> Enum.count(fn user ->
        user.is_customer
      end)

    [
      %{status: "new_trials", count: new_trials, label: "New Trials"},
      %{status: "new_customers", count: new_customers, label: "New Customers"}
    ]
  end

  @doc """
  Gets users whose trial will end within the next 3 days.
  Returns a list of users with their email, name, and trial_end date.
  Limited to 10 users.
  """
  def get_users_with_ending_trial do
    today = Date.utc_today()
    three_days_from_now = Date.add(today, 3)

    query =
      from u in User,
        where: fragment("date(?)", u.trial_end) >= ^today,
        where: fragment("date(?)", u.trial_end) <= ^three_days_from_now,
        order_by: [asc: u.trial_end],
        limit: 10,
        select: %{
          id: u.id,
          email: u.email,
          name: u.name,
          trial_end: u.trial_end
        }

    Repo.all(query)
  end

  @doc """
  Gets new customers (users with active subscriptions).
  Returns a list of users with their email, name, and subscription start date.
  Limited to 10 users.
  """
  def get_new_customers do
    query =
      from u in User,
        where: fragment("?.subscription->>'status' = ?", u, "active"),
        where: fragment("?.subscription->>'stripe_subscription_id' IS NOT NULL", u),
        order_by: [desc: fragment("(?.subscription->>'period_start')::timestamp", u)],
        limit: 10,
        select: %{
          id: u.id,
          email: u.email,
          name: u.name,
          subscription_start: fragment("(?.subscription->>'period_start')::timestamp", u)
        }

    Repo.all(query)
  end

  @doc """
  Gets existing customers (users with active subscriptions).
  Returns a list of users with their email, name, and subscription start date.
  Limited to the specified number of users.
  """
  def get_existing_customers(limit \\ 10) do
    query =
      from u in User,
        where: fragment("?.subscription->>'status' = ?", u, "active"),
        where: fragment("?.subscription->>'stripe_subscription_id' IS NOT NULL", u),
        order_by: [asc: u.email],
        limit: ^limit,
        select: %{
          id: u.id,
          email: u.email,
          name: u.name,
          subscription_start: fragment("(?.subscription->>'period_start')::timestamp", u)
        }

    Repo.all(query)
  end

  @doc """
  Gets the total count of existing customers (users with active subscriptions).
  """
  def get_total_customers_count do
    query =
      from u in User,
        where: fragment("?.subscription->>'status' = ?", u, "active"),
        where: fragment("?.subscription->>'stripe_subscription_id' IS NOT NULL", u),
        select: count(u.id)

    Repo.one(query)
  end

  # Helper function to clean up empty filter values
  defp clean_empty_filters(%{"filters" => filters} = params) when is_list(filters) do
    filters =
      Enum.map(filters, fn filter ->
        filter
        |> maybe_remove_empty_value()
        |> maybe_remove_empty_op()
      end)
      # Keep only filters with more than just a field
      |> Enum.filter(&(map_size(&1) > 1))

    %{params | "filters" => filters}
  end

  # Handle filters as a map with numeric keys (as seen in the error log)
  defp clean_empty_filters(%{"filters" => filters} = params) when is_map(filters) do
    filters =
      filters
      |> Enum.map(fn {key, filter} ->
        filter =
          filter
          |> maybe_remove_empty_value()
          |> maybe_remove_empty_op()

        # Only keep filters that have more than just a field
        if map_size(filter) > 1 do
          {key, filter}
        else
          nil
        end
      end)
      |> Enum.reject(&is_nil/1)
      |> Map.new()

    %{params | "filters" => filters}
  end

  defp clean_empty_filters(params), do: params

  # Helper functions to clean filter values
  defp maybe_remove_empty_value(%{"value" => value} = filter) when value in ["", nil] do
    Map.delete(filter, "value")
  end

  defp maybe_remove_empty_value(filter), do: filter

  defp maybe_remove_empty_op(%{"op" => op} = filter) when op in ["", nil] do
    Map.delete(filter, "op")
  end

  defp maybe_remove_empty_op(filter), do: filter

  # Convert filters from map format to list format if needed
  defp normalize_filters_format(%{"filters" => filters} = params)
       when is_map(filters) and not is_list(filters) do
    filters_list =
      filters
      |> Enum.map(fn {_key, filter} -> filter end)
      |> Enum.filter(fn filter ->
        # Keep only filters that have a field and either an op or a value
        Map.has_key?(filter, "field") and
          (Map.has_key?(filter, "op") or Map.has_key?(filter, "value"))
      end)

    %{params | "filters" => filters_list}
  end

  defp normalize_filters_format(params), do: params

  @doc """
  Returns a paginated list of posts with filters.
  """
  def list_posts_with_filters(
        user_id_filter,
        status_filter,
        network_filter,
        post_id_filter,
        final_status_date,
        page,
        per_page,
        sort_field \\ "inserted_at",
        sort_direction \\ :desc
      ) do
    try do
      # Use the AdminPostView schema for querying
      query = from(p in Crosspost.Admin.Resources.AdminPostView)

      # Apply filters
      query =
        if post_id_filter && post_id_filter != "" do
          from p in query,
            where: p.id == ^post_id_filter
        else
          query
        end

      query =
        if user_id_filter && user_id_filter != "" do
          from p in query,
            where: p.user_id == ^user_id_filter
        else
          query
        end

      query =
        cond do
          is_list(status_filter) && status_filter != [] ->
            from p in query,
              where: p.status in ^status_filter

          true ->
            # When no status filter is selected, don't apply any status filter
            query
        end

      query =
        cond do
          is_list(network_filter) && network_filter != [] ->
            from p in query,
              where: fragment("? && ?", p.social_networks, ^network_filter)

          true ->
            # When no network filter is selected, don't apply any network filter
            query
        end

      # Apply date filters
      query =
        case final_status_date do
          "today" ->
            today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
            today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59.999999])

            from p in query,
              where: p.inserted_at >= ^today_start and p.inserted_at <= ^today_end

          "yesterday" ->
            yesterday = Date.add(Date.utc_today(), -1)
            yesterday_start = DateTime.new!(yesterday, ~T[00:00:00])
            yesterday_end = DateTime.new!(yesterday, ~T[23:59:59.999999])

            from p in query,
              where: p.inserted_at >= ^yesterday_start and p.inserted_at <= ^yesterday_end

          "this_week" ->
            start_of_week = Date.beginning_of_week(Date.utc_today())
            end_of_week = Date.end_of_week(Date.utc_today())
            week_start = DateTime.new!(start_of_week, ~T[00:00:00])
            week_end = DateTime.new!(end_of_week, ~T[23:59:59.999999])

            from p in query,
              where: p.inserted_at >= ^week_start and p.inserted_at <= ^week_end

          "last_30_days" ->
            thirty_days_ago = DateTime.add(DateTime.utc_now(), -30 * 24 * 3600, :second)

            from p in query,
              where: p.inserted_at >= ^thirty_days_ago

          date when is_binary(date) and date != "" ->
            # Try to parse as a specific date for published_at filtering
            case parse_datetime(date) do
              {:ok, parsed_date} ->
                # Get the start and end of the day
                start_of_day = %{parsed_date | hour: 0, minute: 0, second: 0}
                end_of_day = %{parsed_date | hour: 23, minute: 59, second: 59}

                from p in query,
                  where:
                    p.published_at >= ^start_of_day and
                      p.published_at <= ^end_of_day

              _ ->
                query
            end

          _ ->
            query
        end

      # Count total posts for pagination
      total_count = Repo.aggregate(query, :count, :id)
      total_pages = ceil(total_count / per_page)

      # Apply sorting
      query =
        case sort_field do
          "published_at" ->
            # For published_at sorting, we need to handle nulls properly
            case sort_direction do
              :desc ->
                from p in query,
                  order_by: [desc_nulls_last: p.published_at, desc: p.id]

              :asc ->
                from p in query,
                  order_by: [asc_nulls_last: p.published_at, desc: p.id]
            end

          field when field in ["inserted_at", "updated_at"] ->
            # For standard date fields, use straightforward sorting
            direction = if sort_direction == :asc, do: :asc, else: :desc

            from p in query,
              order_by: [{^direction, field(p, ^String.to_atom(field))}, desc: p.id]

          _ ->
            # Default sorting
            from p in query,
              order_by: [desc: p.updated_at, desc: p.id]
        end

      # Apply pagination
      posts =
        query
        |> limit(^per_page)
        |> offset(^((page - 1) * per_page))
        |> Repo.all()

      {:ok, %{posts: posts, total_count: total_count, total_pages: total_pages}}
    rescue
      e in Ecto.Query.CastError ->
        {:error, "Invalid filter parameters: #{e.message}"}

      e in _ ->
        {:error, "An error occurred while fetching posts: #{inspect(e)}"}
    end
  end

  # Helper function to parse datetime strings
  defp parse_datetime(date_string) when is_binary(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} ->
        {:ok, DateTime.new!(date, ~T[00:00:00], "Etc/UTC")}

      _ ->
        :error
    end
  end

  defp parse_datetime(_), do: :error

  @doc """
  Fetches current subscription information from Stripe for a user.
  Returns {:ok, subscription_data} if successful, {:error, reason} otherwise.
  """
  def fetch_subscription_info(%User{} = user) do
    with %{subscription: %{stripe_subscription_id: sub_id}} when is_binary(sub_id) <- user,
         {:ok, stripe_sub} <-
           Stripe.Subscription.retrieve(sub_id, expand: ["items.data.price.product"]),
         {:ok, features} <-
           Crosspost.Billing.Stripe.get_features(
             stripe_sub.items.data
             |> List.first()
             |> Map.get(:price)
             |> Map.get(:product)
             |> Map.get(:id)
           ) do
      subscription_data = %{
        "status" => stripe_sub.status,
        "period_start" => DateTime.from_unix!(stripe_sub.current_period_start),
        "period_end" => DateTime.from_unix!(stripe_sub.current_period_end),
        "stripe_customer_id" => stripe_sub.customer,
        "stripe_subscription_id" => stripe_sub.id,
        "stripe_product_id" =>
          stripe_sub.items.data
          |> List.first()
          |> Map.get(:price)
          |> Map.get(:product)
          |> Map.get(:id),
        "stripe_price_id" =>
          stripe_sub.items.data |> List.first() |> Map.get(:price) |> Map.get(:id),
        "features" => features.data |> Enum.map(& &1.entitlement_feature.lookup_key)
      }

      {:ok, subscription_data}
    else
      %{subscription: nil} ->
        {:error, "User has no subscription"}

      %{subscription: %{stripe_subscription_id: nil}} ->
        {:error, "No Stripe subscription ID found"}

      {:error, %Stripe.Error{} = error} ->
        {:error, "Stripe error: #{error.message}"}

      error ->
        {:error, "Unexpected error: #{inspect(error)}"}
    end
  end
end
