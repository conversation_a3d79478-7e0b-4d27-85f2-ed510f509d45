defmodule Crosspost.Plans do
  @moduledoc """
  The Plans context.
  """

  import Ecto.Query, warn: false

  alias Crosspost.Repo
  alias Crosspost.Accounts.{Plan, User}

  @doc """
  Adds a user to a plan.
  """
  def add_user_to_plan(%User{} = user, %Plan{} = plan) do
    user
    |> Ecto.Changeset.change(%{plan_id: plan.key})
    |> Repo.update()
  end

  @doc """
  Lists all active (non-archived) plans.
  """
  def list_active_plans do
    Plan
    |> where([p], p.archived == false)
    |> order_by([p], asc: p.price)
    |> Repo.all()
  end

  def get_plan(key) do
    Repo.get_by(Plan, key: key)
  end

  def get_plan!(key) do
    Repo.get_by!(Plan, key: key)
  end

  def get_plan_by_stripe_price_id(stripe_price_id) do
    case Repo.get_by(Plan, stripe_price_id: stripe_price_id) do
      nil -> {:error, {:plan_not_found, stripe_price_id}}
      plan -> {:ok, plan}
    end
  end

  def get_default_plan do
    Repo.get_by!(Plan, default: true)
  end
end
