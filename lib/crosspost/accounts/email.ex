defmodule Crosspost.Accounts.Email do
  use Swoosh.Mailer, otp_app: :crosspost

  import Swoosh.Email

  def deliver_workspace_invitation(email, workspace, code) do
    url = CrosspostWeb.Endpoint.url()
    invite_url = "#{url}/workspaces/#{workspace.slug}/join/#{code}"

    new()
    |> to(email)
    |> from({"JustCrossPost", "<EMAIL>"})
    |> subject("Invitation to join #{workspace.name} on JustCrossPost")
    |> text_body("""
    You've been invited to join #{workspace.name} on JustCrossPost!

    Click the link below to accept the invitation:
    #{invite_url}

    If you don't have a JustCrossPost account yet, you'll be asked to create one before joining the workspace.

    This invitation link will expire in 7 days.

    Best regards,
    The JustCrossPost Team
    """)
    |> html_body("""
    <div style="max-width: 600px; margin: 0 auto; font-family: system-ui, -apple-system, sans-serif;">
      <div style="background-color: #f3f4f6; padding: 2rem; border-radius: 8px; margin: 2rem 0;">
        <h1 style="color: #1f2937; margin-bottom: 1.5rem; font-size: 1.5rem;">
          You've been invited to join #{workspace.name} on JustCrossPost!
        </h1>

        <p style="color: #4b5563; margin-bottom: 1.5rem; line-height: 1.5;">
          You've been invited to collaborate in the <strong>#{workspace.name}</strong> workspace on JustCrossPost.
        </p>

        <div style="margin: 2rem 0;">
          <a href="#{invite_url}"
             style="display: inline-block; background-color: #2563eb; color: white; padding: 0.75rem 1.5rem;
                    text-decoration: none; border-radius: 6px; font-weight: 500;">
            Accept Invitation
          </a>
        </div>

        <p style="color: #6b7280; font-size: 0.875rem; margin-top: 2rem;">
          If you don't have a JustCrossPost account yet, you'll be asked to create one before joining the workspace.
        </p>

        <p style="color: #6b7280; font-size: 0.875rem; margin-top: 1rem;">
          This invitation link will expire in 7 days.
        </p>

        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 2rem 0;" />

        <p style="color: #6b7280; font-size: 0.875rem;">
          Best regards,<br/>
          The JustCrossPost Team
        </p>
      </div>
    </div>
    """)
  end
end
