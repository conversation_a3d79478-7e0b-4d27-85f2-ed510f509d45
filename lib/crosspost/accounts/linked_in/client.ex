defmodule Crosspost.Accounts.LinkedIn.Client do
  require <PERSON><PERSON>

  alias Ueberauth.Strategy.LinkedIn.OAuth

  @callback fetch_organizations(map()) :: {:ok, list()} | {:error, any()}
  @callback refresh_token(map()) :: {:ok, map()} | {:error, any()}

  @behaviour __MODULE__

  @doc """
  Refreshes the access token for a LinkedIn connection using its refresh token.

  ## Parameters
    - connection: The connection struct containing the refresh token

  ## Returns
    - {:ok, %{access_token: String.t(), refresh_token: String.t(), expires_at: DateTime.t()}} on success
    - {:error, any()} on failure
  """
  @impl true
  def refresh_token(%{encrypted_refresh_token: refresh_token} = _connection)
      when is_binary(refresh_token) do
    try do
      case OAuth.refresh_token!(refresh_token) do
        {:ok, token} ->
          expires_at =
            case token.expires_at do
              nil ->
                nil

              timestamp when is_integer(timestamp) ->
                DateTime.from_unix!(timestamp)
            end

          {:ok,
           %{
             access_token: token.access_token,
             refresh_token: token.refresh_token,
             expires_at: expires_at
           }}

        {:error, error} ->
          Logger.error("Error refreshing LinkedIn token", %{
            event: "linkedin.refresh_token_error",
            error: inspect(error)
          })

          {:error, error}
      end
    rescue
      e ->
        Logger.error("Exception refreshing LinkedIn token", %{
          event: "linkedin.refresh_token_exception",
          error: inspect(e),
          stacktrace: __STACKTRACE__
        })

        {:error, e}
    end
  end

  @impl true
  def refresh_token(_connection) do
    {:error, :no_refresh_token}
  end

  @impl true
  def fetch_organizations(connection) do
    url =
      "https://api.linkedin.com/rest/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&state=APPROVED"

    headers = [
      {"Authorization", "Bearer #{connection.encrypted_access_token}"},
      {"Content-Type", "application/json"},
      {"LinkedIn-Version", "202411"},
      {"X-RestLi-Protocol-Version", "2.0.0"}
    ]

    opts =
      [
        headers: headers,
        url: url
      ]
      |> add_test_options()

    case Req.get(url, opts) do
      {:ok, %Req.Response{status: 200, body: %{"elements" => elements}}} ->
        # Extract organization IDs and fetch details
        org_ids =
          elements
          |> Enum.map(fn element ->
            case element["organization"] do
              "urn:li:organization:" <> id -> id
              id when is_binary(id) -> String.replace(id, "urn:li:organization:", "")
              id -> to_string(id)
            end
          end)

        case fetch_organization_details(org_ids, connection) do
          {:ok, organizations} ->
            {:ok, organizations}

          error ->
            error
        end

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("Failed to fetch LinkedIn organizations", %{
          event: "linkedin.fetch_organizations_failed",
          status: status,
          body: inspect(body)
        })

        {:error, "Failed to fetch organizations: HTTP #{status}"}

      {:error, reason} ->
        Logger.error("Error fetching LinkedIn organizations", %{
          event: "linkedin.fetch_organizations_error",
          error: inspect(reason)
        })

        {:error, reason}
    end
  end

  defp fetch_organization_details(org_ids, connection) do
    # Build the organizations lookup URL with IDs
    ids_param = Enum.join(org_ids, ",")
    url = "https://api.linkedin.com/rest/organizations?ids=List(#{ids_param})"

    headers = [
      {"Authorization", "Bearer #{connection.encrypted_access_token}"},
      {"Content-Type", "application/json"},
      {"LinkedIn-Version", "202411"},
      {"X-RestLi-Protocol-Version", "2.0.0"}
    ]

    opts =
      [
        headers: headers,
        url: url
      ]
      |> add_test_options()

    case Req.get(url, opts) do
      {:ok, %Req.Response{status: 200, body: %{"results" => results}}} ->
        organizations =
          results
          |> Map.values()
          |> Enum.map(fn org ->
            # Extract logo URLs if available
            resolved_logo_urls = %{
              "cropped" => get_logo_url(org, "cropped", connection),
              "original" => get_logo_url(org, "original", connection)
            }

            # Keep all original data and add resolved logo URLs
            Map.put(org, "resolved_logo_urls", resolved_logo_urls)
          end)

        {:ok, organizations}

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("Failed to fetch LinkedIn organization details", %{
          event: "linkedin.fetch_organization_details_failed",
          status: status,
          body: inspect(body)
        })

        {:error, "Failed to fetch organization details: HTTP #{status}"}

      {:error, reason} ->
        Logger.error("Error fetching LinkedIn organization details", %{
          event: "linkedin.fetch_organization_details_error",
          error: inspect(reason)
        })

        {:error, reason}
    end
  end

  # Helper function to extract logo URLs from organization data
  defp get_logo_url(org, type, connection) do
    case get_in(org, ["logoV2"]) do
      %{"cropped" => cropped_urn, "original" => original_urn} ->
        urn = if type == "cropped", do: cropped_urn, else: original_urn

        case urn do
          "urn:li:digitalmediaAsset:" <> _ ->
            # Convert digitalmediaAsset URN to image URN and encode it
            image_urn =
              urn
              |> String.replace("digitalmediaAsset:", "image:")
              |> URI.encode_www_form()

            url = "https://api.linkedin.com/rest/images/#{image_urn}?fields=downloadUrl"

            headers = [
              {"Authorization", "Bearer #{connection.encrypted_access_token}"},
              {"Content-Type", "application/json"},
              {"LinkedIn-Version", "202411"},
              {"X-RestLi-Protocol-Version", "2.0.0"}
            ]

            opts =
              [
                headers: headers,
                url: url
              ]
              |> add_test_options()

            case Req.get(url, opts) do
              {:ok, %Req.Response{status: 200, body: %{"downloadUrl" => download_url}}} ->
                download_url

              {:ok, %Req.Response{status: status, body: body}} ->
                Logger.error("Failed to fetch image URL", %{
                  event: "linkedin.fetch_image_url_failed",
                  status: status,
                  urn: image_urn,
                  body: inspect(body)
                })

                nil

              {:error, reason} ->
                Logger.error("Error fetching image URL", %{
                  event: "linkedin.fetch_image_url_error",
                  error: inspect(reason),
                  urn: image_urn
                })

                nil
            end

          _ ->
            nil
        end

      _ ->
        nil
    end
  end

  defp add_test_options(opts) do
    Keyword.merge(opts, Application.get_env(:crosspost, :linked_in_req_options) || [])
  end
end
