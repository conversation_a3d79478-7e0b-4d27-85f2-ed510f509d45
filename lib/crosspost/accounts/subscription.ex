defmodule Crosspost.Accounts.Subscription do
  @moduledoc """
  Embedded schema for user subscription details.
  """
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :plan, :string
    field :status, :string
    field :period_start, :utc_datetime
    field :period_end, :utc_datetime
    field :features, {:array, :string}, default: []
    field :stripe_customer_id, :string
    field :stripe_subscription_id, :string
    field :stripe_product_id, :string
    field :stripe_price_id, :string
  end

  def changeset(subscription, attrs) do
    subscription
    |> cast(attrs, [
      :status,
      :plan,
      :period_start,
      :period_end,
      :features,
      :stripe_customer_id,
      :stripe_subscription_id,
      :stripe_product_id,
      :stripe_price_id
    ])
  end
end
