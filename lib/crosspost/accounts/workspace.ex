defmodule Crosspost.Accounts.Workspace do
  use Ecto.Schema
  import Ecto.Changeset

  require Logger

  alias Crosspost.Accounts.{WorkspaceUser, Connection, User}
  alias Crosspost.Publishing.{Post, Attachment}

  schema "workspaces" do
    field :name, :string
    field :slug, :string
    field :settings, :map, default: %{}
    field :is_default, :boolean, default: false
    field :invitations, {:array, :map}, default: []

    has_many :workspace_users, WorkspaceUser
    has_many :users, through: [:workspace_users, :user]
    has_many :posts, Post, on_delete: :delete_all
    has_many :attachments, Attachment, on_delete: :delete_all
    has_many :connections, Connection, on_delete: :delete_all

    belongs_to :owner, User

    timestamps()
  end

  def changeset(workspace, attrs) do
    workspace
    |> cast(attrs, [:name, :settings, :is_default, :invitations, :owner_id])
    |> validate_required([:name])
    |> validate_length(:name, min: 1, max: 255)
    |> unique_constraint(:name)
    |> maybe_add_random_suffix_to_slug()
    |> unique_constraint(:slug)
  end

  defp maybe_add_random_suffix_to_slug(changeset) do
    case get_change(changeset, :name) do
      nil ->
        changeset

      name ->
        base_slug =
          name
          |> String.downcase()
          |> String.replace(~r/[^a-z0-9\s-]/, "")
          |> String.replace(~r/\s+/, "-")
          |> String.trim("-")

        # Always add random suffix for default workspaces
        slug =
          if get_change(changeset, :is_default) || get_field(changeset, :is_default) do
            suffix =
              :crypto.strong_rand_bytes(4)
              |> Base.url_encode64(padding: false)
              |> String.downcase()

            "#{base_slug}-#{suffix}"
          else
            base_slug
          end

        put_change(changeset, :slug, slug)
    end
  end

  def add_invitation(workspace, email) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    valid_until = DateTime.add(now, 7, :day)

    # Check if email is already invited and invitation is still valid
    existing_invitation =
      Enum.find(workspace.invitations || [], fn invitation ->
        invitation["email"] == email &&
          invitation["invited_at"] &&
          case DateTime.from_iso8601(invitation["valid_until"]) do
            {:ok, valid_until, _offset} -> DateTime.compare(valid_until, now) == :gt
            _ -> false
          end
      end)

    if existing_invitation do
      {:error, :already_invited}
    else
      # Remove any expired invitations for this email
      cleaned_invitations =
        Enum.reject(workspace.invitations || [], fn invitation ->
          invitation["email"] == email
        end)

      # Add new invitation
      new_invitation = %{
        "email" => email,
        "code" => generate_invitation_code(),
        "invited_at" => now |> DateTime.to_iso8601(),
        "valid_until" => valid_until |> DateTime.to_iso8601()
      }

      workspace
      |> change(%{invitations: [new_invitation | cleaned_invitations]})
    end
  end

  def find_invitation(workspace, code) do
    now = DateTime.utc_now()

    Logger.debug("Finding invitation",
      workspace_id: workspace.id,
      code: code,
      invitations: workspace.invitations
    )

    invitation =
      Enum.find(workspace.invitations || [], fn invitation ->
        valid =
          invitation["code"] == code &&
            invitation["invited_at"] &&
            case DateTime.from_iso8601(invitation["valid_until"]) do
              {:ok, valid_until, _offset} ->
                comparison = DateTime.compare(valid_until, now)

                Logger.debug("Checking invitation validity",
                  code: code,
                  valid_until: valid_until,
                  now: now,
                  comparison: comparison
                )

                comparison == :gt

              error ->
                Logger.debug("Failed to parse valid_until",
                  code: code,
                  error: inspect(error)
                )

                false
            end

        Logger.debug("Invitation validity check",
          code: code,
          invitation_code: invitation["code"],
          valid: valid
        )

        valid
      end)

    Logger.debug("Find invitation result",
      code: code,
      found: invitation != nil
    )

    invitation
  end

  def remove_invitation(workspace, code) do
    workspace
    |> cast(%{invitations: Enum.reject(workspace.invitations || [], &(&1["code"] == code))}, [
      :invitations
    ])
  end

  defp generate_invitation_code do
    :crypto.strong_rand_bytes(16)
    |> Base.url_encode64(padding: false)
  end
end
