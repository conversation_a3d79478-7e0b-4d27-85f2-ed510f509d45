defmodule Crosspost.Accounts.SocialProfile do
  use Ecto.Schema
  import Ecto.Changeset

  @type t() :: %__MODULE__{}

  @derive {Jason.Encoder,
           only: [
             :id,
             :name,
             :bsky_handle,
             :x_handle,
             :mastodon_handle,
             :user_id,
             :inserted_at,
             :updated_at
           ]}

  schema "social_profiles" do
    field :name, :string
    field :bsky_handle, :string
    field :x_handle, :string
    field :mastodon_handle, :string

    belongs_to :user, Crosspost.Accounts.User

    timestamps()
  end

  def changeset(social_profile, attrs) do
    social_profile
    |> cast(attrs, [:name, :bsky_handle, :x_handle, :mastodon_handle, :user_id])
    |> validate_required([:name, :user_id])
    |> clean_handles()
    |> validate_at_least_one_handle()
    |> unique_constraint([:name, :user_id], message: "Profile name already taken")
  end

  defp validate_at_least_one_handle(changeset) do
    bsky_handle = get_field(changeset, :bsky_handle)
    x_handle = get_field(changeset, :x_handle)
    mastodon_handle = get_field(changeset, :mastodon_handle)

    if is_nil(bsky_handle) && is_nil(x_handle) && is_nil(mastodon_handle) do
      add_error(changeset, :base, "At least one social handle is required")
    else
      changeset
    end
  end

  defp clean_handles(changeset) do
    changeset
    |> update_change(:bsky_handle, &clean_handle/1)
    |> update_change(:x_handle, &clean_handle/1)
    |> update_change(:mastodon_handle, &clean_mastodon_handle/1)
  end

  defp clean_handle(nil), do: nil

  defp clean_handle(handle) when is_binary(handle) do
    handle
    |> String.trim()
    |> String.replace_prefix("@", "")
  end

  # Special handling for Mastodon handles which include the instance
  defp clean_mastodon_handle(nil), do: nil

  defp clean_mastodon_handle(handle) when is_binary(handle) do
    handle
    |> String.trim()
    |> String.replace_prefix("@", "")
    |> String.split("@")
    |> case do
      [username, instance] -> "#{username}@#{instance}"
      [username] -> username
    end
  end
end
