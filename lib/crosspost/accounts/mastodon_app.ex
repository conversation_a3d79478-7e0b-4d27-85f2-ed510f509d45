defmodule Crosspost.Accounts.MastodonApp do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
   only: [
     :id,
     :instance,
     # This is usually public
     :client_id,
     :inserted_at,
     :updated_at
   ]}

  schema "mastodon_apps" do
    field :instance, :string
    field :client_id, :string
    field :client_secret, :string

    timestamps()
  end

  @doc false
  def changeset(mastodon_app, attrs) do
    mastodon_app
    |> cast(attrs, [:instance, :client_id, :client_secret])
    |> validate_required([:instance, :client_id, :client_secret])
    |> unique_constraint(:instance)
  end
end

defimpl Ueberauth.Strategy.Mastodon.API.MastodonAppToString, for: Crosspost.Accounts.MastodonApp do
  def to_string(mastodon_app) do
    "https://" <> String.trim_leading(mastodon_app.instance, "https://")
  end
end
