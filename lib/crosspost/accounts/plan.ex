defmodule Crosspost.Accounts.Plan do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:key, :string, []}
  @derive {Phoenix.Param, key: :key}

  @derive {Inspect,
           only: [
             :key,
             :name,
             :description,
             :price,
             :stripe_price_id,
             :highlight,
             :enabled,
             :cta,
             :action,
             :features,
             :archived,
             :default
           ]}

  schema "plans" do
    field :name, :string
    field :description, :string
    field :price, :integer
    field :stripe_price_id, :string
    field :highlight, :boolean, default: false
    field :enabled, :boolean, default: false
    field :cta, :string
    field :action, :string
    field :features, {:array, :string}, default: []
    field :archived, :boolean, default: false
    field :default, :boolean, default: false

    has_many :users, Crosspost.Accounts.User

    timestamps()
  end

  def changeset(plan, attrs) do
    plan
    |> cast(attrs, [
      :key,
      :name,
      :description,
      :price,
      :stripe_price_id,
      :highlight,
      :enabled,
      :cta,
      :action,
      :features,
      :archived,
      :default
    ])
    |> validate_required([:key, :name, :price, :features])
  end
end
