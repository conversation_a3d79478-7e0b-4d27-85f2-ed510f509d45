defmodule Crosspost.Accounts.X.Client do
  @moduledoc """
  Client for the X (Twitter) API.
  """

  require Logger

  @callback search_profiles(String.t(), String.t(), String.t()) ::
              {:ok, list()} | {:error, String.t()}

  @callback get_profile(String.t(), String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @behaviour __MODULE__

  @impl true
  def get_profile(access_token, user_id) do
    url = "https://api.twitter.com/2/users/#{user_id}"

    # Include user.fields parameter to get subscription_type
    params = %{
      "user.fields" =>
        "description,profile_image_url,url,location,verified,protected,public_metrics,subscription_type"
    }

    opts = [
      headers: [
        {"Authorization", "Bearer #{access_token}"},
        {"Content-Type", "application/json"}
      ],
      params: params,
      url: url
    ]

    case Req.get(url, opts) do
      {:ok, %Req.Response{status: 200, body: %{"data" => profile}}} ->
        {:ok, profile}

      {:ok, %Req.Response{status: 401}} ->
        {:error, :expired_token}

      {:ok, %Req.Response{status: status, body: body}} ->
        error_msg =
          case body do
            %{"errors" => [%{"message" => message} | _]} -> message
            %{"error" => error} when is_binary(error) -> error
            _ -> inspect(body)
          end

        Logger.error("Failed to fetch X profile", %{
          event: "x.client.get_profile.error",
          status: status,
          body: body,
          user_id: user_id
        })

        {:error, "Failed to fetch X profile: #{error_msg}"}

      {:error, %Req.TransportError{reason: reason}} ->
        Logger.error("Transport error fetching X profile", %{
          event: "x.client.get_profile.transport_error",
          error: inspect(reason),
          user_id: user_id
        })

        {:error, "Transport error: #{inspect(reason)}"}

      {:error, reason} ->
        Logger.error("Error fetching X profile", %{
          event: "x.client.get_profile.error",
          error: inspect(reason),
          user_id: user_id
        })

        {:error, "Request failed: #{inspect(reason)}"}
    end
  end

  @impl true
  def search_profiles(access_token, refresh_token, query) do
    url = "https://api.twitter.com/1.1/users/search.json"

    params = %{
      "q" => query,
      "count" => "20",
      "include_entities" => "false"
    }

    # Create OAuth headers
    timestamp = :os.system_time(:second) |> to_string()
    nonce = :crypto.strong_rand_bytes(32) |> Base.encode64() |> binary_part(0, 32)

    oauth_params = %{
      "oauth_consumer_key" => System.get_env("TWITTER_CONSUMER_KEY"),
      "oauth_nonce" => nonce,
      "oauth_signature_method" => "HMAC-SHA1",
      "oauth_timestamp" => timestamp,
      "oauth_token" => access_token,
      "oauth_version" => "1.0"
    }

    # Combine all parameters for signature
    all_params = Map.merge(params, oauth_params)

    # Create signature
    signature = create_signature("GET", url, all_params, refresh_token)
    oauth_params = Map.put(oauth_params, "oauth_signature", signature)

    # Build Authorization header
    authorization_header = build_oauth_header(oauth_params)

    Logger.debug("OAuth Header: #{authorization_header}")
    Logger.debug("Params: #{inspect(params)}")
    Logger.debug("URL: #{url}")

    # Build the full URL with query parameters
    query_string = URI.encode_query(params)
    full_url = "#{url}?#{query_string}"

    opts = [
      headers: [
        {"Authorization", authorization_header}
      ],
      url: full_url
    ]

    case Req.get(full_url, opts) do
      {:ok, %Req.Response{status: 200, body: users}} when is_list(users) ->
        {:ok, users}

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("X API Error",
          status: status,
          body: body,
          headers: authorization_header,
          url: full_url
        )

        {:error, "Failed to search profiles with status #{status}: #{inspect(body)}"}

      {:error, error} ->
        {:error, "Request failed: #{inspect(error)}"}
    end
  end

  defp create_signature(method, url, params, token_secret) do
    # Sort parameters and create parameter string
    param_string =
      params
      |> Enum.sort()
      |> Enum.map(fn {k, v} -> "#{uri_encode(k)}=#{uri_encode(v)}" end)
      |> Enum.join("&")

    # Create signature base string
    signature_base_string =
      [
        method,
        uri_encode(url),
        uri_encode(param_string)
      ]
      |> Enum.join("&")

    Logger.debug("Signature Base String: #{signature_base_string}")

    # Create signing key
    signing_key =
      [
        uri_encode(System.get_env("TWITTER_CONSUMER_SECRET")),
        uri_encode(token_secret)
      ]
      |> Enum.join("&")

    # Generate signature
    :crypto.mac(:hmac, :sha, signing_key, signature_base_string)
    |> Base.encode64()
  end

  defp build_oauth_header(oauth_params) do
    header_value =
      oauth_params
      |> Enum.sort()
      |> Enum.map(fn {k, v} -> ~s(#{uri_encode(k)}="#{uri_encode(v)}") end)
      |> Enum.join(", ")

    "OAuth " <> header_value
  end

  defp uri_encode(value) do
    value
    |> to_string()
    |> URI.encode(&URI.char_unreserved?/1)
  end
end
