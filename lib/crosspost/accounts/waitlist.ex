defmodule Crosspost.Accounts.Waitlist do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :id,
             :email,
             :used_at,
             :invited_at,
             :inserted_at,
             :updated_at
           ]}

  @derive {Flop.Schema,
           filterable: [:email, :spam_status, :invitation_code],
           sortable: [:email, :invited_at, :used_at, :inserted_at, :updated_at],
           default_order: %{
             order_by: [:inserted_at],
             order_directions: [:desc]
           }}

  schema "waitlist" do
    field :email, :string
    field :invitation_code, :string
    field :used_at, :utc_datetime
    field :invited_at, :utc_datetime
    field :spam_status, Ecto.Enum, values: [:spam, :potential_spam]
    timestamps()
  end

  def changeset(waitlist, attrs) do
    waitlist
    |> cast(attrs, [:email, :invitation_code, :used_at, :invited_at, :spam_status])
    |> validate_required([:email])
    |> unique_constraint(:email)
    |> unique_constraint(:invitation_code, where: "invitation_code IS NOT NULL")
  end
end
