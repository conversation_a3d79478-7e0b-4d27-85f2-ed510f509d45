defmodule Crosspost.Accounts.User do
  use Ecto.Schema

  @derive {Inspect,
           only: [
             :id,
             :uid,
             :email,
             :name,
             :default_workspace_id,
             :timezone,
             :trial_end,
             :plan_key,
             :subscription,
             :settings,
             :status,
             :customer
           ]}

  import Ecto.Changeset

  alias Crosspost.Publishing.Post

  alias Crosspost.Accounts.Workspace
  alias Crosspost.Accounts.WorkspaceUser
  alias Crosspost.Accounts.Plan
  alias Crosspost.Accounts.Connection
  alias Crosspost.Accounts.FeatureUsage
  alias Crosspost.Accounts.UserFeature
  alias Crosspost.Accounts.EnabledFeature
  alias Crosspost.Accounts.SocialProfile
  alias Crosspost.Accounts.Subscription

  defmodule Settings do
    use Ecto.Schema

    import Ecto.Changeset

    @fields [:onboarding_completed, :sidebar_hidden, :default_networks]

    @derive {Jason.Encoder, only: @fields}

    @primary_key false
    embedded_schema do
      field :onboarding_completed, :boolean, default: false
      field :sidebar_hidden, :boolean, default: false
      field :default_networks, {:array, :string}, default: []
    end

    def changeset(settings, attrs) do
      settings
      |> cast(attrs, @fields)
      |> validate_required(@fields)
    end
  end

  @derive {Jason.Encoder,
           only: [
             :uid,
             :status,
             :email,
             :name,
             :confirmed_at,
             :settings,
             :info,
             :timezone,
             :inserted_at,
             :updated_at,
             :trial_end,
             :enabled_features,
             :social_profiles,
             :workspace_settings,
             :customer
           ]}

  @status_enum [:trial, :trial_ended, :active, :canceled, :past_due, :unpaid]

  schema "users" do
    field :uid, Ecto.UUID
    field :status, Ecto.Enum, values: @status_enum, default: :trial
    field :customer, :boolean, default: false
    field :email, :string
    field :name, :string
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :current_password, :string, virtual: true, redact: true
    field :confirmed_at, :utc_datetime
    field :info, :map, default: %{}
    field :timezone, :string, default: "Etc/UTC"
    field :trial_end, :utc_datetime
    field :workspace_settings, :map, virtual: true

    embeds_one :subscription, Subscription, on_replace: :delete
    embeds_one :settings, Settings, on_replace: :update

    belongs_to :default_workspace, Workspace

    belongs_to :plan, Plan,
      foreign_key: :plan_key,
      references: :key,
      type: :string

    has_many :workspace_users, WorkspaceUser
    has_many :workspaces, through: [:workspace_users, :workspace]

    has_many :owned_workspaces, Workspace,
      foreign_key: :owner_id,
      on_delete: :delete_all

    has_many :posts, Post
    has_many :connections, Connection
    has_many :social_profiles, SocialProfile

    has_many :workspace_connections, through: [:workspaces, :connections]

    has_many :user_features, UserFeature
    has_many :features, through: [:user_features, :feature], on_replace: :delete
    has_many :feature_usages, FeatureUsage
    has_many :enabled_features, EnabledFeature
    has_many :usage_counters, EnabledFeature, where: [feature_type: :counter]

    timestamps()
  end

  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :email,
      :name,
      :default_workspace_id,
      :password,
      :hashed_password,
      :current_password,
      :confirmed_at,
      :info,
      :timezone,
      :trial_end,
      :plan_key,
      :status,
      :customer
    ])
    |> cast_embed(:subscription)
    |> cast_embed(:settings)
    |> unique_constraint(:email)
    |> foreign_key_constraint(:plan_key)
    |> validate_trial_end_for_customer()
  end

  defp validate_trial_end_for_customer(changeset) do
    customer = get_field(changeset, :customer)
    trial_end = get_field(changeset, :trial_end)

    if customer == true and not is_nil(trial_end) do
      add_error(changeset, :trial_end, "trial_end cannot be set for customers")
    else
      changeset
    end
  end

  @doc """
  A user changeset for registration.
  """
  def registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email, :password, :confirmed_at, :trial_end, :customer])
    |> put_embed(:settings, %Settings{})
    |> validate_email(opts)
    |> validate_password(opts)
    |> unique_constraint(:email)
    |> foreign_key_constraint(:plan_key)
  end

  @doc """
  A user changeset for registration through OAuth providers.
  This changeset doesn't require a password since authentication is handled by the provider.
  """
  def oauth_registration_changeset(user, attrs) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    user
    |> cast(attrs, [:email, :name, :timezone, :trial_end])
    |> validate_required([:email])
    |> validate_email(validate_email: true)
    |> put_change(:confirmed_at, now)
    |> put_embed(:settings, %Settings{})
    |> unique_constraint(:email)
    |> foreign_key_constraint(:plan_key)
  end

  defp validate_email(changeset, opts) do
    changeset
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> maybe_validate_unique_email(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 12, max: 72)
    # Examples of additional password validation:
    # |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    # |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    # |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/, message: "at least one digit or punctuation character")
    |> maybe_hash_password(opts)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 72, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_validate_unique_email(changeset, opts) do
    if Keyword.get(opts, :validate_email, true) do
      changeset
      |> unsafe_validate_unique(:email, Crosspost.Repo)
      |> unique_constraint(:email)
    else
      changeset
    end
  end

  @doc """
  A user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """
  def email_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_email(opts)
    |> case do
      %{changes: %{email: _}} = changeset -> changeset
      %{} = changeset -> add_error(changeset, :email, "did not change")
    end
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(%Crosspost.Accounts.User{hashed_password: hashed_password}, password)
      when is_binary(hashed_password) and byte_size(password) > 0 do
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Bcrypt.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, nil) do
    changeset
  end

  def validate_current_password(changeset, password) do
    changeset = cast(changeset, %{current_password: password}, [:current_password])

    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end

  def beta_signup_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_required([:email])
    |> validate_email(opts)
  end

  def profile_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:name, :email, :timezone])
    |> validate_required([:name, :email])
    |> validate_timezone()
    |> validate_email(opts)
  end

  defp validate_timezone(changeset) do
    case get_change(changeset, :timezone) do
      nil ->
        changeset

      timezone ->
        if timezone in (Tzdata.canonical_zone_list() ++ ["Etc/UTC"]) do
          changeset
        else
          add_error(changeset, :timezone, "is not a valid timezone")
        end
    end
  end
end
