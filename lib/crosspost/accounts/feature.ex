defmodule Crosspost.Accounts.Feature do
  use Ecto.Schema

  import Ecto.Changeset

  alias Crosspost.Accounts.UserFeature

  @reset_periods [:yearly, :monthly, :daily]
  @types [:counter, :limit, :boolean]

  @primary_key {:key, :string, []}

  schema "features" do
    field :usage_key, :string
    field :type, Ecto.Enum, values: @types
    field :reset_period, Ecto.Enum, values: @reset_periods, default: nil
    field :limit, :integer
    field :name, :string
    field :description, :string
    field :coming_soon, :boolean, default: false

    has_many :user_features, UserFeature, foreign_key: :feature_key, references: :key
    has_many :users, through: [:user_features, :user]

    timestamps()
  end

  def changeset(feature, attrs) do
    feature
    |> cast(attrs, [
      :key,
      :name,
      :description,
      :type,
      :usage_key,
      :reset_period,
      :limit
    ])
    |> validate_required([
      :key,
      :name,
      :description,
      :type,
      :reset_period
    ])
  end
end
