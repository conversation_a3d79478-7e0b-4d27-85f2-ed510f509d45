defmodule Crosspost.Accounts.EnabledFeature do
  use Ecto.Schema

  @derive {Jason.Encoder,
           only: [
             :feature_key,
             :feature_type,
             :feature_name,
             :feature_description,
             :feature_coming_soon,
             :current_usage,
             :usage_key,
             :limit,
             :plan_key,
             :reset_period,
             :period_start,
             :period_end
           ]}

  @primary_key false
  schema "enabled_features" do
    field :user_id, :id
    field :feature_key, :string
    field :feature_type, Ecto.Enum, values: [:counter, :limit, :boolean]
    field :feature_name, :string
    field :feature_description, :string
    field :feature_coming_soon, :boolean
    field :usage_key, :string
    field :current_usage, :integer
    field :limit, :integer
    field :plan_key, :string
    field :reset_period, Ecto.Enum, values: [:yearly, :monthly, :daily]
    field :period_start, :utc_datetime
    field :period_end, :utc_datetime
    field :order, :integer

    belongs_to :user, Crosspost.Accounts.User, define_field: false
  end
end
