defmodule Crosspost.Accounts.FeatureUsage do
  use Ecto.Schema

  import Ecto.Changeset

  alias Crosspost.Accounts.{Feature, User, Workspace}

  @primary_key {:id, :binary_id, autogenerate: true}

  @types [:counter, :limit, :boolean]

  schema "feature_usages" do
    field :usage_key, :string
    field :type, Ecto.Enum, values: @types
    field :value, :integer, default: 0

    belongs_to :workspace, Workspace
    belongs_to :user, User

    belongs_to :feature, Feature,
      foreign_key: :feature_key,
      references: :key,
      type: :string

    timestamps()
  end

  def changeset(feature_usage, attrs) do
    feature_usage
    |> cast(attrs, [
      :feature_key,
      :type,
      :value,
      :workspace_id,
      :user_id,
      :usage_key
    ])
    |> validate_required([
      :feature_key,
      :type,
      :value,
      :user_id,
      :usage_key
    ])
    |> unique_constraint([:user_id, :workspace_id, :feature_key])
    |> unique_constraint([:user_id, :workspace_id, :usage_key])
  end
end
