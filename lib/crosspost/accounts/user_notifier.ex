defmodule Crosspost.Accounts.UserNotifier do
  import Swoosh.Email
  require Logger

  alias Crosspost.Mailer

  # Delivers the email using the application mailer.
  defp deliver(recipient, subject, text_body, html_body) do
    email =
      new()
      |> to(recipient)
      |> from({"JustCrossPost", "<EMAIL>"})
      |> subject(subject)
      |> text_body(text_body)
      |> html_body(html_body)

    case Mailer.deliver(email) do
      {:ok, metadata} ->
        Logger.info("Email sent successfully to #{recipient}",
          email_metadata: metadata,
          email_type: subject
        )

        {:ok, email}

      {:error, reason} = error ->
        Logger.error("Failed to send email to #{recipient}",
          error: inspect(reason),
          email_type: subject
        )

        error
    end
  end

  @doc """
  Deliver instructions to confirm account.
  """
  def deliver_confirmation_instructions(user, url) do
    text_body = """
    ==============================

    Hi #{user.email},

    You can confirm your account by visiting the URL below:

    #{url}

    If you didn't create an account with us, please ignore this.

    ==============================
    """

    html_body = """
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
        <h2 style="color: #333; margin-bottom: 20px;">Welcome to JustCrossPost!</h2>

        <p style="color: #666; line-height: 1.5;">Hi #{user.email},</p>

        <p style="color: #666; line-height: 1.5;">Please confirm your account by clicking the button below:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="#{url}"
             style="background-color: #4f46e5; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 5px; font-weight: bold;">
            Confirm Account
          </a>
        </div>

        <p style="color: #666; line-height: 1.5;">
          Or copy and paste this link in your browser: <br>
          <a href="#{url}" style="color: #4f46e5;">#{url}</a>
        </p>

        <p style="color: #666; line-height: 1.5; margin-top: 30px;">
          If you didn't create an account with us, please ignore this email.
        </p>
      </div>
    </div>
    """

    deliver(user.email, "Confirm Your JustCrossPost Account", text_body, html_body)
  end

  @doc """
  Deliver instructions to reset a user password.
  """
  def deliver_reset_password_instructions(user, url) do
    text_body = """
    ==============================

    Hi #{user.email},

    You can reset your password by visiting the URL below:

    #{url}

    If you didn't request this change, please ignore this.

    ==============================
    """

    html_body = """
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
        <h2 style="color: #333; margin-bottom: 20px;">Reset Your Password</h2>

        <p style="color: #666; line-height: 1.5;">Hi #{user.email},</p>

        <p style="color: #666; line-height: 1.5;">You can reset your password by clicking the button below:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="#{url}"
             style="background-color: #4f46e5; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 5px; font-weight: bold;">
            Reset Password
          </a>
        </div>

        <p style="color: #666; line-height: 1.5;">
          Or copy and paste this link in your browser: <br>
          <a href="#{url}" style="color: #4f46e5;">#{url}</a>
        </p>

        <p style="color: #666; line-height: 1.5; margin-top: 30px;">
          If you didn't request this change, please ignore this email.
        </p>
      </div>
    </div>
    """

    deliver(user.email, "Reset Your JustCrossPost Password", text_body, html_body)
  end

  @doc """
  Deliver instructions to update a user email.
  """
  def deliver_update_email_instructions(user, url) do
    text_body = """
    ==============================

    Hi #{user.email},

    You can change your email by visiting the URL below:

    #{url}

    If you didn't request this change, please ignore this.

    ==============================
    """

    html_body = """
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
        <h2 style="color: #333; margin-bottom: 20px;">Update Your Email</h2>

        <p style="color: #666; line-height: 1.5;">Hi #{user.email},</p>

        <p style="color: #666; line-height: 1.5;">You can update your email by clicking the button below:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="#{url}"
             style="background-color: #4f46e5; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 5px; font-weight: bold;">
            Update Email
          </a>
        </div>

        <p style="color: #666; line-height: 1.5;">
          Or copy and paste this link in your browser: <br>
          <a href="#{url}" style="color: #4f46e5;">#{url}</a>
        </p>

        <p style="color: #666; line-height: 1.5; margin-top: 30px;">
          If you didn't request this change, please ignore this email.
        </p>
      </div>
    </div>
    """

    deliver(user.email, "Update Your JustCrossPost Email", text_body, html_body)
  end
end
