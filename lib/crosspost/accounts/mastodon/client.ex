defmodule Crosspost.Accounts.Mastodon.Client do
  use Crosspost.HTTP

  require Logger

  @callback fetch_instance_info(domain :: String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @behaviour __MODULE__

  def search_profiles(access_token, instance, query) do
    url = "https://#{instance}/api/v2/search?q=#{URI.encode(query)}&type=accounts&limit=5"

    opts = [
      headers: [{"Authorization", "Bearer #{access_token}"}],
      url: url
    ]

    case Req.get(url, opts) do
      {:ok, %{status: 200, body: %{"accounts" => accounts}}} ->
        {:ok, accounts}

      {:ok, %{status: status, body: body}} ->
        {:error, "Failed to search profiles with status #{status}: #{inspect(body)}"}
    end
  end

  def fetch_instance_info(domain) when is_binary(domain) do
    client = http("https://#{domain}")

    case get(client, "/api/v2/instance") do
      {:ok, json} ->
        {:ok, json}

      {:error, reason} ->
        Logger.error("Failed to fetch Mastodon instances", %{
          event: "mastodon.instances.fetch_failed",
          reason: reason,
          domain: domain
        })

        {:error, "Failed to fetch instance info"}
    end
  end
end
