defmodule Crosspost.Accounts.Contracts.OAuthAuth do
  @moduledoc """
  Contract for validating OAuth authentication data.
  """
  use Drops.Contract

  schema(atomize: true) do
    %{
      required(:uid) => string(:filled?),
      required(:info) => %{
        required(:name) => maybe(:string),
        required(:first_name) => maybe(:string),
        required(:last_name) => maybe(:string),
        required(:nickname) => maybe(:string),
        required(:email) => maybe(:string),
        required(:location) => maybe(:string),
        required(:description) => maybe(:string),
        required(:image) => maybe(:string),
        required(:phone) => maybe(:string),
        required(:birthday) => maybe(:string),
        required(:urls) => map(:string)
      },
      required(:credentials) => %{
        required(:token) => string(:filled?),
        required(:refresh_token) => string(:filled?),
        required(:expires_at) => any()
      }
    }
  end
end
