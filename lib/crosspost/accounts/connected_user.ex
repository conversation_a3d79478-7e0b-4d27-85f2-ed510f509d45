defmodule Crosspost.Accounts.ConnectedUser do
  use Ecto.Schema

  @type t() :: %__MODULE__{}

  @derive {Jason.Encoder,
           only: [
             :id,
             :uid,
             :workspace_id,
             :email,
             :name,
             :settings,
             :timezone,
             :networks
           ]}

  @networks [:bsky, :mastodon, :linkedin, :x, :canonical]

  schema "connected_users" do
    field :uid, Ecto.UUID
    field :email, :string
    field :name, :string
    field :workspace_id, :id
    field :default_workspace_id, :id
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :confirmed_at, :utc_datetime
    field :settings, :map, default: %{}
    field :info, :map, default: %{}
    field :timezone, :string, default: "Etc/UTC"

    # LinkedIn fields
    field :linkedin_id, :string
    field :encrypted_linkedin_token, Crosspost.Encrypted.Text
    field :encrypted_linkedin_refresh_token, Crosspost.Encrypted.Text
    field :linkedin_token_expires_at, :utc_datetime

    # Twitter fields
    field :twitter_id, :string
    field :encrypted_twitter_token, Crosspost.Encrypted.Text
    field :encrypted_twitter_secret, Crosspost.Encrypted.Text

    # X fields
    field :x_id, :string
    field :encrypted_x_token, Crosspost.Encrypted.Text
    field :encrypted_x_refresh_token, Crosspost.Encrypted.Text
    field :x_expires_at, :utc_datetime

    # Mastodon fields
    field :mastodon_id, :string
    field :encrypted_mastodon_token, Crosspost.Encrypted.Text
    field :encrypted_mastodon_refresh_token, Crosspost.Encrypted.Text
    field :mastodon_expires_at, :utc_datetime
    field :mastodon_instance, :string

    # Bluesky fields
    field :bsky_id, :string
    field :bsky_username, :string
    field :encrypted_bsky_token, Crosspost.Encrypted.Text
    field :encrypted_bsky_refresh_token, Crosspost.Encrypted.Text

    field :networks, {:array, Ecto.Enum}, values: @networks, default: []

    timestamps()
  end
end
