defmodule Crosspost.Accounts.Bsky.Client do
  @moduledoc """
  Client for the Bluesky API.
  """

  require Logger

  @base_url Application.compile_env(:crosspost, :bsky_base_url, "https://bsky.social/xrpc")
  @video_url "https://video.bsky.app/xrpc"

  @callback create_post(String.t(), String.t(), String.t(), map() | nil, list(), list()) ::
              {:ok, map()} | {:error, String.t()}

  @callback upload_blob(String.t(), String.t(), String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @callback upload_video(String.t(), String.t(), String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @callback get_video_status(String.t(), String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @callback get_service_auth(String.t(), String.t()) ::
              {:ok, map()} | {:error, String.t()}

  @callback resolve_handle(String.t(), String.t()) :: {:ok, String.t()} | {:error, String.t()}

  @callback search_profiles(String.t(), String.t()) ::
              {:ok, list()} | {:error, String.t()}

  @callback refresh_session(String.t()) ::
              {:ok, {String.t(), String.t(), DateTime.t() | nil}} | {:error, String.t()}

  @behaviour __MODULE__

  def authenticate(identifier, password) do
    url = "#{@base_url}/com.atproto.server.createSession"

    opts =
      [
        json: %{identifier: identifier, password: password},
        url: url
      ]
      |> add_bsky_options()

    case Req.post(url, opts) do
      {:ok, %{status: 200, body: body}} ->
        {:ok, %{access_token: body["accessJwt"], refresh_token: body["refreshJwt"]}}

      {:ok, %{status: 401}} ->
        {:error, :invalid_credentials}

      {:ok, %{status: 429, body: %{"message" => message}}} ->
        {:error, {:rate_limited, message}}

      {:ok, %{status: 429}} ->
        {:error, {:rate_limited, "Rate limit exceeded. Please try again later."}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @impl true
  def create_post(access_token, identifier, text, parent \\ nil, images \\ [], facets \\ []) do
    url = "#{@base_url}/com.atproto.repo.createRecord"

    record = %{
      "$type" => "app.bsky.feed.post",
      "text" => text,
      "createdAt" => DateTime.utc_now() |> DateTime.to_iso8601()
    }

    record = if parent, do: Map.put(record, "reply", parent), else: record
    record = if images != [], do: add_images_to_record(record, images), else: record
    record = if facets != [], do: Map.put(record, "facets", facets), else: record

    repo = format_identifier(identifier)

    payload = %{
      "repo" => repo,
      "collection" => "app.bsky.feed.post",
      "record" => record
    }

    opts =
      [
        json: payload,
        headers: [{"Authorization", "Bearer #{access_token}"}],
        url: url
      ]
      |> add_bsky_options()

    case Req.post(url, opts) do
      {:ok, %{status: 200, body: response_body}} ->
        {:ok, response_body}

      {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
        {:error, :expired_token}

      {:ok, %{status: status, body: response_body}} ->
        error_msg =
          case response_body do
            %{"error" => error} when is_binary(error) -> error
            %{"message" => message} when is_binary(message) -> message
            _ -> inspect(response_body)
          end

        Logger.error("Failed to create post", %{
          event: "bsky.client.create_post.error",
          status: status,
          error: error_msg
        })

        {:error, "Bluesky API error: #{status} - #{error_msg}"}

      {:error, %Req.TransportError{reason: reason}} ->
        error_msg =
          case reason do
            {:closed, partial} -> "Connection closed: #{inspect(partial)}"
            atom when is_atom(atom) -> "Transport error: #{atom}"
            other -> "Transport error: #{inspect(other)}"
          end

        Logger.error("Transport error", %{
          event: "bsky.client.create_post.transport_error",
          error: error_msg
        })

        {:error, error_msg}

      {:error, reason} ->
        error_msg =
          case reason do
            error when is_binary(error) -> error
            error when is_atom(error) -> Atom.to_string(error)
            _ -> inspect(reason)
          end

        Logger.error("HTTP request failed", %{
          event: "bsky.client.create_post.http_error",
          error: error_msg
        })

        {:error, "HTTP request failed: #{error_msg}"}
    end
  end

  @impl true
  def upload_blob(access_token, data, content_type \\ "image/jpeg") do
    url = "#{@base_url}/com.atproto.repo.uploadBlob"

    opts =
      [
        body: data,
        headers: [
          {"Authorization", "Bearer #{access_token}"},
          {"Content-Type", content_type}
        ],
        url: url
      ]
      |> add_bsky_options()

    case Req.post(url, opts) do
      {:ok, %{status: 200, body: response_body}} ->
        {:ok, response_body["blob"]}

      {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
        {:error, :expired_token}

      {:ok, %{status: status, body: response_body}} ->
        {:error, "Bluesky API error: #{status} - #{inspect(response_body)}"}

      {:error, %Req.TransportError{reason: reason}} ->
        {:error, reason}

      {:error, reason} ->
        {:error, "HTTP request failed: #{inspect(reason)}"}
    end
  end

  @impl true
  def upload_video(access_token, data, content_type \\ "video/mp4", filename \\ "video.mp4") do
    url = "#{@video_url}/app.bsky.video.uploadVideo"

    case get_service_auth(access_token, "video.bsky.app") do
      {:ok, %{"token" => service_token}} ->
        did = get_did_from_token(access_token)
        params = [did: did, name: filename]

        opts =
          [
            body: data,
            headers: [
              {"Authorization", "Bearer #{service_token}"},
              {"Content-Type", content_type}
            ],
            params: params,
            url: url
          ]
          |> add_bsky_options()

        case Req.post(url, opts) do
          {:ok, %{status: 200, body: response_body}} ->
            {:ok, response_body}

          {:ok,
           %{
             status: 409,
             body: %{"error" => "already_exists", "jobId" => job_id}
           }} ->
            {:ok, %{"jobId" => job_id}}

          {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
            {:error, :expired_token}

          {:ok, %{status: status, body: response_body}} ->
            error_msg =
              case response_body do
                %{"error" => error, "message" => message} -> "#{error}: #{message}"
                %{"error" => error} -> error
                %{"message" => message} -> message
                _ -> inspect(response_body)
              end

            Logger.error("Video upload failed", %{
              event: "bsky.client.upload_video.error",
              status: status,
              error: error_msg
            })

            {:error, "Bluesky API error: #{status} - #{inspect(response_body)}"}

          {:error, %Req.TransportError{reason: reason}} ->
            Logger.error("Video upload transport error", %{
              event: "bsky.client.upload_video.transport_error",
              reason: inspect(reason)
            })

            {:error, reason}

          {:error, reason} ->
            Logger.error("Video upload HTTP error", %{
              event: "bsky.client.upload_video.http_error",
              reason: inspect(reason)
            })

            {:error, "HTTP request failed: #{inspect(reason)}"}
        end

      {:error, reason} ->
        Logger.error("Failed to get service auth for video upload", %{
          event: "bsky.client.upload_video.service_auth_failed",
          reason: inspect(reason)
        })

        {:error, reason}
    end
  end

  @impl true
  def get_video_status(access_token, job_id) do
    url = "https://video.bsky.app/xrpc/app.bsky.video.getJobStatus"

    case get_service_auth(access_token, "video.bsky.app") do
      {:ok, %{"token" => service_token}} ->
        opts =
          [
            headers: [{"Authorization", "Bearer #{service_token}"}],
            params: [jobId: job_id],
            url: url
          ]
          |> add_bsky_options()

        case Req.get(url, opts) do
          {:ok, %{status: 200, body: response_body}} ->
            {:ok, response_body}

          {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
            {:error, :expired_token}

          {:ok, %{status: status, body: response_body}} ->
            error_msg =
              case response_body do
                %{"error" => error, "message" => message} -> "#{error}: #{message}"
                %{"error" => error} -> error
                %{"message" => message} -> message
                _ -> inspect(response_body)
              end

            Logger.error("Video status check failed", %{
              event: "bsky.client.get_video_status.error",
              status: status,
              error: error_msg
            })

            {:error, "Bluesky API error: #{status} - #{inspect(response_body)}"}

          {:error, %Req.TransportError{reason: reason}} ->
            Logger.error("Video status check transport error", %{
              event: "bsky.client.get_video_status.transport_error",
              reason: inspect(reason)
            })

            {:error, reason}

          {:error, reason} ->
            Logger.error("Video status check HTTP error", %{
              event: "bsky.client.get_video_status.http_error",
              reason: inspect(reason)
            })

            {:error, "HTTP request failed: #{inspect(reason)}"}
        end

      {:error, reason} ->
        Logger.error("Failed to get service auth for video status check", %{
          event: "bsky.client.get_video_status.service_auth_failed",
          reason: inspect(reason)
        })

        {:error, reason}
    end
  end

  @impl true
  def refresh_session(refresh_token) do
    url = "#{@base_url}/com.atproto.server.refreshSession"

    opts =
      [
        headers: [{"Authorization", "Bearer #{refresh_token}"}],
        url: url
      ]
      |> add_bsky_options()

    case Req.post(url, opts) do
      {:ok, %{status: 200, body: body}} ->
        {:ok, {body["accessJwt"], body["refreshJwt"], body["expiresAt"]}}

      {:ok, %{status: status, body: _body}} ->
        Logger.error("Session refresh failed", %{
          event: "bsky.client.refresh_session.error",
          status: status
        })

        {:error, "Failed to refresh session"}

      {:error, %Req.TransportError{reason: reason}} ->
        {:error, reason}
    end
  end

  def get_profile(access_token, identifier) do
    url = "#{@base_url}/app.bsky.actor.getProfile"
    formatted_identifier = format_identifier(identifier)

    opts =
      [
        headers: [{"Authorization", "Bearer #{access_token}"}],
        params: [actor: formatted_identifier],
        url: url
      ]
      |> add_bsky_options()

    case Req.get(url, opts) do
      {:ok, %{status: 200, body: profile}} ->
        {:ok, profile}

      {:ok, %{status: status, body: body}} ->
        {:error, "Failed to fetch Bluesky profile with status #{status}: #{inspect(body)}"}

      {:error, reason} ->
        {:error, "HTTP request failed: #{inspect(reason)}"}
    end
  end

  @impl true
  def resolve_handle(access_token, handle) do
    url = "#{@base_url}/com.atproto.identity.resolveHandle"

    opts =
      [
        headers: [{"Authorization", "Bearer #{access_token}"}],
        params: [handle: handle],
        url: url
      ]
      |> add_bsky_options()

    case Req.get(url, opts) do
      {:ok, %{status: 200, body: %{"did" => did}}} ->
        {:ok, did}

      {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
        {:error, :expired_token}

      {:ok, %{status: status, body: body}} ->
        {:error, "Failed to resolve handle with status #{status}: #{inspect(body)}"}

      {:error, reason} ->
        {:error, "HTTP request failed: #{inspect(reason)}"}
    end
  end

  @impl true
  def search_profiles(access_token, query) do
    url = "#{@base_url}/app.bsky.actor.searchActors?limit=5"

    opts = [
      headers: [{"Authorization", "Bearer #{access_token}"}],
      params: [term: query, limit: 5],
      url: url
    ]

    case Req.get(url, opts) do
      {:ok, %{status: 200, body: %{"actors" => actors}}} ->
        {:ok, actors}

      {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
        {:error, :expired_token}

      {:ok, %{status: status, body: body}} ->
        {:error, "Failed to search profiles with status #{status}: #{inspect(body)}"}

      {:error, reason} ->
        {:error, "HTTP request failed: #{inspect(reason)}"}
    end
  end

  @impl true
  def get_service_auth(access_token, _host) do
    url = "#{@base_url}/com.atproto.server.getServiceAuth"
    scope = "com.atproto.repo.uploadBlob"
    did = get_did_from_token(access_token)
    exp = DateTime.to_unix(DateTime.utc_now()) + 60 * 30
    pds_host = get_pds_host_from_token(access_token)
    aud = if pds_host, do: "did:web:#{pds_host}", else: nil

    if is_nil(aud) do
      {:error, "Could not extract PDS host from token"}
    else
      opts =
        [
          headers: [{"Authorization", "Bearer #{access_token}"}],
          params: %{
            aud: aud,
            scope: scope,
            iss: did,
            exp: exp,
            lxm: scope
          },
          url: url
        ]
        |> add_bsky_options()

      case Req.get(url, opts) do
        {:ok, %{status: 200, body: response_body}} ->
          {:ok, response_body}

        {:ok, %{status: 400, body: %{"error" => "ExpiredToken"}}} ->
          {:error, :expired_token}

        {:ok, %{status: status, body: response_body}} ->
          error_msg =
            case response_body do
              %{"error" => error, "message" => message} -> "#{error}: #{message}"
              %{"error" => error} -> error
              %{"message" => message} -> message
              _ -> inspect(response_body)
            end

          Logger.error("Service auth failed", %{
            event: "bsky.client.get_service_auth.error",
            status: status,
            error: error_msg
          })

          {:error, "Bluesky API error: #{status} - #{inspect(response_body)}"}

        {:error, %Req.TransportError{reason: reason}} ->
          Logger.error("Service auth transport error", %{
            event: "bsky.client.get_service_auth.transport_error",
            reason: inspect(reason)
          })

          {:error, reason}

        {:error, reason} ->
          Logger.error("Service auth HTTP error", %{
            event: "bsky.client.get_service_auth.http_error",
            reason: inspect(reason)
          })

          {:error, "HTTP request failed: #{inspect(reason)}"}
      end
    end
  end

  defp add_bsky_options(opts) do
    Keyword.merge(opts, Application.get_env(:crosspost, :bsky_req_options) || [])
  end

  defp format_identifier(identifier) do
    cond do
      String.starts_with?(identifier, "did:") -> identifier
      String.contains?(identifier, ".") -> identifier
      true -> "#{identifier}.bsky.social"
    end
  end

  defp add_external_embed(record, link) do
    external = %{
      "$type" => "app.bsky.embed.external",
      "external" => %{
        "uri" => link.url,
        "title" => link.title,
        "description" => link.description
      }
    }

    external =
      if link.thumb do
        put_in(external, ["external", "thumb"], link.thumb)
      else
        external
      end

    Map.put(record, "embed", external)
  end

  defp add_images_to_record(record, media) do
    {images, videos} =
      Enum.split_with(media, fn item ->
        case item do
          %{type: type} when type in [:image, :video] -> type == :image
          _ -> false
        end
      end)

    link =
      Enum.find(media, fn item ->
        case item do
          %{type: :link} -> true
          _ -> false
        end
      end)

    cond do
      link != nil ->
        add_external_embed(record, link)

      not Enum.empty?(videos) ->
        [video | _] = videos

        Map.put(record, "embed", %{
          "$type" => "app.bsky.embed.video",
          "video" => video.blob,
          "aspectRatio" => %{
            "width" => video.width,
            "height" => video.height
          }
        })

      not Enum.empty?(images) ->
        image_embeds =
          Enum.map(images, fn image ->
            embed = %{
              "alt" => image.alt || "image",
              "image" => image.blob
            }

            if image.width && image.height do
              Map.put(embed, "aspectRatio", %{
                "width" => image.width,
                "height" => image.height
              })
            else
              embed
            end
          end)

        Map.put(record, "embed", %{
          "$type" => "app.bsky.embed.images",
          "images" => image_embeds
        })

      true ->
        record
    end
  end

  defp get_did_from_token(token) do
    case String.split(token, ".") do
      [_header, payload, _signature] ->
        case Base.decode64(payload, padding: false) do
          {:ok, decoded} ->
            case Jason.decode(decoded) do
              {:ok, %{"sub" => did}} -> did
              _ -> nil
            end

          :error ->
            nil
        end

      _ ->
        nil
    end
  end

  defp get_pds_host_from_token(token) do
    case String.split(token, ".") do
      [_header, payload, _signature] ->
        case Base.decode64(payload, padding: false) do
          {:ok, decoded} ->
            case Jason.decode(decoded) do
              {:ok, %{"iss" => "did:web:" <> host}} -> host
              {:ok, %{"aud" => "did:web:" <> host}} -> host
              _ -> nil
            end

          :error ->
            nil
        end

      _ ->
        nil
    end
  end
end
