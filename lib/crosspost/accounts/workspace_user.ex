defmodule Crosspost.Accounts.WorkspaceUser do
  use Ecto.Schema
  import Ecto.Changeset

  alias Crosspost.Accounts.{User, Workspace}

  schema "workspace_users" do
    belongs_to :workspace, Workspace
    belongs_to :user, User
    field :settings, :map, default: %{}

    timestamps()
  end

  def changeset(workspace_user, attrs) do
    workspace_user
    |> cast(attrs, [:workspace_id, :user_id, :settings])
    |> validate_required([:workspace_id, :user_id])
    |> unique_constraint([:workspace_id, :user_id])
  end
end
