defmodule Crosspost.TestHelpers do
  alias Crosspost.Accounts

  def create_test_user do
    case Accounts.get_user_by_email("<EMAIL>") do
      nil ->
        {:ok, user} =
          Accounts.register_user(%{
            email: "<EMAIL>",
            password: "supersecret312",
            password_confirmation: "supersecret312"
          })

        IO.puts("Test user created: #{user.email}")

      user ->
        IO.puts("Test user already exists: #{user.email}")
    end
  end
end
