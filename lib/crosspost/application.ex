defmodule Crosspost.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    :ok = Application.ensure_started(:inets)

    if System.get_env("E2E_TEST") == "true" do
      setup_test_mocks()
    end

    :logger.add_handler(:my_sentry_handler, Sentry.LoggerHandler, %{
      config: %{
        metadata: [
          :file,
          :line,
          :event,
          :user_id,
          :workspace_id,
          :network,
          :post_id,
          :content_id,
          :reason,
          :result
        ],
        capture_log_messages: true,
        level: :error
      }
    })

    # LoggerJSON.Ecto.attach("logger-json-queries", [:crosspost, :repo, :query], :info)
    # LoggerJSON.Plug.attach("logger-json-requests", [:crosspost, :plug, :stop], :info)

    if System.get_env("SENTRY_DSN") do
      opt_ins = [
        OpenTelemetry.SemConv.ClientAttributes.client_port(),
        OpenTelemetry.SemConv.NetworkAttributes.network_local_address(),
        OpenTelemetry.SemConv.NetworkAttributes.network_local_port(),
        OpenTelemetry.SemConv.NetworkAttributes.network_transport()
      ]

      OpentelemetryBandit.setup(opt_in_attrs: opt_ins)

      OpentelemetryPhoenix.setup(adapter: :bandit, endpoint_prefix: [CrosspostWeb.Endpoint])
      OpentelemetryOban.setup(trace: [:jobs])
      OpentelemetryEcto.setup([:crosspost, :repo], db_statement: :enabled)
    end

    children =
      [
        CrosspostWeb.Telemetry,
        Crosspost.Repo,
        {Ecto.Migrator,
         repos: Application.fetch_env!(:crosspost, :ecto_repos), skip: skip_migrations?()},
        {DNSCluster, query: Application.get_env(:crosspost, :dns_cluster_query) || :ignore},
        {Phoenix.PubSub, name: Crosspost.PubSub},
        CrosspostWeb.Endpoint,
        {Oban, Application.fetch_env!(:crosspost, Oban)},
        Crosspost.Vault,
        {PlugAttack.Storage.Ets, [name: Crosspost.PlugAttack.Storage]}
      ] ++
        List.wrap(
          if System.get_env("MIX_ENV") != "prod" do
            Crosspost.Workers.TestPublishPostWorker
          end
        )

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Crosspost.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    CrosspostWeb.Endpoint.config_change(changed, removed)
    :ok
  end

  defp skip_migrations?() do
    # By default, sqlite migrations are run when using a release
    System.get_env("RELEASE_NAME") != nil
  end

  defp setup_test_mocks do
    Mox.defmock(Crosspost.Accounts.LinkedIn.ClientMock, for: Crosspost.Accounts.LinkedIn.Client)

    Mox.stub(Crosspost.Accounts.LinkedIn.ClientMock, :fetch_organizations, fn _connection ->
      {:ok,
       [
         %{
           "id" => "123456",
           "name" => "Test Organization",
           "vanityName" => "test-org",
           "resolved_logo_urls" => %{
             "cropped" => "https://example.com/logo-cropped.jpg",
             "original" => "https://example.com/logo.jpg"
           }
         }
       ]}
    end)

    Mox.set_mox_from_context(Crosspost.Accounts.LinkedIn.ClientMock)
    Mox.set_mox_global(Crosspost.Accounts.LinkedIn.ClientMock)
  end
end
