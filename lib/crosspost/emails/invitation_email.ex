defmodule Crosspost.Emails.InvitationEmail do
  import Swoosh.Email

  @discord_url "https://discord.gg/KCTpe9ArHv"

  def invitation_email(email, invitation_url) do
    new()
    |> to(email)
    |> from({"JustCrossPost", "<EMAIL>"})
    |> subject("Your Invitation to JustCrossPost Beta is here!")
    |> text_body("""
    Hello!

    Thanks for showing interest in JustCrossPost app!

    The wait is over, you can now sign up using this URL:

    #{invitation_url}

    You are welcome to join our Discord server where you can report issues and share feedback: #{@discord_url}

    Happy crossposting!
    Peter
    """)
    |> html_body("""
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6;">
      <h2 style="color: #2563eb; margin-bottom: 24px;">Welcome to JustCrossPost!</h2>

      <p style="margin-bottom: 16px;">Hello!</p>

      <p style="margin-bottom: 16px;">
        Thanks for showing interest in JustCrossPost app!
      </p>

      <p style="margin-bottom: 16px; padding: 16px; background-color: #f3f4f6; border-radius: 6px;">
        Please note that JustCrossPost is currently in <strong>closed beta stage</strong>.
      </p>

      <p style="margin-bottom: 16px;">
        The wait is over, you can now sign up using this URL:
      </p>

      <div style="margin: 24px 0; text-align: center;">
        <a href="#{invitation_url}"
           style="background-color: #2563eb; color: white; padding: 12px 24px;
                  text-decoration: none; border-radius: 6px; display: inline-block;
                  font-weight: bold; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
          Sign Up Now
        </a>
      </div>

      <div style="margin: 32px 0; padding: 24px; background-color: #f0f9ff; border: 2px solid #bae6fd; border-radius: 8px;">
        <h3 style="color: #0369a1; margin-top: 0; margin-bottom: 16px;">Join Our Community!</h3>
        <p style="margin-bottom: 16px;">
          Get help, share feedback, and connect with other users in our Discord community.
        </p>
        <div style="text-align: center;">
          <a href="#{@discord_url}"
             style="background-color: #5865F2; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 6px; display: inline-block;
                    font-weight: bold; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
            Join Discord Server
          </a>
        </div>
      </div>

      <div style="margin-top: 32px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
        <p style="margin: 0;">Happy crossposting!</p>
        <p style="margin: 0; color: #4b5563;">Peter</p>
      </div>
    </div>
    """)
  end
end
