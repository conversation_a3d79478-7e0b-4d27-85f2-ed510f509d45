defmodule Crosspost.Logger.FileBackend do
  @moduledoc """
  A logger backend that logs messages to a file in JSON format.
  """

  @behaviour :gen_event

  defstruct path: "",
            io_device: nil,
            format: nil,
            level: nil,
            metadata: nil

  @impl true
  def init({__MODULE__, name}) when is_atom(name) do
    config = Application.get_env(:logger, name, [])
    {:ok, init(config, %__MODULE__{})}
  end

  @impl true
  def handle_call({:configure, options}, state) do
    {:ok, :ok, configure(options, state)}
  end

  @impl true
  def handle_event({level, _gl, {Logger, msg, ts, md}}, state) do
    %{level: log_level} = state

    if meet_level?(normalize_level(level), normalize_level(log_level)) do
      log_event(level, msg, ts, md, state)
    end

    {:ok, state}
  end

  def handle_event(:flush, state) do
    {:ok, state}
  end

  def handle_event(_, state) do
    {:ok, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:ok, state}
  end

  @impl true
  def code_change(_old_vsn, state, _extra) do
    {:ok, state}
  end

  @impl true
  def terminate(_reason, %{io_device: nil}), do: :ok
  def terminate(_reason, %{io_device: io_device}), do: File.close(io_device)

  ## Helpers

  defp meet_level?(_lvl, nil), do: true

  defp meet_level?(lvl, min) do
    Logger.compare_levels(lvl, min) != :lt
  end

  # Normalize deprecated :warn to :warning
  defp normalize_level(:warn), do: :warning
  defp normalize_level(level), do: level

  defp init(config, state) do
    path = Keyword.get(config, :path)
    level = config |> Keyword.get(:level) |> normalize_level()
    format = Keyword.get(config, :format, {Crosspost.Logger.JsonFormatter, :format})
    metadata = Keyword.get(config, :metadata, [])

    # Ensure log directory exists
    path |> Path.dirname() |> File.mkdir_p!()

    # Open log file
    {:ok, io_device} = File.open(path, [:append, :utf8])

    %{state | path: path, io_device: io_device, format: format, level: level, metadata: metadata}
  end

  defp configure(options, state) do
    config = Keyword.merge(Application.get_env(:logger, state), options)
    Application.put_env(:logger, state, config)
    init(config, state)
  end

  defp log_event(level, msg, ts, md, %{format: {mod, fun}, io_device: io_device} = _state) do
    output = apply(mod, fun, [level, msg, ts, md])
    IO.write(io_device, output)
  end
end
