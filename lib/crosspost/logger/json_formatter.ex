defmodule Crosspost.Logger.JSONFormatter do
  @moduledoc """
  Formats log messages as JSON strings.
  """

  @excluded_metadata_keys [:erl_level, :pid, :gl]

  @doc """
  Formats a log entry into a JSON string.

  ## Parameters
    * level - The log level (:debug, :info, :warn, :error)
    * message - The log message
    * timestamp - The timestamp of the log
    * metadata - Additional metadata for the log entry
  """
  def format(level, message, timestamp, metadata) do
    message = IO.iodata_to_binary(message)

    metadata_map =
      metadata
      |> convert_metadata()
      |> filter_metadata()
      |> sanitize_metadata()
      |> remove_empty_values()

    entry =
      if String.starts_with?(message, "QUERY") do
        %{
          level: level,
          timestamp: to_unix_timestamp(timestamp),
          payload: Map.merge(metadata_map, parse_ecto_query(message))
        }
      else
        payload = if map_size(metadata_map) == 0, do: nil, else: metadata_map

        %{
          level: level,
          timestamp: to_unix_timestamp(timestamp),
          message: message
        }
        |> maybe_add_field("payload", payload)
      end

    Jason.encode!(entry) |> Kernel.<>("\n")
  end

  # Filter out excluded metadata keys
  defp filter_metadata(metadata) when is_map(metadata) do
    Map.drop(metadata, @excluded_metadata_keys)
  end

  # Handle both keyword lists and maps for metadata
  defp convert_metadata(metadata) when is_list(metadata), do: Enum.into(metadata, %{})
  defp convert_metadata(metadata) when is_map(metadata), do: metadata
  defp convert_metadata(_), do: %{}

  # Convert non-JSON-encodable values to strings
  defp sanitize_metadata(metadata) when is_map(metadata) do
    metadata
    |> Map.to_list()
    |> Enum.map(fn {k, v} -> {k, sanitize_value(v)} end)
    |> Map.new()
  end

  defp remove_empty_values(map) when is_map(map) do
    map
    |> Enum.reject(fn {_k, v} -> is_empty_value?(v) end)
    |> Map.new()
  end

  defp is_empty_value?(nil), do: true
  defp is_empty_value?(""), do: true
  defp is_empty_value?(map) when is_map(map), do: map_size(map) == 0
  defp is_empty_value?(list) when is_list(list), do: length(list) == 0
  defp is_empty_value?(_), do: false

  defp sanitize_value(%_{} = struct), do: sanitize_struct(struct)
  defp sanitize_value(value) when is_pid(value), do: inspect(value)
  defp sanitize_value(value) when is_function(value), do: inspect(value)
  defp sanitize_value(value) when is_port(value), do: inspect(value)
  defp sanitize_value(value) when is_reference(value), do: inspect(value)
  defp sanitize_value(value) when is_tuple(value), do: inspect(value)
  defp sanitize_value(value) when is_map(value), do: sanitize_metadata(value)
  defp sanitize_value(value) when is_list(value), do: Enum.map(value, &sanitize_value/1)

  defp sanitize_value(value) when is_binary(value) do
    case String.valid?(value) do
      true -> value
      false -> "[unsupported]"
    end
  end

  defp sanitize_value(value), do: value

  # Handle structs by converting them to maps and sanitizing
  defp sanitize_struct(struct) do
    struct
    |> Map.from_struct()
    |> Map.drop([:__meta__, :__struct__])
    |> sanitize_metadata()
  end

  defp to_unix_timestamp({{year, month, day}, {hour, minute, second, millisecond}}) do
    datetime = {{year, month, day}, {hour, minute, second}}
    unix_seconds = :calendar.datetime_to_gregorian_seconds(datetime) - 62_167_219_200
    unix_seconds * 1000 + div(millisecond, 1000)
  end

  # Parse Ecto query logs into structured data
  defp parse_ecto_query(message) do
    # Extract status and source
    status_match = Regex.run(~r/QUERY (OK|ERROR) source="([^"]*)"/, message)
    # Extract timing information
    db_time = extract_timing(message, "db")
    idle_time = extract_timing(message, "idle")
    # Extract query
    query = extract_query(message)
    # Extract function and location
    {fun, location} = extract_function_info(message)

    %{}
    |> maybe_add_field("status", status_match && Enum.at(status_match, 1))
    |> maybe_add_field("source", status_match && Enum.at(status_match, 2))
    |> maybe_add_field("db", db_time)
    |> maybe_add_field("idle", idle_time)
    |> maybe_add_field("query", query)
    |> maybe_add_field("fun", fun)
    |> maybe_add_field("location", clean_ansi(location))
    |> remove_empty_values()
  end

  defp extract_timing(message, type) do
    case Regex.run(~r/#{type}=(\d+\.\d+)ms/, message) do
      [_, time] -> String.to_float(time)
      _ -> nil
    end
  end

  defp extract_query(message) do
    case String.split(message, "\n") do
      [_, query | _] -> String.trim(query)
      _ -> nil
    end
  end

  defp extract_function_info(message) do
    case Regex.run(~r/↳\s+([^,]+),\s+at:\s+([^\s]+)/, message) do
      [_, fun, location] -> {fun, location}
      _ -> {nil, nil}
    end
  end

  defp clean_ansi(nil), do: nil
  defp clean_ansi(string), do: string |> String.replace(~r/\e\[[0-9;]*[mG]/, "")

  defp maybe_add_field(map, _key, nil), do: map
  defp maybe_add_field(map, key, value), do: Map.put(map, key, value)
end
