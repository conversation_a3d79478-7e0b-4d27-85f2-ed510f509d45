defmodule Crosspost.HTTP do
  defmacro __using__(_opts) do
    quote do
      import Crosspost.HTTP
    end
  end

  def get(client, path, opts \\ []) do
    opts = Keyword.put(opts, :url, path)

    case Req.get(client, opts) do
      {:ok, %Req.Response{status: _status, body: body}} ->
        {:ok, body}

      {:error, error} ->
        {:error, error}
    end
  end

  def http(base_url, opts \\ []) do
    opts = default_opts() |> Keyword.merge(opts)

    Enum.reduce(opts, Req.new(base_url: base_url), fn
      {:access_token, token}, request ->
        Req.Request.put_header(request, "Authorization", "Bearer #{token}")

      opt, request ->
        Req.Request.merge_options(request, [opt])
    end)
  end

  def default_opts do
    Application.get_env(:crosspost, :http_req_options, [])
  end
end
