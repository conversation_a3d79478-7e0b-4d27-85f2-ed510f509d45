defmodule Crosspost.Workspaces do
  @moduledoc """
  The Workspaces context.
  """
  import Ecto.Query, warn: false

  require Logger

  alias Crosspost.Repo
  alias Crosspost.Accounts.{Workspace, WorkspaceUser}
  alias Crosspost.Accounts.Email
  alias Crosspost.Mailer

  @doc """
  Gets a workspace by ID.
  """
  def get_workspace!(id), do: Repo.get!(Workspace, id)

  def get_workspace_by_slug!(slug), do: Repo.get_by!(Workspace, slug: slug)

  @doc """
  Gets a workspace by slug.
  """
  def get_workspace_by_slug(slug), do: Repo.get_by(Workspace, slug: slug)

  @doc """
  Lists all workspaces for a user, including the default workspace.
  """
  def list_user_workspaces(user) do
    Workspace
    |> join(:inner, [w], wu in WorkspaceUser, on: wu.workspace_id == w.id)
    |> where([w, wu], wu.user_id == ^user.id)
    |> order_by([w], desc: w.is_default, asc: w.name)
    |> Repo.all()
  end

  @doc """
  Adds a user to a workspace.
  """
  def add_user_to_workspace(workspace, user) do
    %WorkspaceUser{}
    |> WorkspaceUser.changeset(%{
      workspace_id: workspace.id,
      user_id: user.id
    })
    |> Repo.insert()
  end

  @doc """
  Removes a user from a workspace.
  """
  def remove_user_from_workspace(user, workspace) do
    from(wu in WorkspaceUser,
      where: wu.user_id == ^user.id and wu.workspace_id == ^workspace.id
    )
    |> Repo.delete_all()
  end

  @doc """
  Checks if a user belongs to a workspace.
  """
  def user_in_workspace?(user_id, workspace_id) when is_integer(user_id) do
    from(w in WorkspaceUser,
      where: w.user_id == ^user_id and w.workspace_id == ^workspace_id,
      select: count(w.id)
    )
    |> Repo.one() > 0
  end

  def user_in_workspace?(user, workspace) do
    user_in_workspace?(user.id, workspace.id)
  end

  def resolve_workspace(user, workspace_id) do
    with workspace when not is_nil(workspace) <- get_workspace!(workspace_id),
         true <- user_in_workspace?(user.id, workspace.id) do
      {:ok, workspace}
    else
      nil -> {:error, :not_found}
      false -> {:error, :not_authorized}
    end
  end

  @doc """
  Creates a new workspace.
  """
  def create_workspace(user, attrs \\ %{}) do
    %Workspace{}
    |> Workspace.changeset(attrs)
    |> Ecto.Changeset.put_assoc(:owner, user)
    |> Ecto.Changeset.put_assoc(:workspace_users, [%WorkspaceUser{user_id: user.id}])
    |> Repo.insert()
  end

  @doc """
  Deletes a workspace and all associated data within a transaction.

  This function handles the deletion of a workspace and all its associated data,
  ensuring that foreign key constraints are respected. It first checks if any users
  have this workspace set as their default, and if so, returns an error.

  Returns `{:ok, workspace}` if successful, or `{:error, reason}` if deletion fails.
  """
  def delete_workspace(workspace) do
    if workspace.is_default do
      raise "Cannot delete default workspace #{workspace.id}"
    end

    Repo.delete(workspace)
  end

  def update_workspace(workspace, attrs) do
    workspace
    |> Workspace.changeset(attrs)
    |> Repo.update()
  end

  def send_invites(workspace, emails) do
    state = %{
      workspace: workspace,
      already_invited: [],
      failed: [],
      successful: []
    }

    # Add invitations to workspace
    state =
      Enum.reduce(emails, state, fn email, acc ->
        case Workspace.add_invitation(acc.workspace, email) do
          {:error, :already_invited} ->
            %{acc | already_invited: [email | acc.already_invited]}

          changeset ->
            case update_workspace(changeset.data, changeset.changes) do
              {:ok, workspace_with_invite} ->
                %{acc | workspace: workspace_with_invite, successful: [email | acc.successful]}

              _error ->
                %{acc | failed: [email | acc.failed]}
            end
        end
      end)

    # Send invitation emails only for successful invites
    for invitation <- state.workspace.invitations,
        invitation["email"] in state.successful do
      case Email.deliver_workspace_invitation(
             invitation["email"],
             state.workspace,
             invitation["code"]
           )
           |> Mailer.deliver() do
        {:ok, _} ->
          Logger.info("Workspace invitation email sent", %{
            email: invitation["email"],
            workspace_id: state.workspace.id,
            event: "workspaces.invitation_sent"
          })

          :ok

        {:error, error} ->
          Logger.error("Failed to send invitation email", %{
            error: inspect(error),
            email: invitation["email"],
            workspace_id: state.workspace.id,
            event: "workspaces.invitation_failed"
          })

          :error
      end
    end

    # Return result
    case {state.successful, state.already_invited, state.failed} do
      {[], [], []} ->
        {:error, "No valid email addresses provided"}

      {[], already_invited, []} ->
        {:error, "#{length(already_invited)} email(s) already have valid invitations"}

      {_successful, [], []} ->
        :ok

      {_successful, _already_invited, []} ->
        :ok

      {_successful, _already_invited, _failed} ->
        {:error, "Some invitations failed to send"}
    end
  end
end
