defmodule Crosspost.Accounts do
  @moduledoc """
  The Accounts context.
  """

  require Lo<PERSON>

  @linkedin_profile_url "https://api.linkedin.com/v2/userinfo"

  import Ecto.Query, warn: false

  alias Crosspost.Accounts.{
    Connection,
    ConnectedUser,
    User,
    UserToken,
    UserNotifier,
    MastodonApp,
    Workspace,
    WorkspaceUser,
    Plan,
    Waitlist,
    SocialProfile
  }

  alias Crosspost.Repo
  alias Crosspost.Workspaces
  alias Crosspost.Plans

  @trial_days 14

  @callback refresh_user_bsky_token(ConnectedUser.t()) ::
              {:ok, ConnectedUser.t()} | {:error, String.t()}

  @callback get_social_profile!(integer()) :: SocialProfile.t() | nil

  @doc """
  Gets a user by their Stripe customer ID.

  ## Examples

      iex> get_user_by_stripe_customer_id("cus_123")
      %User{}

      iex> get_user_by_stripe_customer_id("nonexistent")
      nil

  """
  def get_user_by_stripe_customer_id(customer_id) when is_binary(customer_id) do
    Repo.one(
      from u in User,
        where: fragment("subscription->>'stripe_customer_id' = ?", ^customer_id)
    )
  end

  def get_user_by_stripe_customer_id(_), do: nil

  def user_from_auth(provider, params) do
    case check_existing_connection(provider, params.uid) do
      nil ->
        create_user_from_auth(provider, params)

      _connection ->
        {:error, :account_exists}
    end
  end

  def check_existing_connection(provider, uid) do
    Repo.one(
      from c in Connection,
        where: c.platform == ^provider and c.platform_user_id == ^uid
    )
  end

  def create_user_from_auth(provider, auth, invitation_code) do
    case get_valid_invitation(invitation_code) do
      {:ok, waitlist} ->
        case create_user_from_auth(provider, Map.merge(auth, %{email: waitlist.email})) do
          {:ok, user} ->
            {:ok, enrolled_user} = enroll_in_trial(user)
            {:ok, enrolled_user}

          {:error, error} ->
            Logger.error("Failed to create user from auth",
              error: inspect(error),
              info: auth.info
            )

            {:error, error}
        end

      {:error, :invalid_invitation} ->
        {:error, :invalid_invitation}
    end
  end

  def create_user_from_auth("x", %{info: info, email: email, credentials: credentials, uid: uid}) do
    create_user_with_connection(%{
      platform: "x",
      platform_user_id: uid,
      email: email,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: credentials.expires_at,
      info: dump_info(info)
    })
  end

  def create_user_from_auth("linkedin", %{
        info: info,
        email: email,
        credentials: credentials,
        uid: uid
      }) do
    create_user_with_connection(%{
      platform: "linkedin",
      platform_user_id: uid,
      email: email,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: DateTime.from_unix!(credentials.expires_at),
      info: dump_info(info)
    })
  end

  def create_user_from_auth("twitter", %{info: info, credentials: credentials, uid: uid}) do
    create_user_with_connection(%{
      platform: "twitter",
      platform_user_id: uid,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.secret,
      info: dump_info(info)
    })
  end

  def create_user_from_auth("mastodon", %{
        info: info,
        email: email,
        credentials: credentials,
        uid: uid
      }) do
    [_id, instance] = String.split(uid, "@")

    create_user_with_connection(%{
      platform: "mastodon",
      platform_user_id: uid,
      email: email,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: credentials.expires_at,
      settings: %{"instance" => instance},
      info: dump_info(info)
    })
  end

  defp create_user_with_connection(connection_attrs) do
    Repo.transaction(fn ->
      with {:ok, user} <- create_oauth_user(connection_attrs),
           {:ok, workspace} <- create_default_workspace(user),
           {:ok, user} <- update_user(user, %{default_workspace_id: workspace.id}),
           {:ok, _connection} <-
             create_connection(
               Map.merge(connection_attrs, %{
                 user_id: user.id,
                 workspace_id: workspace.id
               })
             ) do
        user
      else
        {:error, error} ->
          Logger.error("Failed to create user with connection", error: inspect(error))
          Repo.rollback(error)
      end
    end)
  end

  defp create_oauth_user(%{info: info, email: email} = _connection_attrs),
    do: do_create_oauth_user(email, info)

  defp create_oauth_user(%{info: %{email: email} = info} = _connection_attrs),
    do: do_create_oauth_user(email, info)

  def do_create_oauth_user(email, info) do
    attrs = %{
      email: email,
      name: info.name,
      timezone: "Etc/UTC"
    }

    %User{}
    |> User.oauth_registration_changeset(attrs)
    |> Repo.insert()
  end

  defp create_default_workspace(user) do
    Workspaces.create_workspace(user, %{
      owner_id: user.id,
      name: "Default Workspace",
      is_default: true,
      slug: "default-#{Base.url_encode64(:crypto.strong_rand_bytes(6), padding: false)}"
    })
  end

  def update_user_from_auth(network, user, workspace, attrs)
      when network in ["x", "twitter", "linkedin", "mastodon", "bsky"] do
    connection_attrs = build_connection_attrs(network, user, workspace, attrs)

    case get_connection(user.id, network, workspace.id) do
      nil -> create_connection(connection_attrs)
      connection -> update_connection(connection, connection_attrs)
    end
    |> case do
      {:ok, _connection} -> {:ok, user}
      {:error, changeset} -> {:error, changeset}
    end
  end

  # Helper function to build connection attributes based on network
  defp build_connection_attrs("x", user, workspace, %{
         info: info,
         credentials: credentials,
         uid: uid
       }) do
    %{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "x",
      platform_user_id: uid,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: credentials.expires_at,
      info: dump_info(info)
    }
  end

  defp build_connection_attrs("linkedin", user, workspace, %{
         info: info,
         credentials: credentials,
         uid: uid
       }) do
    %{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "linkedin",
      platform_user_id: uid,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: DateTime.from_unix!(credentials.expires_at),
      info: dump_info(info)
    }
  end

  defp build_connection_attrs("twitter", user, workspace, %{
         info: info,
         credentials: credentials,
         uid: uid
       }) do
    %{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "twitter",
      platform_user_id: uid,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.secret,
      info: dump_info(info)
    }
  end

  defp build_connection_attrs("mastodon", user, workspace, %{
         info: info,
         credentials: credentials,
         uid: uid
       }) do
    instance =
      case String.split(uid, "@") do
        [_id, instance] -> instance
        _ -> nil
      end

    %{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "mastodon",
      platform_user_id: uid,
      encrypted_access_token: credentials.token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: credentials.expires_at,
      settings: %{"instance" => instance},
      info: dump_info(info)
    }
  end

  defp build_connection_attrs("bsky", user, workspace, %{
         uid: uid,
         credentials: credentials,
         info: info
       }) do
    %{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "bsky",
      platform_user_id: uid,
      encrypted_access_token: credentials.access_token,
      encrypted_refresh_token: credentials.refresh_token,
      expires_at: credentials.expires_at,
      settings: %{
        "username" => info["handle"]
      },
      info: info
    }
  end

  def get_connection(%ConnectedUser{} = user, platform) do
    get_connection(user.id, platform, user.workspace_id)
  end

  def get_connection(platform, workspace_id) when is_atom(platform) do
    Repo.get_by(Connection, platform: Atom.to_string(platform), workspace_id: workspace_id)
  end

  def get_connection(platform, workspace_id) do
    Repo.get_by(Connection, platform: platform, workspace_id: workspace_id)
  end

  def get_connection(user_id, platform, workspace_id) do
    Repo.get_by(Connection, user_id: user_id, workspace_id: workspace_id, platform: platform)
  end

  def get_linkedin_profile(access_token) do
    headers = [
      {"Authorization", "Bearer #{access_token}"},
      {"Content-Type", "application/json"}
    ]

    case Req.get(@linkedin_profile_url, headers: headers) do
      {:ok, %Req.Response{status: 200, body: body}} ->
        {:ok, body}

      {:ok, %Req.Response{status: status}} ->
        {:error, "Failed to fetch profile: HTTP #{status}"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  def get_user_from_auth(provider, %{uid: uid}) do
    Connection
    |> where([c], c.platform == ^provider and c.platform_user_id == ^uid)
    |> join(:inner, [c], u in User, on: c.user_id == u.id)
    |> select([_, u], u)
    |> Repo.one()
  end

  def get_user_from_auth("x", %{uid: uid}) do
    Repo.get_by(User, x_id: uid)
  end

  def get_user_from_auth("twitter", %{uid: uid}) do
    Repo.get_by(User, twitter_id: uid)
  end

  def get_user_from_auth("mastodon", %{uid: uid}) do
    Repo.get_by(User, mastodon_id: uid)
  end

  def get_user_from_auth("bsky", %{uid: uid}) do
    Repo.get_by(User, bsky_id: uid)
  end

  def get_user_by_email(email) when is_binary(email) do
    Repo.get_by(User, email: email)
  end

  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  def get_user!(id), do: Repo.get!(User, id)

  def get_base_user!(id), do: Repo.get!(User, id)

  @doc """
  Gets a user with workspace settings for a specific workspace.
  Raises if the user doesn't exist or if the user doesn't have access to the workspace.
  """
  def get_workspace_user!(user_id, workspace_id) do
    query =
      from u in User,
        left_join: wu in WorkspaceUser,
        on: wu.user_id == u.id and wu.workspace_id == ^workspace_id,
        where: u.id == ^user_id,
        select_merge: %{workspace_settings: wu.settings},
        preload: [:plan, :enabled_features, :social_profiles]

    Repo.one!(query)
  end

  @doc """
  Updates workspace-specific settings for a user.
  """
  def update_workspace_user_settings(user_id, workspace_id, settings) do
    case Repo.get_by(WorkspaceUser, user_id: user_id, workspace_id: workspace_id) do
      nil ->
        {:error, :not_found}

      workspace_user ->
        workspace_user
        |> WorkspaceUser.changeset(%{settings: settings})
        |> Repo.update()
        |> case do
          {:ok, _workspace_user} -> {:ok, get_workspace_user!(user_id, workspace_id)}
          error -> error
        end
    end
  end

  def register_user(attrs) do
    Repo.transaction(fn ->
      with {:ok, user} <- do_register_user(attrs),
           {:ok, workspace} <- create_default_workspace(user),
           {:ok, user} <- update_user(user, %{default_workspace_id: workspace.id}),
           {:ok, user} <- enroll_in_trial(user),
           {:ok, _} <- Crosspost.FeatureUsages.inc_counter(user, "workspaces") do
        Logger.info("User registered and enrolled in trial successfully", %{
          user_id: user.id,
          event: "user_registration.complete"
        })

        user
      else
        {:error, error} ->
          Logger.error("Failed to register and enroll user", %{
            error: inspect(error),
            event: "user_registration.failed"
          })

          Repo.rollback(error)
      end
    end)
  end

  defp trial_end() do
    DateTime.add(DateTime.utc_now(), @trial_days, :day)
  end

  def change_user_registration(%User{} = user, attrs \\ %{}) do
    User.registration_changeset(user, attrs, hash_password: false, validate_email: false)
  end

  def change_user_email(user, attrs \\ %{}) do
    User.email_changeset(user, attrs, validate_email: false)
  end

  def apply_user_email(user, password, attrs) do
    user
    |> User.email_changeset(attrs)
    |> User.validate_current_password(password)
    |> Ecto.Changeset.apply_action(:update)
  end

  def update_user_email(user, token) do
    context = "change:#{user.email}"

    with {:ok, query} <- UserToken.verify_change_email_token_query(token, context),
         %UserToken{sent_to: email} <- Repo.one(query),
         {:ok, _} <- Repo.transaction(user_email_multi(user, email, context)) do
      :ok
    else
      _ -> :error
    end
  end

  defp user_email_multi(user, email, context) do
    changeset =
      user
      |> User.email_changeset(%{email: email})
      |> User.confirm_changeset()

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(:tokens, UserToken.by_user_and_contexts_query(user, [context]))
  end

  @doc ~S"""
  Delivers the update email instructions to the given user.

  ## Examples

      iex> deliver_user_update_email_instructions(user, current_email, &url(~p"/settings/confirm_email/#{&1}"))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_user_update_email_instructions(%User{} = user, current_email, update_email_url_fun)
      when is_function(update_email_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "change:#{current_email}")

    Repo.insert!(user_token)
    UserNotifier.deliver_update_email_instructions(user, update_email_url_fun.(encoded_token))
  end

  def change_user_password(user, attrs \\ %{}) do
    User.password_changeset(user, attrs, hash_password: false)
  end

  def update_user_password(user, password, attrs) do
    changeset =
      user
      |> User.password_changeset(attrs)
      |> User.validate_current_password(password)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)
    Repo.one(query) |> Repo.preload([:plan, :enabled_features])
  end

  def delete_user_session_token(token) do
    Repo.delete_all(UserToken.by_token_and_context_query(token, "session"))
    :ok
  end

  def deliver_user_confirmation_instructions(%User{} = user, confirmation_url_fun)
      when is_function(confirmation_url_fun, 1) do
    if user.confirmed_at do
      {:error, :already_confirmed}
    else
      {encoded_token, user_token} = UserToken.build_email_token(user, "confirm")
      Repo.insert!(user_token)
      UserNotifier.deliver_confirmation_instructions(user, confirmation_url_fun.(encoded_token))
    end
  end

  def confirm_user(token) do
    with {:ok, query} <- UserToken.verify_email_token_query(token, "confirm"),
         %User{} = user <- Repo.one(query),
         {:ok, %{user: user}} <- Repo.transaction(confirm_user_multi(user)) do
      {:ok, user}
    else
      _ -> :error
    end
  end

  defp confirm_user_multi(user) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, User.confirm_changeset(user))
    |> Ecto.Multi.delete_all(:tokens, UserToken.by_user_and_contexts_query(user, ["confirm"]))
  end

  def deliver_user_reset_password_instructions(%User{} = user, reset_password_url_fun)
      when is_function(reset_password_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "reset_password")
    Repo.insert!(user_token)
    UserNotifier.deliver_reset_password_instructions(user, reset_password_url_fun.(encoded_token))
  end

  def get_user_by_reset_password_token(token) do
    with {:ok, query} <- UserToken.verify_email_token_query(token, "reset_password"),
         %User{} = user <- Repo.one(query) do
      user
    else
      _ -> nil
    end
  end

  def reset_user_password(user, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, User.password_changeset(user, attrs))
    |> Ecto.Multi.delete_all(:tokens, UserToken.by_user_and_contexts_query(user, :all))
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  def create_beta_user(attrs \\ %{}) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    %Waitlist{}
    |> Map.merge(%{invited_at: now})
    |> Waitlist.changeset(attrs)
    |> Repo.insert()
  end

  def generate_invitation_code do
    :crypto.strong_rand_bytes(16)
    |> Base.url_encode64(padding: false)
  end

  def update_user_settings(%User{} = user, attrs) do
    user
    |> User.changeset(%{settings: attrs})
    |> Repo.update()
  end

  def set_sidebar_hidden(%User{} = user, hidden) when is_boolean(hidden) do
    update_user_settings(user, %{sidebar_hidden: hidden})
  end

  def sidebar_hidden?(%User{} = user) do
    user.settings.sidebar_hidden
  end

  defp dump_info(info) when is_struct(info) do
    info
    |> Map.from_struct()
    |> Map.drop([:__meta__, :__struct__])
    |> dump_info()
  end

  defp dump_info(info) when is_map(info) do
    info
    |> Enum.map(fn {k, v} -> {k, dump_value(v)} end)
    |> Enum.into(%{})
  end

  defp dump_value(%{__struct__: _} = struct), do: dump_info(struct)
  defp dump_value(value) when is_map(value), do: dump_info(value)
  defp dump_value(value) when is_list(value), do: Enum.map(value, &dump_value/1)
  defp dump_value(value), do: value

  def change_user_profile(%User{} = user, attrs \\ %{}) do
    User.profile_changeset(user, attrs)
  end

  def update_user_profile(%User{} = user, password, attrs) do
    changeset = User.profile_changeset(user, attrs)

    if user.hashed_password do
      changeset
      |> User.validate_current_password(password)
      |> Repo.update()
    else
      Repo.update(changeset)
    end
  end

  def delete_user(%User{} = user) do
    Repo.transaction(fn ->
      Repo.update(User.changeset(user, %{default_workspace_id: nil}))
      Repo.delete(Repo.get!(Workspace, user.default_workspace_id))
      Repo.delete(user)
    end)
  end

  def refresh_user_bsky_token(%ConnectedUser{} = user) do
    case get_connection(user, "bsky") do
      nil ->
        {:error, :no_connection}

      connection ->
        case bsky_client().refresh_session(connection.encrypted_refresh_token) do
          {:ok, {new_access_token, new_refresh_token, expires_at}} ->
            {:ok, _updated_connection} =
              update_connection(connection, %{
                encrypted_access_token: new_access_token,
                encrypted_refresh_token: new_refresh_token,
                expires_at: expires_at
              })

            {:ok, get_connected_user!(user.id, user.default_workspace_id)}

          {:error, reason} ->
            {:error, reason}
        end
    end
  end

  def bsky_client do
    Application.get_env(:crosspost, :bsky_client, Crosspost.Accounts.Bsky.Client)
  end

  def create_or_get_mastodon_app(instance) do
    formatted_instance = format_mastodon_instance(instance)

    case Repo.get_by(MastodonApp, instance: formatted_instance) do
      nil ->
        create_mastodon_app(formatted_instance)

      app ->
        {:ok, app}
    end
  end

  defp create_mastodon_app(instance) do
    redirect_uri = CrosspostWeb.Endpoint.url() <> "/auth/mastodon/callback"

    Logger.info(
      "Creating Mastodon app for instance #{instance} with redirect URI: #{redirect_uri}"
    )

    params = %{
      client_name: "JustCrossPost",
      redirect_uris: redirect_uri,
      scopes: "read write follow",
      website: CrosspostWeb.Endpoint.url()
    }

    mastodon_app = %MastodonApp{instance: instance}

    case Ueberauth.Strategy.Mastodon.API.app_create(mastodon_app, params) do
      {:ok, %{body: body}} ->
        Logger.info("Successfully created Mastodon app for instance #{instance}")

        %MastodonApp{}
        |> MastodonApp.changeset(%{
          instance: instance,
          client_id: body["client_id"],
          client_secret: body["client_secret"]
        })
        |> Repo.insert()

      error ->
        Logger.error("Failed to create Mastodon app for instance #{instance}: #{inspect(error)}")
        {:error, "Failed to create Mastodon app: #{inspect(error)}"}
    end
  end

  def format_mastodon_instance(instance) do
    if String.starts_with?(instance, ["http://", "https://"]) do
      instance
    else
      "https://" <> instance
    end
  end

  def get_valid_invitation(invitation_code) do
    case Repo.one(
           from w in Waitlist,
             where: w.invitation_code == ^invitation_code and is_nil(w.used_at)
         ) do
      nil -> {:error, :invalid_invitation}
      waitlist -> {:ok, waitlist}
    end
  end

  def mark_invitation_used(invitation_code) do
    case Repo.get_by(Waitlist, invitation_code: invitation_code) do
      nil ->
        {:error, :not_found}

      waitlist ->
        waitlist
        |> Waitlist.changeset(%{used_at: DateTime.utc_now()})
        |> Repo.update()
    end
  end

  # Add these new functions for connection management
  def create_connection(attrs \\ %{}) do
    %Connection{}
    |> Connection.changeset(attrs)
    |> Repo.insert()
  end

  def update_connection(%Connection{} = connection, attrs) do
    connection
    |> Connection.changeset(attrs)
    |> Repo.update()
  end

  def delete_connection(%Connection{} = connection) do
    Repo.delete(connection)
  end

  @doc """
  Gets a connected user with social network tokens scoped to a specific workspace.
  Raises if the user doesn't exist or if the user doesn't have access to the workspace.
  """
  def get_connected_user!(user_id, workspace_id) do
    from(u in ConnectedUser,
      where: u.id == ^user_id and u.workspace_id == ^workspace_id
    )
    |> Repo.one!()
  end

  @doc """
  Creates a social profile.

  ## Examples

      iex> create_social_profile(%{name: "John Doe", bsky_handle: "john.bsky.social"})
      {:ok, %SocialProfile{}}

      iex> create_social_profile(%{name: "John Doe"})
      {:error, %Ecto.Changeset{}}

  """
  def create_social_profile(attrs \\ %{}) do
    %SocialProfile{}
    |> SocialProfile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets a social profile by ID.

  Raises `Ecto.NoResultsError` if the Social profile does not exist.

  ## Examples

      iex> get_social_profile!(123)
      %SocialProfile{}

      iex> get_social_profile!(456)
      ** (Ecto.NoResultsError)

  """
  def get_social_profile!(id), do: Repo.get!(SocialProfile, id)

  @doc """
  Deletes a social profile.

  ## Examples

      iex> delete_social_profile(profile)
      {:ok, %SocialProfile{}}

      iex> delete_social_profile(profile)
      {:error, %Ecto.Changeset{}}

  """
  def delete_social_profile(%SocialProfile{} = profile) do
    Repo.delete(profile)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking social profile changes.

  ## Examples

      iex> change_social_profile(profile)
      %Ecto.Changeset{data: %SocialProfile{}}

  """
  def change_social_profile(%SocialProfile{} = profile, attrs \\ %{}) do
    SocialProfile.changeset(profile, attrs)
  end

  def list_user_connections(workspace_id) do
    from(c in Connection, where: c.workspace_id == ^workspace_id)
    |> Repo.all()
  end

  def get_user_social_profile(user_id, profile_id) do
    Repo.get_by(SocialProfile, id: profile_id, user_id: user_id)
  end

  def update_social_profile(%SocialProfile{} = profile, attrs) do
    profile
    |> SocialProfile.changeset(attrs)
    |> Repo.update()
  end

  def count_connected_platforms(user, workspace) do
    from(c in Connection,
      where: c.user_id == ^user.id and c.workspace_id == ^workspace.id
    )
    |> Repo.aggregate(:count)
  end

  def list_connected_users do
    from(u in User,
      join: c in Connection,
      on: c.user_id == u.id,
      where: c.platform in ["x", "bsky"],
      group_by: u.id,
      distinct: true
    )
    |> Repo.all()
  end

  def disconnect_provider(%Connection{} = connection) do
    Repo.delete(connection)
  end

  def disconnect_provider(user, provider, workspace_id) do
    case Repo.get_by(Connection,
           user_id: user.id,
           platform: provider,
           workspace_id: workspace_id
         ) do
      nil -> {:error, :not_found}
      connection -> Repo.delete(connection)
    end
  end

  def update_user(%User{} = user, attrs) do
    attrs = maybe_set_customer_field(attrs)

    user
    |> User.changeset(attrs)
    |> Repo.update()
  end

  defp maybe_set_customer_field(%{status: status} = attrs) do
    customer = status in [:active, :past_due]
    Map.put(attrs, :customer, customer)
  end

  defp maybe_set_customer_field(attrs), do: attrs

  def update_feature_usages(%User{} = user) do
    features = Crosspost.Features.list_features()

    limited_features =
      features
      |> Enum.filter(fn feature ->
        feature.usage_key != nil && feature.type != nil
      end)

    result =
      Enum.reduce_while(limited_features, {:ok, user}, fn feature, {:ok, _user} ->
        attrs = %{
          user_id: user.id,
          workspace_id: user.default_workspace_id,
          feature_key: feature.key,
          usage_key: feature.usage_key,
          type: feature.type,
          value: 0
        }

        case Repo.insert(
               Crosspost.Accounts.FeatureUsage.changeset(
                 %Crosspost.Accounts.FeatureUsage{},
                 attrs
               ),
               on_conflict: {:replace, [:value, :feature_key, :updated_at]},
               conflict_target: [:user_id, :workspace_id, :usage_key]
             ) do
          {:ok, _feature_usage} -> {:cont, {:ok, user}}
          {:error, changeset} -> {:halt, {:error, changeset}}
        end
      end)

    case result do
      {:ok, user} -> {:ok, Repo.preload(user, :feature_usages)}
      error -> error
    end
  end

  @doc """
  Lists all workspaces for a user.
  """
  def list_user_workspaces(%User{} = user) do
    Repo.all(
      from w in Crosspost.Accounts.Workspace,
        join: wu in Crosspost.Accounts.WorkspaceUser,
        on: wu.workspace_id == w.id,
        where: wu.user_id == ^user.id,
        order_by: [desc: w.is_default, asc: w.name]
    )
  end

  def list_workspace_users(workspace) do
    Workspace
    |> join(:inner, [w], wu in WorkspaceUser, on: wu.workspace_id == w.id)
    |> join(:inner, [w, wu], u in User, on: wu.user_id == u.id)
    |> where([w, wu, u], w.id == ^workspace.id)
    |> select([w, wu, u], u)
    |> Repo.all()
  end

  def update_user_features(%User{} = user) do
    user = Repo.preload(user, :plan)
    plan = Repo.get_by!(Plan, key: user.plan_key)
    update_user_features(user, plan)
  end

  def update_user_features(user, plan, attrs \\ %{}) do
    features = Crosspost.Features.features_for_plan(plan)
    user = Repo.preload(user, [:plan, :user_features, :features, :feature_usages])

    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

    # Get all features with usage tracking from the database
    limited_features =
      features
      |> Enum.filter(fn feature ->
        feature.usage_key != nil && feature.type != nil
      end)

    # First, delete any feature usages that are not in the new plan
    new_usage_keys = Enum.map(limited_features, & &1.usage_key)

    {_num_deleted, _} =
      Repo.delete_all(
        from fu in Crosspost.Accounts.FeatureUsage,
          where:
            fu.user_id == ^user.id and
              fu.workspace_id == ^user.default_workspace_id and
              fu.usage_key not in ^new_usage_keys
      )

    # Create or update feature usages for each limited feature
    feature_usages_result =
      Enum.reduce_while(limited_features, {:ok, []}, fn feature, {:ok, acc} ->
        # Find existing feature usage by usage_key
        existing_usage = Enum.find(user.feature_usages, &(&1.usage_key == feature.usage_key))

        # For all features, update or insert while preserving the value
        attrs = %{
          user_id: user.id,
          workspace_id: user.default_workspace_id,
          feature_key: feature.key,
          usage_key: feature.usage_key,
          type: feature.type,
          value: if(existing_usage, do: existing_usage.value, else: 0)
        }

        case Repo.insert(
               %Crosspost.Accounts.FeatureUsage{}
               |> Crosspost.Accounts.FeatureUsage.changeset(attrs),
               on_conflict: {:replace_all_except, [:id, :value]},
               conflict_target: [:user_id, :workspace_id, :usage_key]
             ) do
          {:ok, feature_usage} -> {:cont, {:ok, [feature_usage | acc]}}
          {:error, changeset} -> {:halt, {:error, changeset}}
        end
      end)

    case feature_usages_result do
      {:ok, _feature_usages} ->
        result =
          Ecto.Multi.new()
          |> Ecto.Multi.delete_all(
            :delete_features,
            from(uf in Crosspost.Accounts.UserFeature, where: uf.user_id == ^user.id)
          )
          |> Ecto.Multi.update(
            :user_update,
            User.changeset(user, Map.merge(attrs, %{plan_key: plan.key}))
          )
          |> Ecto.Multi.insert_all(
            :user_features,
            Crosspost.Accounts.UserFeature,
            Enum.map(features, fn feature ->
              %{
                feature_key: feature.key,
                user_id: user.id,
                inserted_at: now,
                updated_at: now
              }
            end)
          )
          |> Repo.transaction()

        case result do
          {:ok, %{user_update: user}} ->
            {:ok,
             Repo.preload(user, [:features, :feature_usages, :enabled_features, :usage_counters],
               force: true
             )}

          {:error, _, error, _} ->
            {:error, error}
        end

      {:error, error} ->
        {:error, error}
    end
  end

  def get_user_by_oauth(provider, uid) do
    provider = String.downcase(provider)

    case Connection
         |> where([c], c.platform == ^provider and c.platform_user_id == ^uid)
         |> join(:inner, [c], u in User, on: c.user_id == u.id)
         |> join(:inner, [c, u], w in Workspace, on: c.workspace_id == w.id)
         |> where([c, u, w], w.is_default == true)
         |> select([c, u, w], u)
         |> limit(1)
         |> Repo.one() do
      %User{} = user -> {:ok, user}
      nil -> {:error, :not_found}
    end
  end

  def trial_subscription(attrs \\ %{}) do
    Map.merge(
      %{
        plan: "pro",
        status: "trialing",
        period_start: DateTime.utc_now(),
        period_end: trial_end()
      },
      attrs
    )
  end

  def enroll_in_trial(%User{} = user) do
    plan = Plans.get_default_plan()
    enroll_in_trial(user, plan)
  end

  def enroll_in_trial(%User{} = user, plan) do
    attrs = %{
      trial_end: trial_end(),
      plan_key: plan.key,
      status: :trial,
      subscription: trial_subscription(%{features: plan.features})
    }

    with {:ok, user} <- update_user(user, attrs) do
      enroll(user, plan, Map.from_struct(user.subscription))
    end
  end

  @doc """
  Enrolls a user in a plan, updating their features and reassigning existing feature usages.

  This function:
  1. Assigns the plan to the user
  2. Updates user features based on the new plan
  3. Ensures feature usages are reassigned to the new plan's features while preserving counters

  ## Examples

      iex> enroll(user, plan)
      {:ok, %User{}}

  """
  def enroll(%User{} = user) do
    enroll(user, user.plan, Map.from_struct(user.subscription))
  end

  def enroll(%User{} = user, %Plan{} = plan, subscription \\ %{}) do
    user = Repo.preload(user, [:plan, :features, :feature_usages])

    # Determine the new status based on the subscription
    new_status =
      case subscription do
        %{status: "active"} -> :active
        %{status: "trialing"} -> :trial
        _ -> user.status
      end

    attrs = %{
      plan_key: plan.key,
      subscription: subscription,
      status: new_status
    }

    Ecto.Multi.new()
    |> Ecto.Multi.run(:user, fn _repo, _changes ->
      update_user(user, attrs)
    end)
    |> Ecto.Multi.run(:enrolled_user, fn _repo, %{user: updated_user} ->
      update_user_features(updated_user, plan)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{enrolled_user: enrolled_user}} ->
        if is_user_upgrading_from_trial?(user, enrolled_user) do
          Crosspost.Features.schedule_cleanup(enrolled_user, scheduled_at: DateTime.utc_now())
        else
          {:ok, enrolled_user}
        end

      {:error, _operation, error, _changes} ->
        {:error, error}
    end
  end

  defp is_user_upgrading_from_trial?(user, enrolled_user) do
    not is_nil(user.trial_end) and
      DateTime.compare(user.trial_end, DateTime.utc_now()) == :lt and
      not is_nil(enrolled_user.subscription.stripe_customer_id) and
      is_nil(user.subscription.stripe_customer_id)
  end

  def subscribed?(%{
        subscription: %{status: "active", stripe_subscription_id: stripe_subscription_id}
      })
      when not is_nil(stripe_subscription_id) do
    true
  end

  def subscribed?(_user), do: false

  @doc """
  Checks if a user has an active subscription or is within their trial period.
  Returns one of:
  - {:ok, :active} - User has an active subscription or trial
  - {:error, :trial_ended} - User's trial has ended
  - {:error, :subscription_ended} - User's subscription has been cancelled or expired
  """
  def is_user_active?(%User{} = user) do
    cond do
      user.customer ->
        {:ok, :active}

      (user.status == :trial and user.trial_end) && Timex.after?(user.trial_end, Timex.now()) ->
        {:ok, :active}

      user.status == :trial_ended ->
        {:error, :trial_ended}

      true ->
        {:error, :subscription_ended}
    end
  end

  @doc """
  Gets a user with connections scoped to a specific workspace.
  This includes both:
  - User's own connections in the workspace
  - All connections belonging to workspaces the user has access to
  """
  def get_user_with_workspace_connections(user, workspace_id) do
    connections_query =
      from(c in Connection,
        join: wu in WorkspaceUser,
        on: wu.workspace_id == c.workspace_id and wu.user_id == ^user.id,
        where: c.workspace_id == ^workspace_id
      )

    Repo.preload(user, workspace_connections: connections_query)
  end

  @doc """
  Cancels a user's subscription using the configured billing provider.
  Updates the user's subscription details in the database to reflect the cancellation.

  Returns `{:ok, user}` if successful, `{:error, reason}` otherwise.
  """
  def cancel_subscription(%User{} = user) do
    provider = Application.get_env(:crosspost, :billing_provider)

    with %{stripe_subscription_id: subscription_id} when is_binary(subscription_id) <-
           user.subscription,
         {:ok, _subscription} <- provider.cancel_subscription(subscription_id) do
      user
      |> Ecto.Changeset.change()
      |> Ecto.Changeset.put_embed(:subscription, %{
        status: "canceled",
        plan: nil,
        period_start: nil,
        period_end: nil,
        features: [],
        stripe_subscription_id: nil,
        stripe_customer_id: user.subscription.stripe_customer_id,
        stripe_product_id: nil,
        stripe_price_id: nil
      })
      |> Repo.update()
    else
      nil -> {:error, :no_subscription}
      error -> error
    end
  end

  @doc """
  Registers a user and enrolls them in a trial. This is a common flow for both
  standard registration and OAuth registration.
  """
  def register_and_enroll_user(%{oauth_provider: provider} = attrs) when not is_nil(provider) do
    Repo.transaction(fn ->
      with {:ok, user} <- create_user_from_auth(provider, attrs),
           {:ok, user} <- enroll_in_trial(user),
           {:ok, _} <- Crosspost.FeatureUsages.inc_counter(user, "workspaces") do
        Logger.info("User registered and enrolled in trial successfully", %{
          user_id: user.id,
          event: "user_registration.complete"
        })

        user
      else
        {:error, error} ->
          Logger.error("Failed to register and enroll user", %{
            error: inspect(error),
            event: "user_registration.failed"
          })

          Repo.rollback(error)
      end
    end)
  end

  def register_and_enroll_user(attrs) do
    Repo.transaction(fn ->
      with {:ok, user} <- do_register_user(attrs),
           {:ok, workspace} <- create_default_workspace(user),
           {:ok, user} <- update_user(user, %{default_workspace_id: workspace.id}),
           {:ok, user} <- enroll_in_trial(user),
           {:ok, _} <- Crosspost.FeatureUsages.inc_counter(user, "workspaces") do
        Logger.info("User registered and enrolled in trial successfully", %{
          user_id: user.id,
          event: "user_registration.complete"
        })

        user
      else
        {:error, %Ecto.Changeset{} = changeset} ->
          Repo.rollback(changeset)

        {:error, error} ->
          Logger.error("Failed to register and enroll user", %{
            error: inspect(error),
            event: "user_registration.failed"
          })

          Repo.rollback(error)
      end
    end)
  end

  defp do_register_user(attrs) do
    %User{}
    |> User.registration_changeset(attrs, validate_email: true)
    |> Repo.insert()
  end

  @doc """
  Updates a user's subscription by merging the provided attributes with existing subscription data.
  This ensures that existing subscription fields are preserved unless explicitly overridden.
  """
  def update_user_subscription(%User{} = user, attrs) do
    subscription = Map.merge(Map.from_struct(user.subscription), attrs)

    user
    |> User.changeset(%{subscription: subscription})
    |> Repo.update()
  end

  def billing_portal_url(%{subscription: %{stripe_customer_id: nil}}), do: nil

  def billing_portal_url(%{subscription: %{stripe_customer_id: customer_id}}) do
    return_url = CrosspostWeb.Endpoint.url() <> "/dashboard"
    provider = Application.get_env(:crosspost, :billing_provider, Crosspost.Billing.Stripe)

    case provider.billing_portal_url(customer_id, return_url) do
      {:ok, url} -> url
      _ -> nil
    end
  end

  @doc """
  Sets the customer field based on status.
  - Sets customer to true for :active and :past_due statuses
  - Sets customer to false for all other statuses
  - Preserves existing customer value if status is not being updated
  """
  def set_customer_field(attrs) when is_map(attrs) do
    case Map.get(attrs, :status) do
      status when status in [:active, :past_due] -> Map.put(attrs, :customer, true)
      status when not is_nil(status) -> Map.put(attrs, :customer, false)
      nil -> attrs
    end
  end
end
