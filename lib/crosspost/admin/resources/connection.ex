defmodule Crosspost.Admin.Resources.Connection do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :id,
             :workspace_id,
             :platform,
             :platform_user_id,
             :expires_at,
             :settings,
             :info,
             :user_id,
             :inserted_at,
             :updated_at
           ]}

  schema "connections" do
    field :platform, :string
    field :platform_user_id, :string
    field :encrypted_access_token, Crosspost.Encrypted.Text
    field :encrypted_refresh_token, Crosspost.Encrypted.Text
    field :expires_at, :utc_datetime
    field :settings, :map, default: %{}
    field :info, :map, default: %{}

    belongs_to :user, Crosspost.Admin.Resources.User
    belongs_to :workspace, Crosspost.Admin.Resources.Workspace

    timestamps()
  end

  @doc false
  def changeset(connection, attrs) do
    connection
    |> cast(attrs, [
      :platform,
      :platform_user_id,
      :encrypted_access_token,
      :encrypted_refresh_token,
      :expires_at,
      :settings,
      :info,
      :user_id,
      :workspace_id
    ])
    |> validate_required([
      :platform,
      :platform_user_id,
      :encrypted_access_token,
      :user_id,
      :workspace_id
    ])
    |> unique_constraint([:platform, :platform_user_id, :workspace_id],
      name: "connections_platform_user_workspace_index"
    )
  end
end
