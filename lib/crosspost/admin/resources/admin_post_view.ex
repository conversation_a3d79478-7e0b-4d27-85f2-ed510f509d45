defmodule Crosspost.Admin.Resources.AdminPostView do
  @moduledoc """
  Ecto schema for the admin_posts_view SQL view.
  This provides a simplified way to query posts with all their related information.
  """
  use Ecto.Schema

  @primary_key {:id, Ecto.UUID, autogenerate: false}
  @derive {Phoenix.Param, key: :id}

  @derive {
    Flop.Schema,
    filterable: [
      :status,
      :social_networks,
      :user_id,
      :user_email,
      :user_name,
      :published_at,
      :inserted_at,
      :updated_at,
      :failed_networks_count,
      :published_networks_count,
      :scheduled_networks_count,
      :published_networks,
      :failed_networks,
      :scheduled_networks
    ],
    sortable: [
      :status,
      :inserted_at,
      :updated_at,
      :published_at,
      :user_email,
      :user_name,
      :failed_networks_count,
      :published_networks_count,
      :scheduled_networks_count,
      :next_scheduled_at
    ],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:updated_at],
      order_directions: [:desc]
    }
  }

  @networks [:bsky, :mastodon, :linkedin, :x]

  schema "admin_posts_view" do
    field :status, :string
    field :social_networks, {:array, Ecto.Enum}, values: @networks
    field :user_email, :string
    field :user_name, :string
    field :user_confirmed_at, :utc_datetime
    field :user_trial_end, :utc_datetime
    field :user_is_subscribed, :boolean
    field :content_text, :string
    field :content_meta, :map
    field :published_at, :utc_datetime
    field :failed_networks_count, :integer
    field :published_networks_count, :integer
    field :scheduled_networks_count, :integer
    field :next_scheduled_at, :utc_datetime
    field :scheduled_networks, :string
    field :published_networks, :string
    field :failed_networks, :string

    belongs_to :user, Crosspost.Admin.Resources.User
    belongs_to :workspace, Crosspost.Accounts.Workspace

    timestamps()
  end

  @doc """
  Returns a list of posts with filtering, sorting, and pagination.
  """
  def list_posts(params) do
    Flop.validate_and_run(__MODULE__, params, for: __MODULE__)
  end
end
