defmodule Crosspost.Admin.Resources.AdminUser do
  @moduledoc """
  Schema for the admin_users_view, which provides a denormalized view of users with their subscription and plan information.
  """
  use Ecto.Schema

  @derive {
    Flop.Schema,
    filterable: [
      :email,
      :name,
      :status,
      :plan_key,
      :confirmed_at,
      :trial_end,
      :inserted_at,
      :tags
    ],
    sortable: [:email, :name, :plan_key, :confirmed_at, :trial_end, :inserted_at, :updated_at],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:inserted_at],
      order_directions: [:desc]
    }
  }

  @primary_key {:id, :id, autogenerate: false}
  schema "admin_users_view" do
    field :email, :string
    field :name, :string
    field :confirmed_at, :utc_datetime
    field :timezone, :string
    field :trial_end, :utc_datetime
    field :plan_key, :string
    field :tags, {:array, :string}, default: []

    # Status field
    field :status, :string

    # Subscription fields
    field :subscription_status, :string
    field :stripe_customer_id, :string
    field :stripe_subscription_id, :string
    field :stripe_product_id, :string
    field :stripe_price_id, :string
    field :subscription_period_start, :utc_datetime
    field :subscription_period_end, :utc_datetime
    field :subscription_features, {:array, :string}

    # Plan fields
    field :plan_name, :string
    field :plan_description, :string
    field :plan_features, {:array, :string}
    field :plan_stripe_price_id, :string

    embeds_one :subscription, Crosspost.Accounts.Subscription

    timestamps()
  end
end
