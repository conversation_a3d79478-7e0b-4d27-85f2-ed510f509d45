defmodule Crosspost.Admin.Resources.UserFeature do
  use Ecto.Schema
  import Ecto.Changeset

  alias Crosspost.Admin.Resources.Feature

  schema "user_features" do
    belongs_to :user, Crosspost.Admin.Resources.User

    belongs_to :feature, Feature,
      foreign_key: :feature_key,
      references: :key,
      type: :string

    timestamps()
  end

  def changeset(user_feature, attrs) do
    user_feature
    |> cast(attrs, [:user_id, :feature_key])
    |> validate_required([:user_id, :feature_key])
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:feature_key)
    |> unique_constraint([:user_id, :feature_key])
  end
end
