defmodule Crosspost.Admin.Resources.Attachment do
  @moduledoc """
  Admin resource for attachments.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @derive {
    Flop.Schema,
    filterable: [:id, :type, :status, :user_id],
    sortable: [:id, :inserted_at, :filename],
    default_order: %{
      order_by: [:inserted_at],
      order_directions: [:desc]
    }
  }

  @primary_key false
  schema "attachments" do
    field :id, :binary_id, primary_key: true
    field :filename, :string
    field :content_type, :string
    field :type, Ecto.Enum, values: [:image, :video, :link]
    field :status, Ecto.Enum, values: [:completed, :pending, :failed]
    field :source_url, :string
    field :preview_url, :string
    field :metadata, :map, default: %{}

    belongs_to :user, Crosspost.Admin.Resources.User

    timestamps()
  end

  @doc """
  Creates a changeset for the attachment.
  """
  def changeset(attachment, attrs) do
    attachment
    |> cast(attrs, [
      :filename,
      :content_type,
      :type,
      :status,
      :source_url,
      :preview_url,
      :metadata
    ])
    |> validate_required([:filename, :content_type, :type, :status])
  end
end
