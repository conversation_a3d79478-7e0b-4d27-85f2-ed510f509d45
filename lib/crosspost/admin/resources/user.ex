defmodule Crosspost.Admin.Resources.User do
  @moduledoc """
  Admin-specific User schema for managing users in the admin panel.
  """
  import Ecto.Changeset

  use Ecto.Schema

  @derive {
    Flop.Schema,
    filterable: [
      :email,
      :name,
      :plan_key,
      :confirmed_at,
      :trial_end,
      :inserted_at,
      :status,
      :has_posts,
      :tags
    ],
    sortable: [:email, :name, :plan_key, :confirmed_at, :trial_end, :inserted_at, :updated_at],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:inserted_at],
      order_directions: [:desc]
    }
  }

  schema "users" do
    field :uid, Ecto.UUID
    field :email, :string
    field :name, :string
    field :hashed_password, :string, redact: true
    field :confirmed_at, :utc_datetime
    field :info, :map
    field :timezone, :string
    field :trial_end, :utc_datetime
    field :status, :string, virtual: true
    field :has_posts, :boolean, virtual: true
    field :tags, {:array, :string}, default: []

    embeds_one :subscription, Crosspost.Accounts.Subscription, on_replace: :update
    embeds_one :settings, Crosspost.Accounts.User.Settings, on_replace: :update

    belongs_to :plan, Crosspost.Admin.Resources.Plan,
      foreign_key: :plan_key,
      references: :key,
      type: :string

    has_many :posts, Crosspost.Admin.Resources.Post
    has_many :feature_usages, Crosspost.Accounts.FeatureUsage
    has_many :connections, Crosspost.Admin.Resources.Connection
    has_many :user_features, Crosspost.Admin.Resources.UserFeature
    has_many :features, through: [:user_features, :feature], on_replace: :delete
    has_many :enabled_features, Crosspost.Accounts.EnabledFeature

    has_one :account, Crosspost.Admin.Marketing.Account, foreign_key: :uid, references: :uid

    timestamps()
  end

  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :email,
      :name,
      :timezone,
      :info,
      :plan_key,
      :trial_end,
      :confirmed_at,
      :tags
    ])
    |> cast_embed(:subscription)
    |> cast_embed(:settings)
    |> validate_required([:email])
  end

  @doc """
  Changeset for managing user features.
  """
  def feature_changeset(user, features) do
    user
    |> change()
    |> put_assoc(:features, features)
  end
end
