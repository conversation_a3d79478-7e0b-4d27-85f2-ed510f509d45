defmodule Crosspost.Admin.Resources.ObanJob do
  use Ecto.Schema

  @derive {
    Flop.Schema,
    filterable: [:state, :queue, :worker, :attempt, :max_attempts, :priority, :tags],
    sortable: [
      :id,
      :state,
      :queue,
      :worker,
      :attempt,
      :priority,
      :inserted_at,
      :scheduled_at,
      :attempted_at,
      :completed_at,
      :discarded_at,
      :cancelled_at
    ],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:inserted_at],
      order_directions: [:desc]
    }
  }

  @primary_key {:id, :id, autogenerate: false}
  schema "oban_jobs" do
    field :state, Ecto.Enum,
      values: [:available, :scheduled, :executing, :retryable, :completed, :discarded, :cancelled]

    field :queue, :string
    field :worker, :string
    field :args, :map
    field :errors, {:array, :map}
    field :attempt, :integer
    field :max_attempts, :integer
    field :priority, :integer
    field :tags, {:array, :string}
    field :meta, :map
    field :attempted_by, {:array, :string}
    field :uniq_key, :string, virtual: true

    timestamps(type: :utc_datetime, updated_at: false)
    field :scheduled_at, :utc_datetime
    field :attempted_at, :utc_datetime
    field :completed_at, :utc_datetime
    field :discarded_at, :utc_datetime
    field :cancelled_at, :utc_datetime
  end

  @doc """
  Converts an Oban.Job struct to an Admin.Resources.ObanJob struct.
  """
  def from_oban_job(job) do
    Map.take(job, [
      :id,
      :state,
      :queue,
      :worker,
      :args,
      :errors,
      :attempt,
      :max_attempts,
      :priority,
      :tags,
      :meta,
      :attempted_by,
      :uniq_key,
      :inserted_at,
      :scheduled_at,
      :attempted_at,
      :completed_at,
      :discarded_at,
      :cancelled_at
    ])
  end
end
