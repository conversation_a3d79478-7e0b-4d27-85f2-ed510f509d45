defmodule Crosspost.Admin.Resources.Plan do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:key, :string, []}
  @derive {Phoenix.Param, key: :key}
  schema "plans" do
    field :name, :string
    field :description, :string
    field :price, :integer
    field :stripe_price_id, :string
    field :highlight, :boolean, default: false
    field :enabled, :boolean, default: false
    field :default, :boolean, default: false
    field :cta, :string
    field :action, :string
    field :features, {:array, :string}, default: []
    field :archived, :boolean, default: false

    has_many :users, Crosspost.Admin.Resources.User

    timestamps()
  end

  @doc false
  def changeset(plan, attrs) do
    plan
    |> cast(attrs, [
      :key,
      :name,
      :description,
      :price,
      :stripe_price_id,
      :highlight,
      :enabled,
      :default,
      :cta,
      :action,
      :archived,
      :features
    ])
    |> validate_required([:key, :name, :price])
  end
end
