defmodule Crosspost.Admin.Resources.Post do
  @moduledoc """
  Admin-specific Post schema for managing posts in the admin panel.
  """
  use Ecto.Schema
  import Ecto.Changeset

  @derive {
    Flop.Schema,
    filterable: [:status, :social_networks, :user_id],
    sortable: [:status, :inserted_at, :updated_at, :published_at],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:updated_at],
      order_directions: [:desc]
    },
    adapter_opts: [
      alias_fields: [:published_at],
      custom_fields: []
    ]
  }

  @networks [:bsky, :mastodon, :linkedin, :x]
  @statuses [
    "draft",
    "pending",
    "scheduled",
    "publishing",
    "partially_published",
    "published",
    "failed"
  ]

  @primary_key {:id, Ecto.UUID, autogenerate: false}
  schema "posts" do
    field :status, :string
    field :social_networks, {:array, Ecto.Enum}, values: @networks
    field :published_at, :utc_datetime, virtual: true

    belongs_to :user, Crosspost.Admin.Resources.User
    belongs_to :workspace, Crosspost.Accounts.Workspace

    has_many :post_content, Crosspost.Publishing.PostContent
    has_many :post_statuses, Crosspost.Publishing.PostStatus
    has_many :final_statuses, Crosspost.Publishing.PostStatus, where: [is_final: true]

    has_many :schedules, Crosspost.Publishing.Schedule

    timestamps()
  end

  @doc """
  Converts a regular Post struct to an Admin Post struct.
  """
  def from_post(post) do
    Map.take(post, [
      :id,
      :status,
      :social_networks,
      :user_id,
      :user,
      :post_content,
      :post_statuses,
      :schedules,
      :inserted_at,
      :updated_at
    ])
  end

  @doc """
  Changeset for updating a post in the admin panel.
  """
  def changeset(post, attrs) do
    post
    |> cast(attrs, [:status])
    |> validate_inclusion(:status, @statuses)
  end
end
