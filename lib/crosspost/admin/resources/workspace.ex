defmodule Crosspost.Admin.Resources.Workspace do
  @moduledoc """
  Admin-specific Workspace schema for managing workspaces in the admin panel.
  """
  import Ecto.Changeset

  use Ecto.Schema

  @derive {
    Flop.Schema,
    filterable: [:name, :owner_id],
    sortable: [:name, :owner_id, :inserted_at],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:inserted_at],
      order_directions: [:desc]
    }
  }

  schema "workspaces" do
    field :name, :string
    field :slug, :string
    field :settings, :map
    field :is_default, :boolean, default: false
    field :invitations, {:array, :map}, default: []

    belongs_to :owner, Crosspost.Admin.Resources.User

    has_many :workspace_users, Crosspost.Accounts.WorkspaceUser
    has_many :users, through: [:workspace_users, :user]
    has_many :posts, Crosspost.Admin.Resources.Post
    has_many :connections, Crosspost.Accounts.Connection

    timestamps()
  end

  def changeset(workspace, attrs) do
    workspace
    |> cast(attrs, [:name, :settings, :is_default, :owner_id])
    |> validate_required([:name, :owner_id])
  end
end
