defmodule Crosspost.Admin.Resources.Feature do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Flop.Schema,
           filterable: [:key, :name, :type, :reset_period],
           sortable: [:key, :name, :type, :reset_period, :inserted_at]}

  @primary_key {:key, :string, autogenerate: false}
  @derive {Phoenix.Param, key: :key}

  schema "features" do
    field :name, :string
    field :description, :string
    field :usage_key, :string
    field :type, Ecto.Enum, values: [:counter, :limit, :boolean]
    field :reset_period, Ecto.Enum, values: [:daily, :monthly, :yearly], default: nil
    field :limit, :integer
    field :coming_soon, :boolean, default: false

    timestamps()
  end

  def changeset(feature, attrs) do
    feature
    |> cast(attrs, [
      :key,
      :name,
      :description,
      :usage_key,
      :type,
      :reset_period,
      :limit,
      :coming_soon
    ])
    |> validate_required([:name, :description, :type])
    |> maybe_generate_key()
    |> validate_required([:key])
    |> validate_format(:key, ~r/^[a-z0-9_]+$/,
      message: "must contain only lowercase letters, numbers, and underscores"
    )
    |> unique_constraint(:key)
    |> unique_constraint(:usage_key)
  end

  defp maybe_generate_key(changeset) do
    if get_field(changeset, :key) do
      changeset
    else
      case get_change(changeset, :name) do
        nil ->
          changeset

        name ->
          key =
            name
            |> String.downcase()
            |> String.replace(~r/[^a-z0-9]+/, "_")
            |> String.trim("_")

          put_change(changeset, :key, key)
      end
    end
  end
end
