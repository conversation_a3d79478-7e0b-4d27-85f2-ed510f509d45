defmodule Crosspost.Admin.Marketing.Subscribers do
  @moduledoc """
  Context for querying marketing subscriber data.
  """

  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Admin.Marketing.Account
  alias Crosspost.Accounts.Waitlist

  @doc """
  Lists accounts based on the specified group and filter/sort/pagination parameters.
  """
  def list_accounts_with_filters(
        group_key,
        page,
        per_page,
        sort_field,
        sort_direction,
        connected_platforms \\ []
      ) do
    if group_key == "waitlisted_not_signed_up" do
      # Handle waitlist entries separately
      query =
        from w in Waitlist,
          where: not is_nil(w.invited_at) and is_nil(w.used_at) and is_nil(w.spam_status),
          select: %Account{
            id: w.id,
            email: w.email,
            name: nil,
            status: "waitlisted",
            conversion_source: "waitlist",
            published_post_count: 0,
            connected_platforms: []
          }

      # Only allow sorting by fields that exist on Waitlist or the Account struct
      allowed_sort_fields = ["email", "id"]
      sort_field = if sort_field in allowed_sort_fields, do: sort_field, else: "id"
      sort_direction = sort_direction || :asc

      # Apply sorting
      query =
        case sort_field do
          "email" -> from q in query, order_by: [{^sort_direction, q.email}]
          "id" -> from q in query, order_by: [{^sort_direction, q.id}]
          _ -> from q in query, order_by: [{^sort_direction, q.id}]
        end

      accounts = Repo.all(query)
      total_count = length(accounts)

      total_pages =
        if per_page > 0 do
          Float.ceil(total_count / per_page) |> trunc()
        else
          1
        end

      paged_accounts =
        accounts
        |> Enum.slice((page - 1) * per_page, per_page)

      %{
        accounts: paged_accounts,
        total_count: total_count,
        total_pages: total_pages
      }
    else
      # Regular account handling
      base_query = from(a in Account)
      query = apply_group_filter(base_query, group_key)

      # Filter by connected_platforms if provided
      query =
        if is_list(connected_platforms) and connected_platforms != [] do
          from a in query,
            where:
              fragment(
                "? @> ?::varchar[]",
                a.connected_platforms,
                type(^connected_platforms, {:array, :string})
              )
        else
          query
        end

      # Create Flop params
      flop = %Flop{
        page: page,
        page_size: per_page,
        order_by: [sort_field],
        order_directions: [sort_direction || :asc]
      }

      case Flop.validate_and_run(query, flop, for: Account) do
        {:ok, {accounts, meta}} ->
          %{
            accounts: accounts,
            total_count: meta.total_count,
            total_pages: meta.total_pages
          }

        {:error, _meta} ->
          %{accounts: [], total_count: 0, total_pages: 0}
      end
    end
  end

  @doc """
  Applies a filter to the query based on the group key.
  """
  def apply_group_filter(query, group_key) do
    case group_key do
      "all" ->
        query

      # 1. Customers: customer = true
      "customers" ->
        from a in query, where: a.customer == true

      # 2. Users from Waitlist with published posts: customer = false AND conversion_source = waitlist AND posts_count > 0 AND status = :trial_ended
      "waitlist_with_posts" ->
        from a in query,
          where:
            a.customer == false and a.conversion_source == "waitlist" and
              a.published_post_count > 0 and a.status == :trial_ended

      # 3. Users from Waitlist without published posts: customer = false AND conversion_source = waitlist AND posts_count = 0 AND status = :trial_ended
      "waitlist_without_posts" ->
        from a in query,
          where:
            a.customer == false and a.conversion_source == "waitlist" and
              a.published_post_count == 0 and a.status == :trial_ended

      # 4. Users from Registration with published posts: customer = false AND conversion_source = registration AND posts_count > 0
      "registration_with_posts" ->
        from a in query,
          where:
            a.customer == false and a.conversion_source == "registration" and
              a.published_post_count > 0

      # 5. Users from Registration without published posts: customer = false AND conversion_source = registration AND posts_count = 0
      "registration_without_posts" ->
        from a in query,
          where:
            a.customer == false and a.conversion_source == "registration" and
              a.published_post_count == 0

      nil ->
        query

      _ ->
        query
    end
  end

  @doc """
  Returns the list of emails for waitlisted users who haven't signed up yet.
  """
  def waitlisted_not_signed_up_emails do
    from(w in Waitlist,
      where: not is_nil(w.invited_at) and is_nil(w.used_at) and is_nil(w.spam_status),
      select: w.email
    )
    |> Repo.all()
  end
end
