defmodule Crosspost.Admin.Marketing.Emails do
  @moduledoc """
  Context for managing MailerLite groups and subscribers.
  """

  alias Crosspost.Repo
  alias Crosspost.Admin.Marketing.Subscribers
  alias Crosspost.Admin.Marketing.Account

  import Ecto.Query
  require Logger

  @group_names %{
    "customers" => "Customers",
    "waitlist_with_posts" => "Waitlist: With Published Posts",
    "waitlist_without_posts" => "Waitlist: Without Published Posts",
    "registration_with_posts" => "Registration: With Published Posts",
    "registration_without_posts" => "Registration: Without Published Posts",
    "waitlisted_not_signed_up" => "Waitlisted Not Signed Up"
  }

  # Get timeout values from config with defaults
  @max_import_retries Application.compile_env(:crosspost, :mailer_lite_max_import_retries, 30)
  @retry_delay_ms Application.compile_env(:crosspost, :mailer_lite_retry_delay_ms, 10_000)

  @doc """
  Synchronizes a specific group of users with MailerLite.

  Finds or creates the MailerLite group and imports the subscribers.
  """
  def sync_group(group_key) do
    Logger.info("Starting sync for group key: #{group_key}")

    # Store the calling process PID (likely to be LiveView process)
    caller_pid = self()

    group_name = @group_names[group_key] || raise "Unknown group key: #{group_key}"
    config = [api_key: mailer_lite_api_key()]

    with {:ok, group} <- find_or_create_mailerlite_group(config, group_name),
         _ <- Logger.info("Found/created MailerLite group: #{inspect(group)}"),
         {:ok, subscribers} <- get_subscribers_for_group(group_key),
         _ <- Logger.info("Found #{length(subscribers)} subscribers for group #{group_key}") do
      # Get current subscribers in the group
      {:ok, %{"data" => current_subscribers}} =
        mailer_lite().get_group_subscribers(config, group["id"])

      current_emails = MapSet.new(current_subscribers, & &1["email"])
      new_emails = MapSet.new(subscribers, & &1.email)
      to_remove = MapSet.difference(current_emails, new_emails)
      to_add = MapSet.difference(new_emails, current_emails)

      # Remove subscribers that shouldn't be in the group anymore
      removed_subscribers =
        Enum.reduce(to_remove, [], fn email, acc ->
          subscriber = Enum.find(current_subscribers, &(&1["email"] == email))

          if subscriber do
            case mailer_lite().unassign_subscriber_from_group(
                   config,
                   subscriber["id"],
                   group["id"]
                 ) do
              {:ok, _} ->
                [%{email: email, status: "removed"} | acc]

              {:error, reason} ->
                [%{email: email, status: "failed", error: inspect(reason)} | acc]
            end
          else
            acc
          end
        end)

      # Format ALL subscribers intended for the group (new or existing)
      formatted_subscribers =
        subscribers
        |> Enum.map(fn %{
                         email: email,
                         connected_platforms: platforms,
                         published_post_count: post_count,
                         status: status,
                         name: name,
                         customer: customer
                       } ->
          formatted_platforms =
            (platforms || [])
            |> Enum.map(fn platform ->
              case platform do
                "x" -> "platform-x"
                other -> other
              end
            end)
            |> Enum.join(", ")

          user_status = format_user_status(status)

          %{
            email: email,
            fields: %{
              platforms: formatted_platforms,
              has_published_posts: if(post_count > 0, do: "yes", else: "no"),
              user_status: user_status,
              name: name,
              is_customer: if(customer == true, do: "yes", else: "no")
            }
          }
        end)

      if subscribers == [] do
        # If the initial DB query found no subscribers for this group filter
        Logger.info("No subscribers found in database for group '#{group_name}'")

        {:ok,
         %{
           message: "No subscribers to sync for group '#{group_name}'",
           details: %{
             removed: removed_subscribers,
             imported: [],
             updated: []
           }
         }}
      else
        # Prepare details for tracking before the import call
        # Categorize based on whether they were in the 'to_add' set
        {to_import_details, to_update_details} =
          formatted_subscribers
          |> Enum.map(&%{email: &1.email, status: "pending"})
          |> Enum.split_with(&MapSet.member?(to_add, &1.email))

        Process.put(:sync_details, %{
          removed: removed_subscribers,
          to_import: to_import_details,
          to_update: to_update_details
        })

        Logger.info(
          "Importing/Updating #{length(formatted_subscribers)} subscribers to group #{group["id"]}",
          group_id: group["id"],
          count: length(formatted_subscribers)
        )

        # Call MailerLite API with ALL formatted subscribers (new and existing)
        case mailer_lite().import_subscribers_to_group(config, group["id"], formatted_subscribers) do
          {:ok, %{"import_progress_url" => progress_url}} ->
            import_id = Path.basename(progress_url)
            # Wait for import to complete (uses details from Process dict)
            wait_for_import_completion(config, import_id, pid: caller_pid)

          {:error, %{"errors" => errors}} when is_map(errors) ->
            # ... existing error handling for invalid emails ...
            # Ensure details are included in the error result
            {:error,
             %{
               message: Map.get(errors, "message", "Import failed due to invalid data"),
               # Use stored details
               details: Process.get(:sync_details)
             }}

          {:error, %{status: _status, body: %{"message" => message} = _body}} ->
            {:error,
             %{
               message: message,
               # Use stored details
               details: Process.get(:sync_details)
             }}

          {:error, error} ->
            {:error,
             %{
               message: "Unknown error occurred during import: #{inspect(error)}",
               # Use stored details
               details: Process.get(:sync_details)
             }}
        end
      end
    else
      {:error, reason} ->
        Logger.error("Failed to sync group #{group_key}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp wait_for_import_completion(config, import_id, opts) do
    pid = Keyword.get(opts, :pid)
    retries = Keyword.get(opts, :retries, @max_import_retries)

    if retries > 0 do
      Logger.info("Checking import progress",
        event: "mailerlite.import_progress.check",
        import_id: import_id,
        retries_left: retries,
        max_retries: @max_import_retries
      )

      case mailer_lite().get_import_progress(config, import_id) do
        {:ok, %{"data" => %{"done" => true} = data}} ->
          Logger.info("Import completed successfully",
            event: "mailerlite.import_progress.complete",
            import_id: import_id,
            processed: data["processed"],
            imported: data["imported"],
            errored: data["errored"]
          )

          # Get the tracking details
          %{removed: removed, to_import: to_import, to_update: to_update} =
            Process.get(:sync_details)

          # Update statuses based on the import result
          imported =
            to_import
            |> Enum.map(fn subscriber ->
              %{subscriber | status: "imported"}
            end)

          updated =
            to_update
            |> Enum.map(fn subscriber ->
              %{subscriber | status: "updated"}
            end)

          {:ok,
           %{
             message: "Import completed successfully",
             data: data,
             details: %{
               removed: removed,
               imported: imported,
               updated: updated
             }
           }}

        {:ok, %{"data" => %{"done" => false} = data}} ->
          progress = %{
            processed: data["processed"],
            total: data["total"],
            percent: data["percent"],
            errored: data["errored"]
          }

          Logger.info("Import in progress",
            event: "mailerlite.import_progress.pending",
            import_id: import_id,
            retries_left: retries,
            progress: progress
          )

          # Send progress update to the LiveView if a PID was provided
          if pid && is_pid(pid) && Process.alive?(pid) do
            send(pid, {:sync_progress_update, progress})
          end

          Process.sleep(@retry_delay_ms)
          wait_for_import_completion(config, import_id, pid: pid, retries: retries - 1)

        {:error, reason} ->
          Logger.error("Failed to check import progress",
            event: "mailerlite.import_progress.error",
            import_id: import_id,
            retries_left: retries,
            error: inspect(reason)
          )

          {:error, reason}
      end
    else
      Logger.error("Import did not complete after #{@max_import_retries} retries",
        event: "mailerlite.import_progress.timeout",
        import_id: import_id,
        max_retries: @max_import_retries,
        total_time_seconds: @max_import_retries * @retry_delay_ms / 1000
      )

      {:error, :import_timeout}
    end
  end

  def get_group_keys(), do: Map.keys(@group_names)

  @doc """
  Returns the display name for a given group key.
  """
  def get_group_name(key) do
    @group_names[key] || key
  end

  defp find_or_create_mailerlite_group(config, group_name) do
    module = mailer_lite()
    Logger.debug("Using MailerLite module: #{inspect(module)}")

    case module.list_groups(config, %{name: group_name}) do
      {:ok, %{"data" => [group | _]}} ->
        {:ok, group}

      {:ok, %{"data" => []}} ->
        case module.create_group(config, group_name) do
          {:ok, %{"data" => group}} -> {:ok, group}
          {:error, reason} -> {:error, "Failed to create group: #{inspect(reason)}"}
          other -> {:error, "Unexpected response when creating group: #{inspect(other)}"}
        end

      {:error, reason} ->
        {:error, "Failed to list groups: #{inspect(reason)}"}

      other ->
        {:error, "Unexpected response when listing groups: #{inspect(other)}"}
    end
  end

  defp get_subscribers_for_group("waitlisted_not_signed_up") do
    emails = Subscribers.waitlisted_not_signed_up_emails()

    {:ok,
     Enum.map(
       emails,
       &%{
         email: &1,
         connected_platforms: [],
         published_post_count: 0,
         has_published_posts: false,
         name: nil,
         customer: false
       }
     )}
  end

  defp get_subscribers_for_group(group_key) do
    query = from(a in Account)
    query = Subscribers.apply_group_filter(query, group_key)

    subscribers =
      Repo.all(
        from a in query,
          select: %{
            email: a.email,
            connected_platforms: a.connected_platforms,
            published_post_count: a.published_post_count,
            has_published_posts: a.published_post_count > 0,
            status: a.status,
            name: a.name,
            customer: a.customer
          }
      )

    {:ok, subscribers}
  end

  defp mailer_lite do
    Application.get_env(:crosspost, :mailer_lite_module)
  end

  defp mailer_lite_api_key do
    Application.get_env(:crosspost, :mailer_lite_api_key)
  end

  defp format_user_status(status) do
    case status do
      :active -> "Active"
      :trial_ended -> "Trial Ended"
      :trial -> "Trial"
      :inactive -> "Inactive"
      :banned -> "Banned"
      _ -> "Unknown"
    end
  end
end
