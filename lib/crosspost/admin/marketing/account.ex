defmodule Crosspost.Admin.Marketing.Account do
  use Ecto.Schema

  @derive {
    Flop.Schema,
    filterable: [:email, :status, :conversion_source, :published_post_count, :customer],
    sortable: [:email, :status, :conversion_source, :published_post_count, :customer],
    pagination_types: [:page],
    default_limit: 20,
    default_order: %{
      order_by: [:email],
      order_directions: [:asc]
    }
  }

  @status_enum [:trial, :active, :canceled, :past_due, :unpaid, :trial_ended]

  @primary_key false
  schema "accounts" do
    field :id, :integer, primary_key: true
    field :uid, Ecto.UUID
    field :customer, :boolean, default: false
    field :status, Ecto.Enum, values: @status_enum
    field :email, :string
    field :name, :string
    field :conversion_source, :string
    field :published_post_count, :integer
    field :connected_platforms, {:array, :string}
  end
end
