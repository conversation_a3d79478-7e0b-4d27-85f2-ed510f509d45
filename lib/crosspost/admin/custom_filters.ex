defmodule Crosspost.Admin.CustomFilters do
  @moduledoc """
  Custom filter functions for Flop queries in the Admin context.
  """
  import Ecto.Query

  @doc """
  Filter for the published_at virtual field.
  This is used for filtering posts by their publication date.

  Note: This filter is no longer used for sorting as we're now using alias fields
  for the published_at field. It's kept for filtering functionality.
  """
  def published_at_filter(query, %Flop.Filter{value: value, op: op}, _opts) do
    # Create a subquery to get the latest final status for each post
    latest_final_status_query =
      from ps in Crosspost.Publishing.PostStatus,
        where: ps.is_final == true,
        group_by: ps.post_id,
        select: %{
          post_id: ps.post_id,
          published_at: max(ps.inserted_at)
        }

    # Join with the subquery to get published_at
    query =
      from p in query,
        left_join: fs in subquery(latest_final_status_query),
        on: p.id == fs.post_id,
        select: %{p | published_at: fs.published_at}

    # Apply the filter based on the operator
    case op do
      :>= -> where(query, [p, fs], fs.published_at >= ^value)
      :<= -> where(query, [p, fs], fs.published_at <= ^value)
      :> -> where(query, [p, fs], fs.published_at > ^value)
      :< -> where(query, [p, fs], fs.published_at < ^value)
      :== -> where(query, [p, fs], fs.published_at == ^value)
      :!= -> where(query, [p, fs], fs.published_at != ^value)
      _ -> query
    end
  end
end
