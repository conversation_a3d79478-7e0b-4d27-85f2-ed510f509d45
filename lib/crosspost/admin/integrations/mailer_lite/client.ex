defmodule Crosspost.Admin.Integrations.MailerLite.Client do
  @moduledoc """
  Implementation of the MailerLite API client using Req.
  """
  @behaviour Crosspost.Admin.Integrations.MailerLite
  require Logger

  alias Crosspost.Admin.Integrations.MailerLite

  @base_url "https://connect.mailerlite.com/api"

  @impl MailerLite
  def list_groups(config, filter \\ %{}) do
    Logger.debug("Listing MailerLite groups",
      event: "mailerlite.list_groups",
      filter: filter
    )

    req = base_req(config)
    query_params = Enum.map(filter, fn {k, v} -> {"filter[#{k}]", v} end)

    case Req.get!(req, url: "#{@base_url}/groups", params: query_params) do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite list_groups response",
          event: "mailerlite.list_groups.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def create_group(config, name) do
    Logger.debug("Creating MailerLite group",
      event: "mailerlite.create_group",
      name: name
    )

    req = base_req(config)
    body = %{name: name}

    case Req.post!(req, url: "#{@base_url}/groups", json: body) do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite create_group response",
          event: "mailerlite.create_group.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def import_subscribers_to_group(config, group_id, subscribers) do
    Logger.debug("Importing subscribers to MailerLite group",
      event: "mailerlite.import_subscribers",
      group_id: group_id,
      subscriber_count: length(subscribers)
    )

    req = base_req(config)
    body = %{subscribers: subscribers}

    case Req.post!(req, url: "#{@base_url}/groups/#{group_id}/import-subscribers", json: body) do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite import_subscribers response",
          event: "mailerlite.import_subscribers.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def get_import_progress(config, import_id) do
    Logger.debug("Getting MailerLite import progress",
      event: "mailerlite.get_import_progress",
      import_id: import_id
    )

    req = base_req(config)

    case Req.get!(req, url: "#{@base_url}/subscribers/import/#{import_id}") do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite get_import_progress response",
          event: "mailerlite.get_import_progress.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def get_group_subscribers(config, group_id) do
    Logger.debug("Getting MailerLite group subscribers",
      event: "mailerlite.get_group_subscribers",
      group_id: group_id
    )

    req = base_req(config)

    case Req.get!(req, url: "#{@base_url}/groups/#{group_id}/subscribers") do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite get_group_subscribers response",
          event: "mailerlite.get_group_subscribers.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def update_subscriber(config, subscriber_id, fields) do
    Logger.debug("Updating MailerLite subscriber",
      event: "mailerlite.update_subscriber",
      subscriber_id: subscriber_id,
      fields: fields
    )

    req = base_req(config)
    body = %{fields: fields}

    case Req.put!(req, url: "#{@base_url}/subscribers/#{subscriber_id}", json: body) do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite update_subscriber response",
          event: "mailerlite.update_subscriber.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  @impl MailerLite
  def unassign_subscriber_from_group(config, subscriber_id, group_id) do
    Logger.debug("Unassigning subscriber from MailerLite group",
      event: "mailerlite.unassign_subscriber",
      subscriber_id: subscriber_id,
      group_id: group_id
    )

    req = base_req(config)

    case Req.delete!(req, url: "#{@base_url}/subscribers/#{subscriber_id}/groups/#{group_id}") do
      %{status: status, body: body} = response ->
        Logger.debug("MailerLite unassign_subscriber response",
          event: "mailerlite.unassign_subscriber.response",
          status: status,
          body: body
        )

        handle_response(response)
    end
  end

  defp base_req(config) do
    api_key = Keyword.fetch!(config, :api_key)

    Logger.debug("Creating MailerLite request",
      event: "mailerlite.request",
      base_url: @base_url
    )

    Req.new(base_url: @base_url)
    |> Req.Request.put_header("Authorization", "Bearer #{api_key}")
    |> Req.Request.put_header("Content-Type", "application/json")
    |> Req.Request.put_header("Accept", "application/json")
  end

  defp handle_response(%{status: status, body: body}) when status in [200, 201, 204] do
    {:ok, body}
  end

  defp handle_response(%{status: status, body: body}) do
    Logger.error("MailerLite API error",
      event: "mailerlite.error",
      status: status,
      body: body
    )

    {:error, %{status: status, body: body}}
  end
end
