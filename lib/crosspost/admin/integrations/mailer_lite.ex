defmodule Crosspost.Admin.Integrations.MailerLite do
  @moduledoc """
  Behaviour for interacting with the MailerLite API.
  """

  @typedoc "MailerLite API configuration"
  @type config :: keyword()

  @typedoc "MailerLite Group resource"
  @type group :: map()

  @typedoc "MailerLite Subscriber resource"
  @type subscriber :: %{email: String.t(), fields: map()}

  @typedoc "MailerLite API response"
  @type response :: {:ok, map()} | {:error, any()}

  @doc """
  Lists groups from MailerLite.
  Supports filtering by name.
  """
  @callback list_groups(config :: config(), filter :: map()) :: response()

  @doc """
  Creates a group in MailerLite.
  """
  @callback create_group(config :: config(), name :: String.t()) :: response()

  @doc """
  Imports subscribers in bulk to a specific group.
  """
  @callback import_subscribers_to_group(
              config :: config(),
              group_id :: String.t() | integer(),
              subscribers :: list(subscriber())
            ) :: response()

  @doc """
  Gets the progress of a bulk import operation.
  """
  @callback get_import_progress(config :: config(), import_id :: String.t()) :: response()

  @doc """
  Gets subscribers belonging to a group.
  """
  @callback get_group_subscribers(config :: config(), group_id :: String.t() | integer()) ::
              response()

  @doc """
  Updates a subscriber's fields.
  """
  @callback update_subscriber(config :: config(), subscriber_id :: String.t(), fields :: map()) ::
              response()

  @doc """
  Unassigns a subscriber from a group.
  """
  @callback unassign_subscriber_from_group(
              config :: config(),
              subscriber_id :: String.t(),
              group_id :: String.t() | integer()
            ) :: response()
end
