defmodule Crosspost.Workers.TestPublishPostWorker do
  use Oban.Worker,
    queue: :default,
    max_attempts: 3,
    unique: [
      period: :infinity,
      keys: [:post_id, :network],
      states: [:scheduled, :available, :executing, :retryable]
    ]

  use GenServer

  alias Crosspost.Accounts
  alias Crosspost.Publishing

  require Logger

  # ETS table for storing published posts
  @table_name :test_published_posts

  # Client API

  def start_link(_opts \\ []) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def clear_published do
    GenServer.call(__MODULE__, :clear)
  end

  def get_published do
    GenServer.call(__MODULE__, :get_all)
  end

  def get_published_for_network(network) do
    GenServer.call(__MODULE__, {:get_for_network, network})
  end

  # Server callbacks

  @impl GenServer
  def init(_opts) do
    table = :ets.new(@table_name, [:set, :protected, :named_table])
    {:ok, %{table: table}}
  end

  @impl GenServer
  def handle_call(:clear, _from, state) do
    :ets.delete_all_objects(@table_name)
    {:reply, :ok, state}
  end

  @impl GenServer
  def handle_call(:get_all, _from, state) do
    posts =
      case :ets.tab2list(@table_name) do
        [] -> []
        posts -> Enum.map(posts, fn {_key, post_data} -> post_data end)
      end

    {:reply, posts, state}
  end

  @impl GenServer
  def handle_call({:get_for_network, network}, _from, state) do
    posts =
      :ets.tab2list(@table_name)
      |> Enum.filter(fn {_key, post_data} ->
        post_data.content
        |> Enum.any?(&(&1.network == Atom.to_string(network)))
      end)
      |> Enum.map(fn {_key, post_data} -> post_data end)

    {:reply, posts, state}
  end

  @impl GenServer
  def handle_cast({:store_published, post_data}, state) do
    key = {post_data.user_id, post_data.network, DateTime.to_unix(post_data.published_at)}
    :ets.insert(@table_name, {key, post_data})
    {:noreply, state}
  end

  # Oban Worker Implementation

  @impl Oban.Worker
  def perform(
        %Oban.Job{
          args: %{"post_id" => post_id, "user_id" => user_id, "network" => network},
          attempt: _attempt
        } = _job
      ) do
    Logger.debug("Publishing post #{post_id} to #{network}")

    user = Accounts.get_user!(user_id)

    {:ok, post} = Publishing.get_post(post_id, user)
    GenServer.cast(__MODULE__, {:store_published, post})

    :ok
  end
end
