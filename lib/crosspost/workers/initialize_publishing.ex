defmodule Crosspost.Workers.InitializePublishing do
  use Crosspost.Worker,
    queue: :publishing,
    unique: [
      period: :infinity,
      states: [:available, :scheduled, :executing],
      keys: [:post_id, :network]
    ],
    max_attempts: 3

  alias Crosspost.Posts
  alias Crosspost.Publishing
  alias Crosspost.Accounts

  import Publishing, only: [backend_module: 1]
  import Crosspost, only: [broadcast: 2]

  args_schema do
    field :user_id, :id, required: true
    field :post_id, :string, required: true
    field :network, :enum, required: true, values: Application.compile_env(:crosspost, :networks)
  end

  @impl true
  def process(%{args: %{post_id: post_id, network: network}} = job) do
    Logger.metadata(%{
      post_id: post_id,
      network: network,
      job_id: job.id
    })

    case Posts.get_post(post_id) do
      nil ->
        {:error, :post_not_found}

      post ->
        Logger.metadata(%{user_id: post.user_id, workspace_id: post.workspace_id})

        case Publishing.get_post_status(post_id, network) do
          %{status: :published, is_final: true} ->
            Logger.info("Skipping already published network", %{
              event: "worker.initialize_publishing.skip_published"
            })

            {:discard, :already_published}

          _status ->
            case Posts.update_status(post, "publishing") do
              {:ok, updated_post} ->
                broadcast("posts:#{updated_post.user_id}", {:post_updated, updated_post})

                case maybe_refresh_connection(updated_post, network) do
                  {:ok, _} ->
                    :ok

                  {:error, reason} = error ->
                    {:ok, _} =
                      Publishing.create_post_status(
                        post_id,
                        network,
                        %{
                          status: :failed,
                          outcome: %{
                            error: %{
                              result: inspect(reason),
                              message: "Failed to refresh connection"
                            }
                          },
                          is_final: true
                        }
                      )

                    error
                end

              {:error, reason} = error ->
                Logger.error("Failed to initialize publishing", %{
                  event: "worker.initialize_publishing.error",
                  error: inspect(reason)
                })

                error
            end
        end
    end
  end

  defp maybe_refresh_connection(post, network) do
    case Accounts.get_connection(network, post.workspace_id) do
      nil ->
        {:error, :connection_not_found}

      connection ->
        Logger.info("Refreshing connection", %{
          event: "worker.initialize_publishing.refresh_connection",
          connection_id: connection.id
        })

        case backend_module(network).refresh_connection(connection) do
          :ok ->
            Logger.info("Refreshing skipped", %{
              event: "worker.initialize_publishing.refresh_connection_skipped",
              connection_id: connection.id
            })

            {:ok, connection}

          {:ok, {access_token, refresh_token, expires_at}} ->
            {:ok, updated_connection} =
              Accounts.update_connection(connection, %{
                encrypted_access_token: access_token,
                encrypted_refresh_token: refresh_token,
                expires_at: expires_at
              })

            Logger.info("Refreshed connection", %{
              event: "worker.initialize_publishing.refresh_connection_success",
              connection_id: connection.id
            })

            :timer.sleep(2000)

            {:ok, updated_connection}

          {:error, reason} ->
            Logger.error("Refreshing connection failed", %{
              event: "worker.initialize_publishing.refresh_connection_error",
              error: reason
            })

            {:error, :connection_refresh_failed}
        end
    end
  end
end
