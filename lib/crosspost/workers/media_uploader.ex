defmodule Crosspost.Workers.MediaUploader do
  use Crosspost.Worker,
    queue: :media,
    max_attempts: 3,
    recorded: true

  require Logger

  alias Crosspost.{Repo, Accounts, Publishing, Attachments}
  alias Crosspost.Publishing.PostContent

  @impl true
  def backoff(%Job{attempt: _attempt} = _job) do
    1
  end

  @impl true
  def timeout(_job), do: :timer.seconds(300)

  @impl true
  def process(
        %Oban.Job{
          args: %{
            "attachment_id" => attachment_id,
            "content_id" => content_id,
            "user_id" => user_id,
            "workspace_id" => workspace_id,
            "network" => network
          }
        } = _job
      ) do
    {:ok, content} = get_content(content_id)

    Logger.metadata(
      user_id: user_id,
      workspace_id: workspace_id,
      attachment_id: attachment_id,
      content_id: content_id,
      post_id: content.post_id,
      network: network
    )

    with {:ok, attachment} <- get_attachment(attachment_id, content_id),
         {:ok, user} <- get_connected_user(user_id, workspace_id),
         {:ok, backend} <- get_backend(String.to_existing_atom(network)),
         {:ok, media} <- upload_media(backend, attachment, user, content) do
      Logger.info("Successfully uploaded media", %{
        event: "worker.media_uploader.media_upload_success",
        media: media,
        metadata: attachment.metadata
      })

      {:ok, media}
    else
      {:error, reason} = error ->
        Logger.error("Media upload failed", %{
          event: "worker.media_uploader.media_upload_failed",
          attachment_id: attachment_id,
          error: reason
        })

        Sentry.capture_message("Media upload failed",
          extra: %{
            attachment_id: attachment_id,
            error: reason
          }
        )

        error
    end
  end

  @impl true
  def after_process(state, %Oban.Job{attempt: attempt, max_attempts: max_attempts} = job, result)
      when state in [:error, :discard] do
    case get_content(job.args["content_id"]) do
      {:ok, content} ->
        create_post_status(content, %{
          status: if(attempt == max_attempts, do: :failed, else: :media_upload_failed),
          outcome: %{error: inspect(result)},
          is_final: attempt == max_attempts
        })

      _ ->
        :ok
    end

    :ok
  end

  def after_process(:complete, _job, _result), do: :ok
  def after_process(_state, _job, _result), do: :ok

  defp create_post_status(content, params) do
    {:ok, _} =
      Publishing.create_post_status(
        content.post_id,
        content.network,
        Map.merge(params, %{content_id: content.id})
      )
  end

  defp get_content(content_id) do
    case Repo.get(PostContent, content_id) |> Repo.preload([:post, :workspace]) do
      nil -> {:error, :content_not_found}
      content -> {:ok, content}
    end
  end

  defp get_attachment(id, content_id) do
    case Attachments.get_content_attachment(id, content_id) do
      nil -> {:error, :attachment_not_found}
      attachment -> {:ok, attachment}
    end
  end

  defp get_connected_user(id, workspace_id) do
    case Accounts.get_connected_user!(id, workspace_id) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  rescue
    Ecto.NoResultsError -> {:error, :user_not_found}
  end

  defp get_backend(network) do
    {:ok, Publishing.backend_module(network)}
  end

  defp upload_media(_backend, %{type: :link, source_url: nil} = link, _user, _content) do
    {:ok,
     %{
       type: :link,
       url: link.metadata["url"],
       title: link.metadata["title"],
       description: link.metadata["description"],
       thumb: nil,
       order: 0
     }}
  end

  defp upload_media(backend, attachment, user, content) do
    create_post_status(content, %{status: :media_uploading})

    case backend.upload_media_file(attachment, user) do
      {:ok, nil} ->
        {:ok, nil}

      {:ok, uploaded_file} ->
        media = Map.put(uploaded_file, :order, attachment.order)

        create_post_status(content, %{status: :media_uploaded, outcome: %{media: media}})

        Logger.info("Media upload successful", %{
          event: "worker.media_uploader.media_upload_success",
          media: media
        })

        {:ok, media}

      {:error, reason} ->
        error_msg = reason || "Unknown error during media upload"

        Logger.error("Media upload failed", %{
          event: "worker.media_uploader.media_upload_failed",
          error: error_msg
        })

        {:error, error_msg}
    end
  end
end
