defmodule Crosspost.Workers.FinalizePublishing do
  use Crosspost.Worker,
    queue: :publishing,
    max_attempts: 3

  alias Crosspost.Repo
  alias Crosspost.Publishing.Post
  alias Crosspost.Posts
  alias Crosspost.FeatureUsages

  args_schema do
    field :user_id, :id, required: true
    field :post_id, :string, required: true
    field :network, :enum, required: true, values: Application.compile_env(:crosspost, :networks)
    field :publish_now, :boolean, required: false, default: false
  end

  @impl true
  def process(%{args: %{post_id: post_id, network: network, publish_now: publish_now}} = job) do
    Logger.metadata(%{
      post_id: post_id,
      network: network,
      publish_now: publish_now,
      job_id: job.id
    })

    Logger.info("Starting post status update", %{event: "worker.finalize_publishing.start"})

    with {:ok, post} <- fetch_post(post_id),
         {:ok, overall_status} <- calculate_overall_status(post) do
      Logger.info("Post status updated", %{
        event: "worker.finalize_publishing.success",
        overall_status: overall_status
      })

      case update_post_status(post, overall_status, publish_now) do
        {:ok, _updated_post} -> :ok
        error -> error
      end
    else
      error ->
        Logger.error("Error updating post status", %{
          event: "worker.finalize_publishing.error",
          error: error
        })

        {:error, error}
    end
  end

  defp fetch_post(post_id) do
    case Repo.get(Post, post_id) |> Repo.preload([:post_statuses, :content]) do
      nil -> {:error, :post_not_found}
      post -> {:ok, post}
    end
  end

  defp calculate_overall_status(post) do
    latest_statuses =
      post.post_statuses
      |> Enum.filter(& &1.is_final)
      |> Enum.group_by(& &1.network)
      |> Enum.map(fn {network, statuses} ->
        latest = Enum.max_by(statuses, & &1.inserted_at)
        {network, latest}
      end)
      |> Map.new()

    # Get all networks this post should be published to
    networks = MapSet.new(post.social_networks)

    # Count statuses by type
    published_count = count_status(latest_statuses, :published)
    failed_count = count_status(latest_statuses, :failed)
    total_networks = MapSet.size(networks)
    missing_count = total_networks - map_size(latest_statuses)

    status =
      cond do
        published_count == total_networks ->
          "published"

        failed_count == total_networks ->
          "failed"

        published_count > 0 and failed_count > 0 and missing_count == 0 ->
          "partially_published"

        missing_count > 0 ->
          "pending"

        true ->
          "pending"
      end

    {:ok, status}
  end

  defp count_status(statuses, status) do
    Enum.count(statuses, fn {_network, post_status} ->
      post_status.status == status
    end)
  end

  defp update_post_status(post, new_status, publish_now) when is_binary(new_status) do
    if new_status != post.status do
      case Posts.update_status(post, new_status) do
        {:ok, updated_post} = result ->
          if new_status == "published" and not publish_now do
            post = Repo.preload(post, [:workspace, user: [:default_workspace]])
            workspace_owner = Repo.get!(Crosspost.Accounts.User, post.workspace.owner_id)

            case FeatureUsages.inc_counter(workspace_owner, "scheduled_posts") do
              {:ok, _result} ->
                :ok

              {:error, :feature_not_found} ->
                :ok
            end
          end

          broadcast("posts:#{updated_post.user_id}", {:post_updated, updated_post})

          result

        error ->
          error
      end
    else
      {:ok, post}
    end
  end
end
