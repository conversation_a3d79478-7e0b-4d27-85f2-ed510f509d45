defmodule Crosspost.Workers.CloudinaryUploadWorker do
  use Crosspost.Worker, queue: :uploads, max_attempts: 6

  alias Crosspost.Publishing
  require Logger

  @impl true
  def process(%Job{args: %{"attachment_id" => attachment_id}}) do
    attachment = Publishing.get_attachment!(attachment_id)

    Logger.metadata(attachment_id: attachment.id, type: attachment.type)

    log_info("Cloudinary upload started")

    case upload_to_cloudinary(attachment, upload_opts(attachment)) do
      {:ok, :skip} ->
        case Publishing.update_attachment(attachment, %{status: "completed"}) do
          {:ok, _} ->
            :ok

          {:error, reason} ->
            log_error("Failed to update attachment status to completed", %{
              attachment_id: attachment.id,
              reason: inspect(reason)
            })
        end

        :discard

      {:ok, uploaded_file} ->
        attrs = build_attrs(attachment, uploaded_file)

        log_info("Cloudinary upload completed", attrs)

        case Publishing.update_attachment(attachment, attrs) do
          {:ok, updated_attachment} ->
            broadcast_success(updated_attachment)
            :ok

          {:error, changeset} ->
            log_error("Failed to update attachment", %{errors: changeset.errors})
            {:error, :update_failed}
        end

      {:error, :missing_source} ->
        log_error("Missing source for attachment", %{attachment_id: attachment.id})
        :discard

      {:error, error} ->
        log_error("Upload to Cloudinary failed", %{error: error})
        {:error, :upload_failed}
    end
  end

  @impl true
  def after_process(:error, %Job{args: %{"attachment_id" => attachment_id}}, _result) do
    attachment = Publishing.get_attachment!(attachment_id)

    Publishing.update_attachment(attachment, %{status: "failed"})

    broadcast_error(attachment_id)
    :ok
  end

  def after_process(_state, _job, _result), do: :ok

  defp upload_opts(%{type: :link} = _attachment) do
    %{
      resource_type: "image",
      transformation: "w_1200,f_auto,q_auto"
    }
    |> maybe_add_upload_preset()
  end

  defp upload_opts(attachment) do
    %{
      resource_type: Atom.to_string(attachment.type),
      transformation: "f_auto,q_auto"
    }
    |> maybe_add_upload_preset()
  end

  defp upload_to_cloudinary(%{type: :link} = attachment, upload_opts) do
    case attachment.metadata do
      %{"image_url" => url} when is_binary(url) and url != "" ->
        storage().upload(url, upload_opts)

      _ ->
        {:ok, :skip}
    end
  end

  defp upload_to_cloudinary(%{blob: nil} = _attachment, _upload_opts) do
    {:error, :missing_source}
  end

  defp upload_to_cloudinary(%{blob: blob} = attachment, upload_opts) do
    file_path = Path.join(System.tmp_dir(), "#{attachment.id}")
    File.write!(file_path, blob)

    try do
      storage().upload(file_path, upload_opts)
    after
      File.rm!(file_path)
    end
  end

  defp build_attrs(%{type: :link} = attachment, uploaded_file) do
    cloudinary_metadata = cloudinary_metadata(uploaded_file)

    %{
      source_url: uploaded_file.secure_url,
      preview_url: preview_url(uploaded_file),
      status: "completed",
      blob: nil,
      metadata:
        Map.merge(attachment.metadata, %{
          "size" => uploaded_file.bytes,
          "width" => uploaded_file.width,
          "height" => uploaded_file.height,
          "image_url" => uploaded_file.secure_url,
          "cloudinary" => cloudinary_metadata
        })
    }
  end

  defp build_attrs(attachment, uploaded_file) do
    cloudinary_metadata = cloudinary_metadata(uploaded_file)

    %{
      source_url: uploaded_file.secure_url,
      preview_url: preview_url(uploaded_file),
      status: "completed",
      metadata:
        Map.merge(attachment.metadata, %{
          "size" => uploaded_file.bytes,
          "width" => uploaded_file.width,
          "height" => uploaded_file.height,
          "image_url" => uploaded_file.secure_url,
          "cloudinary" => cloudinary_metadata
        })
    }
  end

  defp cloudinary_metadata(uploaded_file) do
    %{
      "public_id" => uploaded_file.public_id,
      "version" => uploaded_file.version,
      "resource_type" => uploaded_file.resource_type,
      "size" => uploaded_file.bytes,
      "width" => uploaded_file.width,
      "height" => uploaded_file.height
    }
  end

  defp preview_url(%{resource_type: "video", secure_url: url}),
    do: String.replace(url, ~r/\.[^.]+$/, ".jpg")

  defp preview_url(_), do: nil

  defp broadcast_success(attachment) do
    message = %{
      id: attachment.id,
      filename: attachment.filename,
      content_type: attachment.content_type,
      source_url: attachment.source_url,
      preview_url: attachment.preview_url,
      status: "completed"
    }

    broadcast("attachments:#{attachment.user_id}", {:attachment_updated, message})
  end

  defp broadcast_error(attachment_id) do
    attachment = Publishing.get_attachment!(attachment_id)

    broadcast(
      "attachments:#{attachment.user_id}",
      {:attachment_error, %{id: attachment.id, message: "Failed to process uploaded file."}}
    )
  end

  defp maybe_add_upload_preset(opts) do
    case Application.get_env(:crosspost, :cloudinary_upload_preset) do
      preset when is_binary(preset) and preset != "" ->
        Map.put(opts, :upload_preset, preset)

      _ ->
        opts
    end
  end

  defp storage do
    Application.get_env(:crosspost, :storage, Crosspost.Media.Storage)
  end
end
