defmodule Crosspost.Workers.FeatureUsageCleanup do
  use Crosspost.Worker,
    queue: :users,
    max_attempts: 3,
    unique: [
      period: :infinity,
      keys: [:user_id, :usage_key],
      states: [:scheduled, :available, :executing, :retryable]
    ]

  args_schema do
    field :user_id, :id, required: true
    field :usage_key, :string, required: true
  end

  alias Crosspost.Users
  alias Crosspost.FeatureUsages

  @impl true
  def process(%Oban.Job{args: %{user_id: user_id, usage_key: usage_key}}) do
    with {:ok, user} <- get_user(user_id),
         {:ok, _usage} <- FeatureUsages.reset_usage(user, usage_key),
         {:ok, _job} <- schedule_next_cleanup(user, usage_key) do
      Logger.info("Feature usage was reset", %{
        event: "worker.feature_usage_cleanup",
        user_id: user.id,
        usage_key: usage_key
      })

      :ok
    else
      {:error, error} ->
        Logger.error("Failed to reset usage", %{
          event: "worker.feature_usage_cleanup",
          user_id: user_id,
          usage_key: usage_key,
          error: error
        })

        {:error, "Failed to reset usage"}

      {:skip, reason} ->
        Logger.warning("Skipped feature usage cleanup", %{
          event: "worker.feature_usage_cleanup",
          user_id: user_id,
          usage_key: usage_key,
          reason: reason
        })

        {:discard, reason}
    end
  end

  defp get_user(user_id) do
    case Users.get_user(user_id) do
      nil -> {:skip, "User not found"}
      user -> {:ok, user}
    end
  end

  defp schedule_next_cleanup(%{subscription: %{period_end: period_end}} = user, usage_key)
       when not is_nil(period_end) do
    %{user_id: user.id, usage_key: usage_key}
    |> new(scheduled_at: period_end)
    |> Oban.insert()
  end

  defp schedule_next_cleanup(%{subscription: %{period_end: nil}}, _usage_key),
    do: {:skip, "User has no subscription"}

  defp schedule_next_cleanup(_user, _usage_key), do: {:skip, "User has no subscription"}
end
