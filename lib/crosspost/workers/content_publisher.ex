defmodule Crosspost.Workers.ContentPublisher do
  use Crosspost.Worker,
    queue: :publishing,
    unique: [period: :infinity, states: [:available, :scheduled, :executing]],
    max_attempts: 3,
    recorded: true

  require Logger

  alias Crosspost.{Repo, Publishing, Accounts, Posts}
  alias Crosspost.Publishing.{PostContent}
  alias Oban.Pro.Workflow

  import Publishing, only: [backend_module: 1]

  args_schema do
    field :user_id, :id, required: true
    field :post_id, :string, required: true
    field :content_id, :string, required: true
    field :network, :enum, required: true, values: Application.compile_env(:crosspost, :networks)
  end

  @impl true
  def backoff(%Job{attempt: _attempt} = _job) do
    1
  end

  @impl true
  def process(
        %{args: %{post_id: post_id, content_id: content_id, user_id: user_id, network: network}} =
          job
      ) do
    Logger.metadata(%{
      post_id: post_id,
      content_id: content_id,
      user_id: user_id,
      network: network,
      job_id: job.id,
      job_attempt: job.attempt,
      job_max_attempts: job.max_attempts
    })

    Logger.info("Publishing content", %{event: "worker.content_publisher.start"})

    with {:ok, post} <- fetch_post(post_id),
         {:ok, _status} <- create_publishing_status(post, content_id, network),
         {:ok, content} <- fetch_content(content_id),
         {:ok, user} <- fetch_connected_user(user_id, post.workspace_id) do
      case do_publish_content(content, user, job, network) do
        {:ok, response} = success ->
          {:ok, _} =
            Publishing.create_post_status(post_id, network, %{
              status: :published,
              outcome: response,
              content_id: content_id,
              is_final: true
            })

          success

        {:error, reason} ->
          Logger.error("Failed to publish content",
            event: "worker.content_publisher.publish_error",
            workspace_id: post.workspace_id,
            error: inspect(reason)
          )

          details =
            case reason do
              %{message: _message} -> reason
              reason when is_binary(reason) -> %{message: reason}
              reason -> %{message: "Unexpected error", result: inspect(reason)}
            end

          {:ok, _} =
            Publishing.create_post_status(
              post_id,
              network,
              %{
                status: :failed,
                outcome: %{error: details},
                content_id: content_id,
                is_final: true
              }
            )

          {:error, details}
      end
    else
      {:error, reason} = error ->
        is_final = job.attempt == job.max_attempts

        Logger.error("Failed to publish content",
          event: "worker.content_publisher.publish_error",
          error: inspect(reason)
        )

        {:ok, _} =
          Publishing.create_post_status(post_id, network, %{
            status: :failed,
            outcome: %{error: %{message: "Failed to publish content", result: inspect(reason)}},
            is_final: is_final
          })

        error
    end
  end

  defp fetch_post(post_id) do
    case Posts.get_post!(post_id) do
      nil -> {:error, :post_not_found}
      post -> {:ok, post}
    end
  end

  defp create_publishing_status(post, content_id, network) do
    Publishing.create_post_status(post.id, network, %{
      status: :publishing,
      content_id: content_id,
      outcome: %{},
      is_final: false
    })
  end

  defp fetch_connected_user(user_id, workspace_id) do
    try do
      case Accounts.get_connected_user!(user_id, workspace_id) do
        nil -> {:error, :user_not_found}
        user -> {:ok, user}
      end
    rescue
      Ecto.NoResultsError -> {:error, :user_not_found}
    end
  end

  defp fetch_content(content_id) do
    case Repo.get(PostContent, content_id)
         |> Repo.preload([:attachments, post: [content: :attachments]]) do
      nil -> {:error, :content_not_found}
      content -> {:ok, content}
    end
  end

  defp do_publish_content(content, user, job, network) do
    # Get previous content's outcome if this isn't the first piece
    previous =
      if content.order > 0 do
        parent_name = job_name(content.post_id, network, content.order - 1)
        Workflow.get_recorded(job, parent_name)
      else
        %{}
      end

    # Get media for this specific network
    # For other networks, use content-specific job names
    media =
      content.attachments
      |> Enum.map(fn attachment ->
        parent_name = "media_#{content.id}_#{network}_#{attachment.id}"
        media = Workflow.get_recorded(job, parent_name)

        Logger.info("Found media for network", %{
          event: "worker.content_publisher.media_found",
          order: content.order,
          parent_name: parent_name,
          media: media,
          attachment_id: attachment.id
        })

        media
      end)
      |> Enum.reject(&is_nil(&1))
      |> Enum.sort_by(& &1.order)

    Logger.info("Publishing content", %{
      event: "worker.content_publisher.publishing_content",
      order: content.order,
      text: content.text,
      media: media
    })

    connection = Accounts.get_connection(network, user.workspace_id)

    opts = [
      previous: previous,
      media: media,
      connection: connection
    ]

    case backend_module(network).publish_content(content, user, opts) do
      {:ok, result} = success ->
        Logger.info("Published content successfully", %{
          event: "worker.content_publisher.publish_success",
          order: content.order,
          result: result
        })

        success

      error ->
        Logger.error("Failed to publish content", %{
          event: "worker.content_publisher.publish_error",
          order: content.order,
          error: error
        })

        error
    end
  end

  @doc """
  Generates a standardized job name for a content publishing job.
  """
  def job_name(post_id, network, order) do
    "content_#{post_id}_#{network}_#{order}"
  end
end
