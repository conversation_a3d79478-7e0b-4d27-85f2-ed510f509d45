defmodule Crosspost.Workers.TestUploadWorker do
  use Oban.Pro.Worker, queue: :uploads

  require Logger

  import Crosspost, only: [broadcast: 2]

  alias Crosspost.Publishing

  @test_files_dir "priv/static/uploads/test"

  @impl true
  def process(%Job{args: %{"attachment_id" => attachment_id}}) do
    attachment = Publishing.get_attachment!(attachment_id)

    case attachment.type do
      :link ->
        attrs = %{
          status: "completed"
        }

        case Publishing.update_attachment(attachment, attrs) do
          {:ok, updated_attachment} ->
            broadcast_success(updated_attachment)
            :ok

          {:error, changeset} ->
            Logger.error("Failed to update attachment: #{inspect(changeset.errors)}")
            {:error, :update_failed}
        end

      _ ->
        filename = attachment.filename
        dest_path = Path.join(@test_files_dir, filename)

        File.mkdir_p!(@test_files_dir)
        File.write!(dest_path, attachment.blob)

        test_url = "/uploads/test/#{filename}"

        preview_url =
          if attachment.content_type && String.starts_with?(attachment.content_type, "video/") do
            "/images/logo.png"
          else
            test_url
          end

        attrs = %{
          source_url: test_url,
          preview_url: preview_url,
          status: "completed",
          metadata:
            attachment.metadata
            |> Map.put("test", %{
              "filename" => filename,
              "resource_type" =>
                if(String.starts_with?(attachment.content_type || "", "video/"),
                  do: "video",
                  else: "image"
                )
            })
        }

        case Publishing.update_attachment(attachment, attrs) do
          {:ok, updated_attachment} ->
            broadcast_success(updated_attachment)
            :ok

          {:error, changeset} ->
            Logger.error("Failed to update attachment: #{inspect(changeset.errors)}")
            {:error, :update_failed}
        end
    end
  end

  @impl true
  def after_process(:error, %Job{args: %{"attachment_id" => attachment_id}}, _result) do
    attachment = Publishing.get_attachment!(attachment_id)

    Publishing.update_attachment(attachment, %{status: "failed"})

    broadcast_error(attachment_id)
    :ok
  end

  def after_process(_state, _job, _result), do: :ok

  defp broadcast_success(attachment) do
    message = %{
      id: attachment.id,
      filename: attachment.filename,
      content_type: attachment.content_type,
      source_url: attachment.source_url,
      preview_url: attachment.preview_url,
      status: "completed"
    }

    broadcast("attachments:#{attachment.user_id}", {:attachment_updated, message})
  end

  defp broadcast_error(attachment_id) do
    attachment = Publishing.get_attachment!(attachment_id)

    broadcast(
      "attachments:#{attachment.user_id}",
      {:attachment_error, %{id: attachment.id, message: "Failed to process uploaded file."}}
    )
  end
end
