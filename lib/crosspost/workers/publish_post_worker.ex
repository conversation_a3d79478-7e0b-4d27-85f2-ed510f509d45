defmodule Crosspost.Workers.PublishPostWorker do
  use Oban.Pro.Worker,
    queue: :publishing,
    max_attempts: 3,
    unique: [
      period: :infinity,
      keys: [:post_id, :network],
      states: [:scheduled, :available, :executing, :retryable]
    ]

  require Logger

  alias Crosspost.Publishing

  args_schema do
    field :user_id, :id, required: true
    field :post_id, :string, required: true
    field :network, :enum, required: true, values: Application.compile_env(:crosspost, :networks)
    field :publish_now, :boolean, required: false, default: false
  end

  @impl Oban.Pro.Worker
  def process(
        %Oban.Job{
          args: %{user_id: user_id, post_id: post_id, network: network, publish_now: publish_now}
        } = job
      ) do
    Logger.metadata(%{
      user_id: user_id,
      post_id: post_id,
      network: network,
      publish_now: publish_now,
      job_id: job.id
    })

    Logger.info("Starting post publication for network", %{
      event: "worker.publish_post_worker.started"
    })

    case Publishing.get_post!(post_id) do
      %{status: "published"} = _post ->
        Logger.info("Post already fully published", %{
          event: "worker.publish_post_worker.post_already_published"
        })

        {:ok, :skipped}

      post ->
        already_published =
          Enum.any?(post.post_statuses, fn status ->
            status.network == network && status.status == :published && status.is_final
          end)

        if already_published do
          Logger.info("Network already published", %{
            event: "worker.publish_post_worker.network_already_published"
          })

          :ok
        else
          case Publishing.schedule_workflow(post, network, publish_now: publish_now) do
            {:ok, _workflow} ->
              Logger.info("Successfully scheduled workflow for network", %{
                event: "worker.publish_post_worker.workflow_scheduled"
              })

              :ok

            {:error, error} ->
              Logger.error("Failed to schedule workflow for network", %{
                event: "worker.publish_post_worker.network_workflow_scheduling_failed",
                error: inspect(error)
              })

              {:error, "Failed to schedule workflow for #{network}"}
          end
        end
    end
  end
end
