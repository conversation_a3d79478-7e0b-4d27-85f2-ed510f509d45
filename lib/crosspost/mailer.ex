defmodule Crosspost.Mailer do
  use Swoosh.Mailer, otp_app: :crosspost

  import Swoosh.Email

  @admin_email Application.compile_env(:crosspost, :admin_email)

  def deliver_account_deletion_feedback(user_email, feedback) do
    new()
    |> to(@admin_email)
    |> from({"Crosspost App", "<EMAIL>"})
    |> subject("Account Deletion Feedback")
    |> text_body("""
    User with email #{user_email} has deleted their account.

    Feedback:
    #{feedback}
    """)
    |> deliver()
  end
end
