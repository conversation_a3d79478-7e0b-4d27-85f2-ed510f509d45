defmodule Crosspost.Billing.Stub do
  @behaviour Crosspost.Billing.Provider

  @impl true
  def get_session(_session_id) do
    subscription = %{
      id: "sub_mock123",
      status: "active",
      created: System.system_time(:second),
      current_period_end: System.system_time(:second) + 30 * 24 * 60 * 60,
      items: %{
        data: [
          %{
            price: %{
              product: "prod_mock123",
              id: "price_mock123"
            }
          }
        ]
      }
    }

    {:ok, %{customer: "cus_mock123", subscription: subscription}}
  end

  @impl true
  def get_product(product_id) do
    {:ok,
     %{
       id: product_id,
       name: "Mock Product",
       metadata: %{
         "features" => "crossposting,workspaces_basic"
       }
     }}
  end

  @impl true
  def get_features(_product_id) do
    {:ok,
     %{
       data: [
         %{
           entitlement_feature: %{
             lookup_key: "crossposting"
           }
         },
         %{
           entitlement_feature: %{
             lookup_key: "workspaces_basic"
           }
         },
         %{
           entitlement_feature: %{
             lookup_key: "scheduled_posts_s"
           }
         }
       ]
     }}
  end

  @impl true
  def update_subscription(_subscription_id, price_id) do
    subscription = %{
      id: "sub_mock123",
      status: "active",
      created: System.system_time(:second),
      current_period_end: System.system_time(:second) + 30 * 24 * 60 * 60,
      items: %{
        data: [
          %{
            price: %{
              product: "prod_mock123",
              id: price_id
            }
          }
        ]
      }
    }

    {:ok, subscription}
  end

  @impl true
  def cancel_subscription(_subscription_id) do
    subscription = %{
      id: "sub_mock123",
      status: "canceled",
      created: System.system_time(:second),
      current_period_end: System.system_time(:second),
      items: %{
        data: [
          %{
            price: %{
              product: "prod_mock123",
              id: "price_mock123"
            }
          }
        ]
      }
    }

    {:ok, subscription}
  end

  @impl true
  def billing_portal_url(_customer_id, _return_url) do
    {:ok, "https://billing.stripe.com/p/login/test_14k8w86666666666"}
  end
end
