defmodule Crosspost.Billing.Stripe do
  @behaviour Crosspost.Billing.Provider

  defmodule Features do
    use Drops.Contract

    schema(atomize: true) do
      %{
        required(:data) =>
          list(%{
            required(:entitlement_feature) => %{
              required(:lookup_key) => string(:filled?)
            }
          })
      }
    end
  end

  @impl true
  def get_session(session_id) do
    Stripe.Checkout.Session.retrieve(session_id, expand: ["subscription"])
  end

  @impl true
  def get_product(product_id) do
    Stripe.Product.retrieve(product_id)
  end

  @impl true
  def get_features(product_id) do
    {:ok, data} = Stripe.API.request(%{}, :get, "/v1/products/#{product_id}/features", %{}, [])

    Features.conform(data)
  end

  @impl true
  def update_subscription(subscription_id, price_id) do
    Stripe.Subscription.update(subscription_id, %{
      proration_behavior: "always_invoice",
      items: [
        %{
          id: get_subscription_item_id(subscription_id),
          price: price_id
        }
      ]
    })
  end

  @impl true
  def cancel_subscription(subscription_id) do
    Stripe.Subscription.cancel(subscription_id)
  end

  defp get_subscription_item_id(subscription_id) do
    case Stripe.Subscription.retrieve(subscription_id, expand: ["items"]) do
      {:ok, subscription} ->
        subscription.items.data
        |> List.first()
        |> Map.get(:id)

      {:error, error} ->
        {:error, error}
    end
  end

  def create_billing_portal_session(customer_id, return_url) do
    Stripe.BillingPortal.Session.create(%{
      customer: customer_id,
      return_url: return_url
    })
  end

  @impl true
  def billing_portal_url(customer_id, return_url) do
    case Stripe.BillingPortal.Session.create(%{
           customer: customer_id,
           return_url: return_url
         }) do
      {:ok, %{url: url}} -> {:ok, url}
      {:error, reason} -> {:error, reason}
    end
  end
end
