defmodule Crosspost.Billing.Provider do
  @type subscription_item :: %{
          price: %{
            id: String.t(),
            product: String.t()
          }
        }

  @type subscription :: %{
          id: String.t(),
          status: String.t(),
          created: integer(),
          current_period_end: integer(),
          items: %{
            data: [subscription_item()]
          }
        }

  @type session :: %{
          customer: String.t(),
          subscription: subscription()
        }

  @type product :: %{
          id: String.t(),
          name: String.t(),
          metadata: map()
        }

  @type feature :: %{
          required(:entitlement_feature) => %{
            required(:lookup_key) => String.t()
          }
        }

  @type features_response :: %{
          required(:data) => [feature()]
        }

  @callback get_session(session_id :: String.t()) ::
              {:ok, session()} | {:error, any()}

  @callback get_product(product_id :: String.t()) ::
              {:ok, product()} | {:error, any()}

  @callback get_features(product_id :: String.t()) ::
              {:ok, features_response()} | {:error, any()}

  @callback update_subscription(subscription_id :: String.t(), price_id :: String.t()) ::
              {:ok, subscription()} | {:error, any()}

  @callback cancel_subscription(subscription_id :: String.t()) ::
              {:ok, subscription()} | {:error, any()}

  @callback billing_portal_url(customer_id :: String.t(), return_url :: String.t()) ::
              {:ok, String.t()} | {:error, any()}
end
