defmodule Crosspost.ErrorFormatter do
  def format_changeset_errors(%Ecto.Changeset{} = changeset, _prefix \\ "") do
    changeset
    |> format_changeset_errors_recursive()
    |> Enum.join("\n")
  end

  defp format_changeset_errors_recursive(%Ecto.Changeset{} = changeset, prefix \\ "") do
    errors =
      Ecto.Changeset.traverse_errors(changeset, fn
        {msg, opts} when is_binary(msg) ->
          Enum.reduce(opts, msg, fn {key, value}, acc ->
            String.replace(acc, "%{#{key}}", to_string(value))
          end)

        {msg, _opts} when is_map(msg) ->
          msg
          |> Map.values()
          |> List.flatten()
          |> Enum.map(&format_error_value/1)
          |> Enum.join(", ")
      end)
      |> Enum.map(fn {field, errors} ->
        formatted_errors = format_error_value(errors)
        "#{prefix}#{field}: #{formatted_errors}"
      end)

    # Handle nested changesets in changes
    nested_errors =
      changeset.changes
      |> Enum.flat_map(fn
        {field, changesets} when is_list(changesets) ->
          changesets
          |> Enum.with_index()
          |> Enum.flat_map(fn {nested_changeset, index} ->
            format_changeset_errors_recursive(nested_changeset, "#{prefix}  #{field}[#{index}].")
          end)

        {field, %Ecto.Changeset{} = nested_changeset} ->
          format_changeset_errors_recursive(nested_changeset, "#{prefix}  #{field}.")

        _ ->
          []
      end)

    errors ++ nested_errors
  end

  # Helper to format error values that might be maps, lists, or strings
  defp format_error_value(value) when is_map(value) do
    value
    |> Map.values()
    |> List.flatten()
    |> Enum.map(&format_error_value/1)
    |> Enum.join(", ")
  end

  defp format_error_value(value) when is_list(value) do
    value
    |> List.flatten()
    |> Enum.map(&format_error_value/1)
    |> Enum.join(", ")
  end

  defp format_error_value(value) when is_binary(value), do: value
  defp format_error_value(value), do: inspect(value)
end
