defmodule Crosspost.Websites do
  @moduledoc """
  Context for fetching and parsing website information.
  """

  require Logger

  @facebook_user_agent "facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)"
  @fallback_user_agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

  @doc """
  Fetches a website's content from the given URL.
  Returns {:ok, html_content} or {:error, reason}.

  First attempts to fetch with Facebook user agent to ensure we get OpenGraph meta tags.
  If that fails with a non-200 status code, falls back to a standard browser user agent.
  """
  def fetch_website(url) when is_binary(url) do
    opts = Application.get_env(:crosspost, :website_req_options, [])
    # Add max_redirects to follow redirects
    opts = Keyword.put(opts, :max_redirects, 5)

    # First try with Facebook user agent
    facebook_opts = Keyword.put(opts, :headers, [{"user-agent", @facebook_user_agent}])

    Logger.debug("Fetching website with Facebook user agent",
      url: url,
      event: "fetch_website.fb_ua"
    )

    case Req.get(url, facebook_opts) do
      {:ok, %{status: 200, body: html}} ->
        {:ok, html}

      {:ok, %{status: status}} ->
        # If we get a non-200 status, try with fallback user agent
        Logger.debug(
          "Facebook user agent request failed with status #{status}, trying fallback user agent",
          url: url,
          status: status,
          event: "fetch_website.fallback_ua"
        )

        fallback_opts = Keyword.put(opts, :headers, [{"user-agent", @fallback_user_agent}])

        case Req.get(url, fallback_opts) do
          {:ok, %{status: 200, body: html}} ->
            Logger.info("Successfully fetched website with fallback user agent",
              url: url,
              event: "fetch_website.fallback_success"
            )

            {:ok, html}

          {:ok, %{status: fallback_status}} ->
            {:error, "Failed to fetch website with both user agents: HTTP #{fallback_status}"}

          {:error, fallback_reason} ->
            {:error,
             "Failed to fetch website with fallback user agent: #{inspect(fallback_reason)}"}
        end

      {:error, reason} ->
        # If the first request errors out completely, try with fallback user agent
        Logger.debug("Facebook user agent request failed with error, trying fallback user agent",
          url: url,
          error: inspect(reason),
          event: "fetch_website.fallback_ua"
        )

        fallback_opts = Keyword.put(opts, :headers, [{"user-agent", @fallback_user_agent}])

        case Req.get(url, fallback_opts) do
          {:ok, %{status: 200, body: html}} ->
            Logger.info("Successfully fetched website with fallback user agent",
              url: url,
              event: "fetch_website.fallback_success"
            )

            {:ok, html}

          {:ok, %{status: fallback_status}} ->
            {:error, "Failed to fetch website with both user agents: HTTP #{fallback_status}"}

          {:error, fallback_reason} ->
            {:error, "Failed to fetch website with both user agents: #{inspect(fallback_reason)}"}
        end
    end
  end

  @doc """
  Extracts OpenGraph information from a website using opengraph_parser.
  Returns {:ok, map} with url, title, description, and image_url,
  or {:error, reason}.
  """
  def og_info(url) when is_binary(url) do
    Logger.info("Fetching OpenGraph data", url: url, event: "og_info.start")

    with {:ok, html} <- fetch_website(url) do
      try do
        # Use opengraph_parser to extract OpenGraph metadata
        og_data = OpenGraph.parse(html)

        # Extract metadata
        title = extract_title(og_data, html)
        description = extract_description(og_data, html)
        image_url = extract_image(og_data, url)

        # Log results
        Logger.info("Successfully parsed OpenGraph data",
          event: "og_info.success",
          url: url,
          has_title: title != nil,
          has_description: description != "",
          has_image: image_url != nil,
          image_url: image_url
        )

        # Log warning if no image found
        if image_url == nil do
          Logger.warning("No image URL found in OpenGraph data",
            event: "og_info.no_image",
            url: url
          )
        end

        {:ok,
         %{
           url: url,
           title: title,
           description: description,
           image_url: image_url
         }}
      rescue
        error ->
          Logger.error("Failed to parse website with opengraph_parser",
            event: "og_info.parser_error",
            url: url,
            error: inspect(error),
            stacktrace: Exception.format_stacktrace(__STACKTRACE__)
          )

          {:error, "Failed to parse website"}
      end
    else
      {:error, reason} ->
        Logger.error("Failed to fetch website",
          event: "og_info.fetch_error",
          url: url,
          error: reason
        )

        {:error, "Failed to parse website"}
    end
  end

  # Helper function to extract title with fallbacks
  defp extract_title(%OpenGraph{title: title}, _html) when is_binary(title) and title != "",
    do: title

  defp extract_title(_og_data, html) do
    # Log fallback to HTML title
    Logger.debug("Falling back to HTML title", event: "og_info.title_fallback")

    case Floki.parse_document(html) do
      {:ok, document} ->
        Floki.find(document, "title")
        |> Floki.text()
        |> String.slice(0, 300)
        |> case do
          "" -> nil
          title -> title
        end

      _ ->
        nil
    end
  end

  # Helper function to extract description with fallbacks
  defp extract_description(%OpenGraph{description: desc}, _html)
       when is_binary(desc) and desc != "",
       do: desc

  defp extract_description(_og_data, html) do
    # Log fallback to HTML description
    Logger.debug("Falling back to HTML description", event: "og_info.description_fallback")

    case Floki.parse_document(html) do
      {:ok, document} ->
        Floki.find(document, "meta[name='description']")
        |> get_meta_content()
        |> case do
          nil -> ""
          desc -> String.slice(desc, 0, 300)
        end

      _ ->
        ""
    end
  end

  # Helper function to extract image URL and handle relative URLs
  defp extract_image(%OpenGraph{image: image}, base_url) when is_binary(image) and image != "" do
    # Log image URL before conversion
    Logger.debug("Found image URL in OpenGraph data",
      event: "og_info.image_found",
      original_image_url: image
    )

    result = maybe_convert_to_absolute_url(image, base_url)

    # Log image URL after conversion
    Logger.debug("Converted image URL",
      event: "og_info.image_converted",
      original_url: image,
      converted_url: result
    )

    result
  end

  defp extract_image(_og_data, base_url) do
    Logger.debug("No image found in OpenGraph data",
      event: "og_info.no_image_data",
      base_url: base_url
    )

    nil
  end

  defp get_meta_content(elements) do
    case elements do
      [{_tag, attrs, _children} | _] ->
        Enum.find_value(attrs, fn
          {"content", value} -> value
          _ -> nil
        end)

      _ ->
        nil
    end
  end

  # URL conversion helpers
  defp maybe_convert_to_absolute_url(nil, _base_url), do: nil
  defp maybe_convert_to_absolute_url("", _base_url), do: nil
  defp maybe_convert_to_absolute_url("http" <> _rest = url, _base_url), do: url

  defp maybe_convert_to_absolute_url("//" <> rest, base_url) do
    Logger.debug("Converting protocol-relative URL",
      event: "og_info.convert_protocol_relative",
      original_url: "//" <> rest,
      base_url: base_url
    )

    URI.parse(base_url).scheme <> ":" <> "//" <> rest
  end

  defp maybe_convert_to_absolute_url("/" <> _rest = path, base_url) do
    Logger.debug("Converting absolute path URL",
      event: "og_info.convert_absolute_path",
      original_url: path,
      base_url: base_url
    )

    base_uri = URI.parse(base_url)
    "#{base_uri.scheme}://#{base_uri.host}#{path}"
  end

  defp maybe_convert_to_absolute_url(relative_path, base_url) do
    Logger.debug("Converting relative URL",
      event: "og_info.convert_relative",
      original_url: relative_path,
      base_url: base_url
    )

    base_uri = URI.parse(base_url)
    base_path = Path.dirname(base_uri.path || "/")
    "#{base_uri.scheme}://#{base_uri.host}#{base_path}/#{relative_path}"
  end
end
