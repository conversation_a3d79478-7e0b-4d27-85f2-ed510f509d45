defmodule Crosspost.Payments do
  @moduledoc """
  The Payments context handles all payment-related operations using Stripe.
  """
  require Logger

  def create_checkout_session(price_id) do
    params = %{
      payment_method_types: ["card"],
      line_items: [
        %{
          price: price_id,
          quantity: 1
        }
      ],
      mode: "subscription",
      success_url:
        "#{CrosspostWeb.Endpoint.url()}/payment/success?session_id={CHECKOUT_SESSION_ID}",
      cancel_url: "#{CrosspostWeb.Endpoint.url()}/pricing"
    }

    Logger.info("Creating Stripe checkout session with params: #{inspect(params)}")

    case Stripe.Checkout.Session.create(params) do
      {:ok, session} ->
        Logger.info("Successfully created checkout session: #{inspect(session, pretty: true)}")
        {:ok, session}

      {:error, error} ->
        Logger.error("Failed to create checkout session: #{inspect(error)}")
        {:error, error}
    end
  end

  def get_customer_subscription(customer_id) do
    Logger.info("Fetching subscription for customer: #{customer_id}")

    case Stripe.Subscription.list(%{customer: customer_id, status: "active", limit: 1}) do
      {:ok, %{data: [subscription | _]} = result} ->
        Logger.info("Found active subscription: #{inspect(result, pretty: true)}")
        {:ok, subscription}

      {:ok, %{data: []}} ->
        Logger.info("No active subscription found for customer: #{customer_id}")
        {:error, :no_subscription}

      error ->
        Logger.error("Error fetching subscription: #{inspect(error)}")
        error
    end
  end

  def retrieve_session(session_id) do
    Logger.info("Retrieving checkout session: #{session_id}")

    case Stripe.Checkout.Session.retrieve(session_id, expand: ["subscription"]) do
      {:ok, session} ->
        Logger.info("Retrieved session: #{inspect(session, pretty: true)}")
        {:ok, session}

      {:error, error} ->
        Logger.error("Failed to retrieve session: #{inspect(error)}")
        {:error, error}
    end
  end

  def create_customer(email) do
    Stripe.Customer.create(%{email: email})
  end

  def attach_payment_method(customer_id, payment_method_id) do
    Stripe.PaymentMethod.attach(%{
      customer: customer_id,
      payment_method: payment_method_id
    })
  end

  def get_product_features(product_id) do
    Logger.info("Fetching product features for: #{product_id}")

    case Stripe.Product.retrieve(product_id) do
      {:ok, product} ->
        Logger.info("Retrieved product: #{inspect(product, pretty: true)}")

        # Access struct fields directly
        features = product.metadata["features"] || ""
        max_posts = product.metadata["max_posts_per_month"]

        parsed_features =
          features
          |> String.split(",")
          |> Enum.map(&String.trim/1)

        {:ok,
         %{
           features: parsed_features,
           max_posts: max_posts
         }}

      {:error, error} ->
        Logger.error("Failed to fetch product: #{inspect(error)}")
        {:error, error}
    end
  end
end
