defmodule Crosspost.Logging do
  require Logger

  def log_info(event, payload \\ %{}) do
    log(:info, event, payload)
  end

  def log_warning(event, payload \\ %{}) do
    log(:warning, event, payload)
  end

  def log_error(event, payload \\ %{}) do
    log(:error, event, payload)
  end

  def log_debug(event, payload \\ %{}) do
    log(:debug, event, payload)
  end

  def log(level, event, payload) do
    Logger.log(level, Map.merge(%{event: event}, payload))
  end
end
