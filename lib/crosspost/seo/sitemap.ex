defmodule Crosspost.SEO.Sitemap do
  @moduledoc """
  Handles sitemap generation for the application.
  """

  require Logger

  @base_url "https://justcrosspost.app"

  @static_paths [
    "/",
    "/about",
    "/tos",
    "/contact",
    "/privacy",
    "/plans"
  ]

  def generate do
    target_dir =
      case Application.get_env(:crosspost, :env) do
        :prod -> Application.app_dir(:crosspost, "priv/static")
        _ -> Path.join(File.cwd!(), "priv/static")
      end

    config = [
      store: Sitemapper.FileStore,
      store_config: [path: target_dir],
      sitemap_url: @base_url
    ]

    Logger.info("Generating sitemap at #{Path.join(target_dir, "sitemap.xml")}")

    static_urls()
    |> Sitemapper.generate(config)
    |> Sitemapper.persist(config)
    |> Stream.run()
  end

  defp static_urls do
    @static_paths
    |> Stream.map(fn path ->
      %Sitemapper.URL{
        loc: "#{@base_url}#{path}",
        changefreq: get_changefreq(path),
        priority: get_priority(path),
        lastmod: Date.utc_today()
      }
    end)
  end

  defp get_changefreq("/"), do: :daily
  defp get_changefreq("/plans"), do: :weekly
  defp get_changefreq(_), do: :monthly

  defp get_priority("/"), do: 1.0
  defp get_priority("/plans"), do: 0.9
  defp get_priority(_), do: 0.8
end
