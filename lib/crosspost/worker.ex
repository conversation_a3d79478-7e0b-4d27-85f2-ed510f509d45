defmodule Crosspost.Worker do
  defmacro __using__(opts) do
    quote do
      use Oban.Pro.Worker, unquote(opts)

      require Logger

      import Crosspost.Logging
      import Crosspost, only: [broadcast: 2]

      alias Crosspost.Users

      @impl true
      def before_process(job) do
        with %{"user_id" => user_id} <- job.args do
          user = Users.get_user(user_id)
          Crosspost.set_user_context(user)
        end

        {:ok, job}
      end
    end
  end
end
