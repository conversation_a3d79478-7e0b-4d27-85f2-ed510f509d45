defmodule Crosspost.Security.FormProtection do
  @moduledoc false

  # Minimum time in seconds that should pass between form render and submission
  @min_submission_time 2
  # Maximum time in seconds after which form submission is considered expired
  @max_submission_time 3600

  def generate_form_token do
    Phoenix.Token.sign(CrosspostWeb.Endpoint, "form_token", %{
      timestamp: System.system_time(:second)
    })
  end

  def verify_form_token(token) do
    case Phoenix.Token.verify(CrosspostWeb.Endpoint, "form_token", token, max_age: @max_submission_time) do
      {:ok, %{timestamp: timestamp}} ->
        current_time = System.system_time(:second)
        time_diff = current_time - timestamp

        cond do
          time_diff < @min_submission_time -> {:error, :too_fast}
          time_diff > @max_submission_time -> {:error, :expired}
          true -> :ok
        end

      {:error, _reason} ->
        {:error, :invalid}
    end
  end

  def valid_honeypot?(params) do
    # Check if honeypot field is empty (as it should be)
    Map.get(params, "website", "") == ""
  end
end
