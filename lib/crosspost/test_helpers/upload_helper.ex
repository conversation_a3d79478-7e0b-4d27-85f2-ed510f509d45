defmodule Crosspost.TestHelpers.UploadHelper do
  @test_files_dir "priv/static/uploads/test"

  def setup do
    # Ensure test uploads directory exists
    File.mkdir_p!(@test_files_dir)
  end

  def cleanup do
    # Clean up test files after tests
    File.rm_rf!(@test_files_dir)
  end

  def copy_test_image(filename \\ "test-image.jpg") do
    source = Path.join("test/support/fixtures", filename)
    dest = Path.join(@test_files_dir, filename)
    File.cp!(source, dest)
    "/uploads/test/#{filename}"
  end
end
