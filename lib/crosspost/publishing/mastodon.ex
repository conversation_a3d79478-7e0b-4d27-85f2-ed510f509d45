defmodule Crosspost.Publishing.Mastodon do
  use Crosspost.Publishing.SocialNetwork

  use Crosspost.Media.CloudinaryHelper,
    image_size_limit: 1_000_000,
    transformations: "w_2000,c_limit,q_auto,f_auto"

  require Logger

  @max_retries Application.compile_env(:crosspost, :mastodon_max_retries, 30)
  @retry_delay Application.compile_env(:crosspost, :mastodon_retry_delay, 2000)

  @impl true
  def publish_content(content, user, opts) do
    parent_id = get_in(opts[:previous] || %{}, ["id"])
    media = Keyword.get(opts, :media, [])

    payload = %{status: content.text, visibility: "public"}

    media_ids = Enum.map(media, fn item -> item[:id] end)

    payload = if parent_id, do: Map.put(payload, :in_reply_to_id, parent_id), else: payload
    payload = if media_ids != [], do: Map.put(payload, :media_ids, media_ids), else: payload

    post_status(payload, user)
  end

  @impl true
  def upload_media_file(%{type: type} = attachment, user) when type in [:image, :video] do
    url = "#{ensure_https(user.mastodon_instance)}/api/v2/media"
    headers = [{"Authorization", "Bearer #{user.encrypted_mastodon_token}"}]
    source_url = transform_cloudinary_url(attachment.source_url, attachment.metadata)

    Logger.debug("Processing media upload", %{
      event: "publishing.mastodon.media_upload_start",
      original_url: attachment.source_url,
      transformed_url: source_url,
      user_id: user.id,
      attachment_id: attachment.id
    })

    with {:ok, binary_data} <- media_helper().download_file(source_url),
         {:ok, media_id} <- do_upload_media(url, headers, binary_data, attachment),
         {:ok, media_id} <- wait_for_media_processing(media_id, user) do
      {:ok, %{type: attachment.type, alt: attachment.metadata["alt"], id: media_id}}
    end
  end

  def upload_media_file(%{type: :link}, _user) do
    {:ok, nil}
  end

  defp wait_for_media_processing(media_id, user, retry_count \\ 0, attachment \\ nil) do
    if retry_count >= @max_retries do
      Logger.error("Media processing timeout", %{
        event: "publishing.mastodon.media_processing_timeout",
        media_id: media_id,
        max_retries: @max_retries,
        user_id: user.id
      })

      {:error, "Media processing timeout"}
    else
      url = "#{ensure_https(user.mastodon_instance)}/api/v1/media/#{media_id}"
      headers = [{"Authorization", "Bearer #{user.encrypted_mastodon_token}"}]
      opts = [headers: headers] |> add_test_options()

      case Req.get(url, opts) do
        {:ok, %Req.Response{status: 200, body: %{"url" => url}}} when not is_nil(url) ->
          Logger.debug("Media processing complete", %{
            event: "publishing.mastodon.media_processing_complete",
            media_id: media_id,
            user_id: user.id,
            attachment_id: attachment && attachment.id
          })

          {:ok, media_id}

        {:ok, %Req.Response{status: 206}} ->
          Logger.debug("Media still processing", %{
            event: "publishing.mastodon.media_processing",
            media_id: media_id,
            attempt: retry_count + 1,
            user_id: user.id,
            attachment_id: attachment && attachment.id
          })

          Process.sleep(@retry_delay)
          wait_for_media_processing(media_id, user, retry_count + 1, attachment)

        {:ok, %Req.Response{status: status, body: body}} ->
          Logger.error("Error checking media status", %{
            event: "publishing.mastodon.media_status_check_failed",
            media_id: media_id,
            status: status,
            response: body
          })

          {:error, "Failed to check media status: HTTP #{status}"}

        {:error, reason} ->
          Logger.error("Error checking media status", %{
            event: "publishing.mastodon.media_status_check_error",
            media_id: media_id,
            reason: reason
          })

          {:error, "Failed to check media status"}
      end
    end
  end

  defp post_status({:error, reason}, _user), do: {:error, reason}

  defp post_status(payload, user) do
    url = "#{ensure_https(user.mastodon_instance)}/api/v1/statuses"

    headers = [
      {"Authorization", "Bearer #{user.encrypted_mastodon_token}"},
      {"Content-Type", "application/json"}
    ]

    opts =
      [
        headers: headers,
        json: payload
      ]
      |> add_test_options()

    case Req.post(url, opts) do
      {:ok, %Req.Response{status: status, body: body}} when status in [200, 201] ->
        {:ok, body}

      {:ok, %Req.Response{status: status, body: body}} ->
        {:error, "Failed to post to Mastodon: HTTP #{status} #{inspect(body)}"}

      {:error, %Req.TransportError{reason: :nxdomain}} ->
        {:error,
         "Unable to connect to Mastodon instance. Please check the instance URL and your network connection."}

      {:error, %Req.TransportError{reason: _reason}} ->
        {:error,
         "Network error when connecting to Mastodon. Please check your internet connection."}

      {:error, _reason} ->
        {:error, "An unexpected error occurred while posting to Mastodon."}
    end
  end

  defp ensure_https(url) do
    case String.split(url, "://") do
      [protocol, rest] when protocol in ["http", "https"] -> "https://#{rest}"
      [domain] -> "https://#{domain}"
      _ -> "https://#{url}"
    end
  end

  defp media_helper do
    Application.get_env(:crosspost, :media_helper, Crosspost.Publishing.MediaHelper)
  end

  defp add_test_options(opts) do
    Keyword.merge(opts, Application.get_env(:crosspost, :mastodon_req_options) || [])
  end

  # Move existing media upload logic into private functions
  defp do_upload_media(url, headers, binary_data, attachment) do
    form_multipart = [
      file:
        {binary_data,
         filename: attachment.filename, content_type: MIME.from_path(attachment.filename)}
    ]

    # Add description (alt text) if present
    form_multipart =
      if alt = get_in(attachment.metadata, ["alt"]) do
        [{:description, alt} | form_multipart]
      else
        form_multipart
      end

    Logger.debug("Uploading media to Mastodon", %{
      event: "publishing.mastodon.media_upload_start",
      instance: url,
      filename: attachment.filename
    })

    opts = [headers: headers, form_multipart: form_multipart] |> add_test_options()

    case Req.post(url, opts) do
      {:ok, %Req.Response{status: status, body: resp_body}} when status in [200, 201, 202] ->
        media_id = resp_body["id"]
        {:ok, media_id}

      {:ok, %Req.Response{status: status}} ->
        {:error, "Failed to upload media to Mastodon: HTTP #{status}"}

      {:error, %Req.TransportError{reason: reason}} ->
        {:error, "Network error when uploading media to Mastodon: #{reason}"}

      {:error, reason} ->
        {:error, "Unexpected error uploading media to Mastodon: #{inspect(reason)}"}
    end
  end
end
