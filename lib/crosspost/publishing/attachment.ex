defmodule Crosspost.Publishing.Attachment do
  use Ecto.Schema

  @type t :: %__MODULE__{}

  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}

  alias Crosspost.Publishing.PostContentAttachment
  alias Crosspost.Accounts.User

  @derive {Jason.Encoder,
           only: [
             :id,
             :filename,
             :content_type,
             :source_url,
             :preview_url,
             :metadata,
             :status,
             :type
           ]}

  @types [:image, :video, :link]

  schema "attachments" do
    field :filename, :string
    field :content_type, :string
    field :source_url, :string
    field :preview_url, :string
    field :metadata, :map, default: %{}
    field :status, :string, default: "pending"
    field :type, Ecto.Enum, values: @types
    field :order, :integer, virtual: true
    field :blob, :binary

    belongs_to :user, User, type: :id
    belongs_to :workspace, Crosspost.Accounts.Workspace

    has_many :post_content_attachments, PostContentAttachment, on_delete: :delete_all

    has_many :post_contents,
      through: [:post_content_attachments, :post_content],
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(attachment, attrs) do
    attachment
    |> cast(attrs, [
      :id,
      :filename,
      :content_type,
      :user_id,
      :workspace_id,
      :source_url,
      :preview_url,
      :metadata,
      :status,
      :type,
      :blob
    ])
    |> validate_required([
      :filename,
      :content_type,
      :user_id,
      :workspace_id
    ])
    |> validate_inclusion(:status, ["pending", "completed", "failed"])
    |> maybe_infer_type()
    |> validate_required([:type])
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:workspace_id)
  end

  defp maybe_infer_type(changeset) do
    if is_nil(get_field(changeset, :type)) do
      put_change(changeset, :type, determine_attachment_type(get_field(changeset, :content_type)))
    else
      changeset
    end
  end

  defp determine_attachment_type(nil), do: nil

  defp determine_attachment_type(content_type) do
    cond do
      String.starts_with?(content_type, "image/") -> :image
      String.starts_with?(content_type, "video/") -> :video
      content_type == "application/json" -> :link
      true -> raise "Unsupported attachment type: #{content_type}"
    end
  end
end
