defmodule Crosspost.Publishing.PostContent do
  use Ecto.Schema
  import Ecto.Changeset
  alias Crosspost.Publishing.{Attachment, Post, PostContentAttachment, ContentAttachment}

  @derive {Jason.Encoder, only: [:id, :text, :order, :meta, :network, :attachments]}

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @foreign_key_type :binary_id

  @networks [:bsky, :mastodon, :linkedin, :x, :canonical]

  schema "post_content" do
    field :text, :string
    field :order, :integer, default: 0
    field :meta, :map, default: %{}
    field :network, Ecto.Enum, values: @networks, default: :canonical
    field :sync, :boolean, default: false

    belongs_to :post, Post

    has_one :user, through: [:post, :user]
    has_one :workspace, through: [:post, :workspace]

    has_many :statuses, through: [:post, :post_statuses]

    has_many :content_attachments, ContentAttachment,
      foreign_key: :content_id,
      references: :id

    has_many :post_content_attachments, PostContentAttachment,
      on_delete: :delete_all,
      foreign_key: :post_content_id,
      on_replace: :delete

    many_to_many :attachments, Attachment,
      join_through: PostContentAttachment,
      on_replace: :delete,
      on_delete: :delete_all

    many_to_many :images, Attachment,
      join_through: PostContentAttachment,
      where: [type: :image]

    many_to_many :videos, Attachment,
      join_through: PostContentAttachment,
      where: [type: :video]

    many_to_many :links, Attachment,
      join_through: PostContentAttachment,
      where: [type: :link]

    many_to_many :media, Attachment,
      join_through: PostContentAttachment,
      where: [type: {:in, [:image, :video]}]

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(post_content, attrs) do
    post_content
    |> cast(attrs, [:id, :text, :order, :meta, :network, :sync, :post_id])
    |> validate_required([:order, :network])
    |> validate_text_based_on_network(attrs)
    |> maybe_put_attachments(attrs)
    |> foreign_key_constraint(:post_id)
  end

  defp maybe_put_attachments(changeset, attrs) do
    if Map.has_key?(attrs, :attachments) do
      attachments =
        Enum.with_index(attrs[:attachments])
        |> Enum.map(fn {attachment, index} ->
          case attachment do
            %Attachment{} ->
              %{
                post_content_id: Map.get(attrs, :id),
                attachment_id: attachment.id,
                order: index
              }

            %{attachment_id: _, order: _} = pca_attrs ->
              pca_attrs

            other ->
              raise "Invalid attachment: #{inspect(other)}"
          end
        end)

      put_assoc(changeset, :post_content_attachments, attachments)
    else
      changeset
    end
  end

  defp validate_text_based_on_network(changeset, _attrs) do
    network = get_field(changeset, :network)

    case network do
      :canonical ->
        changeset

      _ ->
        changeset
        |> validate_required([:text], message: "can't be blank for network-specific content")
        |> validate_change(:text, fn :text, text ->
          if text == "" do
            [text: "can't be blank for network-specific content"]
          else
            []
          end
        end)
    end
  end
end
