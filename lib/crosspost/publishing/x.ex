defmodule Crosspost.Publishing.X do
  use Crosspost.Publishing.SocialNetwork

  @image_size_limit 5_000_000

  use Crosspost.Media.CloudinaryHelper,
    image_size_limit: @image_size_limit,
    transformations: "w_2000,c_limit,q_auto,f_auto"

  require Logger

  @impl true
  def refresh_connection(connection) do
    # If token is not expired yet, return :ok
    if DateTime.compare(connection.expires_at, DateTime.utc_now()) == :gt do
      :ok
    else
      case oauth_module().refresh_token(connection.encrypted_refresh_token) do
        {:ok, {_access_token, _refresh_token, _expires_at}} = success ->
          success

        result ->
          result
      end
    end
  end

  defp oauth_module do
    Application.get_env(:crosspost, :x_oauth_module, Ueberauth.Strategy.X.OAuth)
  end

  @impl true
  def publish_content(content, user, opts) do
    Logger.metadata(%{
      network: "x",
      user_id: user.id,
      content_id: content.id
    })

    tweet_payload = create_tweet_payload(content, opts)

    case post_tweet(tweet_payload, user) do
      {:ok, response} ->
        previous = Keyword.get(opts, :previous, %{})

        result = %{
          "parent" => response["data"]["id"],
          "root" =>
            if(map_size(previous) == 0, do: response["data"]["id"], else: previous["root"])
        }

        {:ok, result}

      error ->
        error
    end
  end

  @impl true
  def upload_media_file(%{type: type} = attachment, user) when type in [:image, :video] do
    metadata = Map.get(attachment, :metadata, %{})
    source_url = transform_cloudinary_url(attachment.source_url, metadata)

    Logger.debug("Processing media upload", %{
      event: "publishing.x.media_upload_start",
      original_url: attachment.source_url,
      transformed_url: source_url,
      user_id: user.id,
      attachment_id: attachment.id
    })

    case get_binary_data(source_url, nil, metadata) do
      {:ok, binary_data} ->
        media_category = determine_media_category(attachment.filename)

        case media_category do
          category when category in ["tweet_video", "tweet_gif"] ->
            case chunked_upload_to_twitter(binary_data, media_category, attachment.filename, user) do
              {:ok, media_id} ->
                {:ok, %{type: type, id: media_id}}

              error ->
                error
            end

          _other ->
            case upload_single_media_to_twitter(attachment, user, binary_data) do
              {:ok, media_id} ->
                {:ok, %{type: type, id: media_id}}

              error ->
                error
            end
        end

      {:error, reason} ->
        Logger.error("Error downloading media", %{
          network: "x",
          event: "x.media_download_error",
          error: reason
        })

        {:error, reason}
    end
  end

  def upload_media_file(%{type: :link}, _user) do
    {:ok, nil}
  end

  defp create_tweet_payload(content, opts) do
    payload = %{"text" => content.text}

    media_ids =
      opts[:media]
      |> Enum.reject(&is_nil(&1))
      |> Enum.filter(fn
        %{type: type, id: id} when type in [:image, :video] and is_binary(id) and id != "" -> true
        _ -> false
      end)
      |> Enum.map(& &1.id)

    # Log detailed media information for debugging
    all_media = opts[:media] || []
    invalid_media = Enum.filter(all_media, fn
      %{type: type, id: id} when type in [:image, :video] -> not (is_binary(id) and id != "")
      _ -> false
    end)

    Logger.debug("Creating tweet payload", %{
      event: "publishing.x.create_tweet_payload",
      total_media_count: length(all_media),
      valid_media_ids_count: length(media_ids),
      invalid_media_count: length(invalid_media),
      media_ids: media_ids,
      invalid_media: invalid_media
    })

    payload =
      case media_ids do
        [] -> payload
        ids -> Map.put(payload, "media", %{"media_ids" => ids})
      end

    case opts[:previous] do
      %{"root" => root_id, "parent" => parent_id}
      when is_binary(root_id) and is_binary(parent_id) ->
        Map.put(payload, "reply", %{"in_reply_to_tweet_id" => parent_id})

      _ ->
        payload
    end
  end

  defp upload_single_media_to_twitter(attachment, user, binary_data) do
    url = "https://api.x.com/2/media/upload"
    media_category = determine_media_category(attachment.filename)

    params = [
      media: {binary_data, filename: attachment.filename},
      media_category: media_category
    ]

    headers = [
      {"Authorization", "Bearer #{user.encrypted_x_token}"}
    ]

    opts =
      [
        url: url,
        headers: headers,
        form_multipart: params
      ]
      |> add_test_options()

    request = Req.new(opts)

    case Req.post(request) do
      {:ok, %Req.Response{status: status, body: body}} when status in [200, 201] ->
        json = if is_binary(body), do: Jason.decode!(body), else: body

        media_id = get_media_id_from_response(json)

        if media_id && is_binary(media_id) && media_id != "" do
          Logger.info("Successfully uploaded media", %{
            event: "publishing.x.media_upload_complete",
            media_id: media_id,
            attachment_id: attachment.id,
            filename: attachment.filename,
            user_id: user.id,
            status: status
          })

          {:ok, media_id}
        else
          Logger.error("Invalid media ID in response", %{
            event: "publishing.x.media_upload_invalid_id",
            response: json,
            attachment_id: attachment.id,
            user_id: user.id,
            status: status
          })

          {:error, "Invalid media ID received from X API"}
        end

      {_, %Req.Response{status: status, body: resp_body}} ->
        Logger.error("Failed to upload media", %{
          event: "publishing.x.media_upload_failed",
          status: status,
          user_id: user.id,
          attachment_id: attachment.id,
          response: resp_body
        })

        {:error, "Failed to upload media to X"}
    end
  end

  defp get_binary_data(_source_url, binary_data, _metadata) when is_binary(binary_data),
    do: {:ok, binary_data}

  defp get_binary_data(source_url, nil, metadata) do
    case media_helper().download_file(source_url) do
      {:ok, data} = result ->
        size_bytes = byte_size(data)
        size_kb = Float.round(size_bytes / 1024, 2)
        size_mb = Float.round(size_bytes / 1_048_576, 2)

        Logger.info("Downloaded original image", %{
          event: "publishing.x.image_download.original",
          size_bytes: size_bytes,
          size_kb: size_kb,
          size_mb: size_mb,
          limit_bytes: @image_size_limit,
          limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
          over_limit_percent: Float.round(size_bytes / @image_size_limit * 100, 2)
        })

        if size_bytes > @image_size_limit do
          # Try with progressively more aggressive quality and size settings
          try_optimized_versions(source_url, metadata, [
            # First try: q_80 with width limit
            "w_1600,c_limit,q_80,f_auto",
            # Second try: q_70 with more width limit
            "w_1200,c_limit,q_70,f_auto",
            # Third try: q_60 with more width limit
            "w_1000,c_limit,q_60,f_auto",
            # Fourth try: q_50 with more width limit
            "w_800,c_limit,q_50,f_auto",
            # Fifth try: q_40 with more width limit
            "w_600,c_limit,q_40,f_auto",
            # Sixth try: q_30 with more width limit
            "w_500,c_limit,q_30,f_auto",
            # Seventh try: extreme compression
            "w_400,c_limit,q_20,f_auto",
            # Last resort: most extreme settings
            "w_300,c_limit,q_10,f_auto"
          ])
        else
          result
        end

      error ->
        error
    end
  end

  # Helper function to try multiple optimization settings
  defp try_optimized_versions(_source_url, _metadata, []),
    do: {:error, "Could not reduce image size enough"}

  defp try_optimized_versions(source_url, metadata, [transformation | remaining_transformations]) do
    # Remove any existing transformations from the URL before adding new ones
    base_url = String.replace(source_url, ~r{/w_\d+,c_limit,q_[^/]+/}, "/")
    optimized_url = String.replace(base_url, "/upload/", "/upload/#{transformation}/")

    Logger.info("Trying optimized image", %{
      event: "publishing.x.image_optimization",
      transformation: transformation,
      original_url: source_url,
      optimized_url: optimized_url
    })

    case media_helper().download_file(optimized_url) do
      {:ok, optimized_data} = optimized_result ->
        size_bytes = byte_size(optimized_data)
        size_kb = Float.round(size_bytes / 1024, 2)
        size_mb = Float.round(size_bytes / 1_048_576, 2)

        Logger.info("Downloaded optimized image", %{
          event: "publishing.x.image_optimization.size",
          transformation: transformation,
          original_url: source_url,
          optimized_url: optimized_url,
          size_bytes: size_bytes,
          size_kb: size_kb,
          size_mb: size_mb,
          limit_bytes: @image_size_limit,
          limit_mb: Float.round(@image_size_limit / 1_048_576, 2)
        })

        if size_bytes > @image_size_limit do
          # Still too large, try next transformation
          Logger.debug("Image still too large, trying next transformation", %{
            event: "publishing.x.image_optimization.too_large",
            transformation: transformation,
            size_bytes: size_bytes,
            size_kb: size_kb,
            size_mb: size_mb,
            limit_bytes: @image_size_limit,
            limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
            remaining_transformations: length(remaining_transformations)
          })

          try_optimized_versions(source_url, metadata, remaining_transformations)
        else
          # Success - image is now small enough
          Logger.info("Successfully optimized image", %{
            event: "publishing.x.image_optimization.success",
            transformation: transformation,
            original_url: source_url,
            optimized_url: optimized_url,
            size_bytes: size_bytes,
            size_kb: size_kb,
            size_mb: size_mb,
            limit_bytes: @image_size_limit,
            limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
            percent_of_limit: Float.round(size_bytes / @image_size_limit * 100, 2)
          })

          optimized_result
        end

      error ->
        # If download fails, try next transformation
        Logger.warning("Failed to download optimized image", %{
          event: "publishing.x.image_optimization.download_failed",
          transformation: transformation,
          original_url: source_url,
          optimized_url: optimized_url,
          error: inspect(error)
        })

        try_optimized_versions(source_url, metadata, remaining_transformations)
    end
  end

  defp chunked_upload_to_twitter(binary_data, media_category, filename, user) do
    total_bytes = byte_size(binary_data)
    chunk_size = 1 * 1024 * 1024

    with {:ok, media_id} <- init_chunked_upload(total_bytes, media_category, filename, user),
         :ok <- upload_chunks(binary_data, media_id, chunk_size, user),
         :ok <- finalize_upload(media_id, user),
         :ok <- wait_for_processing(media_id, user) do
      {:ok, media_id}
    else
      error ->
        error
    end
  end

  defp init_chunked_upload(total_bytes, media_category, filename, user) do
    url = "https://api.x.com/2/media/upload"

    params = %{
      "command" => "INIT",
      "total_bytes" => total_bytes,
      "media_type" => get_mime_type(filename),
      "media_category" => media_category
    }

    headers = [
      {"Authorization", "Bearer #{user.encrypted_x_token}"}
    ]

    opts =
      [
        url: url,
        headers: headers,
        form: params
      ]
      |> add_test_options()

    request = Req.new(opts)

    case Req.post(request) do
      {:ok, %Req.Response{status: status, body: body}} when status in [200, 201, 202] ->
        Logger.debug("Response body: #{inspect(body)}")

        media_id = get_media_id_from_response(body)

        if media_id && is_binary(media_id) && media_id != "" do
          Logger.info("Successfully initiated chunked upload", %{
            event: "publishing.x.media_upload_init_complete",
            media_id: media_id,
            user_id: user.id,
            status: status
          })

          {:ok, media_id}
        else
          Logger.error("Error initiating chunked upload. Missing or invalid media id in response", %{
            event: "publishing.x.media_upload_init_failed",
            error: inspect(body),
            status: status,
            user_id: user.id
          })

          {:error, "Error initiating chunked upload. Missing or invalid media id in response"}
        end

      {_, %Req.Response{status: status, body: resp_body}} ->
        Logger.error("Error initiating chunked upload", %{
          event: "publishing.x.media_upload_init_failed",
          response: inspect(resp_body),
          status: status,
          user_id: user.id
        })

        {:error, "Error initiating chunked upload"}
    end
  end

  defp upload_chunks(binary_data, media_id, chunk_size, user) do
    binary_data
    |> Stream.unfold(fn
      <<chunk::binary-size(chunk_size), rest::binary>> -> {chunk, rest}
      <<>> -> nil
      rest -> {rest, <<>>}
    end)
    |> Stream.with_index()
    |> Enum.each(fn {chunk, segment_index} ->
      upload_chunk(chunk, media_id, segment_index, user)
    end)
  end

  defp upload_chunk(chunk, media_id, segment_index, user) do
    url = "https://api.x.com/2/media/upload"

    params = [
      command: "APPEND",
      media_id: media_id,
      segment_index: segment_index,
      media: {chunk, filename: "chunk_#{segment_index}.bin"}
    ]

    headers = [{"Authorization", "Bearer #{user.encrypted_x_token}"}]

    opts =
      [
        url: url,
        headers: headers,
        form_multipart: params
      ]
      |> add_test_options()

    request = Req.new(opts)

    case Req.post(request) do
      {:ok, %Req.Response{status: status}} = response when status >= 200 and status < 300 ->
        Logger.info("Successfully uploaded chunk", %{
          event: "publishing.x.media_upload_chunk_complete",
          media_id: media_id,
          segment_index: segment_index,
          response: inspect(response)
        })

        :ok

      {_, %Req.Response{status: status}} ->
        Logger.error("Error uploading chunk", %{
          event: "publishing.x.media_upload_chunk_failed",
          media_id: media_id,
          segment_index: segment_index,
          status: status
        })

        {:error, "Error uploading chunk to X"}
    end
  end

  defp finalize_upload(media_id, user) do
    url = "https://api.x.com/2/media/upload"

    params = [
      command: "FINALIZE",
      media_id: media_id
    ]

    headers = [
      {"Authorization", "Bearer #{user.encrypted_x_token}"}
    ]

    opts =
      [
        url: url,
        headers: headers,
        form_multipart: params
      ]
      |> add_test_options()

    request = Req.new(opts)

    case Req.post(request) do
      # {
      #   "data": {
      #       "id": "1880028106020515840",
      #       "media_key": "13_1880028106020515840",
      #       "size": 1024,
      #       "expires_after_secs": 86400,
      #       "processing_info": {
      #           "state": "pending",
      #           "check_after_secs": 1
      #       }
      #   }
      # }
      {:ok, %Req.Response{status: status}} when status in [200, 201] ->
        Logger.info("Successfully finalized upload", %{
          event: "publishing.x.media_upload_finalize_complete",
          media_id: media_id,
          user_id: user.id,
          status: status
        })

        :ok

      {_, %Req.Response{status: status, body: resp_body}} ->
        Logger.error("Failed to finalize upload", %{
          event: "publishing.x.media_upload_finalize_failed",
          media_id: media_id,
          user_id: user.id,
          status: status,
          response: inspect(resp_body)
        })

        {:error, "Failed to finalize upload"}
    end
  end

  defp wait_for_processing(media_id, user, attempts \\ 0) do
    if attempts >= 20 do
      raise "Video processing timeout"
    end

    url = "https://api.x.com/2/media/upload"

    params = [
      command: "STATUS",
      media_id: media_id
    ]

    headers = [
      {"Authorization", "Bearer #{user.encrypted_x_token}"}
    ]

    opts =
      [
        url: url,
        headers: headers,
        params: params
      ]
      |> add_test_options()

    request = Req.new(opts)

    case Req.get(request) do
      {:ok, %Req.Response{status: 200, body: json}} ->
        case get_in(json["data"], ["processing_info", "state"]) do
          "succeeded" ->
            :ok

          "failed" ->
            {:error, "Media processing failed"}

          state when state in ["pending", "in_progress"] ->
            :timer.sleep(1000)
            wait_for_processing(media_id, user, attempts + 1)
        end

      {_, %Req.Response{status: status, body: resp_body}} ->
        Logger.error("Unexpected response checking media status", %{
          event: "publishing.x.media_upload_status_check_failed",
          status: status,
          response: inspect(resp_body)
        })

        {:error, "Failed to check media status"}
    end
  end

  defp get_mime_type(filename) do
    extension = Path.extname(filename) |> String.downcase()

    case extension do
      ".mp4" -> "video/mp4"
      ".mov" -> "video/quicktime"
      ".gif" -> "image/gif"
      ".jpg" -> "image/jpeg"
      ".jpeg" -> "image/jpeg"
      ".png" -> "image/png"
      _ -> "application/octet-stream"
    end
  end

  defp determine_media_category(filename) do
    extension = Path.extname(filename) |> String.downcase()

    case extension do
      ".gif" -> "tweet_gif"
      ".mp4" -> "tweet_video"
      ".mov" -> "tweet_video"
      _ -> "tweet_image"
    end
  end

  defp post_tweet(payload, user) do
    headers = [
      {"Authorization", "Bearer #{user.encrypted_x_token}"},
      {"Content-Type", "application/json"}
    ]

    url = "https://api.x.com/2/tweets"

    opts =
      [
        headers: headers,
        body: Jason.encode!(payload)
      ]
      |> add_test_options()

    case Req.post(url, opts) do
      {:ok, %Req.Response{status: status, body: body}} when status in [200, 201] ->
        {:ok, body}

      {:ok,
       %Req.Response{
         status: 403,
         body:
           %{"detail" => "You are not allowed to create a Tweet with duplicate content."} = body
       }} ->
        {:error, body}

      {:ok, %Req.Response{status: 429, body: %{"detail" => "Too Many Requests"}}} ->
        {:error, "Daily limit exceeded on X API"}

      {_, %Req.Response{status: status, body: response_body}} ->
        Logger.error("Failed to post to X", %{
          event: "publishing.x.post_tweet_failed",
          status: status,
          response: inspect(response_body)
        })

        {:error, "Failed to post to X"}
    end
  end

  defp add_test_options(opts) do
    Keyword.merge(opts, Application.get_env(:crosspost, :x_req_options) || [])
  end

  defp media_helper do
    Application.get_env(:crosspost, :media_helper, Crosspost.Publishing.MediaHelper)
  end

  # Helper function to extract media ID from X API response
  # The X API v2 response format can vary, so we need to handle different structures
  defp get_media_id_from_response(response) do
    cond do
      # X API v2 format: {"data": {"id": "..."}}
      is_map(response) && Map.has_key?(response, "data") && is_map(response["data"]) ->
        response["data"]["id"]

      # Legacy format: {"id": "..."}
      is_map(response) && Map.has_key?(response, "id") ->
        response["id"]

      # Fallback
      true ->
        nil
    end
  end
end
