defmodule Crosspost.Publishing.Content.Diff do
  @moduledoc """
  Represents differences between canonical and network-specific content.
  """

  defstruct [
    :network,
    # Current canonical content
    canonical: [],
    # Current network content
    network_content: [],
    # Original canonical content
    original_canonical: [],
    # Original network content
    original_network: [],
    # Map of order -> new text for changed canonical items
    canonical_changes: %{},
    # Map of order -> new text for changed network items
    network_changes: %{},
    # Map of order -> [attachment_ids] for changed attachments
    attachment_changes: %{}
  ]

  @type t :: %__MODULE__{
          network: String.t(),
          canonical: list(map()),
          network_content: list(map()),
          original_canonical: list(map()),
          original_network: list(map()),
          canonical_changes: %{integer() => String.t()},
          network_changes: %{integer() => String.t()},
          attachment_changes: %{integer() => list(String.t())}
        }

  @doc """
  Creates a new diff struct by comparing original and new content for a network.
  """
  def new(content, original_content, network) do
    # Get current content
    canonical = content_for(content, "canonical")
    network_content = content_for(content, network)

    # Get original content
    original_canonical = content_for(original_content, "canonical")
    original_network = content_for(original_content, network)

    # Calculate changes
    canonical_changes = detect_text_changes(canonical, original_canonical)
    network_changes = detect_text_changes(network_content, original_network)
    attachment_changes = detect_attachment_changes(canonical, original_canonical)

    %__MODULE__{
      network: network,
      canonical: canonical,
      network_content: network_content,
      original_canonical: original_canonical,
      original_network: original_network,
      canonical_changes: canonical_changes,
      network_changes: network_changes,
      attachment_changes: attachment_changes
    }
  end

  @doc """
  Returns true if the item at the given order has network-specific changes.
  """
  def network_changed?(%__MODULE__{} = diff, order) do
    Map.has_key?(diff.network_changes, order)
  end

  @doc """
  Returns true if the canonical item at the given order has changed.
  """
  def canonical_changed?(%__MODULE__{} = diff, order) do
    Map.has_key?(diff.canonical_changes, order) || Map.has_key?(diff.attachment_changes, order)
  end

  @doc """
  Returns the text and attachments that should be used for a given network item.
  Takes into account both canonical and network-specific changes.
  """
  def content_for_item(%__MODULE__{} = diff, network_item) do
    text =
      cond do
        # Network-specific change takes precedence for text
        network_changed?(diff, network_item.order) ->
          network_item.text

        # Apply canonical change if it exists
        new_text = Map.get(diff.canonical_changes, network_item.order) ->
          new_text

        # No changes, keep original
        true ->
          network_item.text
      end

    attachments =
      cond do
        # Network-specific attachments take precedence if text was customized
        network_changed?(diff, network_item.order) ->
          Map.get(network_item, :attachments, [])

        # Apply canonical attachment changes if they exist
        _new_attachments = Map.get(diff.attachment_changes, network_item.order) ->
          # Find the canonical content item to get its attachments
          canonical_item = Enum.find(diff.canonical, &(&1.order == network_item.order))
          Map.get(canonical_item, :attachments, [])

        # No changes, keep original
        true ->
          Map.get(network_item, :attachments, [])
      end

    Map.merge(network_item, %{text: text, attachments: attachments})
  end

  defp content_for(content, network) do
    Enum.filter(content, &(&1.network == to_string(network)))
  end

  defp detect_text_changes(new_content, original_content) do
    new_content
    |> Enum.filter(fn new_item ->
      case Enum.find(original_content, &(&1.order == new_item.order)) do
        # New content
        nil -> true
        # Changed content
        old_item -> old_item.text != new_item.text
      end
    end)
    |> Enum.map(&{&1.order, &1.text})
    |> Map.new()
  end

  defp detect_attachment_changes(new_content, original_content) do
    new_content
    |> Enum.filter(fn new_item ->
      case Enum.find(original_content, &(&1.order == new_item.order)) do
        # New content
        nil ->
          true

        old_item ->
          # Compare attachment IDs, handling nil attachments
          new_ids = (Map.get(new_item, :attachments) || []) |> Enum.map(& &1.id) |> Enum.sort()
          old_ids = (Map.get(old_item, :attachments) || []) |> Enum.sort()
          new_ids != old_ids
      end
    end)
    |> Enum.map(&{&1.order, Map.get(&1, :attachments) || []})
    |> Map.new()
  end

  defimpl Inspect do
    def inspect(diff, opts) do
      # Create a more readable inspection format
      changes = %{
        network: diff.network,
        canonical_changes: diff.canonical_changes,
        network_changes: diff.network_changes,
        attachment_changes: diff.attachment_changes
      }

      Inspect.Map.inspect(changes, opts)
    end
  end
end
