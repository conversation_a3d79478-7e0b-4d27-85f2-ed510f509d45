defmodule Crosspost.Publishing.MediaHelper do
  @behaviour __MODULE__

  @callback download_file(String.t()) :: {:ok, binary()} | {:error, String.t()}

  require Logger

  alias Crosspost.Publishing

  @impl true
  def download_file(url) when is_binary(url) and url != "" do
    cond do
      # Match local attachment URLs - using UUID pattern
      String.match?(
        url,
        ~r|^/api/attachments/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|i
      ) ->
        handle_local_attachment(url)

      # Match local uploads from test/dev uploader
      String.match?(url, ~r|^/uploads/|) ->
        handle_local_upload(url)

      # Handle external URLs (must start with http:// or https://)
      String.match?(url, ~r|^https?://|) ->
        handle_external_url(url)

      # Any other URL format is invalid
      true ->
        {:error, "Invalid URL format: #{inspect(url)}"}
    end
  end

  def download_file(_), do: {:error, "Invalid URL"}

  defp handle_local_attachment(url) do
    with id when not is_nil(id) <- extract_attachment_id(url),
         %{blob: blob} when not is_nil(blob) <- Publishing.get_attachment(id) do
      {:ok, blob}
    else
      nil -> {:error, "Attachment not found"}
      _ -> {:error, "Invalid attachment URL"}
    end
  end

  defp handle_local_upload(url) do
    # Get the absolute path to priv directory
    priv_dir = :code.priv_dir(:crosspost)

    # Construct the full path
    full_path = Path.join(priv_dir, "static#{url}")

    case File.read(full_path) do
      {:ok, content} -> {:ok, content}
      {:error, reason} -> {:error, "Failed to read local file: #{inspect(reason)}"}
    end
  end

  defp handle_external_url(url) do
    req_options = Application.get_env(:crosspost, :media_helper_req_options, [])
    req_options = Keyword.put(req_options, :decode_body, false)

    case Req.get(url, req_options) do
      {:ok, %{status: 200, body: body}} ->
        {:ok, body}

      {:ok, %{status: status}} ->
        {:error, "HTTP #{status}"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp extract_attachment_id(url) do
    case Regex.run(
           ~r|^/api/attachments/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$|i,
           url
         ) do
      [_, id] -> id
      _ -> nil
    end
  end
end
