defmodule Crosspost.Publishing.PostContentAttachment do
  use Ecto.Schema
  import Ecto.Changeset

  @foreign_key_type Ecto.UUID
  @primary_key false

  schema "post_content_attachments" do
    field :order, :integer, default: 0

    belongs_to :post_content, Crosspost.Publishing.PostContent,
      type: Ecto.UUID,
      primary_key: true

    belongs_to :attachment, Crosspost.Publishing.Attachment,
      type: Ecto.UUID,
      primary_key: true

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(post_content_attachment, attrs) do
    post_content_attachment
    |> cast(attrs, [:post_content_id, :attachment_id, :order])
    |> validate_required([:post_content_id, :attachment_id])
    |> validate_number(:order, greater_than_or_equal_to: 0)
    |> unique_constraint([:post_content_id, :order],
      name: :post_content_attachments_post_content_id_order_index,
      message: "attachment order must be unique within a post content"
    )
  end
end
