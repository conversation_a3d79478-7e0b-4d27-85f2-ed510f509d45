defmodule Crosspost.Publishing.Bsky do
  use Crosspost.Publishing.SocialNetwork

  @image_size_limit 1_000_000

  use Crosspost.Media.CloudinaryHelper,
    image_size_limit: @image_size_limit,
    transformations: "w_2000,c_limit,q_auto,f_auto"

  require Logger

  alias TextParser.Tokens.{Mention, URL, Tag}

  @bsky_client Application.compile_env(:crosspost, :bsky_client, Crosspost.Accounts.Bsky.Client)
  @accounts Application.compile_env(:crosspost, :accounts, Crosspost.Accounts)

  def accounts, do: @accounts
  def bsky_client, do: @bsky_client

  @impl true
  def refresh_connection(connection) do
    bsky_client().refresh_session(connection.encrypted_refresh_token)
  end

  @impl true
  def publish_content(content, user, opts) do
    previous = Keyword.get(opts, :previous)
    media = Keyword.get(opts, :media, [])

    # For content after first one, we must have previous with parent/root info
    if content.order > 0 and (is_nil(previous) or map_size(previous) == 0) do
      raise "Missing previous info for content order #{content.order}"
    end

    # Build reply field for threading
    reply =
      case previous do
        %{"parent" => parent, "root" => root} when is_map(parent) and is_map(root) ->
          %{
            "parent" => %{
              "cid" => parent["cid"],
              "uri" => parent["uri"]
            },
            "root" => %{
              "cid" => root["cid"],
              "uri" => root["uri"]
            }
          }

        _ ->
          # First post or invalid previous has no reply field
          nil
      end

    case process_text(content.text, content.meta, user) do
      {:ok, {processed_text, facets}, updated_user} ->
        do_create_post(processed_text, updated_user, reply, media, facets, previous || %{})

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp do_create_post(text, user, reply, media, facets, previous) do
    Logger.metadata(user_id: user.id, network: "bsky")

    Logger.info("Attempting to create post", %{
      event: "publishing.bsky.do_create_post.attempt",
      media_count: length(media)
    })

    case @bsky_client.create_post(
           user.encrypted_bsky_token,
           user.bsky_username,
           text,
           reply,
           media,
           facets
         ) do
      {:ok, response} ->
        # For first post, it becomes both parent and root
        # For subsequent posts, use response as parent but keep original root
        result = %{
          "parent" => response,
          "root" => if(map_size(previous) == 0, do: response, else: previous["root"])
        }

        Logger.info("Post created successfully", %{
          event: "publishing.bsky.post_created",
          result: result
        })

        {:ok, result}

      {:error, reason} ->
        Logger.error("Failed to create post", %{
          event: "publishing.bsky.post_creation_failed",
          reason: reason
        })

        {:error, "Failed to create post"}
    end
  end

  @impl true
  def upload_media_file(%{type: :link} = attachment, user) do
    image_url = attachment.metadata["image_url"]
    content_type = "image/#{List.last(String.split(attachment.metadata["image_url"], "."))}"

    Logger.metadata(user_id: user.id, network: "bsky")

    Logger.info("Starting link image upload", %{
      event: "publishing.bsky.upload_media_file.start",
      attachment_id: attachment.id,
      image_url: image_url
    })

    with {:ok, data} <- get_binary_data(image_url, nil, attachment.metadata),
         {:ok, blob} <- do_upload_blob(data, content_type, user) do
      link = %{
        type: :link,
        url: attachment.metadata["url"],
        title: attachment.metadata["title"],
        description: attachment.metadata["description"],
        thumb: blob
      }

      Logger.debug("Uploaded link image", %{
        event: "publishing.bsky.upload_media_file.success",
        link: inspect(link),
        attachment_id: attachment.id
      })

      {:ok, link}
    else
      {:error, reason} ->
        Logger.error("Failed to upload link image", %{
          event: "publishing.bsky.upload_media_file.upload_failed",
          reason: reason,
          user_id: user.id,
          attachment_id: attachment.id
        })

        {:error, reason}
    end
  end

  @impl true
  def upload_media_file(%{type: :video} = attachment, user) do
    Logger.metadata(user_id: user.id, network: "bsky")

    Logger.info("Starting video upload", %{
      event: "publishing.bsky.upload_media_file.start",
      attachment_id: attachment.id
    })

    with {:ok, data} <- get_binary_data(attachment.source_url, nil, attachment.metadata),
         {:ok, %{"jobId" => job_id}} <- do_upload_video(data, attachment.content_type, user),
         {:ok, blob} <- wait_for_video_processing(job_id, user) do
      medium = %{
        type: attachment.type,
        blob: blob,
        alt: attachment.metadata["alt"] || "video",
        width: attachment.metadata["width"],
        height: attachment.metadata["height"]
      }

      Logger.info("Video upload completed", %{
        event: "publishing.bsky.upload_media_file.success",
        attachment_id: attachment.id
      })

      {:ok, medium}
    else
      {:error, reason} ->
        Logger.error("Failed to upload video file", %{
          event: "publishing.bsky.upload_media_file.failed",
          reason: reason,
          attachment_id: attachment.id
        })

        {:error, reason}
    end
  end

  @impl true
  def upload_media_file(%{source_url: source_url} = attachment, user) do
    Logger.metadata(user_id: user.id, network: "bsky")

    Logger.info("Starting media upload", %{
      event: "publishing.bsky.upload_media_file.start",
      attachment_id: attachment.id
    })

    with {:ok, data} <- get_binary_data(source_url, nil, attachment.metadata),
         {:ok, blob} <- do_upload_blob(data, attachment.content_type, user) do
      medium = %{
        type: attachment.type,
        blob: blob,
        alt: attachment.metadata["alt"] || "image",
        width: attachment.metadata["width"],
        height: attachment.metadata["height"]
      }

      Logger.debug("Uploaded medium", %{
        event: "publishing.bsky.upload_media_file.success",
        medium: inspect(medium),
        attachment_id: attachment.id
      })

      {:ok, medium}
    else
      {:error, reason} ->
        Logger.error("Failed to upload media file", %{
          event: "publishing.bsky.upload_media_file.failed",
          reason: reason,
          attachment_id: attachment.id
        })

        {:error, reason}
    end
  end

  defp process_text(text, meta, user) when is_binary(text) do
    parsed_text = TextParser.parse(text)

    # Process mentions first since they might need to update the user struct
    with {:ok, mention_facets, updated_user} <-
           process_mentions(TextParser.get(parsed_text, Mention), meta, user) do
      link_facets = process_links_from_text(parsed_text)
      tag_facets = process_tags_from_text(parsed_text)

      facets =
        (tag_facets ++ link_facets ++ mention_facets)
        |> Enum.sort_by(fn %{index: %{byteStart: start}} -> start end)

      {:ok, {text, facets}, updated_user}
    end
  end

  defp process_mentions(parsed_mentions, _meta, user) do
    Enum.reduce_while(parsed_mentions, {:ok, [], user}, fn mention, {:ok, acc, current_user} ->
      handle = String.trim_leading(mention.value, "@")

      case @bsky_client.resolve_handle(user.encrypted_bsky_token, handle) do
        {:ok, did} ->
          facet = %{
            index: %{
              byteStart: elem(mention.position, 0),
              byteEnd: elem(mention.position, 1)
            },
            features: [
              %{
                "$type" => "app.bsky.richtext.facet#mention",
                did: did
              }
            ]
          }

          {:cont, {:ok, [facet | acc], current_user}}

        {:error, _reason} ->
          {:cont, {:ok, acc, current_user}}
      end
    end)
  end

  defp process_tags_from_text(parsed_text) do
    Enum.map(TextParser.get(parsed_text, Tag), fn tag ->
      {start, stop} = tag.position

      %{
        index: %{
          byteStart: start,
          byteEnd: stop
        },
        features: [
          %{
            "$type" => "app.bsky.richtext.facet#tag",
            tag: String.trim_leading(tag.value, "#")
          }
        ]
      }
    end)
  end

  defp process_links_from_text(parsed_text) do
    Enum.map(TextParser.get(parsed_text, URL), fn %URL{value: uri, position: {start, stop}} ->
      %{
        index: %{
          byteStart: start,
          byteEnd: stop
        },
        features: [
          %{
            "$type" => "app.bsky.richtext.facet#link",
            uri: ensure_http_prefix(uri)
          }
        ]
      }
    end)
  end

  defp ensure_http_prefix(uri) do
    if String.starts_with?(uri, ["http://", "https://"]) do
      uri
    else
      "https://#{uri}"
    end
  end

  defp get_binary_data(_source_url, binary_data, _metadata) when is_binary(binary_data),
    do: {:ok, binary_data}

  defp get_binary_data(source_url, nil, %{"cloudinary" => %{"resource_type" => "video"}}) do
    media_helper().download_file(source_url)
  end

  defp get_binary_data(source_url, nil, metadata) do
    # Start with standard quality settings
    transformed_url = transform_cloudinary_url(source_url, metadata)

    case media_helper().download_file(transformed_url) do
      {:ok, data} = result ->
        size_bytes = byte_size(data)
        size_kb = Float.round(size_bytes / 1024, 2)
        size_mb = Float.round(size_bytes / 1_048_576, 2)

        Logger.info("Downloaded original image", %{
          event: "publishing.bsky.image_download.original",
          size_bytes: size_bytes,
          size_kb: size_kb,
          size_mb: size_mb,
          limit_bytes: @image_size_limit,
          limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
          over_limit_percent: Float.round(size_bytes / @image_size_limit * 100, 2)
        })

        if size_bytes > @image_size_limit do
          # Try with progressively more aggressive quality and size settings
          try_optimized_versions(source_url, metadata, [
            # First try: q_80 with width limit
            "w_1600,c_limit,q_80,f_auto",
            # Second try: q_70 with more width limit
            "w_1200,c_limit,q_70,f_auto",
            # Third try: q_60 with more width limit
            "w_1000,c_limit,q_60,f_auto",
            # Fourth try: q_50 with more width limit
            "w_800,c_limit,q_50,f_auto",
            # Fifth try: q_40 with more width limit
            "w_600,c_limit,q_40,f_auto",
            # Sixth try: q_30 with more width limit
            "w_500,c_limit,q_30,f_auto",
            # Seventh try: extreme compression
            "w_400,c_limit,q_20,f_auto",
            # Last resort: most extreme settings
            "w_300,c_limit,q_10,f_auto"
          ])
        else
          result
        end

      error ->
        error
    end
  end

  # Helper function to try multiple optimization settings
  defp try_optimized_versions(_source_url, _metadata, []),
    do: {:error, "Could not reduce image size enough"}

  defp try_optimized_versions(source_url, metadata, [transformation | remaining_transformations]) do
    optimized_url = transform_cloudinary_url(source_url, metadata, transformation)

    Logger.info("Trying optimized image", %{
      event: "publishing.bsky.image_optimization",
      transformation: transformation,
      original_url: source_url,
      optimized_url: optimized_url
    })

    case media_helper().download_file(optimized_url) do
      {:ok, optimized_data} = optimized_result ->
        size_bytes = byte_size(optimized_data)
        size_kb = Float.round(size_bytes / 1024, 2)
        size_mb = Float.round(size_bytes / 1_048_576, 2)

        Logger.info("Downloaded optimized image", %{
          event: "publishing.bsky.image_optimization.size",
          transformation: transformation,
          original_url: source_url,
          optimized_url: optimized_url,
          size_bytes: size_bytes,
          size_kb: size_kb,
          size_mb: size_mb,
          limit_bytes: @image_size_limit,
          limit_mb: Float.round(@image_size_limit / 1_048_576, 2)
        })

        if size_bytes > @image_size_limit do
          # Still too large, try next transformation
          Logger.debug("Image still too large, trying next transformation", %{
            event: "publishing.bsky.image_optimization.too_large",
            transformation: transformation,
            size_bytes: size_bytes,
            size_kb: size_kb,
            size_mb: size_mb,
            limit_bytes: @image_size_limit,
            limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
            remaining_transformations: length(remaining_transformations)
          })

          try_optimized_versions(source_url, metadata, remaining_transformations)
        else
          # Success - image is now small enough
          Logger.info("Successfully optimized image", %{
            event: "publishing.bsky.image_optimization.success",
            transformation: transformation,
            original_url: source_url,
            optimized_url: optimized_url,
            size_bytes: size_bytes,
            size_kb: size_kb,
            size_mb: size_mb,
            limit_bytes: @image_size_limit,
            limit_mb: Float.round(@image_size_limit / 1_048_576, 2),
            percent_of_limit: Float.round(size_bytes / @image_size_limit * 100, 2)
          })

          optimized_result
        end

      error ->
        # If download fails, try next transformation
        Logger.warning("Failed to download optimized image", %{
          event: "publishing.bsky.image_optimization.download_failed",
          transformation: transformation,
          original_url: source_url,
          optimized_url: optimized_url,
          error: inspect(error)
        })

        try_optimized_versions(source_url, metadata, remaining_transformations)
    end
  end

  defp do_upload_blob(binary_data, content_type, user) do
    case @bsky_client.upload_blob(user.encrypted_bsky_token, binary_data, content_type) do
      {:ok, blob} = result ->
        Logger.info("Uploaded blob", %{
          event: "publishing.bsky.upload_blob.success",
          user_id: user.id,
          blob: blob
        })

        result

      {:error, reason} ->
        Logger.error("Failed to upload blob", %{
          event: "publishing.bsky.upload_blob.failed",
          reason: reason,
          user_id: user.id
        })

        {:error, "Failed to upload blob"}
    end
  end

  defp do_upload_video(binary_data, content_type, user) do
    case @bsky_client.upload_video(user.encrypted_bsky_token, binary_data, content_type) do
      {:ok, _response} = result ->
        Logger.info("Video upload initiated", %{
          event: "publishing.bsky.upload_video.success",
          user_id: user.id
        })

        result

      {:error, reason} ->
        Logger.error("Failed to upload video", %{
          event: "publishing.bsky.upload_video.failed",
          reason: reason,
          user_id: user.id
        })

        {:error, "Failed to upload video"}
    end
  end

  defp wait_for_video_processing(job_id, user, attempts \\ 0) do
    if attempts >= 30 do
      {:error, "Video processing timeout"}
    else
      case @bsky_client.get_video_status(user.encrypted_bsky_token, job_id) do
        {:ok, %{"jobStatus" => %{"state" => "JOB_STATE_COMPLETED", "blob" => blob}}} ->
          {:ok, blob}

        {:ok, %{"jobStatus" => %{"state" => "JOB_STATE_FAILED", "message" => error}}} ->
          {:error, "Video processing failed: #{error}"}

        {:ok, %{"jobStatus" => %{"state" => _state}}} ->
          Process.sleep(2000)
          wait_for_video_processing(job_id, user, attempts + 1)

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  defp media_helper do
    Application.get_env(:crosspost, :media_helper, Crosspost.Publishing.MediaHelper)
  end
end
