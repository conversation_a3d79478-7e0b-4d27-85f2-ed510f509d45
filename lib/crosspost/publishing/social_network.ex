defmodule Crosspost.Publishing.SocialNetwork do
  alias Crosspost.Publishing.PostContent
  alias Crosspost.Accounts.ConnectedUser
  alias Crosspost.Accounts.Connection

  @callback publish_content(
              content :: PostContent.t(),
              user :: ConnectedUser.t(),
              opts :: keyword()
            ) ::
              {:ok, map()} | {:error, any()}

  @callback upload_media_file(
              attachment :: Crosspost.Publishing.Attachment.t(),
              user :: ConnectedUser.t()
            ) ::
              {:ok, String.t()} | {:error, any()}

  @callback refresh_connection(connection :: Connection.t()) ::
              :ok | {:ok, {String.t(), String.t(), DateTime.t() | nil}} | {:error, any()}

  defmacro __using__(_opts) do
    quote do
      @behaviour Crosspost.Publishing.SocialNetwork

      @impl true
      def refresh_connection(connection) do
        :ok
      end

      defoverridable refresh_connection: 1
    end
  end
end
