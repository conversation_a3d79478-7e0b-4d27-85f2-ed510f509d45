defmodule Crosspost.Publishing.PostNetworkStatus do
  use Ecto.Schema

  @primary_key false

  schema "post_network_statuses" do
    field :network, Ecto.Enum, values: [:x, :linkedin, :bsky, :mastodon], primary_key: true
    belongs_to :post, Crosspost.Publishing.Post, type: Ecto.UUID, primary_key: true

    field :text, :string
    field :status, :string
    field :published_at, :utc_datetime
    field :scheduled_at, :utc_datetime
    field :outcome, :map
  end
end
