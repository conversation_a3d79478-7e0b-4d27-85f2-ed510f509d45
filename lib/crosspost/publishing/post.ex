defmodule Crosspost.Publishing.Post do
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}

  alias Crosspost.Publishing.{PostContent, PostStatus, Schedule}

  @derive {Jason.Encoder,
           only: [
             :id,
             :status,
             :published_at,
             :social_networks,
             :twitter_id,
             :linkedin_urn,
             :mastodon_id,
             :content,
             :schedules,
             :user_id,
             :job_id,
             :inserted_at,
             :updated_at
           ]}

  @statuses [
    "draft",
    "pending",
    "scheduled",
    "publishing",
    "partially_published",
    "published",
    "failed"
  ]

  @networks [:bsky, :mastodon, :linkedin, :x, :canonical]

  schema "posts" do
    field :status, :string, default: "draft"
    field :published_at, :utc_datetime
    field :social_networks, {:array, Ecto.Enum}, values: @networks, default: []
    field :twitter_id, :string
    field :linkedin_urn, :string
    field :mastodon_id, :string
    field :workflow_id, :string
    field :preview_text, :string, virtual: true
    field :next_schedule, :utc_datetime, virtual: true

    has_many :content, PostContent, on_replace: :delete, on_delete: :delete_all
    has_many :post_content_attachments, through: [:content, :post_content_attachments]

    has_many :content_attachments, through: [:content, :content_attachments]

    has_many :post_statuses, PostStatus, on_delete: :delete_all
    has_many :schedules, Schedule, on_replace: :delete, on_delete: :delete_all

    has_many :final_statuses, PostStatus, where: [is_final: true]

    belongs_to :user, Crosspost.Accounts.User, type: :id
    belongs_to :job, Oban.Job, type: :id
    belongs_to :workspace, Crosspost.Accounts.Workspace

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(post, attrs) do
    post
    |> cast(attrs, [
      :published_at,
      :social_networks,
      :twitter_id,
      :linkedin_urn,
      :status,
      :user_id,
      :job_id,
      :mastodon_id,
      :workspace_id
    ])
    |> validate_required([:status, :user_id, :workspace_id])
    |> maybe_validate_content(attrs)
    |> cast_assoc(:schedules, with: &Schedule.changeset/2)
    |> validate_schedule_networks()
    |> validate_inclusion(:status, @statuses)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:workspace_id)
  end

  def status_update_changeset(post, attrs) do
    post
    |> cast(attrs, [:status])
    |> validate_inclusion(:status, @statuses)
    |> validate_required([:status])
  end

  defp maybe_validate_content(changeset, %{content: content}) when is_list(content) do
    if Enum.empty?(content) do
      add_error(changeset, :content, "can't be empty")
    else
      cast_assoc(changeset, :content, with: &PostContent.changeset/2)
    end
  end

  defp maybe_validate_content(changeset, _), do: changeset

  defp validate_schedule_networks(changeset) do
    case {get_change(changeset, :schedules), get_field(changeset, :social_networks)} do
      {[_ | _] = schedules, networks} when is_list(networks) ->
        schedule_networks =
          Enum.map(schedules, fn schedule ->
            case schedule.changes do
              %{network: network} -> network
              _ -> nil
            end
          end)
          |> Enum.reject(&is_nil/1)

        if Enum.all?(schedule_networks, &(&1 in networks)) do
          changeset
        else
          add_error(changeset, :schedules, "network must be included in social networks")
        end

      _ ->
        changeset
    end
  end
end
