defmodule Crosspost.Publishing.PostStatus do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @foreign_key_type :binary_id

  @derive {Jason.Encoder,
           only: [
             :id,
             :network,
             :status,
             :outcome,
             :is_final,
             :post_id,
             :inserted_at,
             :updated_at
           ]}

  @networks [:x, :linkedin, :bsky, :mastodon]

  @statuses [
    :publishing,
    :published,
    :failed,
    :retrying,
    :media_uploading,
    :media_uploaded,
    :media_upload_failed
  ]

  schema "post_statuses" do
    field :network, Ecto.Enum, values: @networks
    field :status, Ecto.Enum, values: @statuses
    field :outcome, :map, default: %{}
    field :is_final, :boolean, default: false

    belongs_to :post, Crosspost.Publishing.Post
    belongs_to :content, Crosspost.Publishing.PostContent

    timestamps()
  end

  def changeset(post_status, attrs) do
    post_status
    |> cast(attrs, [:post_id, :network, :status, :outcome, :is_final, :content_id])
    |> validate_required([:post_id, :network, :status])
    |> foreign_key_constraint(:post_id)
    |> foreign_key_constraint(:content_id)
    |> unique_constraint([:post_id, :network], name: :post_statuses_post_id_network_index)
  end
end
