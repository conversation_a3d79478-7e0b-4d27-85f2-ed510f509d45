defmodule Crosspost.Publishing.Schedule do
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @derive {Jason.Encoder, only: [:id, :network, :scheduled_at, :timezone]}

  @networks [:x, :linkedin, :bsky, :mastodon]

  schema "schedules" do
    field :network, Ecto.Enum, values: @networks
    field :timezone, :string
    field :scheduled_at, :utc_datetime

    belongs_to :post, Crosspost.Publishing.Post

    timestamps(type: :utc_datetime)
  end

  def changeset(schedule, attrs) do
    schedule
    |> cast(attrs, [:network, :scheduled_at, :timezone, :post_id])
    |> validate_required([:network, :scheduled_at, :timezone])
    |> truncate_scheduled_time()
    |> foreign_key_constraint(:post_id)
    |> unique_constraint([:post_id, :network])
  end

  defp truncate_scheduled_time(changeset) do
    case get_change(changeset, :scheduled_at) do
      nil ->
        changeset

      scheduled_at ->
        zeroed_time = %DateTime{
          year: scheduled_at.year,
          month: scheduled_at.month,
          day: scheduled_at.day,
          hour: scheduled_at.hour,
          minute: scheduled_at.minute,
          second: 0,
          time_zone: scheduled_at.time_zone,
          zone_abbr: scheduled_at.zone_abbr,
          utc_offset: scheduled_at.utc_offset,
          std_offset: scheduled_at.std_offset
        }

        put_change(changeset, :scheduled_at, zeroed_time)
    end
  end
end
