defmodule Crosspost.Publishing.PostOverview do
  @moduledoc """
  Ecto schema for the post_overviews SQL view.
  This provides a simplified way to query posts for the sidebar with all necessary information.
  """
  use Ecto.Schema
  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Accounts.User
  alias Crosspost.Accounts.Workspace

  @primary_key {:id, Ecto.UUID, autogenerate: false}
  @derive {Phoenix.Param, key: :id}

  schema "post_overviews" do
    field :status, :string
    field :published_at, :utc_datetime
    field :social_networks, {:array, Ecto.Enum}, values: [:bsky, :mastodon, :linkedin, :x, :canonical]
    field :preview_text, :string
    field :preview_network, Ecto.Enum, values: [:bsky, :mastodon, :linkedin, :x, :canonical]
    field :next_schedule_at, :utc_datetime
    field :schedule_timezone, :string
    field :is_scheduled, :boolean
    field :is_draft, :boolean
    field :is_published, :boolean

    belongs_to :user, User
    belongs_to :workspace, Workspace

    timestamps()
  end

  @doc """
  Lists posts for the sidebar with pagination and filtering.
  Respects the user's history limit.
  """
  def list_for_sidebar(user, workspace, filter \\ "all", page \\ 1, per_page \\ 10) do
    # Get history limit from user's enabled features
    history_limit =
      case Enum.find(user.enabled_features, &(&1.usage_key == "history")) do
        %{limit: limit} when is_integer(limit) and limit > 0 ->
          limit

        _ ->
          nil
      end

    # Build base query
    query =
      from po in __MODULE__,
        where: po.user_id == ^user.id,
        where: po.workspace_id == ^workspace.id

    # Apply history limit if present (except for scheduled posts)
    query =
      if history_limit && filter != "scheduled" do
        cutoff_date = DateTime.utc_now() |> DateTime.add(-history_limit * 24 * 3600, :second)
        from po in query, where: po.inserted_at >= ^cutoff_date
      else
        query
      end

    # Apply filter
    query =
      case filter do
        "scheduled" ->
          from po in query, where: po.is_scheduled == true

        "draft" ->
          from po in query, where: po.is_draft == true

        "published" ->
          from po in query, where: po.is_published == true

        _ ->
          query
      end

    # Get total count for pagination
    total_count = Repo.aggregate(query, :count, :id)

    # Apply pagination and ordering
    posts =
      query
      |> order_by([po], desc: po.inserted_at)
      |> limit(^per_page)
      |> offset(^((page - 1) * per_page))
      |> Repo.all()

    # Calculate if there are more posts
    has_more = total_count > page * per_page

    {posts, has_more}
  end

  @doc """
  Gets all posts for the sidebar to display counts.
  """
  def get_all_for_counts(user, workspace) do
    # Get history limit from user's enabled features
    history_limit =
      case Enum.find(user.enabled_features, &(&1.usage_key == "history")) do
        %{limit: limit} when is_integer(limit) and limit > 0 ->
          limit

        _ ->
          nil
      end

    # Build base query
    query =
      from po in __MODULE__,
        where: po.user_id == ^user.id,
        where: po.workspace_id == ^workspace.id

    # Apply history limit if present
    query =
      if history_limit do
        cutoff_date = DateTime.utc_now() |> DateTime.add(-history_limit * 24 * 3600, :second)
        from po in query, where: po.inserted_at >= ^cutoff_date
      else
        query
      end

    # Get all posts
    Repo.all(query)
  end

  @doc """
  Gets all scheduled posts regardless of history limit.
  """
  def get_scheduled_for_counts(user, workspace) do
    query =
      from po in __MODULE__,
        where: po.user_id == ^user.id,
        where: po.workspace_id == ^workspace.id,
        where: po.is_scheduled == true

    Repo.all(query)
  end
end
