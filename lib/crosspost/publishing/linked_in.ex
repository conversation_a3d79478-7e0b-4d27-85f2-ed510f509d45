defmodule Crosspost.Publishing.LinkedIn do
  use Crosspost.Publishing.SocialNetwork

  alias Crosspost.Accounts.LinkedIn.Client

  @impl true
  def refresh_connection(connection) do
    with true <- not is_nil(connection.expires_at),
         true <-
           DateTime.compare(connection.expires_at, DateTime.add(DateTime.utc_now(), 60)) == :lt do
      case Client.refresh_token(connection) do
        {:ok, %{access_token: access_token, refresh_token: refresh_token, expires_at: expires_at}} ->
          {:ok, {access_token, refresh_token, expires_at}}

        {:error, error} ->
          {:error, error}
      end
    else
      _ -> :ok
    end
  end

  require Logger

  alias Crosspost.Accounts
  alias Crosspost.Accounts.LinkedIn.Client

  @register_url "https://api.linkedin.com/rest/images?action=initializeUpload"

  @impl true
  def publish_content(content, user, opts) do
    Logger.info("Starting LinkedIn publish process", %{
      event: "publishing.linkedin.start",
      user_id: user.id,
      content: %{text: content.text, attachment_count: length(content.attachments)}
    })

    connection = Keyword.get(opts, :connection)
    media = Keyword.get(opts, :media, [])
    do_publish_content(content, user, connection, media)
  end

  defp do_publish_content(content, user, connection, media) do
    owner = determine_owner(user, connection)
    link_data = Enum.find(media, &(&1[:type] == :link))

    Logger.debug("LinkedIn content type", %{
      event: "publishing.linkedin.content_type",
      has_link: not is_nil(link_data),
      media_count: length(media)
    })

    if link_data do
      Logger.debug("Publishing LinkedIn post with link", %{
        event: "publishing.linkedin.publishing_link",
        link_data: inspect(link_data)
      })

      # Build payload for link post
      payload = %{
        "author" => owner,
        "commentary" => escape_parens(content.text),
        "visibility" => "PUBLIC",
        "distribution" => %{
          "feedDistribution" => "MAIN_FEED",
          "targetEntities" => [],
          "thirdPartyDistributionChannels" => []
        },
        "content" => %{
          "article" =>
            %{
              "source" => link_data.url,
              "title" => link_data.title,
              "description" => link_data.description,
              "thumbnail" => link_data.thumb
            }
            |> Map.reject(fn {_, v} -> is_nil(v) end)
        },
        "lifecycleState" => "PUBLISHED",
        "isReshareDisabledByAuthor" => false
      }

      post_to_linkedin(payload, user)
    else
      Logger.debug("Publishing LinkedIn post with media", %{
        event: "publishing.linkedin.publishing_media",
        media_count: length(media)
      })

      media_urns = Enum.map(media, & &1.urn)
      has_video = Enum.any?(media, &(&1.type == :video))

      publish_with_media(content.text, media_urns, has_video, owner, user)
    end
  end

  @impl true
  def upload_media_file(%{type: :link} = content_attachment, user) do
    connection = Accounts.get_connection(user.id, "linkedin", content_attachment.workspace_id)

    with {:ok, connection} <- maybe_refresh_token(connection),
         owner = determine_owner(user, connection),
         image_url = content_attachment.metadata["image_url"] do
      thumbnail_urn =
        case image_url && upload_thumbnail(content_attachment, user, owner) do
          {:ok, urn} -> urn
          _ -> nil
        end

      link_data = %{
        type: :link,
        url: content_attachment.metadata["url"],
        title: content_attachment.metadata["title"],
        description: content_attachment.metadata["description"],
        thumb: thumbnail_urn
      }

      {:ok, link_data}
    end
  end

  @impl true
  def upload_media_file(content_attachment, user) do
    connection = Accounts.get_connection(user.id, "linkedin", content_attachment.workspace_id)

    with {:ok, connection} <- maybe_refresh_token(connection),
         owner = determine_owner(user, connection) do
      case content_attachment.type do
        :video ->
          case upload_video_to_linkedin(content_attachment, user, owner) do
            video_urn when is_binary(video_urn) ->
              {:ok,
               %{
                 type: :video,
                 urn: video_urn
               }}

            error ->
              error
          end

        :image ->
          case upload_image_asset_to_linkedin(content_attachment, user, owner) do
            asset_id when is_binary(asset_id) ->
              {:ok,
               %{
                 type: :image,
                 urn: asset_id
               }}

            error ->
              error
          end

        _ ->
          {:error, "Unsupported media type: #{content_attachment.content_type}"}
      end
    end
  end

  defp determine_owner(user, connection) do
    case connection && get_in(connection.settings, ["organization", "id"]) do
      org_id when is_binary(org_id) or is_integer(org_id) ->
        owner = "urn:li:organization:#{org_id}"

        Logger.info("Publishing as organization", %{
          event: "publishing.linkedin.publishing_as_org",
          owner: owner,
          org_id: org_id
        })

        owner

      _ ->
        owner = "urn:li:person:#{user.linkedin_id}"

        Logger.info("Publishing as person", %{
          event: "publishing.linkedin.publishing_as_person",
          owner: owner,
          user_id: user.linkedin_id
        })

        owner
    end
  end

  defp publish_with_media(text, image_urns, has_video, owner, user) do
    # Escape parentheses in the text content
    escaped_text = escape_parens(text)

    Logger.info("Publishing LinkedIn media post", %{
      event: "publishing.linkedin.media_post_start",
      has_video: has_video,
      image_count: length(image_urns),
      owner: owner,
      user_id: user.id
    })

    # Build base payload
    base_payload = %{
      "author" => owner,
      "commentary" => escaped_text,
      "visibility" => "PUBLIC",
      "distribution" => %{
        "feedDistribution" => "MAIN_FEED",
        "targetEntities" => [],
        "thirdPartyDistributionChannels" => []
      },
      "lifecycleState" => "PUBLISHED",
      "isReshareDisabledByAuthor" => false
    }

    # Only add content field if we have media
    payload =
      if Enum.empty?(image_urns) do
        base_payload
      else
        content =
          cond do
            # For single attachment (image or video)
            length(image_urns) == 1 ->
              media_urn = hd(image_urns)

              Logger.debug("Processing single media URN", %{
                event: "publishing.linkedin.process_media",
                media_urn: media_urn,
                has_video: has_video
              })

              # For videos, use the URN as is
              media_id =
                if has_video do
                  media_urn
                else
                  # For images, ensure we're using image: prefix
                  String.replace(media_urn, "digitalmediaAsset:", "image:")
                end

              %{
                "media" => %{
                  "id" => media_id,
                  "title" => if(has_video, do: "Video post", else: "Image post")
                }
              }

            # For multiple images
            true ->
              if has_video do
                video_urn =
                  Enum.find(image_urns, fn urn ->
                    String.contains?(urn, "urn:li:video:")
                  end)

                Logger.debug("Using video from multiple media", %{
                  event: "publishing.linkedin.use_video",
                  video_urn: video_urn
                })

                %{
                  "media" => %{
                    "id" => video_urn,
                    "title" => "Video post"
                  }
                }
              else
                image_list =
                  Enum.map(image_urns, fn urn ->
                    media_id = String.replace(urn, "digitalmediaAsset:", "image:")

                    %{
                      "id" => media_id,
                      "altText" => "Image"
                    }
                  end)

                Logger.debug("Processing multiple images", %{
                  event: "publishing.linkedin.process_images",
                  image_count: length(image_list),
                  image_urns: image_list
                })

                %{
                  "multiImage" => %{
                    "images" => image_list
                  }
                }
              end
          end

        Map.put(base_payload, "content", content)
      end

    url = "https://api.linkedin.com/rest/posts"

    headers = [
      {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
      {"Content-Type", "application/json"},
      {"LinkedIn-Version", "202411"},
      {"X-RestLi-Protocol-Version", "2.0.0"}
    ]

    Logger.info("Sending LinkedIn media post request", %{
      event: "publishing.linkedin.media_post_request",
      url: url,
      payload: payload,
      user_id: user.id
    })

    opts =
      [
        body: Jason.encode!(payload),
        headers: headers,
        url: url
      ]
      |> add_test_options()

    case Req.post(url, opts) do
      {:ok, %Req.Response{status: status} = response} when status in [200, 201] ->
        [urn] = Req.Response.get_header(response, "x-restli-id")

        Logger.info("Posted to LinkedIn successfully", %{
          event: "publishing.linkedin.post_success",
          status: status,
          urn: urn,
          user_id: user.id
        })

        {:ok, %{urn: urn}}

      {:ok, %Req.Response{status: status, body: body}} ->
        error_details =
          case body do
            %{"message" => message} -> message
            %{"serviceErrorCode" => code, "message" => message} -> "#{code}: #{message}"
            _ -> inspect(body)
          end

        Logger.error("Failed to post to LinkedIn", %{
          event: "publishing.linkedin.post_failed",
          status: status,
          body: inspect(body),
          error_details: error_details,
          payload: payload,
          user_id: user.id
        })

        {:error, "Failed to post to LinkedIn: HTTP #{status} #{error_details}"}

      {:error, reason} ->
        Logger.error("Error posting to LinkedIn", %{
          event: "publishing.linkedin.post_error",
          error: inspect(reason),
          user_id: user.id
        })

        {:error, reason}
    end
  end

  defp upload_image_asset_to_linkedin(attachment, user, owner) do
    {aspect_width, aspect_height} =
      case attachment.metadata do
        %{"width" => w, "height" => h} when is_number(w) and is_number(h) ->
          gcd = Integer.gcd(w, h)
          {div(w, gcd), div(h, gcd)}

        _ ->
          {nil, nil}
      end

    register_payload = %{
      "aspectRatioWidth" => aspect_width,
      "aspectRatioHeight" => aspect_height,
      "altText" => attachment.metadata["alt"],
      "initializeUploadRequest" => %{
        "owner" => owner
      }
    }

    Logger.info("Registering image upload", %{
      event: "publishing.linkedin.image_upload_register",
      register_payload: inspect(register_payload),
      user_id: user.id,
      attachment_id: attachment.id,
      owner: owner,
      network: :linkedin
    })

    headers = [
      {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
      {"Content-Type", "application/json"},
      {"LinkedIn-Version", "202411"},
      {"X-RestLi-Protocol-Version", "2.0.0"}
    ]

    opts =
      [
        body: Jason.encode!(register_payload),
        headers: headers,
        url: @register_url
      ]
      |> add_test_options()

    case Req.post(@register_url, opts) do
      {:ok, %Req.Response{status: 200, body: body}} ->
        upload_url = get_in(body, ["value", "uploadUrl"])
        image_urn = get_in(body, ["value", "image"])

        case media_helper().download_file(attachment.source_url) do
          {:ok, binary_data} ->
            upload_headers = [
              {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
              {"Content-Type", attachment.content_type}
            ]

            upload_opts =
              [
                body: binary_data,
                headers: upload_headers,
                url: upload_url
              ]
              |> add_test_options()

            case Req.post(upload_url, upload_opts) do
              {:ok, %Req.Response{status: status}} when status in [200, 201] ->
                image_urn

              {:ok, %Req.Response{status: status, body: body}} ->
                Logger.error(
                  "Failed to upload media to LinkedIn: HTTP #{status} #{inspect(body)}"
                )

                raise "Failed to upload media to LinkedIn"

              {:error, reason} ->
                Logger.error("Error uploading media to LinkedIn: #{inspect(reason)}")
                raise "Error uploading media to LinkedIn"
            end

          {:error, reason} ->
            Logger.error("Error downloading media for LinkedIn: #{inspect(reason)}")
            raise "Error downloading media for LinkedIn: #{inspect(reason)}"
        end

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("Failed to initialize upload with LinkedIn: HTTP #{status} #{inspect(body)}")
        raise "Failed to initialize upload with LinkedIn: #{inspect(body)}"

      {:error, reason} ->
        Logger.error("Error initializing upload with LinkedIn: #{inspect(reason)}")
        raise "Error initializing upload with LinkedIn: #{inspect(reason)}"
    end
  end

  defp upload_video_to_linkedin(attachment, user, owner) do
    case media_helper().download_file(attachment.source_url) do
      {:ok, binary_data} ->
        init_url = "https://api.linkedin.com/v2/videos?action=initializeUpload"
        file_size = byte_size(binary_data)

        init_payload = %{
          "initializeUploadRequest" => %{
            "owner" => owner,
            "fileSizeBytes" => file_size,
            "uploadCaptions" => false,
            "uploadThumbnail" => false
          }
        }

        headers = [
          {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
          {"Content-Type", "application/json"},
          {"LinkedIn-Version", "202411"},
          {"X-RestLi-Protocol-Version", "2.0.0"}
        ]

        Logger.debug("Initializing video upload", %{
          event: "publishing.linkedin.video_upload_init",
          user_id: user.id,
          attachment_id: attachment.id,
          file_size: file_size
        })

        opts =
          [
            body: Jason.encode!(init_payload),
            headers: headers,
            url: init_url
          ]
          |> add_test_options()

        case Req.post(init_url, opts) do
          {:ok, %Req.Response{status: 200, body: body}} ->
            Logger.debug("LinkedIn video initialization response", %{
              event: "publishing.linkedin.video_init_response",
              body: inspect(body),
              user_id: user.id,
              attachment_id: attachment.id
            })

            upload_instructions = get_in(body, ["value", "uploadInstructions"])
            upload_token = get_in(body, ["value", "uploadToken"])
            video_urn = get_in(body, ["value", "video"])

            unless upload_instructions do
              Logger.error("Missing upload instructions in response", %{
                event: "publishing.linkedin.video_init_failed",
                body: inspect(body),
                user_id: user.id,
                attachment_id: attachment.id
              })

              raise "Missing upload instructions in initialization response"
            end

            unless video_urn do
              Logger.error("Missing video URN in response", %{
                event: "publishing.linkedin.video_init_failed",
                body: inspect(body),
                user_id: user.id,
                attachment_id: attachment.id
              })

              raise "Missing video URN in initialization response"
            end

            # Upload each chunk
            uploaded_parts =
              upload_instructions
              |> Enum.with_index()
              |> Enum.map(fn {instruction, index} ->
                upload_url = instruction["uploadUrl"]
                first_byte = instruction["firstByte"]
                last_byte = instruction["lastByte"]
                chunk_size = last_byte - first_byte + 1
                chunk = binary_part(binary_data, first_byte, chunk_size)

                Logger.debug("Uploading chunk #{index + 1}/#{length(upload_instructions)}", %{
                  event: "publishing.linkedin.video_chunk_upload",
                  chunk_size: chunk_size,
                  first_byte: first_byte,
                  last_byte: last_byte,
                  user_id: user.id,
                  attachment_id: attachment.id
                })

                upload_headers = [
                  {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
                  {"Content-Type", attachment.content_type},
                  {"Content-Length", Integer.to_string(chunk_size)}
                ]

                upload_opts =
                  [
                    body: chunk,
                    headers: upload_headers,
                    url: upload_url
                  ]
                  |> add_test_options()

                case Req.post(upload_url, upload_opts) do
                  {:ok, %Req.Response{status: status, headers: headers}}
                  when status in [200, 201] ->
                    etag =
                      case headers do
                        %{"etag" => [etag_value]} when is_binary(etag_value) ->
                          String.trim(etag_value, "\"")

                        _ ->
                          case Regex.run(~r/x-li-ambry-ep=([^&]+)/, upload_url) do
                            [_, encoded_id] ->
                              "/ambry-video/signedId/#{encoded_id}"

                            _ ->
                              Logger.error(
                                "No ETag found in upload response headers: #{inspect(headers)}"
                              )

                              raise "Missing ETag in upload response"
                          end
                      end

                    Logger.debug("Successfully uploaded chunk #{index + 1}", %{
                      event: "publishing.linkedin.video_chunk_success",
                      etag: etag,
                      user_id: user.id,
                      attachment_id: attachment.id
                    })

                    etag

                  {:ok, %Req.Response{status: status, body: body}} ->
                    Logger.error("Failed to upload video chunk #{index + 1}", %{
                      event: "publishing.linkedin.video_chunk_failed",
                      status: status,
                      body: inspect(body),
                      user_id: user.id,
                      attachment_id: attachment.id
                    })

                    raise "Failed to upload video chunk: HTTP #{status}"

                  {:error, reason} ->
                    Logger.error("Error uploading video chunk #{index + 1}", %{
                      event: "publishing.linkedin.video_chunk_error",
                      error: inspect(reason),
                      user_id: user.id,
                      attachment_id: attachment.id
                    })

                    raise "Error uploading video chunk: #{inspect(reason)}"
                end
              end)

            # Step 3: Finalize Upload
            finalize_url = "https://api.linkedin.com/v2/videos?action=finalizeUpload"

            finalize_payload = %{
              "finalizeUploadRequest" => %{
                "video" => video_urn,
                "uploadToken" => upload_token || "",
                "uploadedPartIds" => uploaded_parts
              }
            }

            Logger.debug("Finalizing video upload", %{
              event: "publishing.linkedin.video_finalize",
              video_urn: video_urn,
              parts_count: length(uploaded_parts),
              user_id: user.id,
              attachment_id: attachment.id
            })

            finalize_opts =
              [
                body: Jason.encode!(finalize_payload),
                headers: headers,
                url: finalize_url
              ]
              |> add_test_options()

            case Req.post(finalize_url, finalize_opts) do
              {:ok, %Req.Response{status: status}} when status in [200, 201] ->
                Logger.info("Successfully finalized video upload", %{
                  event: "publishing.linkedin.video_finalize_success",
                  video_urn: video_urn,
                  user_id: user.id,
                  attachment_id: attachment.id
                })

                video_urn

              {:ok, %Req.Response{status: status, body: body}} ->
                Logger.error("Failed to finalize video upload", %{
                  event: "publishing.linkedin.video_finalize_failed",
                  status: status,
                  body: inspect(body),
                  user_id: user.id,
                  attachment_id: attachment.id
                })

                raise "Failed to finalize video upload: #{inspect(body)}"

              {:error, reason} ->
                Logger.error("Error finalizing video upload", %{
                  event: "publishing.linkedin.video_finalize_error",
                  error: inspect(reason),
                  user_id: user.id,
                  attachment_id: attachment.id
                })

                raise "Error finalizing video upload: #{inspect(reason)}"
            end

          {:ok, %Req.Response{status: status, body: body}} ->
            Logger.error("Failed to initialize video upload", %{
              network: :linkedin,
              event: :video_init_failure,
              status: status,
              body: inspect(body),
              user_id: user.id
            })

            raise "Failed to initialize video upload: #{inspect(body)}"

          {:error, reason} ->
            Logger.error("Error initializing video upload", %{
              network: :linkedin,
              event: :video_init_error,
              error: inspect(reason),
              user_id: user.id
            })

            raise "Error initializing video upload: #{inspect(reason)}"
        end

      {:error, reason} ->
        Logger.error("Error downloading media for LinkedIn", %{
          network: :linkedin,
          event: :media_download_error,
          error: inspect(reason),
          user_id: user.id
        })

        raise "Error downloading media for LinkedIn: #{inspect(reason)}"
    end
  end

  defp perform_media_upload(register_url, register_payload, binary_data, user)
       when is_binary(binary_data) do
    headers = [
      {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
      {"Content-Type", "application/json"}
    ]

    opts =
      [
        body: Jason.encode!(register_payload),
        headers: headers,
        url: register_url
      ]
      |> add_test_options()

    case Req.post(register_url, opts) do
      {:ok, %Req.Response{status: 200, body: body}} ->
        upload_url =
          body["value"]["uploadMechanism"][
            "com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"
          ]["uploadUrl"]

        asset_id = body["value"]["asset"]

        upload_headers = [
          {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
          {"Content-Type", "image/jpeg"}
        ]

        upload_opts =
          [
            body: binary_data,
            headers: upload_headers,
            url: upload_url
          ]
          |> add_test_options()

        case Req.post(upload_url, upload_opts) do
          {:ok, %Req.Response{status: 201}} ->
            asset_id

          {:ok, %Req.Response{status: status, body: body}} ->
            Logger.error("Failed to upload media to LinkedIn: HTTP #{status} #{inspect(body)}")

            {:error, "Failed to upload media to LinkedIn"}

          {:error, reason} ->
            Logger.error("Error uploading media to LinkedIn: #{inspect(reason)}")
            {:error, reason}
        end

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("Failed to register upload with LinkedIn: HTTP #{status} #{inspect(body)}")
        {:error, "Failed to register upload with LinkedIn: #{inspect(body)}"}

      {:error, reason} ->
        Logger.error("Error registering upload with LinkedIn: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp perform_media_upload(register_url, register_payload, attachment, user) do
    headers = [
      {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
      {"Content-Type", "application/json"}
    ]

    opts =
      [
        body: Jason.encode!(register_payload),
        headers: headers,
        url: register_url
      ]
      |> add_test_options()

    case Req.post(register_url, opts) do
      {:ok, %Req.Response{status: 200, body: body}} ->
        upload_url =
          body["value"]["uploadMechanism"][
            "com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"
          ]["uploadUrl"]

        asset_id = body["value"]["asset"]

        case media_helper().download_file(attachment.source_url) do
          {:ok, binary_data} ->
            upload_headers = [
              {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
              {"Content-Type", attachment.content_type}
            ]

            upload_opts =
              [
                body: binary_data,
                headers: upload_headers,
                url: upload_url
              ]
              |> add_test_options()

            case Req.post(upload_url, upload_opts) do
              {:ok, %Req.Response{status: 201}} ->
                asset_id

              {:ok, %Req.Response{status: status, body: body}} ->
                Logger.error(
                  "Failed to upload media to LinkedIn: HTTP #{status} #{inspect(body)}"
                )

                raise "Failed to upload media to LinkedIn"

              {:error, reason} ->
                Logger.error("Error uploading media to LinkedIn: #{inspect(reason)}")
                raise "Error uploading media to LinkedIn"
            end

          {:error, reason} ->
            Logger.error("Error downloading media for LinkedIn: #{inspect(reason)}")
            raise "Error downloading media for LinkedIn: #{inspect(reason)}"
        end

      {:ok, %Req.Response{status: status, body: body}} ->
        Logger.error("Failed to register upload with LinkedIn: HTTP #{status} #{inspect(body)}")
        raise "Failed to register upload with LinkedIn: #{inspect(body)}"

      {:error, reason} ->
        Logger.error("Error registering upload with LinkedIn: #{inspect(reason)}")
        raise "Error registering upload with LinkedIn: #{inspect(reason)}"
    end
  end

  defp upload_thumbnail(link_attachment, user, owner) do
    case get_in(link_attachment.metadata, ["image_url"]) do
      url when is_binary(url) and url != "" ->
        case media_helper().download_file(url) do
          {:ok, binary_data} ->
            register_url = "https://api.linkedin.com/v2/assets?action=registerUpload"

            register_payload = %{
              "registerUploadRequest" => %{
                "recipes" => ["urn:li:digitalmediaRecipe:feedshare-image"],
                "owner" => owner,
                "serviceRelationships" => [
                  %{
                    "relationshipType" => "OWNER",
                    "identifier" => "urn:li:userGeneratedContent"
                  }
                ]
              }
            }

            case perform_media_upload(register_url, register_payload, binary_data, user) do
              asset_id when is_binary(asset_id) ->
                # Convert digitalmediaAsset URN to image URN
                {:ok, String.replace(asset_id, "digitalmediaAsset:", "image:")}

              error ->
                error
            end

          {:error, reason} ->
            Logger.warning("Failed to download thumbnail", %{
              event: "publishing.linkedin.thumbnail_download_failed",
              error: inspect(reason),
              user_id: user.id,
              attachment_id: link_attachment.id
            })

            {:error, :download_failed}
        end

      _ ->
        {:error, :no_image_url}
    end
  end

  defp add_test_options(opts) do
    Keyword.merge(opts, Application.get_env(:crosspost, :linked_in_req_options) || [])
  end

  defp media_helper do
    Application.get_env(:crosspost, :media_helper, Crosspost.Publishing.MediaHelper)
  end

  # Add this helper function near the bottom of the module
  defp escape_parens(text) when is_binary(text) do
    text
    |> String.replace(~r/[\(\)\{\}\[\]<>@\|~_]/, fn x -> "\\" <> x end)
  end

  defp escape_parens(nil), do: nil

  # Add this helper function to handle the actual POST request
  defp post_to_linkedin(payload, user) do
    url = "https://api.linkedin.com/rest/posts"

    Logger.info("Preparing LinkedIn post request", %{
      event: "publishing.linkedin.prepare_request",
      url: url,
      payload_keys: Map.keys(payload)
    })

    # Ensure required fields are present according to docs
    payload =
      Map.merge(
        %{
          "visibility" => "PUBLIC",
          "distribution" => %{
            "feedDistribution" => "MAIN_FEED",
            "targetEntities" => [],
            "thirdPartyDistributionChannels" => []
          },
          "lifecycleState" => "PUBLISHED",
          "isReshareDisabledByAuthor" => false
        },
        payload
      )

    headers = [
      {"Authorization", "Bearer #{user.encrypted_linkedin_token}"},
      {"Content-Type", "application/json"},
      {"LinkedIn-Version", "202411"},
      {"X-RestLi-Protocol-Version", "2.0.0"}
    ]

    # Log the full request details for debugging
    Logger.info("Sending LinkedIn post request", %{
      event: "publishing.linkedin.sending_request",
      url: url,
      headers: headers |> Enum.map(fn {k, _} -> k end),
      payload: payload
    })

    opts =
      [
        body: Jason.encode!(payload),
        headers: headers,
        url: url
      ]
      |> add_test_options()

    Logger.debug("LinkedIn request options", %{
      event: "publishing.linkedin.request_options",
      opts: inspect(opts)
    })

    case Req.post(url, opts) do
      {:ok, %Req.Response{status: status} = response} when status in [200, 201] ->
        [urn] = Req.Response.get_header(response, "x-restli-id")

        Logger.info("Posted to LinkedIn successfully", %{
          event: "publishing.linkedin.post_success",
          status: status,
          urn: urn,
          user_id: user.id
        })

        {:ok, %{urn: urn}}

      {:ok, %Req.Response{status: status, body: body}} ->
        error_details =
          case body do
            %{"message" => message} -> message
            %{"serviceErrorCode" => code, "message" => message} -> "#{code}: #{message}"
            _ -> inspect(body)
          end

        Logger.error("Failed to post to LinkedIn", %{
          event: "publishing.linkedin.post_failed",
          status: status,
          body: inspect(body),
          error_details: error_details,
          user_id: user.id
        })

        {:error, "Failed to post to LinkedIn: HTTP #{status} #{error_details}"}

      {:error, reason} ->
        Logger.error("Error posting to LinkedIn", %{
          event: "publishing.linkedin.post_error",
          error: inspect(reason),
          user_id: user.id
        })

        {:error, reason}
    end
  end

  # Add this helper function to handle token refresh
  defp maybe_refresh_token(connection) do
    with true <- not is_nil(connection.expires_at),
         true <-
           DateTime.compare(connection.expires_at, DateTime.add(DateTime.utc_now(), 60)) == :lt do
      Logger.info("LinkedIn token expired or expiring soon, refreshing", %{
        event: "publishing.linkedin.token_refresh",
        expires_at: connection.expires_at
      })

      case Client.refresh_token(connection) do
        {:ok,
         %{
           access_token: new_access_token,
           refresh_token: new_refresh_token,
           expires_at: new_expires_at
         }} ->
          Logger.info("Refreshed LinkedIn token", %{
            event: "publishing.linkedin.token_refresh_success",
            expires_at: new_expires_at
          })

          Accounts.update_connection(connection, %{
            encrypted_access_token: new_access_token,
            encrypted_refresh_token: new_refresh_token,
            expires_at: new_expires_at
          })

        {:error, error} ->
          Logger.error("Failed to refresh LinkedIn token", %{
            event: "publishing.linkedin.token_refresh_failed",
            error: inspect(error)
          })

          {:error, "Failed to refresh LinkedIn token: #{inspect(error)}"}
      end
    else
      _ -> {:ok, connection}
    end
  end
end
