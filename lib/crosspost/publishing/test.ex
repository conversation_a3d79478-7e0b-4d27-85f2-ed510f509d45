defmodule Crosspost.Publishing.Test do
  use Crosspost.Publishing.SocialNetwork

  require Logger

  def publish_content(content, user, []) do
    network = content |> List.first() |> Map.get(:network)
    target_network = if network == "twitter", do: "x", else: network

    post_data = %{
      content: content,
      user_id: user.id,
      post_id: content |> List.first() |> Map.get(:post_id),
      network: target_network,
      published_at: DateTime.utc_now(),
      attempt: 1
    }

    GenServer.cast(Crosspost.Workers.TestPublishPostWorker, {:store_published, post_data})

    {:ok, %{"id" => "test_#{:erlang.unique_integer([:positive])}"}}
  end

  def publish_post(content, user, opts) do
    target_network = Keyword.fetch!(opts, :target_network)

    post_data = %{
      content:
        Enum.map(content, fn item ->
          if item.network == "canonical" do
            %{item | network: to_string(target_network)}
          else
            item
          end
        end),
      user_id: user.id,
      post_id: content |> List.first() |> Map.get(:post_id),
      network: to_string(target_network),
      published_at: DateTime.utc_now(),
      attempt: 1
    }

    # Store in TestPublishPostWorker's cache
    GenServer.cast(Crosspost.Workers.TestPublishPostWorker, {:store_published, post_data})

    # Return a successful response with a test ID
    {:ok, %{"id" => "test_#{:erlang.unique_integer([:positive])}"}}
  end

  def upload_media_file(_attachment, _user) do
    {:ok, %{"id" => "test_#{:erlang.unique_integer([:positive])}"}}
  end
end
