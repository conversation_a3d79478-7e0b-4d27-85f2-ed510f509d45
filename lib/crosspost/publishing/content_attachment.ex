defmodule Crosspost.Publishing.ContentAttachment do
  use Ecto.Schema

  alias Crosspost.Accounts.User
  alias Crosspost.Accounts.Workspace
  alias Crosspost.Publishing.PostContent

  @derive {Jason.Encoder,
           only: [
             :id,
             :content_id,
             :user_id,
             :workspace_id,
             :network,
             :type,
             :filename,
             :content_type,
             :source_url,
             :preview_url,
             :metadata,
             :status,
             :order
           ]}

  @primary_key {:id, Ecto.UUID, autogenerate: false}

  schema "content_attachments" do
    field :order, :integer
    field :network, Ecto.Enum, values: [:mastodon, :bsky, :linkedin, :x, :canonical]
    field :filename, :string
    field :content_type, :string
    field :type, Ecto.Enum, values: [:image, :video, :link]
    field :status, :string
    field :metadata, :map
    field :source_url, :string
    field :preview_url, :string

    belongs_to :content, PostContent, type: :binary_id

    belongs_to :user, User
    belongs_to :workspace, Workspace
  end
end
