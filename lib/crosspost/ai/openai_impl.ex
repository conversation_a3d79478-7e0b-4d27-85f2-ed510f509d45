defmodule Crosspost.AI.OpenAIImpl do
  @behaviour Crosspost.AI.Behaviour

  require Logger

  @impl true
  def generate_tags(text) do
    messages = [
      %{
        role: "system",
        content: """
        You are a helpful assistant that generates relevant hashtags for social media posts.
        Generate exactly 3-5 relevant hashtags for the given text.
        Return only the hashtags, separated by spaces, without any other text or explanation.
        Each hashtag should start with # and use PascalCase for multiple words (e.g. #SocialMedia not #socialMedia).
        Never generate more than 5 hashtags.
        """
      },
      %{role: "user", content: text}
    ]

    case Crosspost.OpenAI.Client.chat_completion(messages, temperature: 0.7) do
      {:ok, response} ->
        tags =
          response["choices"]
          |> List.first()
          |> Map.get("message")
          |> Map.get("content")
          |> String.trim()
          # Split into list of tags
          |> String.split()
          # Take max 5 tags
          |> Enum.join(" ")

        {:ok, tags}

      {:error, error} ->
        Logger.error("[AI.OpenAIImpl] Failed to generate tags: #{inspect(error)}")
        {:error, "Failed to generate tags"}
    end
  end

  @impl true
  def detect_spam_emails(emails) when is_list(emails) do
    messages = [
      %{
        role: "system",
        content: """
        You are an aggressive spam detection system. Your primary goal is to protect the system from potential abuse.
        Analyze each email address for spam patterns. Common indicators include:
        - Random usernames or keyboard patterns
        - Excessive numbers or special characters
        - Temporary/disposable email domains
        - Suspicious word combinations
        - Multiple accounts with similar patterns
        - Generic names with numbers
        - Unusual TLDs
        - Government/official emails from non-official domains
        - Multiple dots or underscores
        - Obvious fake names or businesses

        Err on the side of marking as spam or potential_spam. It's better to be too aggressive than too lenient.

        For each email, respond with one line:
        email:status
        where status is 'spam', 'potential_spam', or 'legitimate'
        """
      },
      %{
        role: "user",
        content: "Please analyze these emails:\n#{Enum.join(emails, "\n")}"
      }
    ]

    case Crosspost.OpenAI.Client.chat_completion(messages,
           model: "gpt-4o",
           temperature: 0.3,
           timeout: :timer.seconds(180)
         ) do
      {:ok, response} ->
        content =
          response["choices"]
          |> List.first()
          |> Map.get("message")
          |> Map.get("content")
          |> String.trim()

        Logger.info("[AI.OpenAIImpl] Raw OpenAI response for spam detection",
          raw_response: content
        )

        result =
          content
          |> String.split("\n")
          |> Enum.map(&String.split(&1, ":"))
          |> Enum.map(fn [email, status] ->
            {email, string_to_spam_status(String.downcase(String.trim(status)))}
          end)
          |> Map.new()

        {:ok, result}

      {:error, %{"message" => message}} ->
        Logger.error("[AI.OpenAIImpl] OpenAI API error: #{inspect(message)}")
        {:error, "OpenAI API error: #{message}"}

      {:error, error} ->
        Logger.error("[AI.OpenAIImpl] Failed to detect spam: #{inspect(error)}")
        {:error, "Request timeout or network error. Please try again with fewer emails."}
    end
  end

  defp string_to_spam_status("spam"), do: :spam
  defp string_to_spam_status("potential spam"), do: :potential_spam
  defp string_to_spam_status("potential_spam"), do: :potential_spam
  defp string_to_spam_status("potentialspam"), do: :potential_spam
  defp string_to_spam_status("legitimate"), do: nil
  defp string_to_spam_status("legit"), do: nil
  defp string_to_spam_status(_), do: nil
end
