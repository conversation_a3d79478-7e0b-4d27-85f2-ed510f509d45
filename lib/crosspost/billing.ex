defmodule Crosspost.Billing do
  @moduledoc """
  Context for handling billing and subscription-related operations.
  """

  alias Crosspost.Accounts.User
  alias Crosspost.Repo

  def get_subscription(subscription_id) do
    Stripe.Subscription.retrieve(subscription_id, expand: ["items.data.price.product"])
  end

  def get_product_features(product_id) do
    case Stripe.Product.retrieve(product_id) do
      {:ok, product} ->
        features = get_in(product, ["metadata", "features"]) || ""

        features
        |> String.split(",")
        |> Enum.map(&String.trim/1)
        |> Enum.map(&String.to_atom/1)

      _ ->
        []
    end
  end

  def get_customer_subscription(customer_id) do
    case Stripe.Subscription.list(%{customer: customer_id, status: "active", limit: 1}) do
      {:ok, %{data: [subscription | _]}} -> {:ok, subscription}
      {:ok, %{data: []}} -> {:error, :no_subscription}
      error -> error
    end
  end

  def subscription_active?(subscription) do
    subscription.status in ["active", "trialing"]
  end

  def cancel_subscription(%User{} = user) do
    provider = Application.get_env(:crosspost, :billing_provider)

    with %{stripe_subscription_id: subscription_id} when is_binary(subscription_id) <- user.subscription,
         {:ok, _subscription} <- provider.cancel_subscription(subscription_id) do
      user
      |> Ecto.Changeset.change()
      |> Ecto.Changeset.put_embed(:subscription, %{
        status: "canceled",
        period_start: nil,
        period_end: nil,
        stripe_subscription_id: nil
      })
      |> Repo.update()
    else
      nil -> {:error, :no_subscription}
      error -> error
    end
  end
end
