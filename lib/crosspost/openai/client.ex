defmodule Crosspost.OpenAI.Client do
  @moduledoc """
  Simple OpenAI client for making chat completion requests using Req HTTP client.
  """

  @base_url "https://api.openai.com/v1"
  @default_timeout :timer.seconds(60)

  def chat_completion(messages, opts \\ []) do
    model = Keyword.get(opts, :model, "gpt-3.5-turbo")
    temperature = Keyword.get(opts, :temperature, 0.7)
    timeout = Keyword.get(opts, :timeout, @default_timeout)

    body = %{
      model: model,
      messages: messages,
      temperature: temperature
    }

    req =
      Req.new(
        base_url: @base_url,
        auth: {:bearer, api_key()},
        json: body,
        connect_options: [timeout: timeout],
        receive_timeout: timeout
      )

    case Req.post(req, url: "/chat/completions") do
      {:ok, %Req.Response{status: 200, body: body}} ->
        {:ok, body}

      {:ok, %Req.Response{body: %{"error" => error}}} ->
        {:error, error}

      {:error, exception} ->
        {:error, exception}
    end
  end

  def api_key do
    System.get_env("OPENAI_API_KEY") ||
      raise "Environment variable OPENAI_API_KEY is missing"
  end
end
