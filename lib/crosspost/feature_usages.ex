defmodule Crosspost.FeatureUsages do
  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Accounts.{User, Workspace, FeatureUsage}

  def inc_counter(%User{} = user, usage_key) do
    user = Repo.preload(user, :features)
    feature = Enum.find(user.features, &(&1.usage_key == usage_key))

    if feature do
      inc_counter(user, Repo.get!(Workspace, user.default_workspace_id), feature)
    else
      {:error, :feature_not_found}
    end
  end

  def inc_counter(%User{} = user, %Workspace{} = workspace, usage_key)
      when is_binary(usage_key) do
    user = Repo.preload(user, :features)
    feature = Enum.find(user.features, &(&1.usage_key == usage_key))

    inc_counter(user, workspace, feature)
  end

  def inc_counter(%User{} = user, %Workspace{} = workspace, feature) do
    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

    # Create a conflict target for the unique constraint
    conflict_target = [:user_id, :workspace_id, :usage_key]

    # Prepare the base values for insert
    base_values = %{
      user_id: user.id,
      workspace_id: workspace.id,
      feature_key: feature.key,
      usage_key: feature.usage_key,
      type: feature.type,
      value: 1,
      inserted_at: now,
      updated_at: now
    }

    case Repo.insert(
           %FeatureUsage{} |> Ecto.Changeset.change(base_values),
           conflict_target: conflict_target,
           on_conflict: [inc: [value: 1], set: [updated_at: now]]
         ) do
      {:ok, _feature_usage} ->
        {:ok, get_counter(user, workspace, feature.usage_key)}

      {:error, error} ->
        {:error, error}
    end
  end

  def dec_counter(%User{} = user, usage_key) do
    user = Repo.preload(user, :features)
    feature = Enum.find(user.features, &(&1.usage_key == usage_key))

    if feature do
      dec_counter(user, Repo.get!(Workspace, user.default_workspace_id), feature)
    else
      {:error, :feature_not_found}
    end
  end

  def dec_counter(%User{} = user, %Workspace{} = workspace, usage_key)
      when is_binary(usage_key) do
    user = Repo.preload(user, :features)
    feature = Enum.find(user.features, &(&1.usage_key == usage_key))

    dec_counter(user, workspace, feature)
  end

  def dec_counter(%User{} = user, %Workspace{} = workspace, feature) do
    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

    # Create a conflict target for the unique constraint
    conflict_target = [:user_id, :workspace_id, :usage_key]

    # Prepare the base values for insert
    base_values = %{
      user_id: user.id,
      workspace_id: workspace.id,
      feature_key: feature.key,
      usage_key: feature.usage_key,
      type: feature.type,
      value: 0,
      inserted_at: now,
      updated_at: now
    }

    Repo.insert(
      %FeatureUsage{} |> Ecto.Changeset.change(base_values),
      conflict_target: conflict_target,
      on_conflict: [inc: [value: -1], set: [updated_at: now]]
    )
  end

  def for_user(%User{} = user, opts \\ []) do
    query = main_query() |> by_user(user)

    query =
      if usage_key = opts[:key] do
        query |> by_usage_key(usage_key)
      else
        query
      end

    Repo.all(query)
  end

  def get_counter(%User{} = user, usage_key) do
    Repo.one(
      main_query()
      |> by_usage_key(usage_key)
      |> by_user(user)
    )
  end

  def get_counter(%User{} = user, %Workspace{} = workspace, usage_key) do
    Repo.one(
      main_query()
      |> by_usage_key(usage_key)
      |> by_user(user)
      |> by_workspace(workspace)
    )
  end

  def main_query do
    from(fu in FeatureUsage)
  end

  defp by_user(query, user) do
    from fu in query, where: fu.user_id == ^user.id
  end

  defp by_workspace(query, workspace) do
    from fu in query, where: fu.workspace_id == ^workspace.id
  end

  defp by_usage_key(query, key) do
    from fu in query, where: fu.usage_key == ^key
  end

  @doc """
  Resets a feature usage counter to 0 for a specific user and usage key.
  Only resets if the feature is a counter type.
  """
  def reset_usage(%User{} = user, usage_key) when is_binary(usage_key) do
    user = Repo.preload(user, [:enabled_features])

    case Enum.find(user.enabled_features, &(&1.usage_key == usage_key)) do
      %{feature_type: :counter} = _feature ->
        query =
          from fu in FeatureUsage,
            where:
              fu.user_id == ^user.id and
                fu.usage_key == ^usage_key

        case Repo.update_all(query, set: [value: 0]) do
          {1, _} -> {:ok, user}
          {0, _} -> {:error, :not_found}
        end

      _ ->
        {:error, :not_counter_feature}
    end
  end
end
