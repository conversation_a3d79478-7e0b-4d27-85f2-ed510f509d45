defmodule Crosspost.Attachments do
  alias Crosspost.Publishing.{Attachment, ContentAttachment}
  alias Crosspost.Repo

  import Ecto.Query

  def get_attachment(id, user) do
    Attachment
    |> where([a], a.id == ^id and a.user_id == ^user.id)
    |> Repo.one()
  end

  def get_content_attachment(id, content_id) do
    ContentAttachment
    |> where([a], a.id == ^id and a.content_id == ^content_id)
    |> Repo.one()
  end

  def update_attachment(%Attachment{} = attachment, attrs) do
    attachment
    |> Attachment.changeset(attrs)
    |> Repo.update()
  end
end
