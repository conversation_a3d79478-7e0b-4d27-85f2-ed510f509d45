defmodule Crosspost.Media.CloudinaryHelper do
  defmacro __using__(opts) do
    quote do
      def transform_cloudinary_url(nil, _metadata), do: nil

      def transform_cloudinary_url(url, metadata, override_transformations \\ nil)
      def transform_cloudinary_url(nil, _metadata, _override_transformations), do: nil

      def transform_cloudinary_url(url, metadata, override_transformations) when is_binary(url) do
        if String.contains?(url, "cloudinary.com") do
          size = metadata["size"]

          if size > unquote(opts[:image_size_limit]) do
            uri = URI.parse(url)
            path_segments = String.split(uri.path, "/", trim: true)

            # Find both upload and any existing transformation segments
            upload_index = Enum.find_index(path_segments, &(&1 == "upload"))

            if upload_index do
              {before_upload, after_upload} = Enum.split(path_segments, upload_index)
              # Remove any existing transformation segment if present
              after_upload = Enum.drop(after_upload, 1)

              transformations =
                if override_transformations do
                  # Use the override transformations directly
                  override_transformations
                else
                  unquote(opts[:transformations])
                end

              new_path =
                (before_upload ++
                   ["upload", transformations] ++ after_upload)
                |> Enum.join("/")
                |> then(&"/#{&1}")

              %{uri | path: new_path} |> URI.to_string()
            else
              url
            end
          else
            url
          end
        else
          url
        end
      end
    end
  end
end
