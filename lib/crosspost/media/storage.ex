defmodule Crosspost.Media.Storage do
  @callback upload(String.t(), map()) :: {:ok, map()} | {:error, any()}
  @callback delete(String.t()) :: {:ok, map()} | {:error, any()}
  @callback sign(map()) :: map()

  @behaviour __MODULE__

  @impl true
  def upload(path, opts) do
    case Cloudex.upload(path, opts) do
      {:ok, _uploaded_image} = result -> result
      {:error, reason} -> {:error, reason}
    end
  end

  @impl true
  def delete(public_id) do
    case Cloudex.delete(public_id) do
      {:ok, _deleted_image} = result -> result
      {:error, reason} -> {:error, reason}
    end
  end

  @impl true
  def sign(params) do
    Cloudex.CloudinaryApi.sign(params)
  end
end
