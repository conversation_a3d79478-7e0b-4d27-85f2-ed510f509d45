defmodule Crosspost.Features do
  @moduledoc """
  Context for managing feature flags, plans, and user features.
  """

  import Ecto.Query

  alias Crosspost.{Repo, Accounts.Feature, Accounts.Plan, Accounts.UserFeature, Accounts.User}
  alias Crosspost.Workers.FeatureUsageCleanup

  require Logger

  @doc """
  Returns the path to the plans configuration file, handling both dev and release environments.
  """
  def plans_path do
    Application.app_dir(:crosspost, "priv/plans.yml")
  end

  def load_config do
    plans_path()
    |> YamlElixir.read_from_file!()
  end

  @doc """
  Returns all available features as Feature structs from the database.
  """
  def list_features do
    Repo.all(Feature)
  end

  @doc """
  Loads features from plans.yml into the database.
  Returns a tuple with counts of successes and failures.
  """
  def load_features do
    features = load_config() |> Map.get("features", [])

    results =
      features
      |> Enum.map(fn feature ->
        attrs = %{
          key: feature["key"],
          name: feature["name"],
          description: feature["description"],
          usage_key: feature["usage_key"] || feature["resource_counter"],
          type: feature["type"] || "boolean",
          reset_period: feature["reset_period"] || "monthly",
          limit: feature["value"]
        }

        %Feature{}
        |> Feature.changeset(attrs)
        |> Repo.insert!(
          on_conflict: :replace_all,
          conflict_target: :key
        )
      end)

    {:ok, results}
  end

  @doc """
  Returns all available plans with their details
  """
  def list_plans do
    load_config()
    |> Map.get("plans", [])
    |> Enum.map(fn plan ->
      # Each plan in the list has its own key field
      {plan["key"], Map.delete(plan, "key")}
    end)
    |> Map.new()
  end

  @doc """
  Returns a specific plan's details
  """
  def get_plan(nil), do: nil

  def get_plan(plan_code) when is_binary(plan_code) do
    list_plans()
    |> Map.get(plan_code)
  end

  @doc """
  Returns a specific feature as Feature struct from the database
  """
  def get_feature(feature_code) when is_binary(feature_code) do
    Repo.get(Feature, feature_code)
  end

  def get_feature!(usage_key) when is_binary(usage_key) do
    Repo.get_by!(Feature, usage_key: usage_key)
  end

  @doc """
  Returns features available for a specific plan
  """
  def features_for_plan(nil), do: []

  def features_for_plan(%{features: features}) when is_list(features) do
    Repo.all(from(f in Feature, where: f.key in ^features))
  end

  def features_for_plan(%Plan{} = plan) do
    features_for_plan(%{features: plan.features})
  end

  @doc """
  Returns the display name for a feature
  """
  def feature_name(feature_code) when is_binary(feature_code) do
    case get_feature(feature_code) do
      nil -> feature_code
      %Feature{name: name} -> name
    end
  end

  @doc """
  Returns the display name for a plan
  """
  def plan_name(nil), do: "No Plan"

  def plan_name(plan_code) when is_binary(plan_code) do
    case get_plan(plan_code) do
      nil -> plan_code
      plan -> plan["name"]
    end
  end

  def get_feature_for_user!(%{} = user, feature_key) when is_binary(feature_key) do
    from(f in Feature, where: f.key == ^feature_key)
    |> join(:inner, [f], uf in UserFeature, on: uf.feature_key == f.key)
    |> join(:inner, [f, uf], u in assoc(uf, :user))
    |> where([f, uf, u], u.id == ^user.id)
    |> Repo.one!()
  end

  @doc """
  Checks if a feature is enabled for a user.
  """
  def enabled?(%{enabled_features: enabled_features} = _user, key)
      when is_binary(key) and is_list(enabled_features) do
    Enum.any?(enabled_features, &(&1.feature_key == key or &1.usage_key == key))
  end

  def enabled?(%{enabled_features: enabled_features}, usage_key) when is_binary(usage_key) do
    not is_nil(feature_by_usage_key(enabled_features, usage_key))
  end

  @doc """
  Checks if a user has exceeded the limit for a feature.
  Returns false for boolean features or if the feature is not found.
  """
  def limit_reached?(%{enabled_features: enabled_features}, usage_key)
      when is_binary(usage_key) and is_list(enabled_features) do
    case feature_by_usage_key(enabled_features, usage_key) do
      %{limit: -1} ->
        false

      %{feature_type: :boolean} ->
        false

      %{current_usage: usage, limit: limit} when not is_nil(limit) ->
        usage >= limit

      _ ->
        false
    end
  end

  @doc """
  Checks if a user has exceeded the limit for a feature.
  Returns false for boolean features or if the feature is not found.
  """
  def limit_exceeded?(%{enabled_features: enabled_features}, usage_key)
      when is_binary(usage_key) and is_list(enabled_features) do
    case feature_by_usage_key(enabled_features, usage_key) do
      %{limit: -1} ->
        false

      %{feature_type: :boolean} ->
        false

      %{current_usage: usage, limit: limit} when not is_nil(limit) ->
        usage > limit

      _ ->
        false
    end
  end

  @doc """
  Checks if a feature is available for a user.
  Returns false if the feature is not enabled or if its usage limit is exceeded.
  For counter features, the feature remains available when the limit is reached (usage == limit)
  but becomes unavailable when exceeded (usage > limit).
  """
  def available?(%User{} = user, feature_key) when is_binary(feature_key) do
    cond do
      not enabled?(user, feature_key) -> false
      limit_exceeded?(user, feature_key) -> false
      true -> true
    end
  end

  def get_limit(%User{enabled_features: enabled_features}, usage_key) do
    feature_by_usage_key(enabled_features, usage_key) |> Map.get(:limit)
  end

  defp feature_by_usage_key(%User{enabled_features: enabled_features}, usage_key) do
    Enum.find(enabled_features, &(&1.usage_key == usage_key))
  end

  defp feature_by_usage_key(enabled_features, usage_key) do
    Enum.find(enabled_features, &(&1.usage_key == usage_key))
  end

  @doc """
  Schedules cleanup of feature usages for a user.
  This is called when a user is enrolled in a paid plan for the first time.
  Only schedules cleanup for counter-type features.
  """
  def schedule_cleanup(
        %{enabled_features: enabled_features, subscription: subscription} = user,
        opts \\ []
      ) do
    scheduled_at = Keyword.get(opts, :scheduled_at)

    cond do
      is_nil(subscription) ->
        {:ok, user}

      is_nil(subscription.period_end) and is_nil(scheduled_at) ->
        {:ok, user}

      true ->
        counter_features = enabled_features |> Enum.filter(&(&1.feature_type == :counter))
        cleanup_time = scheduled_at || subscription.period_end

        Enum.each(counter_features, fn feature ->
          %{user_id: user.id, usage_key: feature.usage_key}
          |> FeatureUsageCleanup.new(scheduled_at: cleanup_time)
          |> Oban.insert()
        end)

        {:ok, user}
    end
  end
end
