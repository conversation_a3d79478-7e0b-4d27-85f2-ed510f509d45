defmodule Crosspost.Vault do
  use Cloak.Vault, otp_app: :crosspost

  @impl GenServer
  def init(config) do
    key =
      System.get_env("CLOAK_KEY", "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
      |> Base.decode64!()

    config =
      Keyword.put(config, :ciphers, default: {Cloak.Ciphers.AES.GCM, tag: "AES.GCM.V1", key: key})

    {:ok, config}
  end

  def encrypt!(value) when is_binary(value) do
    Cloak.Vault.encrypt!(__MODULE__, value)
  end

  def decrypt!(encrypted) when is_binary(encrypted) do
    Cloak.Vault.decrypt!(__MODULE__, encrypted)
  end
end
