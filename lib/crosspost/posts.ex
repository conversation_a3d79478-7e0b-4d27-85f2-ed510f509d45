defmodule Crosspost.Posts do
  import Ecto.Query

  alias Crosspost.Repo
  alias Crosspost.Publishing.{Post, PostStatus}

  def get_post(id) do
    Repo.get(Post, id)
  end

  def get_post!(id) do
    Repo.get!(Post, id)
  end

  def update_post(post, attrs) do
    post
    |> Post.changeset(attrs)
    |> Repo.update()
  end

  def update_status(post, status) do
    update_post(post, %{status: status})
  end

  def count_user_posts(user, workspace) do
    from(p in Post, where: p.user_id == ^user.id and p.workspace_id == ^workspace.id)
    |> Repo.aggregate(:count)
  end

  def count_user_posts_this_month(user, workspace) do
    start_of_month =
      Timex.now()
      |> Timex.beginning_of_month()
      |> Timex.to_datetime()

    from(p in Post,
      where:
        p.user_id == ^user.id and p.workspace_id == ^workspace.id and
          p.inserted_at >= ^start_of_month
    )
    |> Repo.aggregate(:count)
  end

  def get_daily_activity(user, workspace, days \\ 14) do
    end_date = Timex.now() |> Timex.end_of_day()
    start_date = Timex.shift(end_date, days: -days) |> Timex.beginning_of_day()

    # Get posts grouped by date and network using PostStatus
    posts_by_date_and_network =
      from(p in Post,
        join: s in PostStatus,
        on: s.post_id == p.id,
        where:
          p.user_id == ^user.id and
            p.workspace_id == ^workspace.id and
            p.inserted_at >= ^start_date and
            p.inserted_at <= ^end_date and
            s.status == :published,
        group_by: [fragment("date(?)", p.inserted_at), s.network],
        select: {
          fragment("date(?)", p.inserted_at),
          s.network,
          count(p.id)
        }
      )
      |> Repo.all()
      |> Enum.reduce(%{}, fn {date, network, count}, acc ->
        date_map = Map.get(acc, date, %{})
        # Convert network atom to string to match the chart's expectations
        network_str = Atom.to_string(network)
        Map.put(acc, date, Map.put(date_map, network_str, count))
      end)

    # Generate all dates in range and fill in missing dates/networks with 0
    Timex.Interval.new(from: start_date, until: end_date, step: [days: 1])
    |> Enum.map(fn datetime ->
      date = datetime |> Timex.to_date()
      networks = Map.get(posts_by_date_and_network, date, %{})
      {date, networks}
    end)
  end

  def list_posts_for_sidebar(user, workspace, filter \\ "all", page \\ 1, per_page \\ 10) do
    # Use the new PostOverview schema for better performance
    alias Crosspost.Publishing.PostOverview

    # Get posts with pagination
    {post_overviews, has_more} =
      PostOverview.list_for_sidebar(user, workspace, filter, page, per_page)

    # If we have post overviews, fetch the actual posts with content
    posts =
      if Enum.empty?(post_overviews) do
        []
      else
        post_ids = Enum.map(post_overviews, & &1.id)

        Post
        |> where([p], p.id in ^post_ids)
        |> preload(:content)
        |> Repo.all()
        |> Enum.map(fn post ->
          # Find the corresponding overview to get the next_schedule
          overview = Enum.find(post_overviews, &(&1.id == post.id))
          %{post | next_schedule: overview.next_schedule_at}
        end)
        # Preserve the order from post_overviews
        |> Enum.sort_by(fn post ->
          Enum.find_index(post_ids, &(&1 == post.id)) || 999
        end)
      end

    {posts, has_more}
  end

  @doc """
  Gets the canonical URL for a published post on a specific network.
  Returns nil if the post is not published or if the network doesn't support canonical URLs.
  """
  def get_network_post_url(post, network) do
    case network do
      :x -> get_x_url(post)
      :linkedin -> get_linkedin_url(post)
      :mastodon -> get_mastodon_url(post)
      :bsky -> get_bsky_url(post)
      _ -> nil
    end
  end

  defp get_x_url(post) do
    case Enum.find(post.final_statuses, &(&1.network == :x)) do
      %{status: :published, outcome: %{"root" => id}} when is_binary(id) ->
        "https://x.com/i/web/status/#{id}"

      _ ->
        nil
    end
  end

  defp get_linkedin_url(post) do
    case Enum.find(post.final_statuses, &(&1.network == :linkedin)) do
      %{status: :published, outcome: %{"urn" => urn}} when is_binary(urn) ->
        "https://www.linkedin.com/feed/update/#{urn}"

      _ ->
        nil
    end
  end

  defp get_bsky_url(post) do
    case Enum.find(post.final_statuses, &(&1.network == :bsky)) do
      %{status: :published, outcome: %{"root" => %{"uri" => "at://" <> rest}}} ->
        case String.split(rest, "/") do
          [did, "app.bsky.feed.post", rkey] -> "https://bsky.app/profile/#{did}/post/#{rkey}"
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp get_mastodon_url(post) do
    case Enum.find(post.final_statuses, &(&1.network == :mastodon)) do
      %{status: :published, outcome: %{"url" => url}} when is_binary(url) -> url
      _ -> nil
    end
  end
end
