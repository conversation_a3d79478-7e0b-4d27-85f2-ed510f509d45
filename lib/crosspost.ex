defmodule Crosspost do
  @moduledoc """
  Crosspost keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.
  """

  def broadcast(topic, data) do
    Phoenix.PubSub.broadcast(Crosspost.PubSub, topic, data)
  end

  def subscribe(topic) do
    Phoenix.PubSub.subscribe(Crosspost.PubSub, topic)
  end

  def set_user_context(nil), do: nil

  def set_user_context(user) do
    Logger.metadata(%{user_id: user.id})

    Sentry.Context.set_user_context(%{
      id: user.id,
      email: user.email,
      username: user.name
    })
  end
end
