defmodule Covex do
  # def start() do
  #   Mix.start()
  #   Mix.env(:test)

  #   Code.require_file("test/test_helper.exs")

  #   cover_path = Path.join([:code.lib_dir(), "tools-4.0", "ebin", "cover.beam"])
  #   :code.load_abs(String.to_charlist(Path.rootname(cover_path)))

  #   :cover.start()

  #   _compiled_modules = :cover.compile_beam_directory(~c"_build/test/lib/crosspost/ebin")

  #   :cover.modules()
  # end

  # def stop(covered_modules) do
  #   File.mkdir_p!("cover")
  #   {:ok, lcov_file} = File.open("cover/lcov.info", [:write])

  #   for module <- covered_modules do
  #     source = module.module_info(:compile)[:source]
  #     source_path = if source, do: List.to_string(source), else: "unknown"

  #     IO.puts(lcov_file, "TN:")
  #     IO.puts(lcov_file, "SF:#{source_path}")

  #     function_coverage =
  #       case :cover.analyse(module, :coverage, :function) do
  #         {:ok, functions} ->
  #           Enum.map(functions, fn
  #             {{m, f, a}, {calls, _}} -> {"#{m}.#{f}/#{a}", calls}
  #             {{f, a}, {calls, _}} -> {"#{f}/#{a}", calls}
  #           end)

  #         {:error, _reason} ->
  #           []
  #       end

  #     for {func_name, _calls} <- function_coverage do
  #       IO.puts(lcov_file, "FN:1,#{func_name}")
  #     end

  #     for {func_name, calls} <- function_coverage do
  #       IO.puts(lcov_file, "FNDA:#{calls},#{func_name}")
  #     end

  #     total_functions = length(function_coverage)
  #     covered_functions = Enum.count(function_coverage, fn {_, calls} -> calls > 0 end)
  #     IO.puts(lcov_file, "FNF:#{total_functions}")
  #     IO.puts(lcov_file, "FNH:#{covered_functions}")

  #     case :cover.analyse(module, :coverage, :line) do
  #       {:ok, lines} ->
  #         {covered_lines, total_lines} =
  #           Enum.reduce(lines, {0, 0}, fn {{_, line_num}, {cov, _}}, {covered, total} ->
  #             IO.puts(lcov_file, "DA:#{line_num},#{cov}")
  #             {covered + cov, total + 1}
  #           end)

  #         IO.puts(lcov_file, "LF:#{total_lines}")
  #         IO.puts(lcov_file, "LH:#{covered_lines}")
  #         IO.puts(lcov_file, "end_of_record")

  #       {:error, _reason} ->
  #         nil
  #     end
  #   end

  #   File.close(lcov_file)
  #   :cover.stop()
  # end
end
