<div class="p-4">
  <div class="mb-6">
    <h2 class="text-2xl font-bold mb-4">Invitation Codes</h2>
    <button phx-click="generate_invitation" class="bg-blue-500 text-white px-4 py-2 rounded">
      Generate New Invitation
    </button>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full bg-white">
      <thead>
        <tr class="bg-gray-100">
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Code
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Created At
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Used At
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Status
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <%= for invitation <- @invitations do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              {invitation.invitation_code}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {Calendar.strftime(invitation.inserted_at, "%Y-%m-%d %H:%M:%S")}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= if invitation.used_at do %>
                {Calendar.strftime(invitation.used_at, "%Y-%m-%d %H:%M:%S")}
              <% else %>
                -
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= if invitation.used_at do %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                  Used
                </span>
              <% else %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  Available
                </span>
              <% end %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
