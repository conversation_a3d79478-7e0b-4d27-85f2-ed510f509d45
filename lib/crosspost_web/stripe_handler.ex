defmodule CrosspostWeb.StripeHandler do
  @behaviour Stripe.WebhookHandler
  require Logger

  alias Crosspost.{Repo, Accounts, Features}
  alias Crosspost.Accounts.User

  @impl true
  def handle_event(%Stripe.Event{type: "customer.subscription.updated"} = event) do
    subscription = event.data.object

    Logger.metadata(
      event_type: event.type,
      stripe_customer_id: subscription.customer
    )

    Logger.info("Processing subscription update event", %{
      event: "stripe.subscription.updated",
      subscription_id: subscription.id,
      status: subscription.status,
      period_start: subscription.current_period_start,
      period_end: subscription.current_period_end
    })

    case Accounts.get_user_by_stripe_customer_id(subscription.customer) do
      %User{} = user ->
        Logger.metadata(user_id: user.id)
        new_period_end = DateTime.from_unix!(subscription.current_period_end)
        should_schedule_cleanup = user.subscription.period_end != new_period_end

        # Determine the new status based on the event
        new_status =
          case subscription.status do
            "active" -> :active
            "trialing" -> :trial
            "canceled" -> :canceled
            "past_due" -> :past_due
            "unpaid" -> :unpaid
            # Treat expired as canceled
            "incomplete_expired" -> :canceled
            # Treat incomplete as past_due for now
            "incomplete" -> :past_due
            # Keep existing status for unknown Stripe statuses
            _ -> user.status
          end

        Logger.info("Found user for subscription update", %{
          event: "stripe.subscription.user_found",
          current_period_end: user.subscription.period_end,
          new_period_end: new_period_end,
          should_schedule_cleanup: should_schedule_cleanup
        })

        # If the new status means the user is a customer, clear trial_end
        user_update_attrs =
          case new_status do
            status when status in [:active, :past_due] -> %{status: new_status, trial_end: nil}
            _ -> %{status: new_status}
          end

        with {:ok, user_with_updated_sub} <-
               Accounts.update_user_subscription(user, %{
                 period_start: DateTime.from_unix!(subscription.current_period_start),
                 period_end: new_period_end,
                 status: subscription.status
               }),
             {:ok, updated_user} <-
               Accounts.update_user(user_with_updated_sub, user_update_attrs) do
          # Logic for success (scheduling cleanup, logging, returning {:ok, event})
          if should_schedule_cleanup do
            # Preload enabled_features before scheduling cleanup
            updated_user_preloaded =
              Repo.preload(updated_user, [:enabled_features, :usage_counters])

            Features.schedule_cleanup(updated_user_preloaded)
          end

          Logger.info("Successfully updated subscription and status", %{
            event: "stripe.subscription.updated_success",
            status: subscription.status,
            user_status: new_status,
            cleanup_scheduled: should_schedule_cleanup
          })

          {:ok, event}
        else
          # Handle errors from either step
          {:error, error} ->
            Logger.error("Failed during subscription update process", %{
              event: "stripe.subscription.update_failed",
              error: inspect(error)
            })

            {:error, error}
        end

      nil ->
        Logger.warning("No user found for subscription update", %{
          event: "stripe.subscription.user_not_found"
        })

        {:error, :user_not_found}
    end
  end

  @impl true
  def handle_event(%Stripe.Event{type: "checkout.session.completed"} = event) do
    Logger.metadata(event_type: event.type)

    Logger.info("Processing checkout completed event", %{
      event: "stripe.checkout.completed",
      session_id: event.data.object.id
    })

    # TODO: Implement your order fulfillment logic here
    # - Update user subscription status
    # - Send confirmation email
    # - etc.

    {:ok, event}
  end

  @impl true
  def handle_event(event) do
    Logger.metadata(event_type: event.type)

    Logger.info("Received unhandled Stripe event", %{
      event: "stripe.event.unhandled"
    })

    :ok
  end
end
