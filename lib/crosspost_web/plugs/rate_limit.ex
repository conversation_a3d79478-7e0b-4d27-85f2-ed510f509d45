defmodule CrosspostWeb.Plugs.RateLimit do
  use PlugAttack

  import Plug.Conn

  def storage do
    {PlugAttack.Storage.Ets, Crosspost.PlugAttack.Storage}
  end

  def form_limit do
    Application.get_env(:crosspost, :plug_attack_form_limit, 20)
  end

  def general_limit do
    Application.get_env(:crosspost, :plug_attack_general_limit, 300)
  end

  rule "limit form submissions", conn do
    if conn.method in ["POST", "PUT", "PATCH"] and
         !String.starts_with?(conn.request_path, "/api/") and
         !String.starts_with?(conn.request_path, "/webhook/") do
      throttle(conn.remote_ip,
        period: 60_000,
        limit: form_limit(),
        storage: storage()
      )
    end
  end

  rule "limit general requests", conn do
    throttle(conn.remote_ip,
      period: 60_000,
      limit: general_limit(),
      storage: storage()
    )
  end

  def allow_action(conn, {:throttle, data}, _opts) do
    conn
    |> add_rate_limit_headers(data)
    |> allow_request()
  end

  def block_action(conn, {:throttle, data}, _opts) do
    conn
    |> add_rate_limit_headers(data)
    |> block_request()
  end

  defp add_rate_limit_headers(conn, data) do
    data = Enum.into(data, %{})

    conn
    |> put_resp_header("x-ratelimit-limit", to_string(data.limit))
    |> put_resp_header("x-ratelimit-remaining", to_string(data.remaining))
    |> put_resp_header("x-ratelimit-reset", to_string(data.expires_at))
  end

  defp block_request(conn) do
    conn
    |> put_resp_header("content-type", "application/json")
    |> send_resp(429, Jason.encode!(%{error: "Too many requests. Please try again later."}))
    |> halt()
  end

  defp allow_request(conn), do: conn
end
