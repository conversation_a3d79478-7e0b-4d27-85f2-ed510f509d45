defmodule CrosspostWeb.Plugs.CheckSubscription do
  @moduledoc """
  Plug to check if a user has an active subscription or trial.
  Redirects to plans page if the user's access has expired.
  """
  import Plug.Conn
  import Phoenix.Controller

  alias CrosspostWeb.Router.Helpers, as: Routes
  alias Crosspost.Accounts

  def init(opts), do: opts

  def call(conn, _opts) do
    user = conn.assigns[:current_user]

    cond do
      is_nil(user) ->
        conn

      true ->
        case Accounts.is_user_active?(user) do
          {:ok, :active} ->
            conn

          {:error, :trial_ended} ->
            if user.subscription.status == "active" do
              {:ok, _user} = Accounts.update_user_subscription(user, %{status: "trial_ended"})
            end

            conn
            |> redirect(to: Routes.page_path(conn, :plans, trial_end: true))
            |> halt()

          {:error, :subscription_ended} ->
            conn
            |> redirect(to: Routes.page_path(conn, :plans, subscription_ended: true))
            |> halt()

          _ ->
            conn
            |> redirect(to: Routes.page_path(conn, :plans))
            |> halt()
        end
    end
  end
end
