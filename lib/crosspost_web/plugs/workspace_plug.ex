defmodule CrosspostWeb.Plugs.WorkspacePlug do
  import Plug.Conn
  import Phoenix.Controller

  alias Crosspost.{Repo, Workspaces, Accounts}

  require Logger

  def init(opts), do: opts

  def call(conn, _opts) do
    user = conn.assigns.current_user

    if user && user.confirmed_at do
      workspace_id = get_session(conn, :workspace_id)

      # Ensure user has required associations loaded
      user = Repo.preload(user, [:plan, :enabled_features, :default_workspace])

      cond do
        # If workspace_id in session matches user's default, use that
        workspace_id && user.default_workspace_id && workspace_id == user.default_workspace_id ->
          user = Accounts.get_user_with_workspace_connections(user, user.default_workspace_id)

          conn
          |> assign(:current_user, user)
          |> assign(:current_workspace, user.default_workspace)
          |> put_session(:workspace_id, user.default_workspace_id)

        # If different workspace_id in session, verify access
        workspace_id ->
          case Workspaces.resolve_workspace(user, workspace_id) do
            {:ok, workspace} ->
              user = Accounts.get_user_with_workspace_connections(user, workspace.id)

              conn
              |> assign(:current_user, user)
              |> assign(:current_workspace, workspace)
              |> put_session(:workspace_id, workspace.id)

            {:error, _} ->
              use_default_workspace(conn, user)
          end

        # No workspace_id in session, use default
        true ->
          use_default_workspace(conn, user)
      end
    else
      conn |> assign(:current_workspace, nil)
    end
  end

  defp use_default_workspace(conn, user) do
    if user.default_workspace do
      user = Accounts.get_user_with_workspace_connections(user, user.default_workspace.id)

      conn
      |> assign(:current_user, user)
      |> assign(:current_workspace, user.default_workspace)
      |> put_session(:workspace_id, user.default_workspace.id)
    else
      raise "User #{user.id} has no default workspace"
    end
  end

  def assign_workspace(conn, workspace_id) do
    user = Repo.preload(conn.assigns.current_user, [:plan, :enabled_features, :default_workspace])

    case Workspaces.resolve_workspace(user, workspace_id) do
      {:ok, workspace} ->
        user = Accounts.get_user_with_workspace_connections(user, workspace.id)

        conn
        |> assign(:current_user, user)
        |> assign(:current_workspace, workspace)
        |> put_session(:workspace_id, workspace.id)

      {:error, _} ->
        conn
        |> put_flash(:error, "Failed to load workspace")
        |> redirect(to: "/dashboard")
        |> halt()
    end
  end
end
