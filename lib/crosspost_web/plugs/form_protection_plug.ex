defmodule CrosspostWeb.Plugs.FormProtectionPlug do
  import Plug.Conn
  import Phoenix.Controller

  alias Crosspost.Security.FormProtection

  def init(opts), do: opts

  def call(conn, _opts) do
    if should_validate?(conn) do
      validate_form_submission(conn)
    else
      conn
    end
  end

  defp should_validate?(conn) do
    Application.get_env(:crosspost, :env) != :test and
      conn.method in ["POST", "PUT", "PATCH"] and
      !String.starts_with?(conn.request_path, "/api/") and
      !String.starts_with?(conn.request_path, "/checkout") and
      !String.starts_with?(conn.request_path, "/subscription/update")
  end

  defp validate_form_submission(conn) do
    with true <- FormProtection.valid_honeypot?(conn.params),
         token when not is_nil(token) <- Map.get(conn.params, "form_token"),
         :ok <- FormProtection.verify_form_token(token) do
      conn
    else
      false ->
        conn
        |> redirect(to: "/")
        |> halt()

      nil ->
        conn
        |> redirect(to: "/")
        |> halt()

      {:error, :too_fast} ->
        conn
        |> redirect(to: "/")
        |> halt()

      {:error, :expired} ->
        conn
        |> redirect(to: "/")
        |> halt()

      {:error, :invalid} ->
        conn
        |> redirect(to: "/")
        |> halt()
    end
  end
end
