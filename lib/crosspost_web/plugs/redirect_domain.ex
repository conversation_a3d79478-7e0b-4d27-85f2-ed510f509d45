defmodule CrosspostWeb.Plugs.RedirectDomain do
  import Plug.Conn
  import Phoenix.Controller

  def init(opts), do: opts

  def call(conn, _opts) do
    case get_req_header(conn, "host") do
      ["crosspost.fly.dev"] ->
        conn
        |> put_status(301)
        |> redirect(
          external: "https://justcrosspost.app#{conn.request_path}#{query_string(conn)}"
        )
        |> halt()

      _ ->
        conn
    end
  end

  defp query_string(conn) do
    if conn.query_string != "", do: "?#{conn.query_string}", else: ""
  end
end
