defmodule CrosspostWeb.AdminComponents do
  use Phoenix.Component
  use CrosspostWeb, :verified_routes

  import CrosspostWeb.CoreComponents
  import Flop.Phoenix

  attr :email, :string, default: ""
  attr :name, :string, default: ""
  attr :created_after, :string, default: ""
  attr :created_before, :string, default: ""
  attr :user_id, :string, default: ""
  attr :on_filter, :string, default: "filter"
  attr :on_reset, :string, default: "reset-filter"
  attr :on_search, :string, default: "search"
  attr :status, :any, default: []

  def users_filter_form(assigns) do
    # Convert status to list if it's not already
    assigns =
      assign(
        assigns,
        :selected_statuses,
        cond do
          is_list(assigns.status) -> assigns.status
          is_binary(assigns.status) && assigns.status != "" -> [assigns.status]
          true -> []
        end
      )

    ~H"""
    <div class="bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <.form for={%{}} phx-change="filter" phx-submit="filter" class="space-y-6">
          <div class="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-4">
            <div class="sm:col-span-2">
              <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
              <div class="mt-1">
                <input
                  type="text"
                  name="email"
                  id="email"
                  value={@email}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Filter by email"
                />
              </div>
            </div>

            <div class="sm:col-span-2">
              <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
              <div class="mt-1">
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={@name}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Filter by name"
                />
              </div>
            </div>

            <div class="sm:col-span-2">
              <label for="created_after" class="block text-sm font-medium text-gray-700">
                Created After
              </label>
              <div class="mt-1">
                <input
                  type="datetime-local"
                  name="created_after"
                  id="created_after"
                  value={@created_after}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div class="sm:col-span-2">
              <label for="created_before" class="block text-sm font-medium text-gray-700">
                Created Before
              </label>
              <div class="mt-1">
                <input
                  type="datetime-local"
                  name="created_before"
                  id="created_before"
                  value={@created_before}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          <div class="flex flex-wrap gap-2 pt-2">
            <input type="hidden" name="status[]" value="" />
            <%= for status <- @selected_statuses do %>
              <input type="hidden" name="status[]" value={status} />
            <% end %>

            <button
              type="button"
              phx-click="toggle_status"
              phx-value-status=""
              class={[
                "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                if(@selected_statuses == [],
                  do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
                  else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                )
              ]}
            >
              All Status
            </button>
            <button
              type="button"
              phx-click="toggle_status"
              phx-value-status="customer"
              class={[
                "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                if("customer" in @selected_statuses,
                  do: "bg-green-50 text-green-700 ring-green-600/20",
                  else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                )
              ]}
            >
              Customer
            </button>
            <button
              type="button"
              phx-click="toggle_status"
              phx-value-status="trial"
              class={[
                "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                if("trial" in @selected_statuses,
                  do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                  else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                )
              ]}
            >
              Trial
            </button>
            <button
              type="button"
              phx-click="toggle_status"
              phx-value-status="trial_ended"
              class={[
                "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                if("trial_ended" in @selected_statuses,
                  do: "bg-yellow-50 text-yellow-700 ring-yellow-600/20",
                  else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                )
              ]}
            >
              Trial Ended
            </button>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              type="button"
              phx-click={@on_reset}
              class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              Reset
            </button>
            <button
              type="submit"
              class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              Filter
            </button>
          </div>
        </.form>
      </div>
    </div>
    """
  end

  attr :user_id, :string, default: ""
  attr :status, :string, default: ""
  attr :network, :list, default: []
  attr :user_options, :list, default: []
  attr :post_id, :string, default: ""
  attr :final_status_date, :string, default: ""
  attr :on_filter, :string, default: "filter"
  attr :on_reset, :string, default: "reset-filter"
  attr :on_search, :string, default: "search"

  def posts_filter_form(assigns) do
    # Convert status string to list if it's not already
    assigns =
      assign(
        assigns,
        :selected_statuses,
        cond do
          is_list(assigns.status) -> assigns.status
          is_binary(assigns.status) && assigns.status != "" -> [assigns.status]
          true -> []
        end
      )

    # Convert network to list if it's not already
    assigns =
      assign(
        assigns,
        :selected_networks,
        cond do
          is_list(assigns.network) -> assigns.network
          is_binary(assigns.network) && assigns.network != "" -> [assigns.network]
          true -> []
        end
      )

    ~H"""
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2">
      <div class="px-4 py-6 sm:p-8">
        <div class="max-w-full space-y-6">
          <.form for={%{}} phx-change="filter" phx-submit="filter" class="space-y-4">
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-3">
              <div>
                <label for="post_id" class="block text-sm font-medium leading-6 text-gray-900">
                  ID
                </label>
                <div class="mt-2">
                  <.input type="text" name="post_id" value={@post_id} placeholder="Filter by ID" />
                </div>
              </div>

              <div>
                <label for="user_id" class="block text-sm font-medium leading-6 text-gray-900">
                  User
                </label>
                <div class="mt-2">
                  <select
                    id="user_id"
                    name="user_id"
                    value={@user_id}
                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  >
                    <option value="">All Users</option>
                    <%= for [key: label, value: value] <- @user_options do %>
                      <option value={value} selected={@user_id == value}>{label}</option>
                    <% end %>
                  </select>
                </div>
              </div>

              <div class="flex items-end">
                <div class="flex gap-2">
                  <.button type="submit">Filter</.button>
                  <.button type="button" phx-click={@on_reset} variant="secondary">Reset</.button>
                </div>
              </div>
            </div>

            <div class="flex flex-col gap-4">
              <div class="flex flex-wrap gap-2 pt-2">
                <input type="hidden" name="status[]" value="" />
                <%= for status <- @selected_statuses do %>
                  <input type="hidden" name="status[]" value={status} />
                <% end %>

                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status=""
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@selected_statuses == [],
                      do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  All Status
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="draft"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("draft" in @selected_statuses,
                      do: "bg-gray-50 text-gray-700 ring-gray-700/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Draft
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="pending"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("pending" in @selected_statuses,
                      do: "bg-yellow-50 text-yellow-700 ring-yellow-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Pending
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="scheduled"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("scheduled" in @selected_statuses,
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Scheduled
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="publishing"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("publishing" in @selected_statuses,
                      do: "bg-purple-50 text-purple-700 ring-purple-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Publishing
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="partially_published"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("partially_published" in @selected_statuses,
                      do: "bg-orange-50 text-orange-700 ring-orange-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Partially Published
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="published"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("published" in @selected_statuses,
                      do: "bg-green-50 text-green-700 ring-green-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Published
                </button>
                <button
                  type="button"
                  phx-click="toggle_status"
                  phx-value-status="failed"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("failed" in @selected_statuses,
                      do: "bg-red-50 text-red-700 ring-red-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Failed
                </button>
              </div>

              <div class="flex flex-wrap gap-2 pt-2">
                <input type="hidden" name="network[]" value="" />
                <%= for network <- @selected_networks do %>
                  <input type="hidden" name="network[]" value={network} />
                <% end %>

                <button
                  type="button"
                  phx-click="toggle_network"
                  phx-value-network=""
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@selected_networks == [],
                      do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  All Networks
                </button>
                <button
                  type="button"
                  phx-click="toggle_network"
                  phx-value-network="x"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("x" in @selected_networks,
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  X
                </button>
                <button
                  type="button"
                  phx-click="toggle_network"
                  phx-value-network="linkedin"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("linkedin" in @selected_networks,
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  LinkedIn
                </button>
                <button
                  type="button"
                  phx-click="toggle_network"
                  phx-value-network="mastodon"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("mastodon" in @selected_networks,
                      do: "bg-purple-50 text-purple-700 ring-purple-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Mastodon
                </button>
                <button
                  type="button"
                  phx-click="toggle_network"
                  phx-value-network="bsky"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if("bsky" in @selected_networks,
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Bluesky
                </button>
              </div>

              <div class="flex flex-wrap gap-2 pt-2">
                <input type="hidden" name="final_status_date" value={@final_status_date} />

                <button
                  type="button"
                  phx-click="toggle_date"
                  phx-value-date=""
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@final_status_date == "",
                      do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  All Time
                </button>
                <button
                  type="button"
                  phx-click="toggle_date"
                  phx-value-date="today"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@final_status_date == "today",
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Today
                </button>
                <button
                  type="button"
                  phx-click="toggle_date"
                  phx-value-date="yesterday"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@final_status_date == "yesterday",
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Yesterday
                </button>
                <button
                  type="button"
                  phx-click="toggle_date"
                  phx-value-date="this_week"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@final_status_date == "this_week",
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  This Week
                </button>
                <button
                  type="button"
                  phx-click="toggle_date"
                  phx-value-date="last_30_days"
                  class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
                    if(@final_status_date == "last_30_days",
                      do: "bg-blue-50 text-blue-700 ring-blue-600/20",
                      else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
                    )
                  ]}
                >
                  Last 30 Days
                </button>
              </div>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end

  attr :fields, :list, required: true
  attr :meta, Flop.Meta, required: true
  attr :form, Phoenix.HTML.Form, required: true
  attr :id, :string, default: nil
  attr :on_change, :string, default: "update-filter"
  attr :target, :string, default: nil

  def filter_form(assigns) do
    assigns = assign(assigns, :grouped_fields, group_fields(assigns.fields))

    ~H"""
    <.form for={@form} class="mt-8">
      <div class="flex flex-col gap-4">
        <div :for={{_group_name, fields} <- @grouped_fields} class="flex items-end gap-4">
          <div class="flex gap-4 flex-1">
            <.filter_fields :let={i} form={@form} fields={fields}>
              <div class={[
                i.type != "datetime-local" && "flex-1",
                i.type == "datetime-local" && "w-64"
              ]}>
                <.input
                  field={i.field}
                  type={i.type}
                  {i.rest}
                  label={if i.type == "datetime-local", do: i.label}
                  placeholder={if i.type != "datetime-local", do: i.label}
                />
              </div>
            </.filter_fields>
          </div>
        </div>

        <div class="flex justify-end mt-4">
          <div class="space-x-2">
            <.button type="submit" phx-disable-with="Filtering...">
              Filter
            </.button>
            <button
              type="button"
              class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
              phx-click={@on_change}
              phx-value-reset="true"
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>
    </.form>
    """
  end

  defp group_fields(fields) do
    fields
    |> Enum.group_by(
      fn {_field, opts} -> Keyword.get(opts, :group) end,
      fn {field, opts} -> {field, opts} end
    )
    |> Enum.sort_by(fn {group, _fields} -> if is_nil(group), do: "", else: group end)
  end

  attr :path, :string, required: true
  attr :meta, Flop.Meta, required: true

  def pagination_links(assigns) do
    ~H"""
    <div class="admin-pagination">
      <.pagination meta={@meta} path={@path} opts={[page_links: {:ellipsis, 6}]} />
    </div>
    """
  end

  attr :tabs, :list,
    required: true,
    doc: "List of maps, each with :key, :label, :count (optional)"

  attr :active_tab, :string, required: true, doc: "The key of the currently active tab"

  attr :on_toggle, :string,
    required: true,
    doc: "The phx-click event name to trigger on tab click"

  def tabs(assigns) do
    ~H"""
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <%= for tab <- @tabs do %>
          <button
            type="button"
            phx-click={@on_toggle}
            phx-value-tab={tab.key}
            class={[
              "whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium flex items-center gap-2",
              if(tab.key == @active_tab,
                do: "border-indigo-500 text-indigo-600",
                else: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
              )
            ]}
            aria-current={if tab.key == @active_tab, do: "page", else: nil}
          >
            <span>{tab.label}</span>
            <%= if tab[:count] do %>
              <span class={[
                "ml-1 hidden rounded-full py-0.5 px-2.5 text-xs font-medium md:inline-block",
                if(tab.key == @active_tab,
                  do: "bg-indigo-100 text-indigo-600",
                  else: "bg-gray-100 text-gray-900"
                )
              ]}>
                {tab.count}
              </span>
            <% end %>
          </button>
        <% end %>
      </nav>
    </div>
    """
  end
end
