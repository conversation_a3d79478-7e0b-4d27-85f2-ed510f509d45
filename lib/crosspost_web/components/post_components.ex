defmodule CrosspostWeb.PostComponents do
  use CrosspostWeb, :html

  import CrosspostWeb.DatetimeHelpers

  def friendly_schedule_time(%{schedules: schedules} = _post, timezone)
      when is_list(schedules) and length(schedules) > 0 do
    scheduled_at =
      schedules
      |> Enum.map(& &1.scheduled_at)
      |> Enum.min_by(&DateTime.to_unix/1, fn -> nil end)

    format_datetime(scheduled_at, timezone)
  end

  def friendly_schedule_time(nil, _timezone), do: "Not set"

  def friendly_schedule_time(datetime, timezone) when is_struct(datetime, DateTime) do
    format_datetime(datetime, timezone)
  end

  attr :post, :map, required: true

  def publish_status_badges(assigns) do
    ~H"""
    <div class="flex flex-wrap gap-2">
      <%= for network <- @post.social_networks do %>
        <div class={publish_status_badge_class(network_status(@post, network))}>
          <.network_icon network={network} status={network_status(@post, network)} />
          <span class="ml-1">{humanize_status(network_status(@post, network))}</span>
        </div>
      <% end %>
    </div>
    """
  end

  # Add this helper to convert status atoms to human-readable strings
  def humanize_status(status) do
    case status do
      :publishing -> "Publishing"
      :published -> "Published"
      :failed -> "Failed"
      :retrying -> "Retrying"
      :draft -> "Draft"
      :media_uploading -> "Media Uploading"
      :media_uploaded -> "Media Uploaded"
      status when is_binary(status) -> Phoenix.Naming.humanize(status)
      _ -> "Unknown"
    end
  end

  attr :network, :atom, required: true
  attr :status, :atom, required: true
  attr :animate, :boolean, default: true

  def network_icon(assigns) do
    ~H"""
    <div
      class={network_icon_classes(@network, @status, animate: @animate)}
      data-network={@network}
      data-status={@status}
    >
      {raw(network_icon_svg(@network))}
    </div>
    """
  end

  def network_icon_classes(network, status, opts \\ []) do
    animate = Keyword.get(opts, :animate, true)
    base_classes = "relative w-5 h-5 flex items-center justify-center"

    color_classes =
      case status do
        :published ->
          network_color_class(network)

        :failed ->
          "text-red-500"

        status when status in [:publishing, :retrying] ->
          "text-gray-400#{animate and " animate-pulse"}"

        # draft or any other state
        _ ->
          "text-gray-400"
      end

    "#{base_classes} #{color_classes}"
  end

  def publish_status_badge_class(status) do
    base_class = "px-3 py-1 rounded-full text-sm font-semibold flex items-center"

    case status do
      :publishing -> "#{base_class} bg-purple-100 text-purple-800"
      :retrying -> "#{base_class} bg-purple-100 text-purple-800"
      :scheduled -> "#{base_class} bg-purple-100 text-purple-800"
      :published -> "#{base_class} bg-green-100 text-brand-white-600"
      :failed -> "#{base_class} bg-red-100 text-red-800"
      _ -> "#{base_class} bg-gray-100 text-gray-800"
    end
  end

  def network_color_class(:linkedin), do: "text-blue-600"
  def network_color_class(:x), do: "text-black"
  def network_color_class(:bsky), do: "text-blue-400"
  def network_color_class(:mastodon), do: "text-purple-600"

  def network_status(post, network) do
    case post.post_statuses do
      statuses ->
        case Enum.find(statuses, &(&1.network == network and &1.is_final == true)) do
          %{status: status} ->
            status

          nil ->
            case post.status do
              "draft" -> :draft
              "pending" -> :publishing
              _ -> :publishing
            end
        end
    end
  end

  def network_icon_svg(:linkedin) do
    ~s(<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" /></svg>)
  end

  def network_icon_svg(:x) do
    ~s(<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" /></svg>)
  end

  def network_icon_svg(:bsky) do
    ~s(<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 600 530"><path d="m135.72 44.03c66.496 49.921 138.02 151.14 164.28 205.46 26.262-54.316 97.782-155.54 164.28-205.46 47.98-36.021 125.72-63.892 125.72 24.795 0 17.712-10.155 148.79-16.111 170.07-20.703 73.984-96.144 92.854-163.25 81.433 117.3 19.964 147.14 86.092 82.697 152.22-122.39 125.59-175.91-31.511-189.63-71.766-2.514-7.3797-3.6904-10.832-3.7077-7.8964-0.0174-2.9357-1.1937 0.51669-3.7077 7.8964-13.714 40.255-67.233 197.36-189.63 71.766-64.444-66.128-34.605-132.26 82.697-152.22-67.108 11.421-142.55-7.4491-163.25-81.433-5.9562-21.282-16.111-152.36-16.111-170.07 0-88.687 77.742-60.816 125.72-24.795z" /></svg>)
  end

  def network_icon_svg(:mastodon) do
    ~s(<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M23.268 5.313c-.35-2.578-2.617-4.61-5.304-5.004C17.51.242 15.792 0 11.813 0h-.03c-3.98 0-4.835.242-5.288.309C3.882.692 1.496 2.518.917 5.127.64 6.412.61 7.837.661 9.143c.074 1.874.088 3.745.26 5.611.118 1.24.325 2.47.62 3.68.55 2.237 2.777 4.098 4.96 4.857 2.336.792 4.849.923 7.256.38.265-.061.527-.132.786-.213.585-.184 1.27-.39 1.774-.753a.057.057 0 0 0 .023-.043v-1.809a.052.052 0 0 0-.02-.041.053.053 0 0 0-.046-.01 20.282 20.282 0 0 1-4.709.545c-2.73 0-3.463-1.284-3.674-1.818a5.593 5.593 0 0 1-.319-1.433.053.053 0 0 1 .066-.054c1.517.363 3.072.546 4.632.546.376 0 .75 0 1.125-.01 1.57-.044 3.224-.124 4.768-.422.038-.008.077-.015.11-.024 2.435-.464 4.753-1.92 4.989-5.604.008-.145.03-1.52.03-1.67.002-.512.167-3.63-.024-5.545zm-3.748 9.195h-2.561V8.29c0-1.309-.55-1.976-1.67-1.976-1.23 0-1.846.79-1.846 2.35v3.403h-2.546V8.663c0-1.56-.617-2.35-1.848-2.35-1.112 0-1.668.668-1.67 1.977v6.218H4.822V8.102c0-1.31.337-2.35 1.011-3.12.696-.77 1.608-1.164 2.74-1.164 1.311 0 2.302.5 2.962 1.498l.638 1.06.638-1.06c.66-.999 1.65-1.498 2.96-1.498 1.13 0 2.043.395 2.74 1.164.675.77 1.012 1.81 1.012 3.12z" /></svg>)
  end
end
