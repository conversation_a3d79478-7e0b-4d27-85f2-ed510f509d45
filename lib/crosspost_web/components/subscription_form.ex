defmodule CrosspostWeb.Components.SubscriptionForm do
  use CrosspostWeb, :html

  @doc """
  Renders a subscription form with a submit button.

  ## Examples
      <.subscription_form
        plan={plan}
        highlight={plan.highlight}
        cta={plan.cta}
      />
  """
  attr :plan, :map, required: true
  attr :highlight, :boolean, default: false
  attr :cta, :string, default: "Get started"

  def subscription_form(assigns) do
    ~H"""
    <form action={~p"/checkout"} method="post" class="mt-6">
      <input type="hidden" name="price_id" value={@plan.stripe_price_id} />
      <input type="hidden" name="_csrf_token" value={get_csrf_token()} />
      <button
        type="submit"
        class={[
          "w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2",
          @highlight &&
            "bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600",
          !@highlight &&
            "bg-white text-indigo-600 ring-1 ring-inset ring-indigo-200 hover:ring-indigo-300"
        ]}
      >
        {@cta}
      </button>
    </form>
    """
  end
end
