defmodule CrosspostWeb.Components.UserRegistrationForm do
  use CrosspostWeb, :live_component

  alias Crosspost.Accounts
  alias Crosspost.Accounts.User
  alias CrosspostWeb.Live.Components.MastodonAuthButtonComponent

  require Logger

  attr :error, :string, default: "Please fix errors below."

  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div>
        <.live_component module={MastodonAuthButtonComponent} id="mastodon-signup" intent="sign-up" />
      </div>

      <div>
        <.link
          href={~p"/auth/linkedin?intent=sign-up"}
          class="flex w-full items-center justify-center gap-3 rounded-md bg-brand-network-linkedin px-3 py-2 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-linkedin"
        >
          <svg
            class="h-5 w-5"
            fill="currentColor"
            role="img"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
          </svg>
          <span class="text-sm font-semibold leading-6">Continue with LinkedIn</span>
        </.link>
      </div>

      <div>
        <.link
          href={~p"/auth/x?intent=sign-up"}
          class="flex w-full items-center justify-center gap-3 rounded-md bg-brand-network-x px-3 py-2 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-x"
        >
          <svg
            class="h-5 w-5"
            fill="currentColor"
            role="img"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
          </svg>
          <span class="text-sm font-semibold leading-6">Continue with X</span>
        </.link>
      </div>

      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="bg-white/60 px-2 text-gray-500">Or continue with</span>
        </div>
      </div>

      <.simple_form
        for={@form}
        id="registration_form"
        phx-submit="save"
        phx-change="validate"
        phx-target={@myself}
        phx-trigger-action={@trigger_submit}
        class="space-y-6 text-left"
      >
        <.live_component module={CrosspostWeb.FormProtectionComponent} id="form-protection" />
        <.error :if={@check_errors}>
          {@error}
        </.error>

        <.input field={@form[:email]} type="email" placeholder="Email" tabindex="1" required />
        <.input field={@form[:password]} type="password" placeholder="Password" tabindex="2" required />

        <:actions>
          <.button tabindex="3" phx-disable-with="Creating account..." class="w-full">
            Create account <span aria-hidden="true">→</span>
          </.button>
        </:actions>
      </.simple_form>

      <div class="mt-8 text-center text-sm text-gray-600">
        <p class="mb-2">
          Already have an account?
        </p>
        <.link navigate={~p"/users/sign-in"} class="font-medium text-blue-600 hover:text-blue-500">
          Sign in
        </.link>
      </div>
    </div>
    """
  end

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:trigger_submit, fn -> false end)
     |> assign_new(:check_errors, fn -> false end)
     |> assign_new(:form, fn ->
       to_form(User.registration_changeset(%User{}, %{}, validate_email: true))
     end)}
  end

  def handle_event("save", %{"user" => user_params}, socket) do
    case Accounts.register_and_enroll_user(user_params) do
      {:ok, user} ->
        case Accounts.deliver_user_confirmation_instructions(
               user,
               &url(~p"/users/confirm/#{&1}")
             ) do
          {:ok, _} ->
            send(
              self(),
              {:registration_success,
               %{
                 flash:
                   "Account created successfully. Please check your email for confirmation instructions.",
                 redirect_to: ~p"/users/sign-in"
               }}
            )

            {:noreply, socket}

          {:error, _reason} ->
            send(
              self(),
              {:registration_success,
               %{
                 flash:
                   "Account created successfully. There was an issue sending the confirmation email - please contact support if you don't receive it within a few minutes.",
                 redirect_to: ~p"/users/sign-in"
               }}
            )

            {:noreply, socket}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, socket |> assign(check_errors: true) |> assign_form(changeset)}
    end
  rescue
    error ->
      Logger.error("User registration failed",
        error: inspect(error),
        event: "user_registration.failed"
      )

      {:noreply,
       socket
       |> assign(check_errors: true, error: "Unexpected error happpened. Please try again later.")}
  end

  def handle_event("validate", %{"user" => user_params}, socket) do
    changeset = User.registration_changeset(%User{}, user_params, validate_email: true)
    {:noreply, assign_form(socket, Map.put(changeset, :action, :validate))}
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    form = to_form(changeset, as: "user")

    if changeset.valid? do
      assign(socket, form: form, check_errors: false)
    else
      assign(socket, form: form)
    end
  end
end
