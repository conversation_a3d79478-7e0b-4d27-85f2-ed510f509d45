defmodule CrosspostWeb.Components.FeatureUpgradeModal do
  use Phoenix.Component

  import CrosspostWeb.CoreComponents
  import CrosspostWeb.Components.SubscriptionForm

  alias Crosspost.Features

  attr :id, :string, required: true
  attr :feature_key, :string, required: true
  attr :current_user, :map, required: true
  attr :plan, :map, required: true
  attr :on_cancel, :any, required: true

  def feature_upgrade_modal(assigns) do
    ~H"""
    <.modal id={@id} on_cancel={@on_cancel}>
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Upgrade Required
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          {Features.feature_name(@feature_key)} feature is not available on your current plan. Upgrade to a higher plan to access this feature.
        </p>

        <.subscription_form plan={@plan} highlight={true} cta={"Upgrade to #{@plan.name}"} />
      </div>
    </.modal>
    """
  end
end
