defmodule CrosspostWeb.Layouts.CookieConsentComponent do
  use CrosspostWeb, :html

  def cookie_consent(assigns) do
    ~H"""
    <div
      id="cookie-consent-banner"
      class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 transform translate-y-full transition-transform duration-300 ease-in-out z-50"
      data-cookie-consent
    >
      <div class="container mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
        <div class="flex-grow">
          <p class="text-sm">
            We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.
            Learn more in our <.link href={~p"/privacy"} class="underline hover:text-blue-300">Privacy Policy</.link>.
          </p>
        </div>
        <div class="flex gap-4">
          <button
            type="button"
            class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
            data-cookie-decline
          >
            Decline
          </button>
          <button
            type="button"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded text-sm"
            data-cookie-accept
          >
            Accept
          </button>
        </div>
      </div>
    </div>
    """
  end
end
