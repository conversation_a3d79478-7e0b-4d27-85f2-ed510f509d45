<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <.head
      current_user={@current_user}
      current_workspace={@current_workspace}
      conn={@conn}
      page_title={assigns[:page_title]}
    />

    <%= if @current_user && Application.get_env(:crosspost, :env, :dev) == :prod do %>
      <.featurebase_scripts current_user={@current_user} />
    <% end %>
  </head>

  <body
    class="h-full flex flex-col"
    id="page-body"
    {if @current_user do
      %{
        "data-user-channel" => "user:#{@current_user.id}",
        "data-user-id" => @current_user.id
      }
    else
      %{}
    end}
  >
    <.user_header
      conn={@conn}
      current_user={@current_user}
      current_workspace={@current_workspace}
    />

    <main class="flex-1 overflow-y-auto">
      {@inner_content}
    </main>

    <footer>
      <.user_footer />
    </footer>

    <.cookie_consent />
  </body>
</html>
