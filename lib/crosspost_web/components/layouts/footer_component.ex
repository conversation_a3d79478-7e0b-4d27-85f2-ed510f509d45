defmodule CrosspostWeb.Layouts.FooterComponent do
  use CrosspostWeb, :html

  def user_footer(assigns) do
    ~H"""
    <footer class="bg-gray-800 text-white">
      <div class="container mx-auto px-4 py-4">
        <div class="flex justify-center md:justify-end items-center text-sm text-gray-400">
          <div class="flex space-x-4">
            <.link href={~p"/plans"} class="hover:text-white">Plans</.link>
            <.link href={~p"/about"} class="hover:text-white">About</.link>
            <.link href={~p"/privacy"} class="hover:text-white">Privacy</.link>
            <.link href={~p"/tos"} class="hover:text-white">Terms</.link>
            <.link href={~p"/contact"} class="hover:text-white">Contact</.link>
          </div>
        </div>
      </div>
    </footer>
    """
  end

  def public_footer(assigns) do
    ~H"""
    <footer class="bg-gray-800 text-white">
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="space-y-4">
            <h3 class="text-xl font-semibold">JustCrossPost</h3>
            <p class="text-sm text-gray-300">
              JustCrossPost simplifies sharing your content across multiple platforms. Post once, share everywhere - efficiently and effectively.
            </p>
          </div>
          <div class="space-y-4">
            <h3 class="text-xl font-semibold">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <.link
                  href={~p"/"}
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Home
                </.link>
              </li>
              <li>
                <.link
                  href={~p"/about"}
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  About
                </.link>
              </li>
              <li>
                <.link
                  href={~p"/tos"}
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Terms of Service
                </.link>
              </li>
              <li>
                <.link
                  href={~p"/privacy"}
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Privacy Policy
                </.link>
              </li>
              <li>
                <.link
                  href={~p"/contact"}
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Contact
                </.link>
              </li>
            </ul>
          </div>
          <div class="space-y-4">
            <h3 class="text-xl font-semibold">Connect With Us</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="https://bsky.app/profile/justcrosspost.app"
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Bluesky
                </a>
              </li>
              <li>
                <a
                  href="https://www.linkedin.com/company/justcrosspost"
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  LinkedIn
                </a>
              </li>
              <li>
                <a
                  href="https://mastodon.social/justcrosspost"
                  rel="me"
                  class="text-sm text-gray-300 hover:text-white transition duration-150 ease-in-out"
                >
                  Mastodon
                </a>
              </li>
            </ul>
          </div>
        </div>
        <!-- Footer copyright section -->
        <div class="mt-12 pt-8 border-t border-gray-700 text-center pb-8">
          <p class="text-sm text-gray-300">
            &copy; {DateTime.utc_now().year} JustCrossPost. All rights reserved.
          </p>
          <p class="text-sm text-gray-300 mt-2">
            Built with 💜 by
            <a
              href="https://solnic.dev"
              class="text-[#9089fc] hover:text-[#ff80b5] transition duration-150 ease-in-out"
            >
              @solnic.dev
            </a>
          </p>
        </div>
      </div>
    </footer>
    """
  end
end
