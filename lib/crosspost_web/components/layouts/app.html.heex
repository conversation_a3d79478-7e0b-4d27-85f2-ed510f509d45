<div class="flash-container" id="flash-container" phx-hook="Flash">
  <.flash_group flash={@flash} />
</div>

<div id="main-container" class="flex h-full bg-white dark:bg-gray-900">
  <!-- Sidebar -->
  <div
    id="sidebar-container"
    class="fixed md:static flex-shrink-0 inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out w-80 h-full -translate-x-full md:translate-x-0"
    style="height: 100vh;"
  >
    <!-- Backdrop for mobile -->
    <div
      id="sidebar-backdrop"
      class="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity md:hidden hidden"
    />

<!-- Sidebar content -->
    <div class="relative h-full z-50 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- Sidebar header -->
      <div class="md:hidden flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Posts</h2>
        <button
          type="button"
          id="mobile-sidebar-close"
          class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
        >
          <span class="sr-only">Close sidebar</span>
          <svg
            class="h-6 w-6"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

<!-- Sidebar main content -->
      <div class="flex-1 flex flex-col min-h-0 h-full">
        <.live_component
          module={CrosspostWeb.PostLive.SidebarComponent}
          id="sidebar"
          current_user={@current_user}
          current_workspace={@current_workspace}
          current_post_id={assigns[:current_post_id]}
          selected_filter={assigns[:selected_filter] || "all"}
        />
      </div>
    </div>
  </div>

<!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <%= if @payment_past_due do %>
      <div class="w-full bg-yellow-50 dark:bg-yellow-900/50 border-b border-yellow-400 text-yellow-800 dark:text-yellow-200 text-center py-2 text-sm flex items-center justify-center gap-2">
        <span class="inline-flex items-center">
          <svg
            class="h-5 w-5 text-yellow-400 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0z"
            />
          </svg>
          <span>
            Your payment is past due. Please
            <a href={Crosspost.Accounts.billing_portal_url(@current_user) || "https://billing.stripe.com/p/login"} target="_blank" class="underline font-medium">
              update your payment information
            </a>
            to avoid interruption.
          </span>
        </span>
      </div>
    <% end %>
    {@inner_content}
  </div>
</div>
