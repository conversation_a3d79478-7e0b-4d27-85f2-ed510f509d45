defmodule CrosspostWeb.Layouts.HeadComponent do
  use CrosspostWeb, :html

  alias Crosspost.Accounts

  def head(assigns) do
    ~H"""
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <!-- Primary Meta Tags -->
    <meta name="title" content={assigns[:page_title] || "JustCrossPost · Home"} />
    <meta
      name="description"
      content="Share your content across multiple social networks and other platforms effortlessly. Post at once or schedule posting to X (Twitter), Bluesky, Mastodon, and more - all from one place. Save time and reach more audiences with our powerful cross-posting platform."
    />
    <meta
      name="keywords"
      content="social media management, cross-posting, cross-posting app, social media automation, X (Twitter), Bluesky, Mastodon, content management, social media scheduler, multi-platform posting, social media tools"
    />
    <meta name="author" content="JustCrossPost" />
    <meta name="robots" content="index, follow" />
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content={"https://justcrosspost.app#{Phoenix.Controller.current_path(@conn)}"}
    />
    <meta property="og:title" content={assigns[:page_title] || "JustCrossPost · Home"} />
    <meta
      property="og:description"
      content="Share your content across multiple social networks effortlessly. Post at once or schedule posting to X (Twitter), Bluesky, Mastodon, and more - all from one place. Save time and reach more audiences with our powerful cross-posting platform."
    />
    <meta property="og:image" content="https://justcrosspost.app/images/og-image.png" />
    <meta property="og:site_name" content="JustCrossPost" />
    <meta property="og:locale" content="en_US" />
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:url"
      content={"https://justcrosspost.app#{Phoenix.Controller.current_path(@conn)}"}
    />
    <meta name="twitter:title" content={assigns[:page_title] || "JustCrossPost · Home"} />
    <meta
      name="twitter:description"
      content="Share your content across multiple social networks effortlessly. Post at once or schedule posting to X (Twitter), Bluesky, Mastodon, and more - all from one place. Save time and reach more audiences with our powerful cross-posting platform."
    />
    <meta name="twitter:image" content="https://justcrosspost.app/images/og-image.png" />
    <meta name="twitter:creator" content="@justcrosspost" />
    <meta name="twitter:site" content="@justcrosspost" />
    <!-- Canonical URL -->
    <link rel="canonical" href={"https://justcrosspost.app#{Phoenix.Controller.current_path(@conn)}"} />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Russo+One&amp;display=swap" rel="stylesheet" />

    <.live_title prefix="JustCrossPost · ">
      {assigns[:page_title] || "Home"}
    </.live_title>

    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="https://justcrosspost.app/images/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="https://justcrosspost.app/images/favicon-16x16.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="https://justcrosspost.app/images/apple-touch-icon.png"
    />
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />

    <script defer phx-track-static type="text/javascript" src={~p"/assets/public.js"}>
    </script>

    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>

    <%= if @current_user do %>
      <script defer type="text/javascript">
        document.addEventListener('DOMContentLoaded', () => JCP.initialize(<%= raw user_data(@current_user, @current_workspace) %>));
      </script>
    <% end %>

    <%= if Application.get_env(:crosspost, :env, :dev) == :prod do %>
      <script defer data-domain="justcrosspost.app" src="https://plausible.io/js/script.js">
      </script>
    <% end %>
    """
  end

  def featurebase_scripts(assigns) do
    ~H"""
    <!-- Import Featurebase SDK -->
    <script>
      !(function (e, t) {
        const a = "featurebase-sdk";
        function n() {
          if (!t.getElementById(a)) {
            var e = t.createElement("script");
            (e.id = a),
            (e.src = "https://do.featurebase.app/js/sdk.js"),
            t.getElementsByTagName("script")[0].parentNode.insertBefore(
              e,
              t.getElementsByTagName("script")[0]
            );
          }
        }
        "function" != typeof e.Featurebase &&
          (e.Featurebase = function () {
            (e.Featurebase.q = e.Featurebase.q || []).push(arguments);
          }),
        "complete" === t.readyState || "interactive" === t.readyState
          ? n()
          : t.addEventListener("DOMContentLoaded", n);
      })(window, document);
    </script>

    <!-- Identify user in Featurebase -->
    <script>
      Featurebase(
        "identify",
        {
          organization: "justcrosspost",
          email: "<%= @current_user.email %>",
          name: "<%= @current_user.name || @current_user.email %>",
          userId: "<%= @current_user.uid %>",
          userHash: "<%= "jcp_user_#{@current_user.uid}" %>",
          profilePicture: "<%= "https://www.gravatar.com/avatar/#{:crypto.hash(:md5, @current_user.email) |> Base.encode16(case: :lower)}?d=mp" %>",
          locale: "en"
        },
        (err) => {
          if (err) {
            console.error(err);
          } else {
            console.log("Data sent successfully!");
          }
        }
      );
    </script>

    <script>
      Featurebase("initialize_portal_widget", {
          organization: 'justcrosspost',
          placement: 'left',
          fullScreen: false,
          initialPage: 'MainView',
          locale: "en"
      });
    </script>
    """
  end

  defp user_data(current_user, current_workspace) do
    user =
      Accounts.get_workspace_user!(current_user.id, current_workspace.id)

    Jason.encode!(user)
  end
end
