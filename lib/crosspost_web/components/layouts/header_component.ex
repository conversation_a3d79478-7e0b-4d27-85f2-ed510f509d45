defmodule CrosspostWeb.Layouts.HeaderComponent do
  use CrosspostWeb, :html

  import CrosspostWeb.Components.Navigation.{MainNav, UserMenu, MobileUserMenu}

  def user_header(assigns) do
    ~H"""
    <header class="px-4 sm:px-6 lg:px-8 bg-gray-800">
      <div class="flex items-center justify-between py-3 text-sm">
        <div class="flex-1 flex justify-center md:justify-start">
          <div class="flex items-center gap-4">
            <button
              type="button"
              id="mobile-sidebar-toggle"
              class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-white hover:text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              data-mobile-sidebar-toggle
            >
              <span class="sr-only">Toggle sidebar</span>
              <svg
                class="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <%= if Phoenix.Controller.current_path(@conn) != "/" do %>
              <a href="/" class="flex items-center">
                <img
                  src={~p"/images/logo-lm.png"}
                  class="h-6 md:h-8 w-auto mr-2 md:mr-3 light-mode-logo"
                />
                <img src={~p"/images/logo.png"} class="h-6 md:h-8 w-auto mr-2 md:mr-3 dark-mode-logo" />
                <span class="text-lg md:text-xl font-russo text-brand-white-600">
                  justcrosspost<span class="text-blue-700">.</span>app
                </span>
              </a>
            <% end %>
          </div>
        </div>

        <div class="flex items-center">
          <.main_nav current_user={@current_user} />
          <.user_menu
            current_user={@current_user}
            current_workspace={@current_workspace}
            current_path={Phoenix.Controller.current_path(@conn)}
          />
          <.mobile_user_menu
            current_user={@current_user}
            current_workspace={@current_workspace}
            current_path={Phoenix.Controller.current_path(@conn)}
          />
        </div>
      </div>
    </header>
    """
  end

  def public_header(assigns) do
    ~H"""
    <header class={[
      "px-4 sm:px-6 lg:px-8"
    ]}>
      <div class="flex items-center justify-between py-3 text-sm">
        <div class="flex-1 flex justify-center md:justify-start">
          <div class="flex items-center gap-4">
            <%= if Phoenix.Controller.current_path(@conn) != "/" do %>
              <a href="/" class="flex items-center">
                <img
                  src={~p"/images/logo-lm.png"}
                  class="h-6 md:h-8 w-auto mr-2 md:mr-3 light-mode-logo"
                />
                <img src={~p"/images/logo.png"} class="h-6 md:h-8 w-auto mr-2 md:mr-3 dark-mode-logo" />
                <span class={[
                  "text-lg md:text-xl font-russo",
                  "text-gray-900 dark:text-brand-white-600"
                ]}>
                  justcrosspost<span class="text-blue-700">.</span>app
                </span>
              </a>
            <% end %>
          </div>
        </div>

        <div class="flex items-center">
          <.main_nav current_user={@current_user} />

          <div class="hidden lg:flex items-center">
            <.link
              :if={!@current_user}
              href={~p"/users/sign-in"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Sign in
            </.link>
            <.link
              :if={@current_user}
              href={~p"/dashboard"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Dashboard
            </.link>
          </div>
        </div>
      </div>
    </header>
    """
  end
end
