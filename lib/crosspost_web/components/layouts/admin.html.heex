<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-100">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title suffix=" · Crosspost Admin">
      {assigns[:page_title] || "Dashboard"}
    </.live_title>
    <script defer phx-track-static type="text/javascript" src={~p"/assets/admin.js"}>
    </script>
    <link phx-track-static rel="stylesheet" href={~p"/assets/admin.css"} />
  </head>
  <body class="h-full">
    <div class="min-h-full">
      <header class="admin-header">
        <div class="admin-header-content">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <h1 class="text-2xl font-bold text-white">
                  <.link href={~p"/admin"}>Crosspost Admin</.link>
                </h1>
              </div>
              <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-4">
                  <.link
                    href={~p"/admin/users"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Users
                  </.link>
                  <.link
                    href={~p"/admin/workspaces"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Workspaces
                  </.link>
                  <.link
                    href={~p"/admin/posts"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Posts
                  </.link>
                  <.link
                    href={~p"/admin/attachments"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Attachments
                  </.link>
                  <.link
                    href={~p"/admin/features"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Features
                  </.link>
                  <.link
                    href={~p"/admin/plans"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Plans
                  </.link>
                  <.link
                    href={~p"/admin/waitlist"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Waitlist
                  </.link>
                  <.link
                    href={~p"/admin/jobs"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Jobs
                  </.link>
                  <.link
                    href={~p"/admin/marketing"}
                    class="text-white hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium"
                  >
                    Marketing
                  </.link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main class="admin-main">
        <div class="admin-main-content">
          <div class="admin-content p-4">
            {@inner_content}
          </div>
        </div>
      </main>
    </div>
  </body>
</html>
