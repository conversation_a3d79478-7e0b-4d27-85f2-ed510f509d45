defmodule CrosspostWeb.FormProtectionComponent do
  use CrosspostWeb, :live_component

  alias Crosspost.Security.FormProtection

  def render(assigns) do
    ~H"""
    <div>
      <.input type="hidden" name="form_token" value={FormProtection.generate_form_token()} />
      <div class="hidden">
        <%!-- Honeypot field - should remain empty --%>
        <.input type="text" name="website" value="" autocomplete="off" />
      </div>
    </div>
    """
  end
end
