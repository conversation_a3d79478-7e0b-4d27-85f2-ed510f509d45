defmodule CrosspostWeb.StatusBadgeComponent do
  use Phoenix.Component

  attr :status, :string, required: true
  attr :class, :string, default: nil

  def status_badge(assigns) do
    ~H"""
    <span
      data-badge="status"
      class={[
        "px-2 py-1 rounded-full text-sm font-semibold",
        @class,
        status_color(@status)
      ]}
    >
      {format_status(@status)}
    </span>
    """
  end

  defp status_color(status) do
    case status do
      "draft" -> "bg-gray-100 text-gray-800"
      "scheduled" -> "bg-yellow-100 text-yellow-800"
      "published" -> "bg-green-100 text-brand-white-600"
      "partially_published" -> "bg-blue-100 text-blue-800"
      "failed" -> "bg-red-100 text-red-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp format_status(status) do
    case status do
      "partially_published" -> "Partially published"
      _ -> status
    end
    |> String.capitalize()
  end
end
