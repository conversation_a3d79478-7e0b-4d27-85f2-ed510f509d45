defmodule CrosspostWeb.Components.PlanChangeForm do
  use CrosspostWeb, :html

  @doc """
  Renders a form for changing subscription plans (upgrade/downgrade).

  ## Examples
      <.plan_change_form
        plan={%{stripe_price_id: "price_123"}}
        highlight={true}
        cta="Upgrade to Pro"
      />
  """
  attr :plan, :map, required: true
  attr :highlight, :boolean, default: false
  attr :cta, :string, required: true

  def plan_change_form(assigns) do
    ~H"""
    <form action={~p"/subscription/update"} method="post">
      <input type="hidden" name="price_id" value={@plan.stripe_price_id} />
      <input type="hidden" name="_csrf_token" value={get_csrf_token()} />
      <button
        type="submit"
        data-confirm={confirmation_text(@plan)}
        class={[
          "w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2",
          @highlight &&
            "bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600",
          !@highlight &&
            "bg-white text-indigo-600 ring-1 ring-inset ring-indigo-200 hover:ring-indigo-300 focus-visible:outline-indigo-600"
        ]}
      >
        {@cta}
      </button>
    </form>
    """
  end

  def confirmation_text(plan) do
    "Are you sure you want to change to the #{plan.name} plan? Your subscription will be updated immediately."
  end
end
