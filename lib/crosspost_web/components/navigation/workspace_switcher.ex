defmodule CrosspostWeb.Components.Navigation.WorkspaceSwitcher do
  use CrosspostWeb, :html

  alias Crosspost.{Workspaces, Features}

  attr :id, :string, default: "workspace-switcher"
  attr :current_user, :map, required: true
  attr :current_workspace, :map, required: true
  attr :current_path, :string, required: true

  def workspace_switcher(assigns) do
    ~H"""
    <div class="relative" id={@id} phx-hook="WorkspaceSwitcher">
      <button
        id={"#{@id}-button"}
        type="button"
        class="flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 min-w-[140px]"
      >
        <.icon name="hero-building-office-2" class="h-4 w-4 mr-1.5 shrink-0" />
        <span class="truncate max-w-[150px]" id={"#{@id}-current-name"}>
          {display_workspace_name(@current_workspace)}
        </span>
        <.icon name="hero-chevron-down-mini" class="h-4 w-4 ml-1 text-white shrink-0" />
      </button>
      <div
        id={"#{@id}-dropdown"}
        class="dropdown-menu absolute left-0 z-10 mt-2 w-56 origin-top-left rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu"
        aria-orientation="vertical"
        aria-labelledby={"#{@id}-button"}
        tabindex="-1"
      >
        <div class="py-1" role="none">
          <%= for workspace <- Workspaces.list_user_workspaces(@current_user) do %>
            <.link
              href={get_workspace_path(workspace.slug, @current_path)}
              class={[
                "flex items-center px-4 py-2 text-sm",
                if(workspace.id == @current_workspace.id,
                  do: "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white",
                  else: "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                )
              ]}
              role="menuitem"
              data-feature={if(workspace.is_default, do: nil, else: "workspaces")}
              data-feature-enabled={
                if(workspace.is_default,
                  do: nil,
                  else: to_string(Features.available?(@current_user, "workspaces"))
                )
              }
            >
              <%= if workspace.id == @current_workspace.id do %>
                <.icon name="hero-check-mini" class="mr-2 h-4 w-4" />
              <% else %>
                <div class="w-4 mr-2"></div>
              <% end %>
              {display_workspace_name(workspace)}
              <%= if workspace.is_default do %>
                <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(Default)</span>
              <% end %>
            </.link>
          <% end %>

          <div class="border-t border-gray-100 dark:border-gray-700 my-1"></div>

          <.link
            navigate={~p"/settings/workspaces"}
            class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            role="menuitem"
          >
            <.icon name="hero-cog-6-tooth-mini" class="mr-2 h-4 w-4" /> Manage workspaces
          </.link>
        </div>
      </div>
    </div>
    """
  end

  defp display_workspace_name(workspace) do
    if workspace.is_default, do: "Personal", else: workspace.name
  end

  defp get_workspace_path(slug, current_path) do
    "/workspace/#{slug}?return_to=#{current_path}"
  end
end
