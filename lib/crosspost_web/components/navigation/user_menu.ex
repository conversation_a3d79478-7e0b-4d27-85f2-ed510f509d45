defmodule CrosspostWeb.Components.Navigation.UserMenu do
  use CrosspostWeb, :html

  alias Crosspost.Features
  alias Crosspost.Accounts
  import CrosspostWeb.Components.Navigation.WorkspaceSwitcher

  attr :current_user, :map, required: true
  attr :current_workspace, :map, required: true
  attr :current_path, :string, required: true

  def user_menu(assigns) do
    ~H"""
    <div class="flex items-center space-x-3">
      <div class="hidden lg:flex items-center space-x-3">
        <.link navigate={~p"/dashboard"}>
          <button class="flex items-center justify-center px-3 py-1.5 border border-transparent text-sm rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-300">
            <.icon name="hero-squares-2x2-mini" class="h-4 w-4 mr-1.5" /> Dashboard
          </button>
        </.link>
        <.link navigate={~p"/calendar"}>
          <button class="flex items-center justify-center px-3 py-1.5 border border-transparent text-sm rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-300">
            <.icon name="hero-calendar" class="h-4 w-4 mr-1.5" /> Calendar
          </button>
        </.link>
        <.workspace_switcher
          :if={Features.enabled?(@current_user, "workspaces")}
          id="desktop-workspace-switcher"
          current_user={@current_user}
          current_workspace={@current_workspace}
          current_path={@current_path}
        />
      </div>

      <div class="relative" id="desktop-user-menu" phx-hook="UserMenu">
        <button
          id="desktop-avatar-button"
          class="flex items-center text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition duration-150 ease-in-out"
          aria-expanded="false"
          aria-haspopup="true"
        >
          <%= if @current_user.email do %>
            <img
              class="h-10 w-10 rounded-full"
              src={user_avatar_url(@current_user)}
              alt={@current_user.email}
            />
          <% else %>
            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
              <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          <% end %>
        </button>

        <div
          id="desktop-dropdown-menu"
          class="absolute right-0 w-48 mt-2 py-2 bg-white dark:bg-gray-800 rounded-md shadow-xl ring-1 ring-black ring-opacity-5 z-20 hidden"
        >
          <.link
            href={~p"/settings"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <.icon name="hero-cog-6-tooth" class="h-4 w-4 mr-1.5 inline-block align-text-bottom" />
            Settings
          </.link>
          <%= if billing_url = Accounts.billing_portal_url(@current_user) do %>
            <.link
              href={billing_url}
              target="_blank"
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <.icon name="hero-credit-card" class="h-4 w-4 mr-1.5 inline-block align-text-bottom" />
              Billing Portal
            </.link>
          <% end %>
          <.link
            href={~p"/plans"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Plans
          </.link>
          <div class="border-t border-gray-100 dark:border-gray-700 my-1"></div>
          <.link
            href={~p"/about"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            About
          </.link>
          <.link
            href={~p"/privacy"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Privacy
          </.link>
          <.link
            href={~p"/tos"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Terms
          </.link>
          <.link
            href={~p"/contact"}
            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Contact
          </.link>
          <div class="border-t border-gray-100 dark:border-gray-700 my-1"></div>
          <.link
            href={~p"/users/sign-out"}
            class="block px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-gray-700 hover:text-red-700 dark:hover:text-red-300"
          >
            Log out
          </.link>
        </div>
      </div>
    </div>
    """
  end

  def user_avatar_url(user) do
    "https://www.gravatar.com/avatar/#{:crypto.hash(:md5, user.email) |> Base.encode16(case: :lower)}?d=mp"
  end
end
