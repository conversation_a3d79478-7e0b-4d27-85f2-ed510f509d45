defmodule CrosspostWeb.Components.Navigation.MobileUserMenu do
  use CrosspostWeb, :html

  alias Crosspost.Features
  import CrosspostWeb.Components.Navigation.WorkspaceSwitcher

  attr :current_user, :map, required: true
  attr :current_workspace, :map, required: true
  attr :current_path, :string, required: true

  def mobile_user_menu(assigns) do
    ~H"""
    <div id="mobile-menu-container" class="fixed inset-0 z-50 hidden lg:hidden" phx-hook="MobileMenu">
      <!-- Background backdrop -->
      <div class="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity duration-300"></div>
      <!-- Slide-out panel -->
      <div class="fixed inset-y-0 right-0 max-w-xs w-full bg-gray-800 shadow-xl transform transition-transform duration-300 ease-in-out translate-x-full">
        <div class="flex items-center justify-between p-4 border-b border-gray-700">
          <div class="text-xl font-bold text-white">Menu</div>
          <button
            id="close-mobile-menu"
            class="rounded-md text-gray-400 hover:text-white focus:ring-2 focus:ring-white"
          >
            <span class="sr-only">Close menu</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div class="px-4 pt-4 pb-6 space-y-6">
          <div class="space-y-4">
            <.link
              navigate={~p"/dashboard"}
              class="flex items-center px-4 py-2 text-base font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Dashboard
            </.link>
            <.link
              navigate={~p"/calendar"}
              class="flex items-center px-4 py-2 text-base font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              Calendar
            </.link>
          </div>

          <div :if={Features.enabled?(@current_user, "workspaces")} class="space-y-4">
            <.workspace_switcher
              id="mobile-workspace-switcher"
              current_user={@current_user}
              current_workspace={@current_workspace}
              current_path={@current_path}
            />
          </div>

          <div class="border-t border-gray-700 pt-4">
            <div class="flex items-center px-4">
              <%= if @current_user.email do %>
                <img
                  class="h-10 w-10 rounded-full"
                  src={"https://www.gravatar.com/avatar/#{:crypto.hash(:md5, @current_user.email) |> Base.encode16(case: :lower)}?d=mp"}
                  alt={@current_user.email}
                />
              <% else %>
                <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              <% end %>
              <div class="ml-3">
                <div class="text-base font-medium text-white">{@current_user.email}</div>
              </div>
            </div>

            <div class="mt-3 space-y-1">
              <.link
                href={~p"/settings"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                Settings
              </.link>
              <div class="border-t border-gray-700 my-1"></div>
              <.link
                href={~p"/plans"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                Plans
              </.link>
              <.link
                href={~p"/about"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                About
              </.link>
              <.link
                href={~p"/privacy"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                Privacy
              </.link>
              <.link
                href={~p"/tos"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                Terms
              </.link>
              <.link
                href={~p"/contact"}
                class="block px-4 py-2 text-base font-medium text-white hover:bg-gray-700"
              >
                Contact
              </.link>
              <div class="border-t border-gray-700 my-1"></div>
              <.link
                href={~p"/users/sign-out"}
                class="block px-4 py-2 text-base font-medium text-red-400 hover:bg-gray-700"
              >
                Sign out
              </.link>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
