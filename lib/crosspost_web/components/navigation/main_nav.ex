defmodule CrosspostWeb.Components.Navigation.MainNav do
  use CrosspostWeb, :html

  def main_nav(assigns) do
    ~H"""
    <nav class={[
      "hidden lg:flex items-center ml-auto mr-8",
      if(assigns[:current_user], do: "", else: "main-nav-light")
    ]}>
      <div class="flex items-center space-x-6">
        <%= unless @current_user do %>
          <.link href={~p"/plans"} class="text-base font-medium transition duration-150 ease-in-out">
            Plans
          </.link>
          <.link href={~p"/about"} class="text-base font-medium transition duration-150 ease-in-out">
            About
          </.link>
          <.link href={~p"/contact"} class="text-base font-medium transition duration-150 ease-in-out">
            Contact
          </.link>
          <.link href={~p"/privacy"} class="text-base font-medium transition duration-150 ease-in-out">
            Privacy
          </.link>
          <.link href={~p"/tos"} class="text-base font-medium transition duration-150 ease-in-out">
            Terms
          </.link>
        <% end %>
        <button
          type="button"
          id="theme-toggle"
          class="flex items-center gap-2 p-2 rounded-md"
          phx-hook="ThemeToggle"
        >
          <!-- Sun icon for light mode -->
          <svg class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
          <!-- Moon icon for dark mode -->
          <svg class="w-5 h-5 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        </button>
      </div>
    </nav>
    """
  end
end
