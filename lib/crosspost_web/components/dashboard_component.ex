defmodule CrosspostWeb.DashboardComponent do
  use CrosspostWeb, :live_component

  alias CrosspostWeb.Components.SocialNetworkIcons

  @networks ["bsky", "linkedin", "mastodon", "x"]

  def update(assigns, socket) do
    daily_activity =
      Crosspost.Posts.get_daily_activity(assigns.current_user, assigns.current_workspace)

    {dates, _} = Enum.unzip(daily_activity)
    formatted_dates = Enum.map(dates, &format_date/1)

    datasets =
      @networks
      |> Enum.map(fn network ->
        %{
          label: network,
          data:
            Enum.map(daily_activity, fn {_date, networks} ->
              Map.get(networks, network, 0)
            end),
          backgroundColor: get_network_color(network),
          borderColor: get_network_color(network),
          borderWidth: 1,
          fill: true
        }
      end)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:activity_dates, Jason.encode!(formatted_dates))
     |> assign(:activity_datasets, Jason.encode!(datasets))}
  end

  def render(assigns) do
    ~H"""
    <div class="space-y-8 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div class="text-gray-500 text-sm font-medium mb-2">Total Posts</div>
          <div class="text-2xl font-bold text-gray-900">{@stats.total_posts}</div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div class="text-gray-500 text-sm font-medium mb-2">Posts This Month</div>
          <div class="text-2xl font-bold text-gray-900">{@stats.posts_this_month}</div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div class="text-gray-500 text-sm font-medium mb-2">Connected Platforms</div>
          <div class="flex gap-3 mt-3">
            <%= for {network, bg_color} <- @networks do %>
              <%= if network_connected?(@current_user, network) do %>
                <div class={["w-10 h-10 rounded-full flex items-center justify-center", bg_color]}>
                  <SocialNetworkIcons.icon network={network} class="w-5 h-5 text-white" />
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
        <div class="text-gray-500 text-sm font-medium mb-4">Daily Activity (Past 14 Days)</div>
        <div class="w-full h-[200px]">
          <canvas
            id="activity-chart"
            phx-hook="ActivityChart"
            data-labels={@activity_dates}
            data-datasets={@activity_datasets}
          >
          </canvas>
        </div>
      </div>

      <div class="text-center mt-12">
        <.link
          navigate={~p"/posts/new"}
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <.icon name="hero-plus" class="w-5 h-5 mr-2" /> Create New Post
        </.link>
      </div>
    </div>
    """
  end

  defp network_connected?(user, network) do
    Enum.any?(user.workspace_connections, fn connection -> connection.platform == network end)
  end

  defp format_date(date) do
    Calendar.strftime(date, "%b %d")
  end

  defp get_network_color("bsky"), do: "rgba(79, 192, 255, 0.7)"
  defp get_network_color("linkedin"), do: "rgba(10, 102, 194, 0.7)"
  defp get_network_color("mastodon"), do: "rgba(99, 100, 255, 0.7)"
  defp get_network_color("x"), do: "rgba(0, 0, 0, 0.7)"
end
