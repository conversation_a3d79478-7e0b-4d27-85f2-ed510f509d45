defmodule CrosspostWeb.DatetimeHelpers do
  @moduledoc """
  Helper functions for formatting dates and times in a user-friendly way.
  """

  use CrosspostWeb, :html

  attr :id, :string, required: true
  attr :name, :string, required: true
  attr :current_user, :any, required: true
  attr :class, :string, default: ""

  def timezone_select(assigns) do
    ~H"""
    <select id={@id} name={@name} class={@class}>
      <optgroup :for={{area, zones} <- list_timezones()} label={area}>
        <option
          :for={{zone, city, offset} <- zones}
          value={zone}
          selected={zone == @current_user.timezone}
        >
          <%= if offset != 0 do %>
            {city} ({format_offset(offset)})
          <% else %>
            {city}
          <% end %>
        </option>
      </optgroup>
    </select>
    """
  end

  defp list_timezones do
    Calendar.put_time_zone_database(Tzdata.TimeZoneDatabase)

    naive_now = NaiveDateTime.local_now()
    utc_now = DateTime.from_naive!(naive_now, "Etc/UTC")

    Tzdata.canonical_zone_list()
    |> Enum.reject(fn zone -> String.starts_with?(zone, "Etc") end)
    |> Enum.map(fn zone ->
      zone_now = DateTime.from_naive!(naive_now, zone)
      offset = DateTime.diff(utc_now, zone_now, :hour)
      [area | cities] = String.split(zone, "/")
      city = cities |> Enum.join(", ") |> String.replace("_", " ")

      {area, zone, city, offset}
    end)
    |> Enum.group_by(
      fn {area, _zone, _city, _offset} -> area end,
      fn {_area, zone, city, offset} -> {zone, city, offset} end
    )
    |> Enum.sort()
  end

  def format_datetime(nil, _timezone), do: nil

  def format_datetime(datetime, timezone) do
    case datetime |> to_datetime() |> DateTime.shift_zone(timezone) do
      {:ok, local_time} ->
        format_relative_or_absolute(local_time)

      {:error, _} ->
        # Fallback to UTC if timezone conversion fails
        format_relative_or_absolute(datetime)
    end
  end

  # Convert NaiveDateTime to DateTime if needed
  defp to_datetime(%DateTime{} = dt), do: dt
  defp to_datetime(%NaiveDateTime{} = dt), do: DateTime.from_naive!(dt, "Etc/UTC")

  defp format_offset(offset) when offset > 0, do: "UTC+#{offset}"
  defp format_offset(offset) when offset < 0, do: "UTC#{offset}"
  defp format_offset(offset) when offset == 0, do: nil

  defp format_relative_or_absolute(datetime) do
    now = DateTime.now!(datetime.time_zone)
    diff_days = Date.diff(datetime, now)

    cond do
      same_day?(datetime, now) ->
        "Today at #{format_time(datetime)}"

      diff_days == 1 ->
        "Tomorrow at #{format_time(datetime)}"

      diff_days == -1 ->
        "Yesterday at #{format_time(datetime)}"

      diff_days > 1 && diff_days < 7 ->
        Calendar.strftime(datetime, "%A at %I:%M %p")

      true ->
        Calendar.strftime(datetime, "%B %d at %I:%M %p")
    end
  end

  defp same_day?(dt1, dt2) do
    Date.compare(DateTime.to_date(dt1), DateTime.to_date(dt2)) == :eq
  end

  defp format_time(datetime) do
    # Removes leading zero from hour
    datetime
    |> Calendar.strftime("%I:%M %p")
    |> String.replace(~r/^0/, "")
  end
end
