defmodule CrosspostWeb.CalendarComponent do
  use CrosspostWeb, :live_component

  import CrosspostWeb.PostComponents, only: [network_icon: 1]

  def update(assigns, socket) do
    posts =
      if connected?(socket) do
        Crosspost.Publishing.list_posts_for_calendar(
          assigns.current_user.id,
          assigns.current_workspace.id
        )
      else
        []
      end

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:view, assigns[:view] || "month")
     |> assign(:current_date, assigns[:current_date] || Date.utc_today())
     |> assign(:posts, posts)}
  end

  def render(assigns) do
    ~H"""
      <div class="p-6 m-6" phx-hook="CalendarExpand" id="calendar-container">
        <div class="px-6 pt-6">
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center space-x-4">
              <button
                phx-click="navigate"
                phx-value-direction="prev"
                phx-target={@myself}
                class="p-2 text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100"
              >
                <.icon name="hero-chevron-left-solid" class="w-5 h-5" />
              </button>

              <h2 class="text-lg font-medium text-gray-900">
                {Calendar.strftime(@current_date, "%B %Y")}
              </h2>

              <button
                phx-click="navigate"
                phx-value-direction="next"
                phx-target={@myself}
                class="p-2 text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100"
              >
                <.icon name="hero-chevron-right-solid" class="w-5 h-5" />
              </button>

              <button
                phx-click="go_to_today"
                phx-target={@myself}
                class="px-3 py-1.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100"
              >
                Today
              </button>
            </div>

            <div class="flex space-x-2">
              <button
                phx-click="switch_view"
                phx-value-view="month"
                phx-target={@myself}
                class={[
                  "px-3 py-2 text-sm font-medium rounded-md",
                  if(@view == "month",
                    do: "bg-blue-100 text-blue-700",
                    else: "text-gray-700 hover:bg-gray-100"
                  )
                ]}
              >
                Month
              </button>
              <button
                phx-click="switch_view"
                phx-value-view="week"
                phx-target={@myself}
                class={[
                  "px-3 py-2 text-sm font-medium rounded-md",
                  if(@view == "week",
                    do: "bg-blue-100 text-blue-700",
                    else: "text-gray-700 hover:bg-gray-100"
                  )
                ]}
              >
                Week
              </button>
            </div>
          </div>
        </div>

        <div class="bg-white">
          <div>
            <%= if @view == "month" do %>
              <div class="grid grid-cols-7 gap-px bg-gray-200">
                <%= for weekday <- ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"] do %>
                  <div class="bg-gray-50 p-2 text-center text-sm font-medium text-gray-700">
                    {weekday}
                  </div>
                <% end %>

                <%= for date <- calendar_dates(:month, @current_date) do %>
                  <div
                    class={[
                      "min-h-[176px] p-2 bg-white dark:bg-gray-800",
                      if(
                        Date.compare(date, @current_date) != :eq and
                          Date.beginning_of_month(date) != Date.beginning_of_month(@current_date),
                        do: "opacity-50"
                      ),
                      if(is_today?(date), do: "bg-blue-50/50 dark:bg-blue-900/20")
                    ]}
                    data-posts={
                      Jason.encode!(
                        Enum.map(posts_for_date(@posts, date), fn {_datetime, posts} ->
                          Enum.map(posts, fn post ->
                            %{
                              id: post.post_id,
                              text: preview_content(post),
                              networks: Map.get(post, :networks, [post.network]),
                              datetime:
                                format_datetime_with_timezone(
                                  post.display_time,
                                  @current_user.timezone
                                ),
                              post_type: post.status
                            }
                          end)
                        end)
                        |> List.flatten()
                      )
                    }
                  >
                    <div class="flex justify-between items-start">
                      <span class={[
                        "text-sm font-medium",
                        if(is_today?(date),
                          do: "text-blue-600 dark:text-blue-400",
                          else: "text-gray-700 dark:text-gray-300"
                        )
                      ]}>
                        {date.day}
                      </span>
                    </div>

                    <div class="mt-2 relative">
                      <% all_posts =
                        posts_for_date(@posts, date) |> Enum.flat_map(fn {_dt, posts} -> posts end) %>
                      <%= if length(all_posts) > 3 do %>
                        <%= for post <- Enum.take(all_posts, 3) do %>
                          <.link
                            navigate={~p"/posts/#{post.post_id}"}
                            id={"calendar-post-#{post.post_id}-#{post.network}"}
                            class={[
                              "group block p-2 mt-1 text-xs rounded hover:bg-opacity-75",
                              case post.status do
                                "published" ->
                                  "bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-800/50"

                                "scheduled" ->
                                  "bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/50 dark:text-purple-300 dark:hover:bg-purple-800/50"

                                "pending" ->
                                  "bg-yellow-50 text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-800/50"
                              end
                            ]}
                            phx-hook="CalendarTooltip"
                            data-tooltip-placement="top"
                          >
                            <div class="truncate">
                              {truncate_content(post)}
                            </div>

                            <div data-tooltip-content class="hidden">
                              <div class="p-2">
                                <div class="text-sm text-gray-900 font-medium mb-1">
                                  {format_datetime_with_timezone(
                                    post.display_time,
                                    @current_user.timezone
                                  )}
                                </div>
                                <div class="flex items-center gap-2 mb-2">
                                  <%= for network <- Map.get(post, :networks, [post.network]) do %>
                                    <.network_icon
                                      network={network}
                                      status={String.to_atom(post.status)}
                                    />
                                  <% end %>
                                </div>
                                <div class="text-sm text-gray-600">
                                  {preview_content(post)}
                                </div>
                              </div>
                            </div>
                          </.link>
                        <% end %>

                        <div class="relative">
                          <button
                            type="button"
                            data-show-more
                            data-date={Date.to_iso8601(date)}
                            class="w-full text-xs text-gray-500 hover:text-gray-700 mt-1 py-1 bg-gray-50 hover:bg-gray-100 rounded text-center"
                          >
                            Show {length(all_posts) - 3} more
                          </button>

                          <div
                            id={"expanded-posts-#{Date.to_iso8601(date)}"}
                            class="hidden"
                            data-date-display={Calendar.strftime(date, "%B %d, %Y")}
                          >
                            <%= for post <- Enum.drop(all_posts, 3) do %>
                              <.link
                                navigate={~p"/posts/#{post.post_id}"}
                                id={"calendar-post-#{post.post_id}-#{post.network}-expanded"}
                                class={[
                                  "group block p-3 mb-3 rounded-lg hover:bg-opacity-75",
                                  case post.status do
                                    "published" -> "bg-green-50 text-green-700 hover:bg-green-100"
                                    "scheduled" -> "bg-purple-50 text-purple-700 hover:bg-purple-100"
                                    "pending" -> "bg-yellow-50 text-yellow-700 hover:bg-yellow-100"
                                  end
                                ]}
                                phx-hook="CalendarTooltip"
                                data-tooltip-placement="right"
                              >
                                <div class="flex items-center justify-between mb-2">
                                  <span class="text-sm font-medium">
                                    {Calendar.strftime(post.display_time, "%I:%M %p")}
                                  </span>
                                  <div class="flex items-center gap-2">
                                    <%= for network <- Map.get(post, :networks, [post.network]) do %>
                                      <.network_icon
                                        network={network}
                                        status={String.to_atom(post.status)}
                                      />
                                    <% end %>
                                  </div>
                                </div>
                                <div class="text-sm">
                                  {preview_content(post)}
                                </div>
                              </.link>
                            <% end %>
                          </div>
                        </div>
                      <% else %>
                        <%= for post <- all_posts do %>
                          <.link
                            navigate={~p"/posts/#{post.post_id}"}
                            id={"calendar-post-#{post.post_id}-#{post.network}"}
                            class={[
                              "group block p-2 mt-1 text-xs rounded hover:bg-opacity-75",
                              case post.status do
                                "published" ->
                                  "bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-800/50"

                                "scheduled" ->
                                  "bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/50 dark:text-purple-300 dark:hover:bg-purple-800/50"

                                "pending" ->
                                  "bg-yellow-50 text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-800/50"
                              end
                            ]}
                            phx-hook="CalendarTooltip"
                            data-tooltip-placement="top"
                          >
                            <div class="truncate">
                              {truncate_content(post)}
                            </div>

                            <div data-tooltip-content class="hidden">
                              <div class="p-2">
                                <div class="text-sm text-gray-900 font-medium mb-1">
                                  {format_datetime_with_timezone(
                                    post.display_time,
                                    @current_user.timezone
                                  )}
                                </div>
                                <div class="flex items-center gap-2 mb-2">
                                  <%= for network <- Map.get(post, :networks, [post.network]) do %>
                                    <.network_icon
                                      network={network}
                                      status={String.to_atom(post.status)}
                                    />
                                  <% end %>
                                </div>
                                <div class="text-sm text-gray-600">
                                  {preview_content(post)}
                                </div>
                              </div>
                            </div>
                          </.link>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="grid grid-cols-8 gap-px bg-gray-200">
                <div class="bg-gray-50 w-20"></div>
                <%= for weekday <- ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"] do %>
                  <div class="bg-gray-50 p-2 text-center text-sm font-medium text-gray-700">
                    {weekday}
                  </div>
                <% end %>

                <% dates = calendar_dates(:week, @current_date) %>
                <% {start_hour, end_hour} = get_week_hour_range(@posts, dates, @current_user.timezone) %>

                <%= for hour <- start_hour..end_hour do %>
                  <div class="bg-gray-50 p-2 text-right text-xs font-medium text-gray-500">
                    {format_hour(hour)}
                  </div>

                  <%= for date <- dates do %>
                    <div class="relative h-8 bg-white overflow-visible">
                      <%= for post <- posts_for_datetime(@posts, date, hour, @current_user.timezone) do %>
                        <.link
                          navigate={~p"/posts/#{post.post_id}"}
                          id={"calendar-post-#{post.post_id}-#{post.network}-#{hour}"}
                          class={[
                            "group absolute inset-x-0 mx-1 text-xs px-1 rounded truncate z-10 hover:z-20",
                            case post.status do
                              "published" ->
                                "bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-800/50"

                              "scheduled" ->
                                "bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/50 dark:text-purple-300 dark:hover:bg-purple-800/50"

                              "pending" ->
                                "bg-yellow-50 text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-800/50"
                            end
                          ]}
                          style={"top: #{minute_offset(post, @current_user.timezone)}px; min-height: 16px;"}
                          data-tooltip-placement="right"
                          phx-hook="CalendarTooltip"
                          data-post={
                            Jason.encode!(%{
                              id: post.post_id,
                              text: preview_content(post),
                              networks: Map.get(post, :networks, [post.network]),
                              datetime:
                                format_datetime_with_timezone(
                                  post.display_time,
                                  @current_user.timezone
                                ),
                              post_type: post.status
                            })
                          }
                        >
                          <div class="truncate leading-4">
                            {truncate_content(post)}
                          </div>
                        </.link>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    """
  end

  # Helper functions remain mostly the same but work with PostNetworkStatus records
  defp calendar_dates(:month, current_date) do
    first_day = Date.beginning_of_month(current_date)
    last_day = Date.end_of_month(current_date)

    first_day_weekday = Date.day_of_week(first_day, :sunday)
    last_day_weekday = Date.day_of_week(last_day, :sunday)

    first_shown_date = Date.add(first_day, -first_day_weekday)
    last_shown_date = Date.add(last_day, 6 - last_day_weekday)

    Date.range(first_shown_date, last_shown_date)
  end

  defp calendar_dates(:week, current_date) do
    sunday = Date.beginning_of_week(current_date, :sunday)
    Date.range(sunday, Date.add(sunday, 6))
  end

  defp get_week_hour_range(_posts, _dates, _timezone) do
    {0, 23}
  end

  defp format_hour(hour) do
    "#{String.pad_leading(to_string(hour), 2, "0")}:00"
  end

  defp is_today?(date) do
    Date.compare(date, Date.utc_today()) == :eq
  end

  defp posts_for_date(posts, date) do
    posts
    |> Enum.filter(fn post ->
      case post.display_time do
        nil ->
          false

        display_time ->
          display_date = DateTime.to_date(display_time)
          Date.compare(display_date, date) == :eq
      end
    end)
    # First collapse networks based on view type
    # Pass :month for day-level grouping
    |> collapse_network_entries(:month)
    |> Enum.sort_by(fn post -> DateTime.to_unix(post.display_time) end, :asc)
    |> Enum.group_by(& &1.display_time)
    |> Enum.map(fn {datetime, posts} -> {datetime, posts} end)
  end

  defp posts_for_datetime(posts, date, hour, timezone) do
    posts
    |> Enum.filter(fn post ->
      case post.display_time do
        nil -> false
        display_time -> in_user_hour?(display_time, date, hour, timezone)
      end
    end)
    # Pass :week for exact time grouping
    |> collapse_network_entries(:week)
  end

  defp in_user_hour?(datetime, date, hour, timezone) do
    case DateTime.shift_zone(datetime, timezone) do
      {:ok, local_time} ->
        local_date = DateTime.to_date(local_time)
        Date.compare(local_date, date) == :eq && local_time.hour == hour

      {:error, _} ->
        false
    end
  end

  defp collapse_network_entries(posts, view_type) do
    posts
    |> Enum.group_by(fn post ->
      case view_type do
        :month ->
          # Group by post ID and date only for month view
          {post.post.id, DateTime.to_date(post.display_time)}

        :week ->
          # Group by post ID and exact time for week view
          {post.post.id, post.display_time}
      end
    end)
    |> Enum.map(fn {{_post_id, _time}, post_entries} ->
      # All entries in this group have the same post ID and (date or time)
      first_entry = List.first(post_entries)

      networks =
        post_entries
        |> Enum.map(& &1.network)
        # Deduplicate networks
        |> Enum.uniq()

      first_entry
      # Store all networks
      |> Map.put(:networks, networks)
      # Keep one network for compatibility
      |> Map.put(:network, List.first(networks))
    end)
    |> List.flatten()
  end

  defp minute_offset(post, timezone) do
    case DateTime.shift_zone(post.display_time, timezone) do
      {:ok, local_time} ->
        minutes = local_time.minute
        trunc(minutes / 60 * 48)

      {:error, _} ->
        0
    end
  end

  defp preview_content(post) do
    post.text || ""
  end

  defp truncate_content(post) do
    text = post.text || ""
    if String.length(text) > 30, do: String.slice(text, 0..27) <> "...", else: text
  end

  defp format_datetime_with_timezone(datetime, timezone) do
    case DateTime.shift_zone(datetime, timezone) do
      {:ok, local_time} ->
        Calendar.strftime(local_time, "%B %d, %Y at %I:%M %p %Z")

      {:error, _} ->
        Calendar.strftime(datetime, "%B %d, %Y at %I:%M %p UTC")
    end
  end

  def handle_event("switch_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view, view)}
  end

  def handle_event("navigate", %{"direction" => direction}, socket) do
    new_date =
      case direction do
        "prev" ->
          case socket.assigns.view do
            "month" -> Timex.shift(socket.assigns.current_date, months: -1)
            "week" -> Timex.shift(socket.assigns.current_date, weeks: -1)
          end

        "next" ->
          case socket.assigns.view do
            "month" -> Timex.shift(socket.assigns.current_date, months: 1)
            "week" -> Timex.shift(socket.assigns.current_date, weeks: 1)
          end
      end

    {:noreply, assign(socket, :current_date, new_date)}
  end

  def handle_event("go_to_today", _, socket) do
    {:noreply, assign(socket, :current_date, Date.utc_today())}
  end
end
