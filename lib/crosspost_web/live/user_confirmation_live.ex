defmodule CrosspostWeb.UserConfirmationLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Accounts

  def render(%{live_action: :edit} = assigns) do
    ~H"""
    <div class="relative min-h-screen">
      <!-- Background gradient -->
      <div
        class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        aria-hidden="true"
      >
        <div
          class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
        >
        </div>
      </div>

      <div class="relative px-6 lg:px-8">
        <div class="mx-auto max-w-5xl pt-20 sm:pt-24 lg:pt-32">
          <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Confirm Your Account
            </h1>
            <p class="mt-6 text-lg leading-8 text-gray-600">
              Please confirm your email address to continue
            </p>
            <div class="mt-10">
              <div class="max-w-lg mx-auto bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
                <.simple_form for={@form} id="confirmation_form" phx-submit="confirm_account">
                  <input type="hidden" name={@form[:token].name} value={@form[:token].value} />
                  <:actions>
                    <.button phx-disable-with="Confirming..." class="w-full">
                      Confirm my account
                    </.button>
                  </:actions>
                </.simple_form>

                <div class="mt-8 text-center text-sm text-gray-600">
                  <p class="mb-2">
                    Already confirmed?
                  </p>
                  <.link
                    navigate={~p"/users/sign-in"}
                    class="font-medium text-blue-600 hover:text-blue-500"
                  >
                    Sign in
                  </.link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(%{"token" => token}, _session, socket) do
    form = to_form(%{"token" => token}, as: "user")
    {:ok, assign(socket, form: form), temporary_assigns: [form: nil]}
  end

  # Do not log in the user after confirmation to avoid a
  # leaked token giving the user access to the account.
  def handle_event("confirm_account", %{"user" => %{"token" => token}}, socket) do
    case Accounts.confirm_user(token) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "User confirmed successfully.")
         |> redirect(to: ~p"/users/sign-in")}

      :error ->
        # If there is a current user and the account was already confirmed,
        # then odds are that the confirmation link was already visited, either
        # by some automation or by the user themselves, so we redirect without
        # a warning message.
        case socket.assigns do
          %{current_user: %{confirmed_at: confirmed_at}} when not is_nil(confirmed_at) ->
            {:noreply, redirect(socket, to: ~p"/")}

          %{} ->
            {:noreply,
             socket
             |> put_flash(:error, "User confirmation link is invalid or it has expired.")
             |> redirect(to: ~p"/")}
        end
    end
  end
end
