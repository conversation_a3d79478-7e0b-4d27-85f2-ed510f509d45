defmodule CrosspostWeb.UserSettingsLive.SocialConnections.Mastodon do
  use CrosspostWeb, :live_component

  require Logger

  alias Crosspost.Accounts
  alias Crosspost.Mastodon

  import CrosspostWeb.UserSettingsLive.SocialConnections.CoreComponent

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:filtered_instances, [])
     |> assign(:selected_instance, nil)}
  end

  @impl true
  def render(%{connection: nil} = assigns) do
    ~H"""
    <div id="mastodon">
      <.connect_tile
        network="mastodon"
        modal="mastodon-settings-modal"
        bg_color="bg-purple-600"
        hover_color="hover:bg-emerald-50"
        display_name="Connect to Mastodon"
      />
      <.instance_selection_modal
        target={@myself}
        filtered_instances={@filtered_instances}
        selected_instance={@selected_instance}
      />
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id={@id}>
      <.connection_tile
        display_name="Mastodon"
        network="mastodon"
        bg_color="bg-purple-600"
        hover_color="hover:bg-emerald-50"
        confirmation_message="Are you sure you want to disconnect your Mastodon account?"
        avatar_url={get_avatar(@connection)}
        username={get_username(@connection)}
        target={@myself}
      />
    </div>
    """
  end

  @impl true
  def handle_event("connect_mastodon", %{"instance" => instance}, socket) do
    if instance && instance != "" do
      {:noreply,
       socket
       |> push_navigate(
         to:
           ~p"/auth/mastodon?instance=#{instance}&workspace_id=#{socket.assigns.current_workspace.id}"
       )}
    else
      {:noreply,
       socket
       |> put_flash(:error, "Please enter a Mastodon instance")}
    end
  end

  @impl true
  def handle_event("search_instances", %{"value" => ""}, socket) do
    {:noreply,
     socket
     |> assign(:filtered_instances, [])}
  end

  @impl true
  def handle_event("search_instances", %{"value" => query}, socket) do
    case Mastodon.Instances.list_instances(query) do
      {:ok, %{"instances" => instances}} ->
        {:noreply,
         socket
         |> assign(:filtered_instances, instances)}

      {:error, _} ->
        {:noreply,
         socket
         |> assign(:filtered_instances, [])}
    end
  end

  @impl true
  def handle_event("select_instance", %{"instance" => instance}, socket) do
    {:noreply,
     socket
     |> assign(:selected_instance, instance)
     |> assign(:filtered_instances, [])}
  end

  @impl true
  def handle_event("disconnect", %{"network" => "mastodon"}, socket) do
    case Accounts.disconnect_provider(socket.assigns.connection) do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(:connection, nil)}

      {:error, reason} ->
        {:error, reason}
    end
  end

  attr :target, :any, required: true
  attr :filtered_instances, :list, required: true
  attr :selected_instance, :any, required: true

  defp instance_selection_modal(assigns) do
    ~H"""
    <.modal id="mastodon-settings-modal">
      <.simple_form for={%{}} phx-submit="connect_mastodon" phx-target={@target}>
        <div class="space-y-4">
          <div class="relative">
            <.label for="instance">Instance</.label>
            <.input
              type="text"
              name="instance"
              value={@selected_instance}
              placeholder="mastodon.social"
              autocomplete="off"
              phx-keyup="search_instances"
              phx-target={@target}
              required
            />
            <div class="absolute z-10 w-full mt-1">
              <div
                :if={not Enum.empty?(@filtered_instances)}
                class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-48 overflow-y-auto"
              >
                <%= for instance <- @filtered_instances do %>
                  <button
                    type="button"
                    phx-click="select_instance"
                    phx-value-instance={instance}
                    phx-target={@target}
                    class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                  >
                    {instance}
                  </button>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        <:actions>
          <div class="flex justify-end space-x-3 mt-4">
            <.button
              type="button"
              phx-click={hide_modal("mastodon-settings-modal")}
              variant="secondary"
            >
              Cancel
            </.button>
            <.button type="submit">
              Connect
            </.button>
          </div>
        </:actions>
      </.simple_form>
    </.modal>
    """
  end

  defp get_username(connection) do
    nickname = connection.info["nickname"]
    instance = connection.settings["instance"]
    "@#{nickname}@#{instance}"
  end

  defp get_avatar(connection) do
    connection.info["image"]
  end
end
