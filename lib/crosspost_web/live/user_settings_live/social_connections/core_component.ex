defmodule CrosspostWeb.UserSettingsLive.SocialConnections.CoreComponent do
  use CrosspostWeb, :live_component

  alias CrosspostWeb.Components.SocialNetworkIcons

  import Phoenix.Component

  attr :network, :string, required: true
  attr :bg_color, :string, required: true
  attr :hover_color, :string, required: true
  attr :display_name, :string, required: true
  attr :modal, :string, required: false, default: ""
  attr :tooltip, :string, required: false, default: ""

  def connect_tile(%{modal: ""} = assigns) do
    ~H"""
    <div id={"connect-#{@network}"}>
      <.link
        navigate={~p"/auth/#{@network}"}
        data-connect-button={@network}
        class={[
          "block p-4 rounded-lg border-2 border-gray-200 transition-all duration-200",
          "hover:bg-gray-50 hover:border-gray-300"
        ]}
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class={[
              "w-10 h-10 rounded-full flex items-center justify-center bg-gray-200 transition-colors duration-200",
              "group-hover:#{@hover_color}"
            ]}>
              <SocialNetworkIcons.icon network={@network} class="w-5 h-5 text-gray-500" />
            </div>
            <div class="flex items-center space-x-1">
              <h3 class="text-base font-semibold">{@display_name}</h3>
              <div :if={@tooltip != ""} class="group/tooltip relative">
                <div class="p-1.5 rounded-full bg-indigo-100 dark:bg-indigo-900/70 text-indigo-700 dark:text-indigo-200 hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors duration-200 shadow-sm cursor-help">
                  <div class="w-4 h-4 flex items-center justify-center text-sm font-bold">?</div>
                </div>
                <div class="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200">
                  {@tooltip}
                </div>
              </div>
            </div>
          </div>
        </div>
      </.link>
    </div>
    """
  end

  def connect_tile(assigns) do
    ~H"""
    <div id={"connect-#{@network}"}>
      <.link
        phx-click={show_modal(@modal)}
        data-connect-button={@network}
        class={[
          "block p-4 rounded-lg border-2 border-gray-200 transition-all duration-200",
          "hover:bg-gray-50 hover:border-gray-300"
        ]}
      >
        <.network_tile
          network={@network}
          display_name={@display_name}
          bg_color={@bg_color}
          hover_color={@hover_color}
        />
      </.link>
    </div>
    """
  end

  def network_tile(assigns) do
    ~H"""
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class={[
          "w-10 h-10 rounded-full flex items-center justify-center bg-gray-200 transition-colors duration-200",
          "group-hover:#{@hover_color}"
        ]}>
          <SocialNetworkIcons.icon network={@network} class="w-5 h-5 text-gray-500" />
        </div>
        <div class="flex items-center space-x-1">
          <h3 class="text-base font-semibold">{@display_name}</h3>
        </div>
      </div>
    </div>
    """
  end

  attr :network, :string, required: true
  attr :display_name, :string, required: true
  attr :bg_color, :string, required: true
  attr :hover_color, :string, required: true
  attr :avatar_url, :string, required: true
  attr :confirmation_message, :string, required: true
  attr :username, :string, required: true
  attr :tooltip, :string, required: false, default: ""
  attr :modal, :boolean, required: false, default: false
  attr :modal_title, :string, required: false
  attr :target, :any, required: false

  def connection_tile(assigns) do
    ~H"""
    <div class="group relative p-4 rounded-lg border-2 transition-all duration-200 bg-emerald-50 border-emerald-200">
      <div class="flex items-start">
        <div class="flex items-center space-x-3 min-w-0 flex-grow">
          <div class={[
            "w-10 h-10 shrink-0 rounded-full flex items-center justify-center transition-colors duration-200",
            @bg_color
          ]}>
            <SocialNetworkIcons.icon network={@network} class="w-5 h-5 text-white" />
          </div>
          <div class="min-w-0 flex-grow">
            <div class="flex items-center space-x-1">
              <h3 class="text-base font-semibold truncate">{@display_name}</h3>
            </div>
            <div class="flex items-center space-x-2 mt-1 min-w-0">
              <img src={@avatar_url} alt={"#{@network} avatar"} class="w-5 h-5 rounded-full shrink-0" />
              <span class="text-xs text-gray-600 truncate">
                {@username}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="absolute inset-0 bg-black/0 group-hover:bg-black/30 dark:group-hover:bg-black/50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div class="flex items-center gap-3">
          <button
            :if={@modal}
            type="button"
            id={"#{@network}-settings-button"}
            phx-click={show_modal("#{@network}-settings-modal")}
            class="p-2 rounded-full bg-white/90 dark:bg-gray-800/90 text-blue-600 dark:text-blue-400 hover:bg-white dark:hover:bg-gray-800 transition-colors duration-200 shadow-sm"
            title={@modal_title}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </button>
          <button
            type="button"
            data-button-disconnect={@network}
            phx-click="disconnect"
            phx-target={@target}
            phx-value-network={@network}
            data-confirm={@confirmation_message}
            class="p-2 rounded-full bg-white/90 dark:bg-gray-800/90 text-rose-500 hover:bg-white dark:hover:bg-gray-800 transition-colors duration-200 shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 shrink-0"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div :if={@tooltip != ""} class="group/tooltip relative">
            <div class="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/70 text-indigo-700 dark:text-indigo-200 hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors duration-200 shadow-sm cursor-help">
              <div class="w-5 h-5 flex items-center justify-center text-base font-bold">?</div>
            </div>
            <div class="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200">
              {@tooltip}
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
