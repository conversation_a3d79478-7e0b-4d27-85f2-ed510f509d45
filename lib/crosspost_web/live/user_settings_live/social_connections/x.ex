defmodule CrosspostWeb.UserSettingsLive.SocialConnections.X do
  use CrosspostWeb, :live_component

  require Logger

  alias Crosspost.Accounts

  import CrosspostWeb.UserSettingsLive.SocialConnections.CoreComponent

  @impl true
  def update(assigns, socket) do
    {:ok, socket |> assign(assigns)}
  end

  @impl true
  def render(%{connection: nil} = assigns) do
    ~H"""
    <div id="x">
      <.connect_tile
        network="x"
        bg_color="bg-black"
        hover_color="hover:bg-emerald-50"
        display_name="Connect to X"
      />
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id={@id}>
      <.connection_tile
        display_name="X"
        network="x"
        bg_color="bg-black"
        hover_color="hover:bg-emerald-50"
        confirmation_message="Are you sure you want to disconnect your X account?"
        avatar_url={get_avatar(@connection)}
        username={get_username(@connection)}
        target={@myself}
      />
    </div>
    """
  end

  @impl true
  def handle_event("disconnect", %{"network" => "x"}, socket) do
    case Accounts.disconnect_provider(socket.assigns.connection) do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(:connection, nil)}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_username(connection) do
    "@" <> (connection.info["nickname"] || "")
  end

  defp get_avatar(connection) do
    connection.info["image"]
  end
end
