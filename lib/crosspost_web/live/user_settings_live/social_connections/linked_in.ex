defmodule CrosspostWeb.UserSettingsLive.SocialConnections.LinkedIn do
  use CrosspostWeb, :live_component

  alias Crosspost.Accounts

  import CrosspostWeb.UserSettingsLive.SocialConnections.CoreComponent

  @impl true
  def update(assigns, socket) do
    organizations =
      if not is_nil(assigns.connection) and not is_map_key(assigns, :organizations) do
        case get_organizations(assigns) do
          {:ok, orgs} -> orgs
          {:error, _} -> []
        end
      else
        []
      end

    # Set default selected organization based on current settings
    selected_organization =
      cond do
        is_nil(assigns.connection) ->
          nil

        is_map_key(assigns, :selected_organization) and not is_nil(assigns.selected_organization) ->
          assigns.selected_organization

        assigns.connection.settings["organization"] ->
          assigns.connection.settings["organization"]["id"]

        true ->
          "personal"
      end

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:organizations, organizations)
     |> assign(:selected_organization, selected_organization)}
  end

  @impl true
  def render(%{connection: nil} = assigns) do
    ~H"""
    <div id="linkedin">
      <.connect_tile
        network="linkedin"
        bg_color="bg-blue-600"
        hover_color="hover:bg-emerald-50"
        display_name="Connect to LinkedIn"
      />
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id={@id}>
      <.connection_tile
        display_name="LinkedIn"
        network="linkedin"
        bg_color="bg-blue-600"
        hover_color="hover:bg-emerald-50"
        confirmation_message="Confirmation message"
        avatar_url={get_avatar(@connection)}
        username={get_username(@connection)}
        target={@myself}
        modal={@organizations != :error}
        modal_title="Configure organization page"
      />
      <.profile_selection_modal
        :if={@organizations != :error}
        target={@myself}
        connection={@connection}
        organizations={@organizations}
        selected_organization={@selected_organization}
      />
    </div>
    """
  end

  def handle_event("disconnect", %{"network" => "linkedin"}, socket) do
    case Accounts.disconnect_provider(socket.assigns.connection) do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(:connection, nil)}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @impl true
  def handle_event("save_organization", %{"organization" => org_id}, socket) do
    connection = socket.assigns.connection
    organizations = socket.assigns.organizations

    organization =
      if org_id != "personal" do
        organizations |> Enum.find(&(&1["id"] == String.to_integer(org_id)))
      else
        nil
      end

    updated_settings =
      case organization do
        nil ->
          Map.delete(connection.settings, "organization")

        _ ->
          Map.put(connection.settings, "organization", organization)
      end

    case Accounts.update_connection(connection, %{settings: updated_settings}) do
      {:ok, updated_connection} ->
        {:noreply,
         socket
         |> assign(:connection, updated_connection)
         |> assign(:selected_organization, org_id)
         |> push_event("hide_modal", %{to: "#close-linkedin-settings"})
         |> put_flash(:info, "LinkedIn organization page configured successfully.")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update LinkedIn organization settings.")}
    end
  end

  defp get_organizations(%{connection: nil}), do: {:ok, []}

  defp get_organizations(%{connection: connection}),
    do: linkedin_client().fetch_organizations(connection)

  attr :connection, :map, required: true
  attr :target, :any, required: true
  attr :organizations, :list, required: true
  attr :selected_organization, :string, required: true

  defp profile_selection_modal(assigns) do
    ~H"""
    <.modal id="linkedin-settings-modal">
      <div class="space-y-4">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Select how you want to post on LinkedIn. You can post as yourself or on behalf of an organization page.
        </p>
        <.simple_form for={%{}} phx-submit="save_organization" phx-target={@target}>
          <div class="space-y-2">
            <label class="block">
              <div class={[
                "w-full p-3 rounded-lg border-2 transition-all duration-200",
                "hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer",
                if(@selected_organization == "personal",
                  do: "border-blue-500 bg-blue-50 dark:bg-blue-900 dark:border-blue-400",
                  else: "border-gray-200 dark:border-gray-700"
                )
              ]}>
                <div class="flex items-center space-x-3">
                  <div class="flex items-center h-5">
                    <input
                      type="radio"
                      name="organization"
                      value="personal"
                      checked={@selected_organization == "personal"}
                      class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                  </div>
                  <div class="flex items-center space-x-3 flex-grow">
                    <img
                      :if={@connection}
                      src={@connection.info["image"]}
                      alt="Your profile picture"
                      class="w-8 h-8 rounded-full"
                    />
                    <div class="flex-grow">
                      <h3 class="font-semibold dark:text-white">Post as yourself</h3>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        {@connection.info["name"]}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </label>

            <%= for org <- @organizations do %>
              <label class="block">
                <div class={[
                  "w-full p-3 rounded-lg border-2 transition-all duration-200",
                  "hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer",
                  if(@selected_organization == to_string(org["id"]),
                    do: "border-blue-500 bg-blue-50 dark:bg-blue-900 dark:border-blue-400",
                    else: "border-gray-200 dark:border-gray-700"
                  )
                ]}>
                  <div class="flex items-center space-x-3">
                    <div class="flex items-center h-5">
                      <input
                        type="radio"
                        name="organization"
                        value={org["id"]}
                        checked={@selected_organization == to_string(org["id"])}
                        class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                    </div>
                    <div class="flex items-center space-x-3 flex-grow">
                      <img
                        :if={org["resolved_logo_urls"]["original"]}
                        src={org["resolved_logo_urls"]["original"]}
                        alt={"#{org["localizedName"]} logo"}
                        class="w-8 h-8 rounded-full"
                      />
                      <div
                        :if={!org["resolved_logo_urls"]["original"]}
                        class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center"
                      >
                        <span class="text-gray-500 text-sm font-semibold">
                          {String.first(org["localizedName"])}
                        </span>
                      </div>
                      <div class="flex-grow">
                        <h3 class="font-semibold dark:text-white">{org["localizedName"]}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                          @{org["vanityName"]}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </label>
            <% end %>
          </div>
          <:actions>
            <div class="flex justify-end space-x-3 mt-4">
              <.button type="button" phx-click={hide_modal("linkedin-modal")}>
                Cancel
              </.button>
              <.button type="submit">
                Save
              </.button>
            </div>
          </:actions>
        </.simple_form>
      </div>
    </.modal>
    """
  end

  defp get_username(connection) do
    if connection.settings["organization"] do
      connection.settings["organization"]["localizedName"]
    else
      connection.info["name"]
    end
  end

  defp get_avatar(connection) do
    cond do
      connection.settings["organization"] ->
        connection.settings["organization"]["resolved_logo_urls"]["original"]

      true ->
        connection.info["image"]
    end
  end

  defp linkedin_client do
    Application.get_env(:crosspost, :linkedin_client, Crosspost.Accounts.LinkedIn.Client)
  end
end
