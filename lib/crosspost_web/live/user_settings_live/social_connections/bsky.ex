defmodule CrosspostWeb.UserSettingsLive.SocialConnections.Bsky do
  use CrosspostWeb, :live_component

  require Logger

  alias Crosspost.Accounts
  alias Crosspost.Accounts.Bsky.Client, as: Client

  import CrosspostWeb.UserSettingsLive.SocialConnections.CoreComponent

  @impl true
  def update(assigns, socket) do
    {:ok, socket |> assign(assigns)}
  end

  @impl true
  def render(%{connection: nil} = assigns) do
    ~H"""
    <div id="bsky">
      <.connect_tile
        network="bsky"
        modal="bsky-settings-modal"
        bg_color="bg-blue-600"
        hover_color="hover:bg-emerald-50"
        display_name="Connect to Bluesky"
      />
      <.app_credentials_modal target={@myself} />
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id={@id}>
      <.connection_tile
        display_name="Bluesky"
        network="bsky"
        bg_color="bg-blue-600"
        hover_color="hover:bg-emerald-50"
        confirmation_message="Are you sure you want to disconnect your <PERSON>ky account?"
        avatar_url={get_avatar(@connection)}
        username={get_username(@connection)}
        target={@myself}
      />
    </div>
    """
  end

  @impl true
  def handle_event("connect", %{"username" => username, "app_password" => app_password}, socket) do
    user = socket.assigns.current_user
    workspace = socket.assigns.current_workspace
    username = String.replace_leading(username, "@", "")

    Logger.metadata(%{
      user_id: user.id,
      username: username,
      workspace_id: workspace.id
    })

    case Client.authenticate(username, app_password) do
      {:ok, %{access_token: access_token, refresh_token: refresh_token}} ->
        case Client.get_profile(access_token, username) do
          {:ok, profile} ->
            auth_params = %{
              uid: profile["did"],
              credentials: %{
                access_token: access_token,
                refresh_token: refresh_token,
                expires_at: DateTime.utc_now() |> DateTime.add(7 * 24 * 60 * 60, :second)
              },
              info: %{
                "handle" => profile["handle"],
                "display_name" => profile["displayName"],
                "description" => profile["description"],
                "avatar" => profile["avatar"],
                "followers_count" => profile["followersCount"],
                "follows_count" => profile["followsCount"],
                "posts_count" => profile["postsCount"]
              }
            }

            case Accounts.update_user_from_auth("bsky", user, workspace, auth_params) do
              {:ok, _updated_user} ->
                connection = Accounts.get_connection(user.id, "bsky", workspace.id)

                send(
                  self(),
                  {:show_flash, {"Bluesky connected successfully.", :success}}
                )

                {:noreply,
                 socket
                 |> assign(:connection, connection)
                 |> push_event("hide_modal", %{to: "#close-bsky-settings"})}

              {:error, reason} ->
                Logger.error("Failed to save Bluesky connection for user",
                  event: "social_connections.bsky_save_error",
                  error: inspect(reason)
                )

                send(
                  self(),
                  {:user_update_error, "Failed to save Bluesky connection", :error}
                )

                {:noreply, socket}
            end

          {:error, reason} ->
            Logger.error("Failed to fetch Bluesky profile for user",
              event: "social_connections.bsky_auth_error",
              error: inspect(reason)
            )

            send(
              self(),
              {:user_update_error, "Failed to fetch Bluesky profile information", :error}
            )

            {:noreply, socket}
        end

      {:error, {:rate_limited, _message}} ->
        Logger.warning("Bluesky rate limit hit for user",
          event: "social_connections.bsky_rate_limit"
        )

        send(
          self(),
          {:show_flash,
           {"Too many connection attempts. Please wait a few minutes before trying again.",
            :error}}
        )

        {:noreply, socket}

      {:error, :invalid_credentials} ->
        send(
          self(),
          {:show_flash,
           {"Invalid username or app password. Please check your credentials.", :error}}
        )

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("Bluesky authentication failed for user",
          event: "social_connections.bsky_auth_error",
          error: inspect(reason)
        )

        send(
          self(),
          {:user_update_error, "Failed to connect to Bluesky", :error}
        )

        {:noreply, socket}
    end
  end

  def handle_event("disconnect", %{"network" => "bsky"}, socket) do
    case Accounts.disconnect_provider(socket.assigns.connection) do
      {:ok, _} ->
        send(
          self(),
          {:show_flash, {"Bluesky disconnected successfully", :success}}
        )

        {:noreply,
         socket
         |> assign(:connection, nil)}

      {:error, reason} ->
        {:error, reason}
    end
  end

  attr :target, :any, required: true

  defp app_credentials_modal(assigns) do
    ~H"""
    <.modal id="bsky-settings-modal">
      <.simple_form for={%{}} phx-submit="connect" phx-target={@target}>
        <div class="space-y-4">
          <div class="text-sm text-gray-600 mb-4">
            <p class="mb-2">
              To connect your Bluesky account, you'll need to create an app password:
            </p>
            <ol class="list-decimal list-inside space-y-1">
              <li>
                Go to
                <.link
                  href="https://bsky.app/settings/app-passwords"
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800"
                >
                  Bluesky App Passwords
                </.link>
              </li>
              <li>Click "Add App Password"</li>
              <li>Give it a name (e.g. "Crosspost")</li>
              <li>Copy the generated password</li>
            </ol>
          </div>
          <div>
            <.label for="username">Username</.label>
            <.input
              type="text"
              name="username"
              value=""
              placeholder="Valid username ie @jane.bsky.social or @jane.org"
              required
            />
          </div>
          <div>
            <.label for="app_password">App Password</.label>
            <.input type="password" name="app_password" value="" required />
          </div>
        </div>
        <:actions>
          <div class="flex justify-end space-x-3 mt-4">
            <.button type="button" phx-click={hide_modal("bsky-settings-modal")}>
              Cancel
            </.button>
            <.button type="submit">
              Connect
            </.button>
          </div>
        </:actions>
      </.simple_form>
    </.modal>
    """
  end

  defp get_username(connection) do
    connection.info["handle"]
  end

  defp get_avatar(connection) do
    connection.info["avatar"]
  end
end
