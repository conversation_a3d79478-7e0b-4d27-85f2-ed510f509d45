defmodule CrosspostWeb.UserSettingsLive.Workspaces do
  use CrosspostWeb, :live_component

  require Logger

  alias Crosspost.Accounts
  alias Crosspost.Accounts.Workspace
  alias Crosspost.FeatureUsages
  alias Crosspost.Features
  alias Crosspost.Plans
  alias Crosspost.Workspaces

  import CrosspostWeb.Components.FeatureUpgradeModal
  import CrosspostWeb.UserSettingsLive.Workspaces.Modals

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div
        :if={Features.limit_reached?(@current_user, "workspaces")}
        class="sm:rounded-md bg-blue-50 dark:bg-blue-900/50 p-4"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <.icon name="hero-information-circle" class="h-5 w-5 text-blue-400" />
          </div>
          <div class="ml-3 flex-1 md:flex md:justify-between">
            <p class="text-sm text-blue-700 dark:text-blue-200">
              You've reached your workspace limit. Upgrade your plan to create additional workspaces and unlock more features.
            </p>
            <p class="mt-3 text-sm md:ml-6 md:mt-0">
              <.link
                navigate={~p"/settings/plan"}
                class="whitespace-nowrap font-medium text-blue-700 hover:text-blue-600 dark:text-blue-200 dark:hover:text-blue-100"
              >
                Upgrade now <span aria-hidden="true">&rarr;</span>
              </.link>
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6" data-testid="workspace-list">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Workspaces</h3>
          <div class="flex gap-4">
            <.button
              phx-click={show_modal("workspace-modal")}
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={Features.limit_reached?(@current_user, "workspaces")}
            >
              <.icon name="hero-plus-mini" class="h-4 w-4 mr-1" /> New Workspace
            </.button>
          </div>
        </div>

        <div class="max-w-xl mx-auto">
          <div
            :for={workspace <- @workspaces}
            class="rounded-lg flex items-center justify-between py-4 hover:bg-gray-50 dark:hover:bg-gray-600/50 -mx-4 px-4 transition-colors duration-150"
            data-testid={"workspace-item-" <> to_string(workspace.id)}
          >
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {workspace.name}
                <%= if workspace.id == @current_workspace.id do %>
                  <span
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-white"
                    data-testid="badge-current"
                  >
                    Current
                  </span>
                <% end %>
                <%= if workspace.is_default do %>
                  <span
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                    data-testid="badge-system"
                  >
                    System
                  </span>
                <% end %>
                <%= if workspace.owner_id != @current_user.id and not workspace.is_default do %>
                  <span
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800"
                    data-testid="badge-external"
                  >
                    External
                  </span>
                <% end %>
              </h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {workspace.slug}
              </p>
            </div>
            <div
              :if={not workspace.is_default and workspace.owner_id == @current_user.id}
              class="flex items-center gap-2"
              id={"workspace-actions-#{workspace.id}"}
            >
              <.button
                phx-click={
                  if Features.enabled?(@current_user, "workspace_invites") do
                    show_modal("invite-modal-#{workspace.id}")
                  else
                    show_modal("feature-upgrade-modal")
                  end
                }
                size="s"
                data-feature="workspace_invites"
                data-feature-enabled={Features.enabled?(@current_user, "workspace_invites")}
              >
                <.icon name="hero-user-plus-mini" class="h-3 w-3 mr-0.5" /> Invite
              </.button>
              <.button
                :if={workspace.owner_id == @current_user.id}
                phx-click={show_modal("members-modal-#{workspace.id}")}
                size="s"
              >
                <.icon name="hero-users-mini" class="h-3 w-3 mr-0.5" /> Members
              </.button>
              <.button phx-click={show_modal("workspace-edit-modal-#{workspace.id}")} size="s">
                <.icon name="hero-pencil-mini" class="h-3 w-3 mr-0.5" /> Edit
              </.button>

              <.button
                :if={workspace.id != @current_workspace.id && !workspace.is_default}
                phx-click="delete_workspace"
                phx-value-id={workspace.id}
                phx-target={@myself}
                size="s"
                variant="danger"
                data-confirm="Are you sure you want to delete this workspace?"
              >
                <.icon name="hero-trash-mini" class="h-3 w-3 mr-0.5" /> Delete
              </.button>
            </div>
          </div>

          <%= if @workspaces == [] do %>
            <div class="text-center py-6">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                You haven't created any additional workspaces yet.
              </p>
            </div>
          <% end %>
        </div>
      </div>
      <%= for workspace <- @workspaces do %>
        <div :if={workspace.owner_id == @current_user.id}>
          <.edit_modal
            id={"workspace-edit-modal-#{workspace.id}"}
            workspace={workspace}
            workspace_form={to_form(Workspace.changeset(workspace, %{}))}
            target={@myself}
          />

          <.invite_modal
            id={"invite-modal-#{workspace.id}"}
            current_user={@current_user}
            workspace={workspace}
            target={@myself}
            invite_form={@invite_form}
          />

          <.members_modal id={"members-modal-#{workspace.id}"} workspace={workspace} target={@myself} />
        </div>
      <% end %>

      <.feature_upgrade_modal
        :if={@upgrade_plan}
        id="feature-upgrade-modal"
        feature_key="workspace_invites"
        current_user={@current_user}
        plan={@upgrade_plan}
        on_cancel={hide_modal("feature-upgrade-modal")}
      />

      <.edit_modal
        id="workspace-modal"
        workspace={%Workspace{}}
        workspace_form={@workspace_form}
        target={@myself}
      />
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    workspaces = Workspaces.list_user_workspaces(assigns.current_user)

    # Find the lowest priced plan that includes workspace_invites
    upgrade_plan =
      Plans.list_active_plans()
      |> Enum.filter(fn plan ->
        "workspace_invites" in plan.features &&
          plan.price > ((assigns.current_user.plan && assigns.current_user.plan.price) || 0)
      end)
      |> Enum.min_by(& &1.price, fn -> nil end)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:workspaces, workspaces)
     |> assign(:workspace_form, to_form(Workspace.changeset(%Workspace{}, %{})))
     |> assign(:invite_form, to_form(%{"emails" => ""}))
     |> assign(:emails, "")
     |> assign(:invite_error, nil)
     |> assign(:upgrade_plan, upgrade_plan)}
  end

  @impl true
  def handle_event("save_workspace", %{"workspace" => %{"id" => id} = workspace_params}, socket)
      when id != "" do
    current_user = socket.assigns.current_user

    workspace = Enum.find(socket.assigns.workspaces, &(&1.id == String.to_integer(id)))

    case Workspaces.update_workspace(workspace, workspace_params) do
      {:ok, _workspace} ->
        workspaces = Workspaces.list_user_workspaces(current_user)

        send(
          self(),
          {:user_update_success, {current_user, "Workspace updated successfully"}, :ok}
        )

        {:noreply,
         socket
         |> push_event("hide_modal", %{to: "#workspace-edit-modal-#{id}"})
         |> assign(:workspaces, workspaces)}

      {:error, _changeset} ->
        send(self(), {:user_update_error, "Failed to update workspace", :error})

        {:noreply,
         socket
         |> put_flash(:error, "Failed to update workspace")}
    end
  end

  @impl true
  def handle_event("save_workspace", %{"workspace" => workspace_params}, socket) do
    current_user = socket.assigns.current_user

    if Features.limit_reached?(current_user, "workspaces") do
      {:noreply,
       socket
       |> put_flash(
         :error,
         "Workspace limit reached. Please upgrade your plan to create more workspaces."
       )}
    else
      case Workspaces.create_workspace(current_user, workspace_params) do
        {:ok, _workspace} ->
          {:ok, _} = FeatureUsages.inc_counter(current_user, "workspaces")

          workspaces = Workspaces.list_user_workspaces(current_user)

          {:noreply,
           socket
           |> assign(:workspaces, workspaces)
           |> assign(:show_workspace_modal, false)
           |> put_flash(:info, "Workspace created successfully")
           |> redirect(to: ~p"/settings/workspaces")}

        {:error, changeset} ->
          send(self(), {:user_update_error, "Failed to create workspace", :error})

          {:noreply,
           socket
           |> assign(:workspace_form, to_form(changeset))}
      end
    end
  end

  @impl true
  def handle_event("delete_workspace", %{"id" => id}, socket) do
    workspace_id = String.to_integer(id)
    current_user = socket.assigns.current_user
    workspace = Workspaces.get_workspace!(workspace_id)

    case Workspaces.delete_workspace(workspace) do
      {:ok, _} ->
        {:ok, _} = FeatureUsages.dec_counter(current_user, "workspaces")

        {:noreply,
         socket
         |> put_flash(:info, "Workspace deleted successfully")
         |> redirect(
           to: ~p"/settings/workspaces",
           flash: %{info: "Workspace deleted successfully"}
         )}

      {:error, _changeset} ->
        send(self(), {:user_update_error, "Failed to delete workspace", :error})

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("validate_emails", %{"value" => value}, socket) do
    {:noreply, assign(socket, :emails, value)}
  end

  @impl true
  def handle_event("send_invites", %{"workspace_id" => workspace_id, "emails" => emails}, socket) do
    workspace = Enum.find(socket.assigns.workspaces, &(&1.id == String.to_integer(workspace_id)))

    emails =
      emails
      |> String.split("\n")
      |> Enum.map(&String.trim/1)
      |> Enum.reject(&(&1 == ""))

    case Workspaces.send_invites(workspace, emails) do
      :ok ->
        send(
          self(),
          {:user_update_success, {socket.assigns.current_user, "Invitations have been sent"}, :ok}
        )

        {:noreply,
         socket
         |> push_event("hide_modal", %{to: "#invite-modal-#{workspace_id}"})
         |> assign(:invite_error, nil)}

      {:error, message} ->
        send(self(), {:user_update_error, message, :error})
        {:noreply, assign(socket, :invite_error, message)}
    end
  end

  @impl true
  def handle_event(
        "remove_member",
        %{"user-id" => user_id, "workspace-id" => workspace_id},
        socket
      ) do
    workspace = Enum.find(socket.assigns.workspaces, &(&1.id == String.to_integer(workspace_id)))
    user = Accounts.get_user!(String.to_integer(user_id))

    case Workspaces.remove_user_from_workspace(user, workspace) do
      {1, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Member removed successfully")
         |> push_navigate(to: ~p"/settings/workspaces")}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to remove member")
         |> push_navigate(to: ~p"/settings/workspaces")}
    end
  end
end
