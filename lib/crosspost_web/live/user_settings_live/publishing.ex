defmodule CrosspostWeb.UserSettingsLive.Publishing do
  use CrosspostWeb, :live_component

  alias Crosspost.Accounts

  @impl true
  def update(assigns, socket) do
    networks = available_networks(assigns.current_workspace)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:networks, networks)}
  end

  defp available_networks(workspace) do
    workspace.connections
    |> Enum.map(& &1.platform)
    |> Enum.sort()
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Default networks</h3>
        <div class="max-w-xl mx-auto">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-5">
            Select which networks should be enabled by default when creating a new post.
          </p>
          <form id="publishing_form" phx-submit="update_publishing" phx-target={@myself}>
            <div class="space-y-4">
              <%= for network <- @networks do %>
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    name="default_networks[]"
                    value={network}
                    id={"network_#{network}"}
                    class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    checked={
                      Enum.member?(
                        @current_user.workspace_settings["default_networks"] || [],
                        network
                      )
                    }
                  />
                  <label
                    for={"network_#{network}"}
                    class="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {Phoenix.Naming.humanize(network)}
                  </label>
                </div>
              <% end %>
            </div>
            <div class="mt-6">
              <button
                type="submit"
                class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Save changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("update_publishing", %{"default_networks" => networks}, socket) do
    user = socket.assigns.current_user
    workspace_id = socket.assigns.current_workspace.id

    case Accounts.update_workspace_user_settings(user.id, workspace_id, %{
           default_networks: networks
         }) do
      {:ok, updated_user} ->
        send(self(), {:reload_page, updated_user})
        {:noreply, socket}

      {:error, _changeset} ->
        send(self(), {:show_flash, {"Failed to update publishing settings.", :error}})
        {:noreply, socket}
    end
  end

  def handle_event("update_publishing", _params, socket) do
    # When no networks are selected, the param is not sent at all
    handle_event("update_publishing", %{"default_networks" => []}, socket)
  end
end
