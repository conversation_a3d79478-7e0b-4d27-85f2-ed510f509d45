defmodule CrosspostWeb.UserSettingsLive.Password do
  use CrosspostWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Change Password</h3>
        <div class="max-w-xl mx-auto">
          <.form
            for={@password_form}
            id="password_form"
            phx-change="validate_password"
            phx-submit="update_password"
          >
            <div class="space-y-6">
              <div>
                <.label for="current_password">Current Password</.label>
                <.input
                  field={@password_form[:current_password]}
                  type="password"
                  id="current_password"
                  required
                />
              </div>

              <div>
                <.label for="password">New Password</.label>
                <.input field={@password_form[:password]} type="password" id="password" required />
              </div>

              <div>
                <.label for="password_confirmation">Confirm New Password</.label>
                <.input
                  field={@password_form[:password_confirmation]}
                  type="password"
                  id="password_confirmation"
                  required
                />
              </div>

              <div class="flex justify-end">
                <.button phx-disable-with="Changing...">Change Password</.button>
              </div>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
