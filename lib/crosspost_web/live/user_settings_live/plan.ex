defmodule CrosspostWeb.UserSettingsLive.Plan do
  use CrosspostWeb, :live_component

  require Logger

  import CrosspostWeb.Components.PlanChangeForm

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div
        :if={@trial_end}
        class="bg-yellow-50 dark:bg-yellow-900/50 border-l-4 border-yellow-400 p-4 mb-4"
        data-test-id="trial-end-notice"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <.icon name="hero-exclamation-triangle" class="h-5 w-5 text-yellow-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700 dark:text-yellow-200">
              Your trial period has ended. Please choose a plan to continue using JustCrossPost.
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
          Subscription Details
        </h3>
        <div :if={@current_user.plan} class="max-w-xl mx-auto">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Current Plan</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {@current_user.plan.name}
                <span
                  :if={@current_user.trial_end}
                  class="ml-2 inline-flex items-center rounded-md bg-indigo-50 px-1.5 py-0.5 text-xs font-medium text-indigo-700 ring-1 ring-inset ring-indigo-600/20 dark:bg-indigo-400/10 dark:text-indigo-400 dark:ring-indigo-400/20"
                >
                  Trial
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {subscription_status(@current_user.subscription.status)}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Start Date</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {format_datetime(@current_user.subscription.period_start)}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Current Period End</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {format_datetime(@current_user.subscription.period_end)}
              </dd>
            </div>
          </dl>

          <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Features</dt>
            <dd class="mt-1">
              <ul :if={@current_user.plan} class="grid grid-cols-1 gap-2">
                <li
                  :for={feature <- @current_user.enabled_features}
                  class="flex items-center text-sm text-gray-900 dark:text-gray-100"
                >
                  <.icon name="hero-check" class="w-4 h-4 text-green-500 mr-2" />
                  <span class="flex-grow">
                    <span
                      :if={feature.feature_coming_soon}
                      class="inline-flex items-center rounded-md bg-indigo-50 px-1.5 py-0.5 text-xs font-medium text-indigo-700 ring-1 ring-inset ring-indigo-600/20 dark:bg-indigo-400/10 dark:text-indigo-400 dark:ring-indigo-400/20 mr-1"
                    >
                      soon
                    </span>
                    {feature.feature_description}
                  </span>
                </li>
              </ul>
            </dd>

            <div
              :if={is_nil(@current_user.trial_end)}
              class="mt-6 flex flex-col sm:flex-row items-start gap-4"
            >
              <%= if upgrade_plans = available_upgrade_plans(@current_user.plan, @plans) do %>
                <%= for plan <- upgrade_plans do %>
                  <div class="w-full sm:w-auto">
                    <.plan_change_form plan={plan} highlight={true} cta={"Upgrade to #{plan.name}"} />
                  </div>
                <% end %>
              <% end %>

              <%= if downgrade_plans = available_downgrade_plans(@current_user.plan, @plans) do %>
                <%= for plan <- downgrade_plans do %>
                  <div class="w-full sm:w-auto">
                    <.plan_change_form
                      plan={plan}
                      highlight={false}
                      cta={"Downgrade to #{plan.name}"}
                    />
                  </div>
                <% end %>
              <% end %>

              <div class="w-full sm:w-auto flex items-center">
                <button
                  phx-click="cancel_subscription"
                  phx-target={@myself}
                  data-confirm="Are you sure you want to cancel your subscription? This action cannot be undone."
                  class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-red-600 text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-800 dark:hover:bg-red-900/50 dark:border-red-500 dark:text-red-500"
                >
                  Cancel Subscription
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Usage</h3>
        <div class="max-w-xl mx-auto">
          <div class="space-y-4">
            <div :for={feature <- feature_counters(@current_user.enabled_features)}>
              <div class="flex justify-between items-center mb-1">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {feature.feature_description}
                </span>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {format_usage_value(feature.current_usage)} / {format_usage_value(feature.limit)}
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class={[
                    "h-2 rounded-full",
                    get_progress_color(feature.current_usage, feature.limit)
                  ]}
                  style={"width: #{get_progress_percentage(feature.current_usage, feature.limit)}%"}
                >
                </div>
              </div>
              <div
                :if={show_reset_period?(feature)}
                class="mt-1 text-xs text-gray-500 dark:text-gray-400"
              >
                Resets {feature.reset_period}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    plans = Crosspost.Admin.list_active_plans()
    features = Crosspost.Features.list_features()
    features = Map.new(features, fn f -> {f.key, f} end)

    trial_end = assigns[:trial_end] || false

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:plans, plans)
     |> assign(:features, features)
     |> assign(:trial_end, trial_end)}
  end

  @impl true
  def handle_event("cancel_subscription", _, socket) do
    user = socket.assigns.current_user

    Logger.metadata(user_id: user.id)

    case user.subscription do
      %{stripe_subscription_id: subscription_id} when is_binary(subscription_id) ->
        case Crosspost.Accounts.cancel_subscription(user) do
          {:ok, updated_user} ->
            send(self(), :subscription_cancelled)

            {:noreply, socket |> assign(:current_user, updated_user)}

          {:error, error} ->
            Logger.error("Failed to cancel subscription", error: inspect(error))

            send(
              self(),
              {:show_flash, {"Failed to cancel subscription.", :error}}
            )

            {:noreply, socket}
        end

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "No active subscription found.")}
    end
  end

  defp available_upgrade_plans(current_plan, plans) do
    plans
    |> Enum.filter(&(&1.enabled && &1.price > current_plan.price))
    |> Enum.sort_by(& &1.price)
  end

  defp available_downgrade_plans(current_plan, plans) do
    plans
    |> Enum.filter(&(&1.enabled && &1.price < current_plan.price))
    |> Enum.sort_by(& &1.price, :desc)
  end

  defp feature_counters(features) do
    Enum.filter(features, fn feature -> feature.feature_type in [:counter, :limit] end)
  end

  defp show_reset_period?(feature) do
    feature.reset_period != nil and feature.limit > 0
  end

  defp get_progress_percentage(nil, _limit), do: 0
  defp get_progress_percentage(_value, nil), do: 0
  defp get_progress_percentage(-1, _limit), do: 0
  defp get_progress_percentage(_value, -1), do: 0

  defp get_progress_percentage(value, limit) when is_number(value) and is_number(limit) do
    percentage = value / limit * 100
    min(percentage, 100)
  end

  defp get_progress_color(nil, _limit), do: "bg-blue-600"
  defp get_progress_color(_value, nil), do: "bg-blue-600"
  defp get_progress_color(-1, _limit), do: "bg-blue-600"
  defp get_progress_color(_value, -1), do: "bg-blue-600"

  defp get_progress_color(value, limit) when is_number(value) and is_number(limit) do
    percentage = value / limit * 100

    cond do
      percentage >= 100 -> "bg-red-600"
      percentage >= 75 -> "bg-yellow-600"
      true -> "bg-blue-600"
    end
  end

  defp format_datetime(nil), do: "-"

  defp format_datetime(%DateTime{} = datetime) do
    datetime
    |> DateTime.to_date()
    |> Calendar.strftime("%B %d, %Y")
  end

  defp format_usage_value(-1), do: "∞"
  defp format_usage_value(value), do: value

  defp subscription_status(nil), do: "No subscription"
  defp subscription_status(status), do: String.capitalize(status)
end
