defmodule CrosspostWeb.UserSettingsLive.SocialConnections do
  use CrosspostWeb, :live_component

  import Phoenix.Component

  alias Crosspost.Mastodon

  require Logger

  @networks [
    {:bsky, "bg-blue-400", "hover:bg-emerald-50"},
    {:linkedin, "bg-blue-600", "hover:bg-emerald-50"},
    {:mastodon, "bg-purple-600", "hover:bg-emerald-50"},
    {:x, "bg-black", "hover:bg-emerald-50"}
  ]

  def resolve_component(network) do
    case network do
      :linkedin ->
        CrosspostWeb.UserSettingsLive.SocialConnections.LinkedIn

      :mastodon ->
        CrosspostWeb.UserSettingsLive.SocialConnections.Mastodon

      :bsky ->
        CrosspostWeb.UserSettingsLive.SocialConnections.Bsky

      :x ->
        CrosspostWeb.UserSettingsLive.SocialConnections.X

      _ ->
        nil
    end
  end

  @impl true
  def update(assigns, socket) do
    connections =
      Crosspost.Accounts.list_user_connections(assigns.current_workspace.id)
      |> Enum.reduce(%{}, fn conn, acc ->
        Map.put(acc, String.to_existing_atom(conn.platform), conn)
      end)

    enabled_features = assigns.current_user.enabled_features

    enabled_networks =
      enabled_features
      |> Enum.filter(&String.starts_with?(&1.feature_key, "crossposting_"))
      |> Enum.map(fn feature ->
        feature.feature_key
        |> String.replace_prefix("crossposting_", "")
        |> String.to_existing_atom()
      end)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:networks, @networks)
     |> assign(:connections, connections)
     |> assign(:enabled_networks, enabled_networks)
     |> assign(:current_workspace, assigns.current_workspace)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <div class="max-w-4xl">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            :for={
              {network, bg_color, hover_color} <-
                sort_networks(@networks, @connections) |> filter_networks(@enabled_networks)
            }
            class="col-span-1"
          >
            <.live_component
              module={resolve_component(network)}
              current_workspace={@current_workspace}
              current_user={@current_user}
              id={network}
              bg_color={bg_color}
              hover_color={hover_color}
              connection={@connections[network]}
            />
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp sort_networks(networks, connections) do
    Enum.sort_by(networks, fn {network, _, _} -> !Map.has_key?(connections, network) end)
  end

  defp filter_networks(networks, enabled_networks) do
    Enum.filter(networks, fn {network, _, _} -> network in enabled_networks end)
  end

  @impl true
  def handle_event("connect_mastodon", %{"instance" => instance}, socket) do
    if instance && instance != "" do
      {:noreply,
       socket
       |> assign(:show_mastodon_modal, false)
       |> push_navigate(
         to:
           ~p"/auth/mastodon?instance=#{instance}&workspace_id=#{socket.assigns.current_workspace.id}"
       )}
    else
      {:noreply,
       socket
       |> put_flash(:error, "Please enter a Mastodon instance")}
    end
  end

  def handle_event("search_instances", %{"value" => ""}, socket) do
    {:noreply,
     socket
     |> assign(:filtered_instances, [])
     |> assign(:show_instance_suggestions, false)}
  end

  def handle_event("search_instances", %{"value" => query}, socket) do
    case Mastodon.Instances.list_instances(query) do
      {:ok, %{"instances" => instances}} ->
        {:noreply,
         socket
         |> assign(:filtered_instances, instances)
         |> assign(:show_instance_suggestions, true)}

      {:error, _} ->
        {:noreply,
         socket
         |> assign(:filtered_instances, [])
         |> assign(:show_instance_suggestions, false)}
    end
  end

  def handle_event("select_instance", %{"instance" => instance}, socket) do
    {:noreply,
     socket
     |> assign(:selected_instance, instance)
     |> assign(:show_instance_suggestions, false)}
  end
end
