defmodule CrosspostWeb.UserSettingsLive.Workspaces.Modals do
  use Phoenix.Component

  import CrosspostWeb.CoreComponents

  alias Crosspost.Accounts

  def edit_modal(assigns) do
    ~H"""
    <.modal id={@id} on_cancel={hide_modal(@id)}>
      <.simple_form for={@workspace_form} phx-submit="save_workspace" phx-target={@target}>
        <input :if={@workspace.id} type="hidden" value={@workspace.id} id={@id <> "-id"} />
        <div class="space-y-4">
          <div>
            <.input
              placeholder="Workspace Name"
              field={@workspace_form[:name]}
              id={@id <> "-name"}
              type="text"
              required
            />
            <.error :for={msg <- @workspace_form[:name].errors}>
              {translate_error(msg)}
            </.error>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <.button
            type="button"
            phx-click={hide_modal(@id)}
            class="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-gray-300 rounded-md shadow-sm hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </.button>
          <.button type="submit" phx-disable-with="Saving..." phx-value-id={@workspace_form.id}>
            Save Workspace
          </.button>
        </div>
      </.simple_form>
    </.modal>
    """
  end

  def invite_modal(assigns) do
    ~H"""
    <.modal id={@id} on_cancel={hide_modal(@id)}>
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Invite users to {@workspace.name}
        </h3>

        <%= if @workspace.owner_id == @current_user.id and not @workspace.is_default do %>
          <.simple_form
            for={@invite_form}
            id={"send-invites-form-#{@workspace.id}"}
            phx-submit="send_invites"
            phx-target={@target}
          >
            <input
              type="hidden"
              name="workspace_id"
              id={"workspace-id-#{@workspace.id}"}
              value={@workspace.id}
            />
            <div>
              <.input
                field={@invite_form[:emails]}
                type="textarea"
                id={"emails-#{@workspace.id}"}
                placeholder="Enter email addresses, one per line"
                rows="3"
                required
              />
            </div>

            <div class="mt-6 flex justify-end space-x-3">
              <.button
                type="button"
                phx-click={hide_modal(@id)}
                class="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-gray-300 rounded-md shadow-sm hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </.button>
              <.button type="submit" phx-disable-with="Sending...">
                Send Invites
              </.button>
            </div>
          </.simple_form>
        <% else %>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            <%= if @workspace.is_default do %>
              You cannot invite users to the default workspace.
            <% else %>
              Only workspace owners can invite users.
            <% end %>
          </p>
          <div class="mt-6 flex justify-end">
            <.button type="button" phx-click={hide_modal(@id)}>
              Close
            </.button>
          </div>
        <% end %>
      </div>
    </.modal>
    """
  end

  def members_modal(assigns) do
    ~H"""
    <.modal id={@id} on_cancel={hide_modal(@id)}>
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Manage Members - {@workspace.name}
        </h3>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div
            :for={member <- Accounts.list_workspace_users(@workspace)}
            class="py-4 flex items-center justify-between"
          >
            <div class="flex items-center min-w-0 gap-x-4">
              <img class="h-8 w-8 rounded-full bg-gray-50" src={gravatar_url(member.email)} alt="" />
              <div class="min-w-0 flex-auto">
                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                  {member.email}
                </p>
                <%= if member.id == @workspace.owner_id do %>
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Owner
                  </p>
                <% end %>
              </div>
            </div>
            <%= if member.id != @workspace.owner_id do %>
              <.button
                type="button"
                phx-click="remove_member"
                phx-value-workspace-id={@workspace.id}
                phx-value-user-id={member.id}
                data-confirm={"Are you sure you want to remove #{member.email} from this workspace?"}
                phx-target={@target}
                variant="danger"
                size="s"
              >
                Remove
              </.button>
            <% end %>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <.button type="button" phx-click={hide_modal("members-modal-#{@workspace.id}")}>
          Close
        </.button>
      </div>
    </.modal>
    """
  end

  defp gravatar_url(email) do
    hash =
      email
      |> String.trim()
      |> String.downcase()
      |> :erlang.md5()
      |> Base.encode16(case: :lower)

    "https://www.gravatar.com/avatar/#{hash}?s=200&d=mp"
  end
end
