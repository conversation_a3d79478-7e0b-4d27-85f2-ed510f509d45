defmodule CrosspostWeb.UserSettingsLive.ProfileInformation do
  use CrosspostWeb, :live_component

  require Logger

  import CrosspostWeb.DatetimeHelpers

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)}
  end

  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Profile Information</h3>
        <div class="max-w-xl mx-auto">
          <.form
            for={@profile_form}
            id="profile_form"
            phx-change="validate_profile"
            phx-submit="update_profile"
          >
            <div class="space-y-6">
              <div>
                <.label for="name">Name</.label>
                <.input field={@profile_form[:name]} type="text" id="name" required />
                <.error :for={error <- @profile_form[:name].errors}>
                  {translate_error(error)}
                </.error>
              </div>

              <div>
                <.label for="email">Email</.label>
                <.input
                  field={@profile_form[:email]}
                  type="email"
                  id="email"
                  required
                  phx-debounce="blur"
                />
              </div>

              <div>
                <.label for="timezone">Timezone</.label>
                <.timezone_select
                  id="timezone"
                  name="user[timezone]"
                  current_user={@current_user}
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                />
              </div>

              <div class="flex justify-end">
                <.button phx-disable-with="Saving...">Update Profile</.button>
              </div>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
