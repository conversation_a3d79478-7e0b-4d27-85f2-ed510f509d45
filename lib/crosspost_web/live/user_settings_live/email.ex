defmodule CrosspostWeb.UserSettingsLive.Email do
  use CrosspostWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="space-y-8">
      <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">Change Email</h3>
        <div class="max-w-xl mx-auto">
          <.form
            for={@email_form}
            id="email_form"
            phx-change="validate_email"
            phx-submit="update_email"
          >
            <div class="space-y-6">
              <div>
                <.label for="email">Email</.label>
                <.input field={@email_form[:email]} type="email" id="email" required />
                <.error :for={error <- @email_form[:email].errors}>
                  {translate_error(error)}
                </.error>
              </div>

              <div>
                <.label for="current_password">Current Password</.label>
                <.input
                  field={@email_form[:current_password]}
                  type="password"
                  id="current_password"
                  required
                />
                <.error :for={error <- @email_form[:current_password].errors}>
                  {translate_error(error)}
                </.error>
              </div>

              <div class="flex justify-end">
                <.button phx-disable-with="Changing...">Change Email</.button>
              </div>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
