defmodule CrosspostWeb.UserSettingsLive.DeleteAccount do
  use CrosspostWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 shadow p-6 sm:rounded-lg">
      <div class="max-w-xl mx-auto">
        <.simple_form for={%{}} as={:user} id="delete_account_form" phx-submit="delete_account">
          <%= if @current_user.hashed_password do %>
            <.input
              type="password"
              label="Enter your password to confirm"
              name="current_password"
              value=""
              required
            />
          <% end %>
          <.input
            type="textarea"
            label="Care to explain why you're removing the account? (Optional)"
            name="feedback"
            value=""
            rows={4}
          />
          <.input
            type="text"
            label="Type 'delete my account' to confirm"
            name="confirmation"
            value=""
            required
          />
          <:actions>
            <div class="flex justify-end">
              <.button
                type="submit"
                class="bg-red-600 hover:bg-red-700 focus:ring-red-500"
                phx-disable-with="Deleting..."
              >
                Delete Account
              </.button>
            </div>
          </:actions>
        </.simple_form>
      </div>
    </div>
    """
  end
end
