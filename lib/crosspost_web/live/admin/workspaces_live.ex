defmodule CrosspostWeb.Admin.WorkspacesLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  import CrosspostWeb.AdminComponents

  @filters [
    owner_id: [
      op: :==,
      label: "Owner",
      type: "select",
      prompt: "Select Owner",
      options: []
    ],
    name: [
      op: :ilike_and,
      label: "Name"
    ]
  ]

  @impl true
  def mount(_params, _session, socket) do
    filters = update_owner_filter_options(@filters)

    {:ok,
     socket
     |> assign(:filters, filters)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Admin.list_workspaces(params) do
      {:ok, {workspaces, meta}} ->
        {:noreply,
         socket
         |> assign(:page_title, "Workspaces")
         |> assign(:workspaces, workspaces)
         |> assign(:meta, meta)
         |> assign(:total_count, meta.total_count)
         |> assign(:form, Phoenix.Component.to_form(meta))}

      {:error, meta} ->
        {:noreply,
         socket
         |> assign(:page_title, "Workspaces")
         |> assign(:workspaces, [])
         |> assign(:meta, meta)}
    end
  end

  @impl true
  def handle_event("update-filter", %{"reset" => "true"}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/workspaces")}
  end

  @impl true
  def handle_event("update-filter", params, socket) do
    params = Map.delete(params, "_target")
    {:noreply, push_patch(socket, to: ~p"/admin/workspaces?#{params}")}
  end

  defp update_owner_filter_options(filters) do
    users = Admin.list_users_for_select()

    user_options =
      users
      |> Enum.map(fn user ->
        display_name = if user.name, do: "#{user.name} (#{user.email})", else: user.email
        [key: display_name, value: user.id]
      end)

    put_in(filters[:owner_id][:options], user_options)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Workspaces</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all workspaces in the system. Total workspaces: {@total_count}
          </p>
        </div>
      </div>

      <.filter_form meta={@meta} id="workspaces-filter" form={@form} fields={@filters} />

      <div class="mt-8">
        <div class="admin-table-container">
          <Flop.Phoenix.table items={@workspaces} meta={@meta} path={~p"/admin/workspaces"}>
            <:col :let={workspace} label="Name" field={:name}>
              <div class="font-medium text-gray-900">
                {workspace.name}
              </div>
            </:col>
            <:col :let={workspace} label="Slug" field={:slug}>
              <div class="text-gray-900">{workspace.slug}</div>
            </:col>
            <:col :let={workspace} label="Owner" field={:owner_id}>
              <div class="text-gray-900">
                {if workspace.owner.name, do: workspace.owner.name, else: workspace.owner.email}
              </div>
            </:col>
            <:col :let={workspace} label="Default?" field={:is_default}>
              <div class="text-gray-900">
                {if workspace.is_default, do: "Yes", else: "No"}
              </div>
            </:col>
            <:col :let={workspace} label="Created" field={:inserted_at}>
              {Calendar.strftime(workspace.inserted_at, "%Y-%m-%d %H:%M")}
            </:col>
            <:action :let={workspace}>
              <div class="flex justify-end">
                <.link
                  navigate={~p"/admin/workspaces/#{workspace.id}"}
                  class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
                >
                  View →
                </.link>
              </div>
            </:action>
          </Flop.Phoenix.table>
        </div>
      </div>

      <.pagination_links meta={@meta} path={~p"/admin/workspaces"} />
    </div>
    """
  end
end
