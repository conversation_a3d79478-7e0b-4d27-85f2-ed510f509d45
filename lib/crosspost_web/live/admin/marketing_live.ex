defmodule CrosspostWeb.Admin.MarketingLive do
  use CrosspostWeb, :live_view

  require Logger

  alias Crosspost.Admin.Marketing.Subscribers
  alias Crosspost.Admin.Marketing.Emails
  alias Crosspost.Admin.Marketing.Account
  alias Crosspost.Repo

  import CrosspostWeb.CoreComponents
  import Ecto.Query

  @groups Emails.get_group_keys()
  @all_group_key "all"
  @default_per_page 20
  @default_sort_field "email"
  @default_sort_direction :asc
  @networks ["bsky", "mastodon", "x", "linkedin"]

  @impl true
  def mount(_params, _session, socket) do
    counts = fetch_group_counts()

    {:ok,
     socket
     |> assign(:page_title, "Marketing Emails")
     |> assign(:groups, @groups)
     |> assign(:group_counts, counts)
     |> assign(:all_group_key, @all_group_key)
     |> assign(:group_filter, @all_group_key)
     |> assign(:accounts, [])
     |> assign(:page, 1)
     |> assign(:per_page, @default_per_page)
     |> assign(:total_count, 0)
     |> assign(:total_pages, 1)
     |> assign(:sort_field, @default_sort_field)
     |> assign(:sort_direction, @default_sort_direction)
     |> assign(:connected_platforms, [])
     |> assign(:networks, @networks)
     |> assign(:syncing, false)
     |> assign(:sync_progress, nil)
     |> assign(:sync_result, nil)
     |> assign(:expanded_sections, %{
       imported: false,
       removed: false,
       updated: false
     }), temporary_assigns: [accounts: []]}
  end

  @impl true
  def handle_params(params, _url, socket) do
    Logger.debug("Handling params", event: "marketing.handle_params", params: params)
    group_filter = Map.get(params, "group", socket.assigns.group_filter)
    page = String.to_integer(Map.get(params, "page", "1"))
    per_page = String.to_integer(Map.get(params, "per_page", "#{@default_per_page}"))
    sort_field = Map.get(params, "sort_field", socket.assigns.sort_field)
    sort_direction = to_sort_direction(Map.get(params, "sort_direction", @default_sort_direction))

    connected_platforms =
      case Map.get(params, "connected_platforms", []) do
        v when is_list(v) -> Enum.reject(v, &(&1 == ""))
        v when is_binary(v) and v != "" -> String.split(v, ",")
        _ -> []
      end

    # Recalculate counts
    counts = fetch_group_counts()

    case Subscribers.list_accounts_with_filters(
           group_filter,
           page,
           per_page,
           sort_field,
           sort_direction,
           connected_platforms
         ) do
      %{accounts: accounts, total_count: total_count, total_pages: total_pages} ->
        {:noreply,
         socket
         |> assign(accounts: accounts)
         |> assign(group_filter: group_filter)
         |> assign(:page, page)
         |> assign(:per_page, per_page)
         |> assign(:total_count, total_count)
         |> assign(:total_pages, total_pages)
         |> assign(:sort_field, sort_field)
         |> assign(:sort_direction, sort_direction)
         |> assign(:connected_platforms, connected_platforms)
         |> assign(:group_counts, counts)
         |> assign(:networks, @networks)}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id="marketing">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Marketing Emails</h1>
          <p class="mt-2 text-sm text-gray-700">
            Manage and sync MailerLite email groups based on user segments.
            Total accounts matching filter: {@total_count}
          </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <.button
            phx-click="sync_group"
            phx-value-group={@group_filter}
            disabled={@syncing || @group_filter == @all_group_key}
            phx-disable-with="Syncing..."
            class="ml-auto"
            data-test="sync-button"
            title={
              if @group_filter == @all_group_key, do: "Select a specific group to sync", else: nil
            }
          >
            Sync '{group_name(@group_filter)}' with MailerLite
          </.button>
        </div>
      </div>
      
    <!-- Sync progress and result display -->
      <div :if={@syncing || @sync_result} class="mt-4 border rounded-md p-4 bg-gray-50">
        <.render_sync_progress
          syncing={@syncing}
          sync_progress={@sync_progress}
          group_filter={@group_filter}
        />
        <.render_sync_result
          :if={@sync_result}
          sync_result={@sync_result}
          expanded_sections={@expanded_sections}
          syncing={@syncing}
        />
      </div>
      
    <!-- Group filter buttons -->
      <div class="mt-4 flex flex-wrap gap-2">
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group=""
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == @all_group_key,
              do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          All Subscribers
          <span class="ml-1 text-xs">({Map.get(@group_counts, @all_group_key, 0)})</span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="customers"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "customers",
              do: "bg-green-50 text-green-700 ring-green-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("customers")}
          <span class="ml-1 text-xs">({Map.get(@group_counts, "customers", 0)})</span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="waitlist_with_posts"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "waitlist_with_posts",
              do: "bg-blue-50 text-blue-700 ring-blue-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("waitlist_with_posts")}
          <span class="ml-1 text-xs">({Map.get(@group_counts, "waitlist_with_posts", 0)})</span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="waitlist_without_posts"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "waitlist_without_posts",
              do: "bg-purple-50 text-purple-700 ring-purple-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("waitlist_without_posts")}
          <span class="ml-1 text-xs">({Map.get(@group_counts, "waitlist_without_posts", 0)})</span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="registration_with_posts"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "registration_with_posts",
              do: "bg-yellow-50 text-yellow-700 ring-yellow-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("registration_with_posts")}
          <span class="ml-1 text-xs">({Map.get(@group_counts, "registration_with_posts", 0)})</span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="registration_without_posts"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "registration_without_posts",
              do: "bg-orange-50 text-orange-700 ring-orange-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("registration_without_posts")}
          <span class="ml-1 text-xs">
            ({Map.get(@group_counts, "registration_without_posts", 0)})
          </span>
        </button>
        <button
          type="button"
          phx-click="toggle_group"
          phx-value-group="waitlisted_not_signed_up"
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@group_filter == "waitlisted_not_signed_up",
              do: "bg-pink-50 text-pink-700 ring-pink-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          {Emails.get_group_name("waitlisted_not_signed_up")}
          <span class="ml-1 text-xs">({Map.get(@group_counts, "waitlisted_not_signed_up", 0)})</span>
        </button>
      </div>
      
    <!-- Connected platforms filter buttons -->
      <div class="mt-4 flex flex-wrap gap-2">
        <%= for network <- @networks do %>
          <button
            type="button"
            phx-click="toggle_platform"
            phx-value-platform={network}
            class={[
              "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
              if(network in @connected_platforms,
                do: "bg-indigo-600 text-white ring-indigo-600",
                else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
              )
            ]}
          >
            {String.capitalize(network)}
          </button>
        <% end %>
        <button
          type="button"
          phx-click="toggle_platform"
          phx-value-platform=""
          class={[
            "inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset",
            if(@connected_platforms == [],
              do: "bg-indigo-50 text-indigo-700 ring-indigo-600/20",
              else: "bg-gray-50 text-gray-600 ring-gray-500/10 hover:bg-gray-100"
            )
          ]}
        >
          All Platforms
        </button>
      </div>

      <div class="mt-8 flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                  >
                    <button
                      type="button"
                      phx-click="sort"
                      phx-value-field="email"
                      class="group inline-flex"
                    >
                      Email
                      <.sort_indicator
                        field="email"
                        current_field={@sort_field}
                        direction={@sort_direction}
                      />
                    </button>
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    <button
                      type="button"
                      phx-click="sort"
                      phx-value-field="name"
                      class="group inline-flex"
                    >
                      Name
                      <.sort_indicator
                        field="name"
                        current_field={@sort_field}
                        direction={@sort_direction}
                      />
                    </button>
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    <button
                      type="button"
                      phx-click="sort"
                      phx-value-field="status"
                      class="group inline-flex"
                    >
                      Status
                      <.sort_indicator
                        field="status"
                        current_field={@sort_field}
                        direction={@sort_direction}
                      />
                    </button>
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    <button
                      type="button"
                      phx-click="sort"
                      phx-value-field="conversion_source"
                      class="group inline-flex"
                    >
                      Source
                      <.sort_indicator
                        field="conversion_source"
                        current_field={@sort_field}
                        direction={@sort_direction}
                      />
                    </button>
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    <button
                      type="button"
                      phx-click="sort"
                      phx-value-field="published_post_count"
                      class="group inline-flex"
                    >
                      Posts
                      <.sort_indicator
                        field="published_post_count"
                        current_field={@sort_field}
                        direction={@sort_direction}
                      />
                    </button>
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Connected Platforms
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <%= for account <- @accounts do %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                      {account.email}
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {account.name || "-"}
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {account.status || "-"}
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {account.conversion_source || "-"}
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 text-center">
                      {account.published_post_count}
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {Enum.join(account.connected_platforms || [], ", ")}
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>

            <div :if={@total_pages > 1} class="mt-4 flex items-center justify-between">
              <div class="text-sm text-gray-700">
                Showing <span class="font-medium">{(@page - 1) * @per_page + 1}</span>
                to <span class="font-medium">{min(@page * @per_page, @total_count)}</span>
                of <span class="font-medium">{@total_count}</span>
                accounts
              </div>
              <div class="flex space-x-2">
                <.button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page - 1}
                  disabled={@page <= 1}
                >
                  Previous
                </.button>
                <.button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page + 1}
                  disabled={@page >= @total_pages}
                >
                  Next
                </.button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("toggle_group", %{"group" => group_key}, socket) do
    Logger.debug("Toggling group", event: "marketing.toggle_group", group: group_key)
    params = build_params(socket.assigns, group: group_key, page: 1)
    {:noreply, push_patch(socket, to: ~p"/admin/marketing?#{params}")}
  end

  @impl true
  def handle_event("sort", %{"field" => field}, socket) do
    Logger.debug("Sorting by field", event: "marketing.sort", field: field)
    current_field = socket.assigns.sort_field
    current_direction = socket.assigns.sort_direction

    {new_field, new_direction} =
      if field == current_field do
        {field, if(current_direction == :asc, do: :desc, else: :asc)}
      else
        # Default to asc for email, desc for count, asc otherwise
        default_dir = if field == "published_post_count", do: :desc, else: :asc
        {field, default_dir}
      end

    params =
      build_params(socket.assigns, sort_field: new_field, sort_direction: new_direction, page: 1)

    {:noreply, push_patch(socket, to: ~p"/admin/marketing?#{params}")}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    Logger.debug("Paginating", event: "marketing.paginate", page: page)
    page = String.to_integer(page)
    params = build_params(socket.assigns, page: page)
    {:noreply, push_patch(socket, to: ~p"/admin/marketing?#{params}")}
  end

  @impl true
  def handle_event("sync_group", %{"group" => group_key}, socket) do
    Logger.debug("Syncing group", event: "marketing.sync_group", group: group_key)

    if group_key == @all_group_key do
      {:noreply,
       socket
       |> put_flash(:error, "Cannot sync 'All' group. Please select a specific group.")}
    else
      lv_pid = self()

      socket = assign(socket, :syncing, true)
      socket = assign(socket, :sync_progress, nil)
      socket = assign(socket, :sync_result, nil)

      # Use Task.Supervisor for better error handling and monitoring
      Task.start_link(fn ->
        Logger.debug("Starting sync task", event: "marketing.sync_task.start", group: group_key)

        # Start with initial progress state
        send(lv_pid, {:sync_progress_update, %{percent: 0, processed: 0, total: 0, errored: 0}})

        case Emails.sync_group(group_key) do
          {:ok, result} ->
            Logger.debug("Sync task completed successfully",
              event: "marketing.sync_task.complete",
              group: group_key,
              result: result
            )

            send(lv_pid, {:sync_complete, {:ok, group_key, result}})

          {:error, reason} ->
            Logger.error("Sync task failed",
              event: "marketing.sync_task.error",
              group: group_key,
              error: reason
            )

            send(lv_pid, {:sync_complete, {:error, group_key, reason}})
        end
      end)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("dismiss_sync_result", _params, socket) do
    {:noreply, assign(socket, :sync_result, nil)}
  end

  @impl true
  def handle_event("toggle_platform", %{"platform" => platform}, socket) do
    current = socket.assigns.connected_platforms || []

    new_platforms =
      cond do
        platform == "" -> []
        platform in current -> List.delete(current, platform)
        true -> [platform | current]
      end

    params = build_params(socket.assigns, connected_platforms: new_platforms, page: 1)
    {:noreply, push_patch(socket, to: ~p"/admin/marketing?#{params}")}
  end

  @impl true
  def handle_event("toggle_section", %{"section" => section}, socket) do
    current = socket.assigns.expanded_sections
    updated = Map.put(current, String.to_atom(section), !current[String.to_atom(section)])
    {:noreply, assign(socket, :expanded_sections, updated)}
  end

  @impl true
  def handle_info({:sync_complete, {:ok, group_key, result}}, socket) do
    message = Map.get(result, :message, "Sync completed for group '#{group_name(group_key)}'")

    details =
      case result do
        %{
          data: %{
            "processed" => processed,
            "imported" => imported_count,
            "errored" => errored_count
          },
          details: %{imported: imported, updated: updated, removed: removed}
        } ->
          %{
            processed: processed || 0,
            imported_count: imported_count || 0,
            errored_count: errored_count || 0,
            imported: imported || [],
            updated: updated || [],
            removed: removed || []
          }

        # Handle cases where import might succeed but process 0 subscribers (no 'data' key maybe)
        %{details: %{imported: imported, updated: updated, removed: removed}} ->
          %{
            processed: 0,
            imported_count: 0,
            errored_count: 0,
            imported: imported || [],
            updated: updated || [],
            removed: removed || []
          }

        # Default structure if result is unexpected
        _ ->
          %{
            processed: 0,
            imported_count: 0,
            errored_count: 0,
            imported: [],
            updated: [],
            removed: []
          }
      end

    sync_result = %{
      status: :success,
      message: message,
      details: details
    }

    {:noreply,
     socket
     |> assign(:syncing, false)
     |> assign(:sync_progress, nil)
     |> assign(:sync_result, sync_result)
     # Refresh counts
     |> assign(:group_counts, fetch_group_counts())}
  end

  def handle_info({:sync_complete, {:error, group_key, reason}}, socket) do
    # Ensure details always has the expected keys, even if empty
    base_details = %{imported: [], updated: [], removed: []}
    error_details = Map.get(reason, :details, %{})
    details = Map.merge(base_details, error_details)

    sync_result = %{
      status: :error,
      message: Map.get(reason, :message, "Failed to sync group '#{group_name(group_key)}'"),
      details: details
    }

    {:noreply,
     socket
     |> assign(:syncing, false)
     |> assign(:sync_progress, nil)
     |> assign(:sync_result, sync_result)}
  end

  def handle_info({:sync_progress_update, progress}, socket) do
    {:noreply, assign(socket, :sync_progress, progress)}
  end

  defp group_name(key) when key == @all_group_key, do: "All"
  defp group_name(key), do: Crosspost.Admin.Marketing.Emails.get_group_name(key)

  defp fetch_group_counts() do
    all_count = Repo.aggregate(Account, :count, :id)
    waitlist_not_signed_up_count = length(Subscribers.waitlisted_not_signed_up_emails())

    group_counts =
      %{
        "customers" => Repo.aggregate(from(a in Account, where: a.customer == true), :count, :id),
        "waitlist_with_posts" =>
          Repo.aggregate(
            from(a in Account,
              where:
                a.customer == false and a.conversion_source == "waitlist" and
                  a.published_post_count > 0
            ),
            :count,
            :id
          ),
        "waitlist_without_posts" =>
          Repo.aggregate(
            from(a in Account,
              where:
                a.customer == false and a.conversion_source == "waitlist" and
                  a.published_post_count == 0
            ),
            :count,
            :id
          ),
        "registration_with_posts" =>
          Repo.aggregate(
            from(a in Account,
              where:
                a.customer == false and a.conversion_source == "registration" and
                  a.published_post_count > 0
            ),
            :count,
            :id
          ),
        "registration_without_posts" =>
          Repo.aggregate(
            from(a in Account,
              where:
                a.customer == false and a.conversion_source == "registration" and
                  a.published_post_count == 0
            ),
            :count,
            :id
          ),
        "waitlisted_not_signed_up" => waitlist_not_signed_up_count,
        @all_group_key => all_count
      }

    group_counts
  end

  defp build_params(assigns, new_params) do
    base_params = %{
      group: assigns.group_filter,
      page: assigns.page,
      per_page: assigns.per_page,
      sort_field: assigns.sort_field,
      sort_direction: assigns.sort_direction,
      connected_platforms: assigns.connected_platforms
    }

    new_params = if is_list(new_params), do: Map.new(new_params), else: new_params
    merged = Map.merge(base_params, new_params)
    # Remove default values to keep URL cleaner
    merged =
      merged
      |> Enum.reject(fn
        {:page, 1} -> true
        {:per_page, @default_per_page} -> true
        {:sort_field, @default_sort_field} -> true
        {:sort_direction, @default_sort_direction} -> true
        {:group, @all_group_key} -> true
        {:connected_platforms, v} when v == [] -> true
        _ -> false
      end)
      |> Map.new()

    # If connected_platforms is a list, join as comma string for URL
    if Map.has_key?(merged, :connected_platforms) and is_list(merged.connected_platforms) do
      Map.update!(merged, :connected_platforms, &Enum.join(&1, ","))
    else
      merged
    end
  end

  # Helper to ensure sort direction is always an atom
  defp to_sort_direction(direction) when is_atom(direction), do: direction
  defp to_sort_direction("asc"), do: :asc
  defp to_sort_direction("desc"), do: :desc
  # Default
  defp to_sort_direction(_), do: @default_sort_direction

  # Sort indicator component
  defp sort_indicator(assigns) do
    ~H"""
    <span class="ml-2 flex-none rounded text-gray-400">
      <%= if @current_field == @field do %>
        <%= if @direction == :asc do %>
          <.icon name="hero-chevron-up" class="h-5 w-5" />
        <% else %>
          <.icon name="hero-chevron-down" class="h-5 w-5" />
        <% end %>
      <% else %>
        <.icon name="hero-chevron-up-down" class="h-5 w-5" />
      <% end %>
    </span>
    """
  end

  defp render_subscriber_list(assigns) do
    ~H"""
    <div class="pl-7 space-y-1 text-sm">
      <%= for subscriber <- @subscribers do %>
        <div class="flex items-center gap-2">
          <div class={[
            "w-2 h-2 rounded-full",
            status_color(subscriber.status)
          ]}>
          </div>
          <span class="text-gray-900">{subscriber.email}</span>
          <%= if Map.has_key?(subscriber, :error) do %>
            <span class="text-red-600 text-xs">({subscriber.error})</span>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  defp status_color(status) do
    case status do
      "imported" -> "bg-green-500"
      "updated" -> "bg-blue-500"
      "removed" -> "bg-gray-500"
      "failed" -> "bg-red-500"
      "pending" -> "bg-yellow-500"
      _ -> "bg-gray-300"
    end
  end

  defp render_sync_details(assigns) do
    ~H"""
    <div :if={@imported != [] or @updated != [] or @removed != []} class="mt-4 space-y-4">
      <!-- Imported subscribers -->
      <div :if={@imported != []} class="space-y-2">
        <button
          type="button"
          phx-click="toggle_section"
          phx-value-section="imported"
          class="flex items-center gap-2 text-sm font-medium text-gray-900"
        >
          <div class={[
            "w-4 h-4 transition-transform",
            @expanded_sections.imported && "rotate-90"
          ]}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </div>
          <span>Imported Subscribers ({length(@imported)})</span>
        </button>

        <div :if={@expanded_sections.imported}>
          <.render_subscriber_list subscribers={@imported} />
        </div>
      </div>
      
    <!-- Updated subscribers -->
      <div :if={@updated != []} class="space-y-2">
        <button
          type="button"
          phx-click="toggle_section"
          phx-value-section="updated"
          class="flex items-center gap-2 text-sm font-medium text-gray-900"
        >
          <div class={[
            "w-4 h-4 transition-transform",
            @expanded_sections.updated && "rotate-90"
          ]}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </div>
          <span>Updated Subscribers ({length(@updated)})</span>
        </button>

        <div :if={@expanded_sections.updated}>
          <.render_subscriber_list subscribers={@updated} />
        </div>
      </div>
      
    <!-- Removed subscribers -->
      <div :if={@removed != []} class="space-y-2">
        <button
          type="button"
          phx-click="toggle_section"
          phx-value-section="removed"
          class="flex items-center gap-2 text-sm font-medium text-gray-900"
        >
          <div class={[
            "w-4 h-4 transition-transform",
            @expanded_sections.removed && "rotate-90"
          ]}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </div>
          <span>Removed Subscribers ({length(@removed)})</span>
        </button>

        <div :if={@expanded_sections.removed}>
          <.render_subscriber_list subscribers={@removed} />
        </div>
      </div>
    </div>
    """
  end

  defp render_sync_progress(assigns) do
    assigns = Map.put(assigns, :syncing, assigns[:syncing] || false)

    ~H"""
    <div :if={@syncing} class="space-y-3">
      <div class="flex items-center gap-2">
        <div class="animate-spin h-5 w-5 text-indigo-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
            />
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900">
          Syncing '{group_name(@group_filter)}' with MailerLite...
        </h3>
      </div>

      <div :if={@sync_progress} class="space-y-1">
        <div class="flex justify-between text-xs text-gray-600">
          <span>Progress: {@sync_progress.percent}%</span>
          <span>{@sync_progress.processed}/{@sync_progress.total} processed</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
          <div class="bg-indigo-600 h-2.5 rounded-full" style={"width: #{@sync_progress.percent}%"}>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp render_sync_result(assigns) do
    assigns = Map.put(assigns, :syncing, assigns[:syncing] || false)

    ~H"""
    <div :if={@sync_result && !@syncing} class="space-y-2">
      <div class="flex items-center gap-2">
        <div :if={@sync_result.status == :success} class="h-5 w-5 text-green-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div :if={@sync_result.status == :error} class="h-5 w-5 text-red-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900">
          {@sync_result.message}
        </h3>
      </div>

      <div :if={@sync_result.details} class="pl-7 text-sm text-gray-600">
        <div :if={@sync_result.status == :success}>
          <p>Processed: {@sync_result.details.processed}</p>
          <p>Imported: {@sync_result.details.imported_count}</p>
          <p :if={@sync_result.details.errored_count > 0} class="text-amber-600">
            Failed: {@sync_result.details.errored_count}
          </p>
        </div>
        <div :if={@sync_result.status == :error} class="text-red-600">
          <p>Error: {@sync_result.message}</p>
        </div>
      </div>

      <.render_sync_details
        :if={Map.get(@sync_result, :details)}
        imported={Map.get(@sync_result.details, :imported, [])}
        updated={Map.get(@sync_result.details, :updated, [])}
        removed={Map.get(@sync_result.details, :removed, [])}
        expanded_sections={@expanded_sections}
      />

      <div class="pl-7 mt-2">
        <button phx-click="dismiss_sync_result" class="text-xs text-indigo-600 hover:text-indigo-800">
          Dismiss
        </button>
      </div>
    </div>
    """
  end
end
