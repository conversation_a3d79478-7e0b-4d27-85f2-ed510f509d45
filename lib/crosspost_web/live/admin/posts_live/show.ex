defmodule CrosspostWeb.Admin.PostsLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Publishing
  alias Crosspost.Repo
  alias Crosspost.Accounts.User
  import CrosspostWeb.PostComponents

  @post_statuses [
    "draft",
    "pending",
    "scheduled",
    "publishing",
    "partially_published",
    "published",
    "failed"
  ]

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_post(id) do
      {:ok, post} ->
        # Load workspace
        workspace = Repo.get!(Crosspost.Accounts.Workspace, post.workspace_id)
        # Load connections for this post
        connections = Admin.connections_for_post(post)

        {:ok,
         socket
         |> assign(:page_title, "Post Details")
         |> assign(:post, post)
         |> assign(:workspace, workspace)
         |> assign(:connections, connections)
         |> assign(:show_outcome_modal, false)
         |> assign(:selected_status, nil)
         |> assign(:expanded_threads, %{})
         |> assign(:expanded_statuses, %{})
         |> assign(:show_connection_modal, false)
         |> assign(:selected_connection, nil)
         |> assign(:post_statuses, @post_statuses)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Post not found")
         |> redirect(to: ~p"/admin/posts")}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("publish-now", _, socket) do
    admin_post = socket.assigns.post

    post = Publishing.get_post_for_publishing!(admin_post.id)
    user = Repo.get!(User, admin_post.user_id)

    case Publishing.publish_post_now(post, user) do
      {:ok, _updated_post} ->
        case Admin.get_post(admin_post.id) do
          {:ok, reloaded_post} ->
            {:noreply,
             socket
             |> assign(:post, reloaded_post)
             |> put_flash(:info, "Post queued for immediate publishing")}

          {:error, _} ->
            {:noreply,
             socket
             |> put_flash(:error, "Post was published but failed to reload the latest status")}
        end

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to publish post: #{reason}")}
    end
  end

  def handle_event(
        "show-outcome",
        %{"status-id" => status_id, "content-id" => content_id},
        socket
      ) do
    status =
      socket.assigns.post.post_statuses
      |> Enum.find(&(&1.id == status_id and &1.content_id == content_id))

    {:noreply, socket |> assign(:selected_status, status) |> assign(:show_outcome_modal, true)}
  end

  def handle_event("close-outcome-modal", _, socket) do
    {:noreply, socket |> assign(:show_outcome_modal, false) |> assign(:selected_status, nil)}
  end

  def handle_event("toggle-thread", %{"network" => network}, socket) do
    expanded_threads = Map.update(socket.assigns.expanded_threads, network, true, &(not &1))
    {:noreply, assign(socket, :expanded_threads, expanded_threads)}
  end

  def handle_event("toggle-statuses", %{"network" => network, "content-id" => content_id}, socket) do
    expanded_statuses =
      Map.update(socket.assigns.expanded_statuses, "#{network}-#{content_id}", true, &(not &1))

    {:noreply, assign(socket, :expanded_statuses, expanded_statuses)}
  end

  def handle_event("update_status", %{"status" => new_status}, socket) do
    case Admin.update_post(socket.assigns.post, %{status: new_status}) do
      {:ok, updated_post} ->
        {:noreply,
         socket
         |> assign(:post, updated_post)
         |> put_flash(:info, "Post status updated successfully")}

      {:error, _} ->
        {:noreply, socket |> put_flash(:error, "Failed to update post status")}
    end
  end

  def handle_event("show-connection", %{"id" => connection_id}, socket) do
    connection =
      Enum.find(socket.assigns.connections, &(&1.id == String.to_integer(connection_id)))

    {:noreply,
     socket
     |> assign(:show_connection_modal, true)
     |> assign(:selected_connection, connection)}
  end

  def handle_event("hide-connection", _, socket) do
    {:noreply,
     socket
     |> assign(:show_connection_modal, false)
     |> assign(:selected_connection, nil)}
  end

  defp thread_expanded?(expanded_threads, network) do
    Map.get(expanded_threads, network, false)
  end

  defp statuses_expanded?(expanded_statuses, key) do
    Map.get(expanded_statuses, key, false)
  end

  defp post_status_list(assigns) do
    ~H"""
    <%= if length(@statuses) > 0 do %>
      <div class="mt-4">
        <button
          type="button"
          class="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
          phx-click="toggle-statuses"
          phx-value-network={@network}
          phx-value-content-id={@content_id}
        >
          <.icon
            name={
              if statuses_expanded?(@expanded_statuses, "#{@network}-#{@content_id}"),
                do: "hero-chevron-up",
                else: "hero-chevron-down"
            }
            class="w-4 h-4"
          />
          <span>Publishing Status ({length(@statuses)})</span>
        </button>

        <div :if={statuses_expanded?(@expanded_statuses, "#{@network}-#{@content_id}")} class="mt-2">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <tbody class="divide-y divide-gray-200">
                <%= for status <- Enum.sort_by(@statuses, & &1.inserted_at, {:desc, NaiveDateTime}) do %>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm w-48">
                      <div class={publish_status_badge_class(status.status)}>
                        <.network_icon
                          network={status.network}
                          status={status.status}
                          animate={false}
                        />
                        <span class="ml-1">{humanize_status(status.status)}</span>
                      </div>
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      {Calendar.strftime(status.inserted_at, "%Y-%m-%d %H:%M")}
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500 w-12 text-center">
                      <.icon
                        :if={status.is_final}
                        name="hero-check-circle"
                        class="w-5 h-5 text-green-500"
                      />
                    </td>
                    <td class="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                      <button
                        type="button"
                        phx-click="show-outcome"
                        phx-value-status-id={status.id}
                        phx-value-content-id={@content_id}
                        class="text-brand-600 hover:text-brand-900"
                      >
                        Show outcome
                      </button>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    <% end %>
    """
  end

  defp post_content_list(assigns) do
    ~H"""
    <div class="space-y-8">
      <div
        :for={{network, contents} <- Enum.group_by(@post.post_content, & &1.network)}
        class="bg-gray-50 rounded-lg overflow-hidden border border-gray-200"
      >
        <div class="bg-white border-b border-gray-200 px-6 py-4">
          <div class="flex items-center gap-2">
            <h4 class="font-medium text-gray-900">
              {Atom.to_string(network) |> String.capitalize()}
            </h4>
            <div class="text-sm text-gray-500">
              <.network_icon network={network} status={@post.status} animate={false} />
            </div>
            <%= if length(contents) > 1 do %>
              <button
                type="button"
                class="ml-auto text-sm text-gray-500 hover:text-gray-700"
                phx-click="toggle-thread"
                phx-value-network={network}
              >
                <%= if thread_expanded?(@expanded_threads, network) do %>
                  <.icon name="hero-chevron-up" class="w-4 h-4" />
                <% else %>
                  <.icon name="hero-chevron-down" class="w-4 h-4" />
                <% end %>
                {length(contents) - 1} replies
              </button>
            <% end %>
          </div>
        </div>

        <div class="divide-y divide-gray-200">
          <%= for {content, index} <- Enum.with_index(Enum.sort_by(contents, & &1.order)) do %>
            <div class={[
              "px-6 py-4",
              index > 0 && !thread_expanded?(@expanded_threads, network) && "hidden",
              index > 0 && "bg-gray-50"
            ]}>
              <div class={[
                "prose prose-sm max-w-none",
                index > 0 && "ml-6 pl-4 border-l-2 border-gray-200"
              ]}>
                <p class="whitespace-pre-wrap">{content.text}</p>

                <div :if={length(content.attachments) > 0} class="mt-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Attachments</h5>
                  <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                    <div :for={attachment <- content.attachments} class="relative">
                      <div class="group aspect-h-7 aspect-w-10 block w-full overflow-hidden rounded-lg bg-gray-100">
                        <img
                          :if={attachment.type == :image}
                          src={attachment.source_url}
                          alt={attachment.metadata["alt"] || ""}
                          class="pointer-events-none object-cover group-hover:opacity-75"
                        />
                        <img
                          :if={attachment.type == :video}
                          src={attachment.preview_url}
                          alt=""
                          class="pointer-events-none object-cover group-hover:opacity-75"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <%= if url = Crosspost.Posts.get_network_post_url(@post, network) do %>
                  <div class="mt-4">
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                    >
                      View published post
                      <span class="sr-only">on {Phoenix.Naming.humanize(network)}</span>
                      <.icon name="hero-arrow-top-right-on-square" class="w-3 h-3 ml-1" />
                    </a>
                  </div>
                <% end %>

                <.post_status_list
                  statuses={
                    @post.post_statuses
                    |> Enum.filter(&(&1.content_id == content.id))
                  }
                  network={network}
                  content_id={content.id}
                  expanded_statuses={@expanded_statuses}
                />
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  defp user_status_badge(user) do
    cond do
      user.subscription && user.subscription.status == "active" &&
          user.subscription.stripe_customer_id ->
        assigns = %{
          text: "Customer",
          classes: "bg-green-50 text-green-700 ring-green-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.trial_end && DateTime.compare(user.trial_end, DateTime.utc_now()) == :gt ->
        assigns = %{
          text: "Trial",
          classes: "bg-blue-50 text-blue-700 ring-blue-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.trial_end && DateTime.compare(user.trial_end, DateTime.utc_now()) == :lt ->
        assigns = %{
          text: "Trial Ended",
          classes: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      true ->
        "—"
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/posts"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to posts
            </.link>
            <.link
              navigate={~p"/admin/posts/#{@post.id}/edit"}
              class="text-sm font-semibold leading-6 text-brand-600 hover:text-brand-900"
            >
              Edit
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Post Details</h1>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <button
            phx-click="publish-now"
            data-confirm="Are you sure you want to publish this post now?"
            type="button"
            class="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Publish Now
          </button>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Post Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about this post and its publishing status.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">User</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div class="flex flex-col">
                    <div class="flex items-center gap-2">
                      <span>{if @post.user.name, do: @post.user.name, else: @post.user.email}</span>
                      {user_status_badge(@post.user)}
                    </div>
                    <span class="text-gray-500">{@post.user.email}</span>
                    <span :if={@post.user.trial_end} class="text-gray-500 text-xs mt-1">
                      Trial ends: {Calendar.strftime(@post.user.trial_end, "%Y-%m-%d %H:%M")}
                    </span>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Workspace</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div class="flex flex-col">
                    <span>{@workspace.name}</span>
                    <span class="text-gray-500">ID: {@workspace.id}</span>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Status</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <form phx-change="update_status" class="flex items-center gap-3">
                    <select
                      name="status"
                      class="block w-full max-w-xs rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6"
                      value={@post.status}
                    >
                      <option
                        :for={status <- @post_statuses}
                        value={status}
                        selected={status == @post.status}
                      >
                        {String.replace(status, "_", " ") |> String.capitalize()}
                      </option>
                    </select>
                    <div class={publish_status_badge_class(String.to_atom(@post.status))}>
                      <span>{humanize_status(String.to_atom(@post.status))}</span>
                    </div>
                  </form>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Networks</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <.publish_status_badges post={@post} />
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@post.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Updated</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@post.updated_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Social Connections</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Connected social media accounts used for this post.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <%= if Enum.empty?(@connections) do %>
                <div class="text-sm text-gray-500">
                  No social connections found for this post's networks.
                </div>
              <% else %>
                <div class="flow-root">
                  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                      <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                          <tr>
                            <th
                              scope="col"
                              class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                            >
                              Platform
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              User
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Platform User ID
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Expires
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                          <%= for connection <- @connections do %>
                            <tr>
                              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                {String.capitalize(connection.platform)}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {connection.user.email}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {connection.platform_user_id}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {if connection.expires_at do
                                  Calendar.strftime(connection.expires_at, "%Y-%m-%d %H:%M")
                                else
                                  "Never"
                                end}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                <button
                                  type="button"
                                  phx-click="show-connection"
                                  phx-value-id={connection.id}
                                  class="text-blue-600 hover:text-blue-900"
                                >
                                  Show
                                </button>
                              </td>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Content</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Post content for each network.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <.post_content_list
                post={@post}
                expanded_threads={@expanded_threads}
                expanded_statuses={@expanded_statuses}
              />
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Schedules</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Publishing schedules for each network.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div :if={length(@post.schedules) == 0} class="text-sm text-gray-500">
                No schedules set
              </div>
              <div :for={schedule <- @post.schedules} class="mb-4 last:mb-0">
                <div class={publish_status_badge_class(:scheduled)}>
                  <.network_icon network={schedule.network} status={:scheduled} />
                  <span class="ml-1">
                    {Calendar.strftime(schedule.scheduled_at, "%Y-%m-%d %H:%M")} ({schedule.timezone})
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <.modal
        :if={@show_outcome_modal}
        id="outcome-modal"
        show
        on_cancel={JS.push("close-outcome-modal")}
      >
        <div class="space-y-4">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Status Outcome</h3>
          <%= if is_nil(@selected_status.outcome) || @selected_status.outcome == "" do %>
            <div class="text-sm text-gray-500">No outcome data available</div>
          <% else %>
            <pre class="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm"><code>{
              outcome = @selected_status.outcome
              case outcome do
                str when is_binary(str) -> str |> Jason.decode!() |> Jason.encode!(pretty: true)
                map when is_map(map) -> Jason.encode!(map, pretty: true)
                _ -> "Unknown outcome type: #{inspect(outcome)}"
              end
            }</code></pre>
          <% end %>
        </div>
      </.modal>

      <.modal
        :if={@show_connection_modal}
        id="connection-modal"
        show
        on_cancel={JS.push("hide-connection")}
      >
        <div :if={@selected_connection} class="space-y-4">
          <h3 class="text-lg font-medium leading-6 text-gray-900">
            Connection Details - {String.capitalize(@selected_connection.platform)}
          </h3>

          <div>
            <h4 class="text-sm font-medium text-gray-900">Settings</h4>
            <div class="mt-2">
              <div
                id="connection-settings-editor"
                phx-hook="JsonEditor"
                data-json={Jason.encode!(@selected_connection.settings)}
              />
            </div>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-900">Info</h4>
            <div class="mt-2">
              <div
                id="connection-info-editor"
                phx-hook="JsonEditor"
                data-json={Jason.encode!(@selected_connection.info)}
              />
            </div>
          </div>
        </div>
      </.modal>
    </div>
    """
  end
end
