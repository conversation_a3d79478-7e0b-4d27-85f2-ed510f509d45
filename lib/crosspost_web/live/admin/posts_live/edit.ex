defmodule CrosspostWeb.Admin.PostsLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  import CrosspostWeb.PostComponents

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "Edit Post")
     |> assign(:post, nil)
     |> assign(:editing_content, nil)}
  end

  @impl true
  def handle_params(%{"id" => id}, _url, socket) do
    case Admin.get_post(id) do
      {:ok, post} ->
        {:noreply,
         socket
         |> assign(:post, post)}

      {:error, :not_found} ->
        {:noreply,
         socket
         |> put_flash(:error, "Post not found")
         |> push_navigate(to: ~p"/admin/posts")}
    end
  end

  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("edit_content", %{"id" => content_id}, socket) do
    content = Enum.find(socket.assigns.post.post_content, &(&1.id == content_id))
    {:noreply, assign(socket, :editing_content, content)}
  end

  def handle_event("cancel_edit", _, socket) do
    {:noreply, assign(socket, :editing_content, nil)}
  end

  def handle_event("save_content", %{"content" => %{"text" => text, "order" => order}}, socket) do
    content = socket.assigns.editing_content
    order = String.to_integer(order)

    case Admin.update_post_content(content, %{text: text, order: order}) do
      {:ok, {:ok, updated_post}} ->
        {:noreply,
         socket
         |> assign(:post, updated_post)
         |> assign(:editing_content, nil)
         |> put_flash(:info, "Content updated successfully")}

      {:ok, {:error, _reason}} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update content")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update content")}
    end
  end

  defp post_content_list(assigns) do
    ~H"""
    <div class="space-y-8">
      <div
        :for={{network, contents} <- Enum.group_by(@post.post_content, & &1.network)}
        class="bg-gray-50 rounded-lg overflow-hidden border border-gray-200"
      >
        <div class="bg-white border-b border-gray-200 px-6 py-4">
          <div class="flex items-center gap-2">
            <h4 class="font-medium text-gray-900">
              {Atom.to_string(network) |> String.capitalize()}
            </h4>
            <div class="text-sm text-gray-500">
              <.network_icon network={network} status={@post.status} animate={false} />
            </div>
          </div>
        </div>

        <div class="divide-y divide-gray-200">
          <%= for {content, index} <- Enum.with_index(Enum.sort_by(contents, & &1.order)) do %>
            <div class="px-6 py-4">
              <div class={[
                "prose prose-sm max-w-none",
                index > 0 && "ml-6 pl-4 border-l-2 border-gray-200"
              ]}>
                <%= if @editing_content && @editing_content.id == content.id do %>
                  <.form for={%{}} id="content-form" phx-submit="save_content" class="space-y-4">
                    <div>
                      <.input type="textarea" name="content[text]" value={content.text} rows="4" />
                    </div>
                    <div>
                      <.input
                        type="number"
                        name="content[order]"
                        value={content.order}
                        min="0"
                        label="Order"
                      />
                    </div>
                    <div class="flex justify-end gap-2">
                      <.button type="button" phx-click="cancel_edit" variant="secondary">
                        Cancel
                      </.button>
                      <.button type="submit">
                        Save
                      </.button>
                    </div>
                  </.form>
                <% else %>
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="text-sm text-gray-500 mb-1">Order: {content.order}</div>
                      <p class="whitespace-pre-wrap">{content.text}</p>
                    </div>
                    <button
                      type="button"
                      phx-click="edit_content"
                      phx-value-id={content.id}
                      class="ml-4 text-sm text-brand-600 hover:text-brand-900"
                    >
                      Edit
                    </button>
                  </div>
                <% end %>

                <div :if={length(content.attachments) > 0} class="mt-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Attachments</h5>
                  <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                    <div :for={attachment <- content.attachments} class="relative">
                      <div class="group aspect-h-7 aspect-w-10 block w-full overflow-hidden rounded-lg bg-gray-100">
                        <img
                          :if={attachment.type == :image}
                          src={attachment.source_url}
                          alt={attachment.metadata["alt"] || ""}
                          class="pointer-events-none object-cover group-hover:opacity-75"
                        />
                        <img
                          :if={attachment.type == :video}
                          src={attachment.preview_url}
                          alt=""
                          class="pointer-events-none object-cover group-hover:opacity-75"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/posts"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to posts
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Edit Post</h1>
        </div>
      </div>

      <%= if @post do %>
        <div class="mt-8 space-y-8">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Post Information</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Details about this post and its publishing status.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <dl class="divide-y divide-gray-100">
                <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900">User</dt>
                  <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {if @post.user.name, do: @post.user.name, else: @post.user.email}
                  </dd>
                </div>

                <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900">Status</dt>
                  <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <form phx-change="update_status" class="flex items-center gap-3">
                      <select
                        name="status"
                        class="block w-full max-w-xs rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6"
                        value={@post.status}
                      >
                        <option
                          :for={
                            status <-
                              ~w(draft pending scheduled publishing partially_published published failed)
                          }
                          value={status}
                          selected={status == @post.status}
                        >
                          {String.replace(status, "_", " ") |> String.capitalize()}
                        </option>
                      </select>
                      <div class={publish_status_badge_class(String.to_atom(@post.status))}>
                        <span>{humanize_status(String.to_atom(@post.status))}</span>
                      </div>
                    </form>
                  </dd>
                </div>

                <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900">Networks</dt>
                  <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <.publish_status_badges post={@post} />
                  </dd>
                </div>

                <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900">Created</dt>
                  <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {Calendar.strftime(@post.inserted_at, "%Y-%m-%d %H:%M")}
                  </dd>
                </div>

                <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900">Updated</dt>
                  <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {Calendar.strftime(@post.updated_at, "%Y-%m-%d %H:%M")}
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Content</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Post content for each network.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <.post_content_list post={@post} editing_content={@editing_content} />
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
