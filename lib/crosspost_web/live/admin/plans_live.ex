defmodule CrosspostWeb.Admin.PlansLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Features.StripeSync

  @impl true
  def mount(_params, _session, socket) do
    plans = Admin.list_plans()
    {:ok, assign(socket, :plans, plans)}
  end

  @impl true
  def handle_event("sync_stripe", _params, socket) do
    case StripeSync.sync_all() do
      {:ok, %{features: feature_results, products: product_results}} ->
        # Count successes and failures
        feature_successes = Enum.count(feature_results, &match?({:ok, _}, &1))
        feature_failures = Enum.count(feature_results, &match?({:error, _}, &1))
        product_successes = Enum.count(product_results, &match?({:ok, _}, &1))
        product_failures = Enum.count(product_results, &match?({:error, _}, &1))

        socket =
          socket
          |> put_flash(
            :info,
            "Sync completed. Features: #{feature_successes} succeeded, #{feature_failures} failed. Products: #{product_successes} succeeded, #{product_failures} failed."
          )
          |> assign(:plans, Admin.list_plans())

        {:noreply, socket}

      {:error, error} ->
        {:noreply, put_flash(socket, :error, "Sync failed: #{inspect(error)}")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Plans</h1>
          <p class="mt-2 text-sm text-gray-700">A list of all plans in the system.</p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <button
            phx-click="sync_stripe"
            type="button"
            class="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Sync with Stripe
          </button>
        </div>
      </div>

      <div class="mt-8">
        <div class="admin-table-container">
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                  >
                    Key
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Name
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Price
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Status
                  </th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <tr :for={plan <- @plans}>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                    {plan.key}
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{plan.name}</td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {Number.Currency.number_to_currency(plan.price / 100)}
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <span class={[
                      "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
                      if(plan.archived,
                        do: "bg-red-50 text-red-700 ring-red-600/20",
                        else: "bg-green-50 text-green-700 ring-green-600/20"
                      )
                    ]}>
                      {if plan.archived, do: "Archived", else: "Active"}
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex justify-end gap-4">
                      <.link
                        navigate={~p"/admin/plans/#{plan.key}/edit"}
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        Edit<span class="sr-only">, <%= plan.name %></span>
                      </.link>
                      <.link
                        navigate={~p"/admin/plans/#{plan.key}"}
                        class="text-gray-600 hover:text-gray-900"
                      >
                        View<span class="sr-only">, <%= plan.name %></span>
                      </.link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
