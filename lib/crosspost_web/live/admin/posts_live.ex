defmodule CrosspostWeb.Admin.PostsLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  import CrosspostWeb.AdminComponents
  import CrosspostWeb.StatusBadgeComponent

  @impl true
  def mount(_params, _session, socket) do
    users = Admin.users_for_post_filter()

    user_options =
      users
      |> Enum.map(fn user ->
        display_name = if user.name, do: "#{user.name} (#{user.email})", else: user.email
        [key: display_name, value: user.id]
      end)

    {:ok,
     socket
     |> assign(:page_title, "Posts")
     |> assign(:user_id_filter, "")
     |> assign(:status_filter, [])
     |> assign(:network_filter, [])
     |> assign(:post_id_filter, "")
     |> assign(:final_status_date, "")
     |> assign(:page, 1)
     |> assign(:per_page, 20)
     |> assign(:user_options, user_options)
     |> assign(:has_error, false)
     |> assign(:sort_field, "inserted_at")
     |> assign(:sort_direction, :desc)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    user_id_filter = params["user_id"] || ""
    status_filter = params["status"] || []
    # Clean up empty status filters
    status_filter = Enum.reject(status_filter, &(&1 == ""))
    network_filter = params["network"] || []
    # Clean up empty network filters
    network_filter =
      if is_list(network_filter), do: Enum.reject(network_filter, &(&1 == "")), else: []

    post_id_filter = params["post_id"] || ""
    final_status_date = params["final_status_date"] || ""
    page = String.to_integer(params["page"] || "1")
    per_page = String.to_integer(params["per_page"] || "20")
    sort_field = params["sort_field"] || socket.assigns.sort_field

    sort_direction =
      (params["sort_direction"] || socket.assigns.sort_direction) |> to_sort_direction()

    case Admin.list_posts_with_filters(
           user_id_filter,
           status_filter,
           network_filter,
           post_id_filter,
           final_status_date,
           page,
           per_page,
           sort_field,
           sort_direction
         ) do
      {:ok, %{posts: posts, total_count: total_count, total_pages: total_pages}} ->
        {:noreply,
         socket
         |> assign(:page_title, "Posts")
         |> assign(:posts, posts)
         |> assign(:user_id_filter, user_id_filter)
         |> assign(:status_filter, status_filter)
         |> assign(:network_filter, network_filter)
         |> assign(:post_id_filter, post_id_filter)
         |> assign(:final_status_date, final_status_date)
         |> assign(:page, page)
         |> assign(:per_page, per_page)
         |> assign(:total_count, total_count)
         |> assign(:total_pages, total_pages)
         |> assign(:sort_field, sort_field)
         |> assign(:sort_direction, sort_direction)
         |> assign(:has_error, false)}

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:page_title, "Posts")
         |> assign(:posts, [])
         |> assign(:has_error, true)
         |> assign(:error_reason, reason)}
    end
  end

  @impl true
  def handle_event("toggle_status", %{"status" => status}, socket) do
    current_statuses = socket.assigns.status_filter

    new_statuses =
      cond do
        # Clicking "All" always clears the filter
        status == "" -> []
        # If status is already selected, remove it
        status in current_statuses -> List.delete(current_statuses, status)
        # If adding a new status when none were selected, just add this one
        current_statuses == [] -> [status]
        # Otherwise add the new status to the list
        true -> [status | current_statuses]
      end

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: socket.assigns.user_id_filter, status: new_statuses, network: socket.assigns.network_filter, post_id: socket.assigns.post_id_filter, final_status_date: socket.assigns.final_status_date, page: 1, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("toggle_network", %{"network" => network}, socket) do
    current_networks = socket.assigns.network_filter

    new_networks =
      cond do
        # Clicking "All" always clears the filter
        network == "" -> []
        # If network is already selected, remove it
        network in current_networks -> List.delete(current_networks, network)
        # If adding a new network when none were selected, just add this one
        current_networks == [] -> [network]
        # Otherwise add the new network to the list
        true -> [network | current_networks]
      end

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: socket.assigns.user_id_filter, status: socket.assigns.status_filter, network: new_networks, post_id: socket.assigns.post_id_filter, final_status_date: socket.assigns.final_status_date, page: 1, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("toggle_date", %{"date" => date}, socket) do
    # For date filter, we want single-select behavior
    new_date = if socket.assigns.final_status_date == date, do: "", else: date

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: socket.assigns.user_id_filter, status: socket.assigns.status_filter, network: socket.assigns.network_filter, post_id: socket.assigns.post_id_filter, final_status_date: new_date, page: 1, per_page: socket.assigns.per_page, sort_field: socket.assigns.sort_field, sort_direction: socket.assigns.sort_direction}}"
     )}
  end

  @impl true
  def handle_event("filter", params, socket) do
    user_id = params["user_id"] || ""
    network = params["network"] || ""
    post_id = params["post_id"] || ""
    final_status_date = params["final_status_date"] || ""

    # Get status from params, ensuring it's always a list and removing empty values
    status = Map.get(params, "status", [])
    status = if is_list(status), do: Enum.reject(status, &(&1 == "")), else: []

    # If user_id is not a UUID, it might be a search term
    user_id =
      case Ecto.UUID.cast(user_id) do
        {:ok, _} -> user_id
        :error -> find_user_id_by_search(user_id, socket.assigns.user_options)
      end

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: user_id, status: status, network: network, post_id: post_id, final_status_date: final_status_date, page: 1, per_page: socket.assigns.per_page, sort_field: socket.assigns.sort_field, sort_direction: socket.assigns.sort_direction}}"
     )}
  end

  @impl true
  def handle_event("reset-filter", _, socket) do
    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{sort_field: socket.assigns.sort_field, sort_direction: socket.assigns.sort_direction}}"
     )}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    page = String.to_integer(page)

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: socket.assigns.user_id_filter, status: socket.assigns.status_filter, network: socket.assigns.network_filter, post_id: socket.assigns.post_id_filter, final_status_date: socket.assigns.final_status_date, page: page, per_page: socket.assigns.per_page, sort_field: socket.assigns.sort_field, sort_direction: socket.assigns.sort_direction}}"
     )}
  end

  @impl true
  def handle_event("search", %{"post_id" => post_id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/posts/#{post_id}")}
  end

  @impl true
  def handle_event("sort", %{"field" => field}, socket) do
    current_field = socket.assigns.sort_field
    current_direction = socket.assigns.sort_direction

    # If clicking the same field, toggle direction
    # If clicking a new field, default to desc
    {new_field, new_direction} =
      if field == current_field do
        {field, if(current_direction == :asc, do: :desc, else: :asc)}
      else
        {field, :desc}
      end

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/posts?#{%{user_id: socket.assigns.user_id_filter, status: socket.assigns.status_filter, network: socket.assigns.network_filter, post_id: socket.assigns.post_id_filter, final_status_date: socket.assigns.final_status_date, page: 1, per_page: socket.assigns.per_page, sort_field: new_field, sort_direction: new_direction}}"
     )}
  end

  # Helper to ensure sort direction is always an atom
  defp to_sort_direction(direction) when is_atom(direction), do: direction
  defp to_sort_direction("asc"), do: :asc
  defp to_sort_direction("desc"), do: :desc
  defp to_sort_direction(_), do: :desc

  # Helper function to find a user ID from the search term
  defp find_user_id_by_search("", _user_options), do: ""

  defp find_user_id_by_search(search_term, user_options) do
    search_term = String.downcase(search_term)

    case Enum.find(user_options, fn [key: label, value: _] ->
           String.contains?(String.downcase(label), search_term)
         end) do
      [key: _label, value: id] -> id
      nil -> search_term
    end
  end

  defp user_status_badge(user) do
    cond do
      user.user_is_subscribed ->
        assigns = %{
          text: "Customer",
          classes: "bg-green-50 text-green-700 ring-green-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.user_trial_end && DateTime.compare(user.user_trial_end, DateTime.utc_now()) == :gt ->
        assigns = %{
          text: "Trial",
          classes: "bg-blue-50 text-blue-700 ring-blue-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.user_trial_end && DateTime.compare(user.user_trial_end, DateTime.utc_now()) == :lt ->
        assigns = %{
          text: "Trial Ended",
          classes: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      true ->
        "—"
    end
  end

  defp sort_indicator(assigns) do
    ~H"""
    <span class="ml-2 flex-none rounded">
      <%= if @current_field == @field do %>
        <%= if @direction == :asc do %>
          <.icon name="hero-arrow-up" class="h-4 w-4" />
        <% else %>
          <.icon name="hero-arrow-down" class="h-4 w-4" />
        <% end %>
      <% end %>
    </span>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <.flash_group flash={@flash} />
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Posts</h1>
          <p class="mt-2 text-sm text-gray-700">
            <%= if @has_error do %>
              Invalid filter parameters
            <% else %>
              A list of all posts in the system. Total posts: {@total_count}
            <% end %>
          </p>
        </div>
      </div>

      <div id="posts-filter-container">
        <.posts_filter_form
          user_id={@user_id_filter}
          status={@status_filter}
          network={@network_filter}
          post_id={@post_id_filter}
          final_status_date={@final_status_date}
          user_options={@user_options}
          on_filter="filter"
          on_reset="reset-filter"
        />

        <%= if @has_error do %>
          <div class="rounded-md bg-red-50 p-4 mt-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Invalid filter parameters
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>
                    {@error_reason || "Please check your filter settings and try again."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="mt-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                    >
                      User
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Status
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Networks
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      <button
                        type="button"
                        phx-click="sort"
                        phx-value-field="inserted_at"
                        class="group inline-flex"
                      >
                        Created
                        <.sort_indicator
                          field="inserted_at"
                          current_field={@sort_field}
                          direction={@sort_direction}
                        />
                      </button>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      <button
                        type="button"
                        phx-click="sort"
                        phx-value-field="published_at"
                        class="group inline-flex"
                      >
                        Published
                        <.sort_indicator
                          field="published_at"
                          current_field={@sort_field}
                          direction={@sort_direction}
                        />
                      </button>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      <button
                        type="button"
                        phx-click="sort"
                        phx-value-field="updated_at"
                        class="group inline-flex"
                      >
                        Updated
                        <.sort_indicator
                          field="updated_at"
                          current_field={@sort_field}
                          direction={@sort_direction}
                        />
                      </button>
                    </th>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <%= for post <- @posts do %>
                    <tr>
                      <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900">
                        <div class="flex items-center gap-2">
                          <.link
                            navigate={~p"/admin/users/#{post.user_id}"}
                            class="text-gray-900 hover:text-gray-700"
                          >
                            {if post.user_name, do: post.user_name, else: post.user_email}
                          </.link>
                          {user_status_badge(post)}
                        </div>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        <.status_badge status={post.status} />
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {post.social_networks
                        |> Enum.map(&Atom.to_string/1)
                        |> Enum.map(&String.capitalize/1)
                        |> Enum.join(", ")}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {Calendar.strftime(post.inserted_at, "%Y-%m-%d %H:%M")}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {if post.published_at do
                          Calendar.strftime(post.published_at, "%Y-%m-%d %H:%M")
                        else
                          "—"
                        end}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {Calendar.strftime(post.updated_at, "%Y-%m-%d %H:%M")}
                      </td>
                      <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium">
                        <div class="flex justify-end gap-4">
                          <.link
                            navigate={~p"/admin/posts/#{post.id}"}
                            class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
                          >
                            View
                          </.link>
                          <.link
                            navigate={~p"/admin/posts/#{post.id}/edit"}
                            class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
                          >
                            Edit
                          </.link>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>

          <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing <span class="font-medium">{(@page - 1) * @per_page + 1}</span>
              to <span class="font-medium">{min(@page * @per_page, @total_count)}</span>
              of <span class="font-medium">{@total_count}</span>
              posts
            </div>
            <div class="flex space-x-2">
              <%= if @page > 1 do %>
                <button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page - 1}
                  class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                >
                  Previous
                </button>
              <% end %>
              <%= if @page < @total_pages do %>
                <button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page + 1}
                  class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                >
                  Next
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const table = document.querySelector('.admin-table-container table');
        if (!table) return;

        table.addEventListener('click', (e) => {
          if (!e.metaKey && !e.ctrlKey) return;

          const row = e.target.closest('tr');
          if (!row) return;

          const link = row.querySelector('a[href^="/admin/posts/"]');
          if (!link) return;

          e.preventDefault();
          const postId = link.href.split('/').pop().split('?')[0];
          navigator.clipboard.writeText(postId).then(() => {
            const flash = document.createElement('div');
            flash.className = 'flash-message';
            flash.textContent = 'Post ID copied to clipboard';
            flash.style.cssText = `
              position: fixed;
              bottom: 20px;
              right: 20px;
              background: #059669;
              color: white;
              padding: 12px 24px;
              border-radius: 6px;
              font-size: 14px;
              z-index: 50;
              opacity: 0;
              transition: opacity 0.2s ease-in-out;
            `;
            document.body.appendChild(flash);
            requestAnimationFrame(() => {
              flash.style.opacity = '1';
              setTimeout(() => {
                flash.style.opacity = '0';
                setTimeout(() => flash.remove(), 200);
              }, 2000);
            });
          });
        });
      });
    </script>
    """
  end
end
