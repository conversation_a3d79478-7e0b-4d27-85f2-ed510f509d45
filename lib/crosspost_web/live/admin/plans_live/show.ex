defmodule CrosspostWeb.Admin.PlansLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Features

  @impl true
  def mount(%{"key" => key}, _session, socket) do
    case Admin.get_plan(key) do
      {:ok, plan} ->
        features = Features.list_features() |> Enum.map(&{&1.key, &1}) |> Map.new()

        {:ok,
         socket
         |> assign(:plan, plan)
         |> assign(:features, features)}

      {:error, :not_found} ->
        {:ok, socket |> put_flash(:error, "Plan not found") |> redirect(to: ~p"/admin/plans")}
    end
  end

  @impl true
  def handle_event("archive", %{"key" => key}, socket) do
    with {:ok, plan} <- Admin.get_plan(key),
         {:ok, _plan} <- Admin.archive_plan(plan) do
      {:noreply,
       socket
       |> put_flash(:info, "Plan archived successfully")
       |> redirect(to: ~p"/admin/plans")}
    else
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to archive plan")
         |> redirect(to: ~p"/admin/plans")}
    end
  end

  @impl true
  def handle_event("toggle_enabled", %{"key" => key}, socket) do
    with {:ok, plan} <- Admin.get_plan(key),
         {:ok, updated_plan} <- Admin.toggle_plan(plan) do
      {:noreply,
       socket
       |> assign(:plan, updated_plan)
       |> put_flash(
         :info,
         "Plan #{if updated_plan.enabled, do: "enabled", else: "disabled"} successfully"
       )}
    else
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update plan")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="px-4 sm:px-0">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Plan Details</h3>
            <p class="mt-1 text-sm leading-6 text-gray-500">Plan information and settings.</p>
          </div>
          <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-4">
            <%= unless @plan.archived do %>
              <button
                phx-click="toggle_enabled"
                phx-value-key={@plan.key}
                class={[
                  "rounded-md px-3 py-2 text-sm font-semibold shadow-sm",
                  if(@plan.enabled,
                    do: "bg-yellow-600 text-white hover:bg-yellow-500",
                    else: "bg-green-600 text-white hover:bg-green-500"
                  )
                ]}
              >
                {if @plan.enabled, do: "Disable", else: "Enable"}
              </button>
            <% end %>
            <.link
              navigate={~p"/admin/plans/#{@plan.key}/edit"}
              class="text-sm font-semibold leading-6 text-gray-900"
            >
              Edit Features
            </.link>
          </div>
        </div>
      </div>

      <div class="mt-6 border-t border-gray-100">
        <dl class="divide-y divide-gray-100">
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Key</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">{@plan.key}</dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Name</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">{@plan.name}</dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Description</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {@plan.description}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Price</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {Number.Currency.number_to_currency(@plan.price / 100)}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Stripe Price ID</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {@plan.stripe_price_id}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Status</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <div class="flex items-center gap-2">
                <span class={[
                  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
                  if(@plan.archived,
                    do: "bg-red-50 text-red-700 ring-red-600/20",
                    else: "bg-green-50 text-green-700 ring-green-600/20"
                  )
                ]}>
                  {if @plan.archived, do: "Archived", else: "Active"}
                </span>
                <span class={[
                  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
                  if(@plan.enabled,
                    do: "bg-green-50 text-green-700 ring-green-600/20",
                    else: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
                  )
                ]}>
                  {if @plan.enabled, do: "Enabled", else: "Disabled"}
                </span>
              </div>
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Settings</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <div class="space-y-2">
                <div>
                  <span class="font-medium">Highlight:</span>
                  {@plan.highlight}
                </div>
                <div>
                  <span class="font-medium">Enabled:</span>
                  {@plan.enabled}
                </div>
              </div>
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Features</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <div class="flex flex-wrap gap-2">
                <div
                  :for={feature_key <- @plan.features}
                  class="inline-flex items-center gap-2 bg-gray-50 px-2 py-1 rounded-md"
                >
                  <span class="text-sm text-gray-900">
                    {case Map.get(@features, feature_key) do
                      nil -> feature_key
                      feature -> "#{feature.key} (#{feature.name})"
                    end}
                  </span>
                </div>
              </div>
            </dd>
          </div>
        </dl>
      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
        <.link navigate={~p"/admin/plans"} class="text-sm font-semibold leading-6 text-gray-900">
          Back
        </.link>
        <%= unless @plan.archived do %>
          <button
            phx-click="archive"
            phx-value-key={@plan.key}
            class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
          >
            Archive Plan
          </button>
        <% end %>
      </div>
    </div>
    """
  end
end
