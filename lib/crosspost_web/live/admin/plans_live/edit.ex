defmodule CrosspostWeb.Admin.PlansLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Features

  @impl true
  def mount(%{"key" => key}, _session, socket) do
    case Admin.get_plan(key) do
      {:ok, plan} ->
        features = Features.list_features() |> Enum.map(&{&1.key, &1}) |> Map.new()

        {:ok,
         socket
         |> assign(:plan, plan)
         |> assign(:features, features)
         |> assign(:available_features, available_features(features, plan))}

      {:error, :not_found} ->
        {:ok, socket |> put_flash(:error, "Plan not found") |> redirect(to: ~p"/admin/plans")}
    end
  end

  @impl true
  def handle_event("save", %{"plan" => params}, socket) do
    case Admin.update_plan(socket.assigns.plan, params) do
      {:ok, updated_plan} ->
        {:noreply,
         socket
         |> assign(:plan, updated_plan)
         |> put_flash(:info, "Plan updated successfully")}

      {:error, _} ->
        {:noreply, socket |> put_flash(:error, "Failed to update plan")}
    end
  end

  @impl true
  def handle_event("add_feature", %{"plan" => %{"feature" => feature_key}}, socket)
      when feature_key != "" do
    plan = socket.assigns.plan
    features = (plan.features ++ [feature_key]) |> Enum.uniq()

    case Admin.update_plan(plan, %{features: features}) do
      {:ok, updated_plan} ->
        {:noreply,
         socket
         |> assign(:plan, updated_plan)
         |> assign(:available_features, available_features(socket.assigns.features, updated_plan))
         |> put_flash(:info, "Feature added successfully")}

      {:error, _} ->
        {:noreply, socket |> put_flash(:error, "Failed to add feature")}
    end
  end

  def handle_event("add_feature", _, socket), do: {:noreply, socket}

  @impl true
  def handle_event("remove_feature", %{"feature" => feature_key}, socket) do
    plan = socket.assigns.plan
    features = Enum.reject(plan.features, &(&1 == feature_key))

    case Admin.update_plan(plan, %{features: features}) do
      {:ok, updated_plan} ->
        {:noreply,
         socket
         |> assign(:plan, updated_plan)
         |> assign(:available_features, available_features(socket.assigns.features, updated_plan))
         |> put_flash(:info, "Feature removed successfully")}

      {:error, _} ->
        {:noreply, socket |> put_flash(:error, "Failed to remove feature")}
    end
  end

  @impl true
  def handle_event("reorder_features", %{"features" => features}, socket) do
    case Admin.update_plan(socket.assigns.plan, %{features: features}) do
      {:ok, updated_plan} ->
        {:noreply,
         socket
         |> assign(:plan, updated_plan)
         |> put_flash(:info, "Features reordered successfully")}

      {:error, _} ->
        {:noreply, socket |> put_flash(:error, "Failed to reorder features")}
    end
  end

  defp available_features(features, plan) do
    features
    |> Map.values()
    |> Enum.reject(&(&1.key in plan.features))
    |> Enum.sort_by(& &1.key)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="px-4 sm:px-0">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Edit Plan Features</h3>
            <p class="mt-1 text-sm leading-6 text-gray-500">
              Add or remove features for {@plan.name} plan.
            </p>
          </div>
          <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <.link
              navigate={~p"/admin/plans/#{@plan.key}"}
              class="text-sm font-semibold leading-6 text-gray-900"
            >
              Back to Plan
            </.link>
          </div>
        </div>
      </div>

      <div class="mt-6 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Plan Settings</h3>
          </div>
          <div class="px-4 py-6 sm:px-6">
            <.form for={%{}} as={:plan} phx-submit="save">
              <div class="space-y-4">
                <div class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input type="hidden" name="plan[enabled]" value="false" />
                    <input
                      type="checkbox"
                      name="plan[enabled]"
                      value="true"
                      checked={@plan.enabled}
                      class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                  </div>
                  <div class="ml-3 text-sm leading-6">
                    <label class="font-medium text-gray-900">Enabled</label>
                    <p class="text-gray-500">Enable this plan to make it available for purchase.</p>
                  </div>
                </div>
                <div class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input type="hidden" name="plan[default]" value="false" />
                    <input
                      type="checkbox"
                      name="plan[default]"
                      value="true"
                      checked={@plan.default}
                      class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                  </div>
                  <div class="ml-3 text-sm leading-6">
                    <label class="font-medium text-gray-900">Default</label>
                    <p class="text-gray-500">Make this the default plan for new users.</p>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-900">Stripe Price ID</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      name="plan[stripe_price_id]"
                      value={@plan.stripe_price_id}
                      class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </div>
                  <p class="mt-2 text-sm text-gray-500">
                    The Stripe Price ID associated with this plan.
                  </p>
                </div>

                <div class="flex justify-end">
                  <.button type="submit" phx-disable-with="Saving...">
                    Save Settings
                  </.button>
                </div>
              </div>
            </.form>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Current Features</h3>
            <p class="mt-1 text-sm text-gray-500">Drag and drop to reorder features.</p>
          </div>
          <div class="px-4 py-6 sm:px-6">
            <div id="feature-list" phx-hook="FeatureList" class="flex flex-col gap-2">
              <div
                :for={feature_key <- @plan.features}
                data-feature-key={feature_key}
                class="flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-md cursor-move"
              >
                <.icon name="hero-bars-3" class="h-4 w-4 text-gray-400 shrink-0" />
                <span class="text-sm text-gray-900 flex-grow">
                  {case Map.get(@features, feature_key) do
                    nil -> feature_key
                    feature -> "#{feature.key} (#{feature.name})"
                  end}
                </span>
                <button
                  type="button"
                  phx-click="remove_feature"
                  phx-value-feature={feature_key}
                  class="text-gray-400 hover:text-gray-500"
                >
                  <.icon name="hero-x-mark" class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          :if={length(@available_features) > 0}
          class="overflow-hidden bg-white shadow sm:rounded-lg"
        >
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Add Features</h3>
          </div>
          <div class="px-4 py-6 sm:px-6">
            <.form for={%{}} as={:plan} phx-submit="add_feature">
              <div class="flex gap-4">
                <div class="flex-1">
                  <select
                    name="plan[feature]"
                    class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  >
                    <option value="">Select a feature to add</option>
                    <option :for={feature <- @available_features} value={feature.key}>
                      {feature.key} ({feature.name})
                    </option>
                  </select>
                </div>
                <div>
                  <button
                    type="submit"
                    class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    Add Feature
                  </button>
                </div>
              </div>
            </.form>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
