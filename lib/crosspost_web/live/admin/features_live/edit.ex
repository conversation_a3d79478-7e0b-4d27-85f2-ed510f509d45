defmodule CrosspostWeb.Admin.FeaturesLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Admin.Resources.Feature

  @impl true
  def mount(%{"key" => key}, _session, socket) do
    case Admin.get_feature(key) do
      {:ok, feature} ->
        changeset = Feature.changeset(feature, %{})
        feature_in_use? = Admin.feature_in_use?(feature)

        {:ok,
         socket
         |> assign(:page_title, "Edit Feature")
         |> assign(:feature, feature)
         |> assign(:feature_in_use?, feature_in_use?)
         |> assign(:form, to_form(changeset))}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Feature not found")
         |> redirect(to: ~p"/admin/features")}
    end
  end

  @impl true
  def handle_event("validate", %{"feature" => feature_params}, socket) do
    changeset =
      socket.assigns.feature
      |> Feature.changeset(feature_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :form, to_form(changeset))}
  end

  @impl true
  def handle_event("save", %{"feature" => feature_params}, socket) do
    case Admin.update_feature(socket.assigns.feature, feature_params) do
      {:ok, feature} ->
        {:noreply,
         socket
         |> put_flash(:info, "Feature updated successfully")
         |> redirect(to: ~p"/admin/features/#{feature.key}")}

      {:error, changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  @impl true
  def handle_event("delete", _params, socket) do
    case Admin.delete_feature(socket.assigns.feature) do
      {:ok, _feature} ->
        {:noreply,
         socket
         |> put_flash(:info, "Feature deleted successfully")
         |> redirect(to: ~p"/admin/features")}

      {:error, :feature_in_use} ->
        {:noreply,
         socket
         |> put_flash(
           :error,
           "Cannot delete this feature because it is being used by one or more plans"
         )}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete feature")
         |> redirect(to: ~p"/admin/features")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/features/#{@feature.key}"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to feature
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Edit Feature</h1>
        </div>
      </div>

      <div class="mt-8">
        <.form
          for={@form}
          id="feature-form"
          phx-change="validate"
          phx-submit="save"
          class="space-y-8 divide-y divide-gray-200"
        >
          <div class="space-y-8 divide-y divide-gray-200">
            <div>
              <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div class="sm:col-span-3">
                  <.input
                    field={@form[:name]}
                    type="text"
                    label="Name"
                    placeholder="Feature Name"
                    required
                  />
                </div>

                <div class="sm:col-span-6">
                  <.input
                    field={@form[:description]}
                    type="textarea"
                    label="Description"
                    placeholder="Feature description"
                    required
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:type]}
                    type="select"
                    label="Type"
                    options={[
                      {"Counter", :counter},
                      {"Limit", :limit},
                      {"Boolean", :boolean}
                    ]}
                    required
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:reset_period]}
                    type="select"
                    label="Reset Period"
                    options={[
                      {"None", nil},
                      {"Daily", :daily},
                      {"Monthly", :monthly},
                      {"Yearly", :yearly}
                    ]}
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:limit]}
                    type="number"
                    label="Limit"
                    placeholder="Feature limit"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:usage_key]}
                    type="text"
                    label="Usage Key"
                    placeholder="usage_key"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input field={@form[:coming_soon]} type="checkbox" label="Coming Soon" />
                </div>
              </div>
            </div>
          </div>

          <div class="pt-5">
            <div class="flex justify-between">
              <%= if not @feature_in_use? do %>
                <.button
                  type="button"
                  phx-click="delete"
                  class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                  data-confirm="Are you sure you want to delete this feature? This action cannot be undone."
                >
                  Delete Feature
                </.button>
              <% else %>
                <div class="text-sm text-gray-500 italic">
                  This feature cannot be deleted because it is being used by one or more plans
                </div>
              <% end %>
              <.button type="submit" phx-disable-with="Saving...">
                Save changes
              </.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
