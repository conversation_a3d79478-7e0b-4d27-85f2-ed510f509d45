defmodule CrosspostWeb.Admin.FeaturesLive.New do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Admin.Resources.Feature

  @impl true
  def mount(_params, _session, socket) do
    changeset = Feature.changeset(%Feature{}, %{})

    {:ok,
     socket
     |> assign(:page_title, "New Feature")
     |> assign(:feature, %Feature{})
     |> assign(:form, to_form(changeset))}
  end

  @impl true
  def handle_event("validate", %{"feature" => feature_params}, socket) do
    changeset =
      %Feature{}
      |> Feature.changeset(feature_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :form, to_form(changeset))}
  end

  @impl true
  def handle_event("save", %{"feature" => feature_params}, socket) do
    case Admin.create_feature(feature_params) do
      {:ok, feature} ->
        {:noreply,
         socket
         |> put_flash(:info, "Feature created successfully")
         |> redirect(to: ~p"/admin/features/#{feature.key}")}

      {:error, changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/features"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to features
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">New Feature</h1>
        </div>
      </div>

      <div class="mt-8">
        <.form
          for={@form}
          id="feature-form"
          phx-change="validate"
          phx-submit="save"
          class="space-y-8 divide-y divide-gray-200"
        >
          <div class="space-y-8 divide-y divide-gray-200">
            <div>
              <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div class="sm:col-span-3">
                  <.input field={@form[:key]} type="text" label="Key" placeholder="feature_key" />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:name]}
                    type="text"
                    label="Name"
                    placeholder="Feature Name"
                    required
                  />
                </div>

                <div class="sm:col-span-6">
                  <.input
                    field={@form[:description]}
                    type="textarea"
                    label="Description"
                    placeholder="Feature description"
                    required
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:type]}
                    type="select"
                    label="Type"
                    options={[
                      {"Counter", :counter},
                      {"Limit", :limit},
                      {"Boolean", :boolean}
                    ]}
                    required
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:reset_period]}
                    type="select"
                    label="Reset Period"
                    options={[
                      {"None", nil},
                      {"Daily", :daily},
                      {"Monthly", :monthly},
                      {"Yearly", :yearly}
                    ]}
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:limit]}
                    type="number"
                    label="Limit"
                    placeholder="Feature limit"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={@form[:usage_key]}
                    type="text"
                    label="Usage Key"
                    placeholder="usage_key"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input field={@form[:coming_soon]} type="checkbox" label="Coming Soon" />
                </div>
              </div>
            </div>
          </div>

          <div class="pt-5">
            <div class="flex justify-end">
              <.button type="submit" phx-disable-with="Saving...">
                Create feature
              </.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
