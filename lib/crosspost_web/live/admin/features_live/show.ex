defmodule CrosspostWeb.Admin.FeaturesLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"key" => key}, _session, socket) do
    case Admin.get_feature(key) do
      {:ok, feature} ->
        {:ok,
         socket
         |> assign(:page_title, "Feature Details")
         |> assign(:feature, feature)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Feature not found")
         |> redirect(to: ~p"/admin/features")}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/features"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to features
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Feature Details</h1>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Feature Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about this feature.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Key</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@feature.key}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Name</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@feature.name}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Description</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@feature.description}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Type</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {String.capitalize(to_string(@feature.type))}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Usage Key</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@feature.usage_key}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Reset Period</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {String.capitalize(to_string(@feature.reset_period))}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Limit</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@feature.limit}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@feature.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Updated</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@feature.updated_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
