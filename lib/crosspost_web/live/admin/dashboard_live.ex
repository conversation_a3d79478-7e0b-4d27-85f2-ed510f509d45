defmodule CrosspostWeb.Admin.DashboardLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      {:ok,
       socket
       |> assign(:page_title, "Dashboard")
       |> assign_stats()}
    else
      {:ok,
       socket
       |> assign(:page_title, "Dashboard")
       |> assign(:posts_per_day, [])
       |> assign(:network_distribution, [])
       |> assign(:publishing_status_distribution, [])
       |> assign(:status_distribution, [])
       |> assign(:signups_per_day, [])
       |> assign(:new_customers_per_day, [])
       |> assign(:ending_trials, [])
       |> assign(:existing_customers, [])
       |> assign(:total_customers_count, 0)}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Dashboard</h1>
          <p class="mt-2 text-sm text-gray-700">Overview of system statistics and metrics.</p>
        </div>
      </div>

      <div class="mt-8 grid grid-cols-1 gap-8">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Trials Ending Soon</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Users whose trial will end within the next 3 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <div class="flow-root">
                  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                      <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                          <tr>
                            <th
                              scope="col"
                              class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                            >
                              User
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Trial Ends
                            </th>
                          </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                          <%= if Enum.empty?(@ending_trials) do %>
                            <tr>
                              <td colspan="2" class="py-4 pl-4 pr-3 text-sm text-gray-500 sm:pl-0">
                                No trials ending soon.
                              </td>
                            </tr>
                          <% else %>
                            <%= for user <- @ending_trials do %>
                              <tr>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                  {user.name || user.email}
                                </td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                  {format_date(user.trial_end)}
                                </td>
                              </tr>
                            <% end %>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Existing Customers</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Users with active subscriptions. Total customers: {@total_customers_count}
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <div class="flow-root">
                  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                      <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                          <tr>
                            <th
                              scope="col"
                              class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                            >
                              User
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Subscribed On
                            </th>
                            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                              <span class="sr-only">View</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                          <%= if Enum.empty?(@existing_customers) do %>
                            <tr>
                              <td colspan="3" class="py-4 pl-4 pr-3 text-sm text-gray-500 sm:pl-0">
                                No customers found.
                              </td>
                            </tr>
                          <% else %>
                            <%= for user <- @existing_customers do %>
                              <tr>
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                  {user.name || user.email}
                                </td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                  {format_date(user.subscription_start)}
                                </td>
                                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                  <.link
                                    navigate={~p"/admin/users/#{user.id}"}
                                    class="text-blue-600 hover:text-blue-900"
                                  >
                                    View →
                                  </.link>
                                </td>
                              </tr>
                            <% end %>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">User Sign-ups</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Number of new users per day over the last 14 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <canvas
                  id="signups-chart"
                  phx-hook="ChartHook"
                  data-spec={signups_chart_spec(@signups_per_day)}
                />
              </div>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">New Customers</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Number of new paying customers per day over the last 14 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <canvas
                  id="customers-chart"
                  phx-hook="ChartHook"
                  data-spec={customers_chart_spec(@new_customers_per_day)}
                />
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">
                Publishing Status Over Time
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Distribution of publishing statuses per day over the last 14 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6" style="height: 480px;">
                <canvas
                  id="status-chart"
                  phx-hook="ChartHook"
                  data-spec={status_chart_spec(@publishing_status_distribution)}
                />
              </div>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">
                Overall Status Distribution
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Distribution of publishing statuses over the last 30 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6" style="height: 480px;">
                <canvas
                  id="status-pie-chart"
                  phx-hook="ChartHook"
                  data-spec={status_pie_chart_spec(@status_distribution)}
                />
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Posts Created</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Number of posts created per day over the last 14 days.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <canvas
                  id="posts-chart"
                  phx-hook="ChartHook"
                  data-spec={posts_chart_spec(@posts_per_day)}
                />
              </div>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Network Distribution</h3>
              <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                Distribution of posts across different social networks.
              </p>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6" style="height: 360px;">
                <canvas
                  id="networks-chart"
                  phx-hook="ChartHook"
                  data-spec={networks_chart_spec(@network_distribution)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp assign_stats(socket) do
    socket
    |> assign(:posts_per_day, Admin.get_posts_per_day(14))
    |> assign(:network_distribution, Admin.get_network_distribution())
    |> assign(:publishing_status_distribution, Admin.get_publishing_status_distribution(14))
    |> assign(:status_distribution, Admin.get_status_distribution(30))
    |> assign(:signups_per_day, Admin.get_signups_per_day(14))
    |> assign(:new_customers_per_day, Admin.get_new_customers_per_day(14))
    |> assign(:ending_trials, Admin.get_users_with_ending_trial())
    |> assign(:existing_customers, Admin.get_existing_customers())
    |> assign(:total_customers_count, Admin.get_total_customers_count())
  end

  defp status_chart_spec(data) do
    # Group data by date and status
    data_by_date =
      data
      |> Enum.group_by(& &1.date)
      |> Map.new(fn {date, entries} ->
        counts =
          entries
          |> Enum.map(&{&1.status, &1.count})
          |> Map.new()

        {date, counts}
      end)

    # Sort dates chronologically
    dates =
      data_by_date
      |> Map.keys()
      |> Enum.sort_by(& &1, Date)

    statuses = ~w(published partially_published publishing pending failed)

    # Create datasets with consistent ordering and fill in missing values with 0
    datasets =
      statuses
      |> Enum.map(fn status ->
        %{
          label: String.capitalize(status),
          data:
            Enum.map(dates, fn date ->
              data_by_date |> Map.get(date, %{}) |> Map.get(status, 0)
            end),
          backgroundColor: status_color(status)
        }
      end)

    # Calculate the maximum total for any date to set a good suggestedMax
    # Default to 10 if there's no data
    max_total =
      if Enum.empty?(dates) do
        10
      else
        dates
        |> Enum.map(fn date ->
          data_by_date
          |> Map.get(date, %{})
          |> Map.values()
          |> Enum.sum()
        end)
        |> Enum.max()
      end

    # Add 25% padding to the max value
    suggested_max = max(trunc(max_total * 1.25), 10)

    Jason.encode!(%{
      type: "bar",
      data: %{
        labels: Enum.map(dates, &format_date/1),
        datasets: datasets
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        scales: %{
          x: %{
            stacked: true,
            ticks: %{
              maxRotation: 45,
              minRotation: 45
            }
          },
          y: %{
            stacked: true,
            beginAtZero: true,
            suggestedMax: suggested_max,
            ticks: %{
              precision: 0
            }
          }
        },
        plugins: %{
          tooltip: %{
            mode: "index",
            intersect: false
          },
          legend: %{
            display: true,
            position: "top"
          }
        }
      }
    })
  end

  defp format_date(%Date{} = date) do
    Calendar.strftime(date, "%B %d")
  end

  defp format_date(date), do: date

  defp status_color("published"), do: "#9CB99C"
  defp status_color("partially_published"), do: "#D4BC94"
  defp status_color("publishing"), do: "#A5B7D4"
  defp status_color("pending"), do: "#B8BDC3"
  defp status_color("failed"), do: "#D4A5A5"
  defp status_color(_), do: "#CFD3D9"

  defp posts_chart_spec(data) do
    # Sort data chronologically by date
    sorted_data = Enum.sort_by(data, & &1.date, Date)

    Jason.encode!(%{
      type: "line",
      data: %{
        labels: Enum.map(sorted_data, fn %{date: date} -> format_date(date) end),
        datasets: [
          %{
            label: "Posts Created",
            data: Enum.map(sorted_data, & &1.count),
            borderColor: "#3B82F6",
            backgroundColor: "#3B82F6",
            tension: 0.1,
            pointRadius: 4
          }
        ]
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        plugins: %{
          title: %{
            display: false
          },
          tooltip: %{
            mode: "index",
            intersect: false
          }
        },
        scales: %{
          y: %{
            beginAtZero: true,
            ticks: %{
              precision: 0
            }
          }
        }
      }
    })
  end

  defp networks_chart_spec(data) do
    # Map of network to its brand color
    network_colors = %{
      # X
      x: "#000000",
      # LinkedIn
      linkedin: "#0A66C2",
      # Mastodon
      mastodon: "#6364FF",
      # Bluesky
      bsky: "#0085FF"
    }

    Jason.encode!(%{
      type: "pie",
      data: %{
        labels: Enum.map(data, & &1.network),
        datasets: [
          %{
            data: Enum.map(data, & &1.count),
            backgroundColor: Enum.map(data, &network_colors[&1.network])
          }
        ]
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        plugins: %{
          title: %{
            display: false
          },
          legend: %{
            position: "right"
          }
        },
        layout: %{
          padding: 20
        }
      }
    })
  end

  defp signups_chart_spec(data) do
    # Sort data chronologically by date
    sorted_data = Enum.sort_by(data, & &1.date, Date)

    Jason.encode!(%{
      type: "line",
      data: %{
        labels: Enum.map(sorted_data, fn %{date: date} -> format_date(date) end),
        datasets: [
          %{
            label: "New Users",
            data: Enum.map(sorted_data, & &1.count),
            borderColor: "#8B5CF6",
            backgroundColor: "#8B5CF6",
            tension: 0.1,
            pointRadius: 4
          }
        ]
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        plugins: %{
          title: %{
            display: false
          },
          tooltip: %{
            mode: "index",
            intersect: false
          }
        },
        scales: %{
          y: %{
            beginAtZero: true,
            ticks: %{
              precision: 0
            }
          }
        }
      }
    })
  end

  defp status_pie_chart_spec(data) do
    Jason.encode!(%{
      type: "pie",
      data: %{
        labels: Enum.map(data, fn %{status: status} -> String.capitalize(status) end),
        datasets: [
          %{
            data: Enum.map(data, & &1.count),
            backgroundColor: Enum.map(data, &status_color(&1.status))
          }
        ]
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        plugins: %{
          title: %{
            display: false
          },
          legend: %{
            position: "right"
          }
        },
        layout: %{
          padding: 20
        }
      }
    })
  end

  defp customers_chart_spec(data) do
    # Sort data chronologically by date
    sorted_data = Enum.sort_by(data, & &1.date, Date)

    Jason.encode!(%{
      type: "line",
      data: %{
        labels: Enum.map(sorted_data, fn %{date: date} -> format_date(date) end),
        datasets: [
          %{
            label: "New Customers",
            data: Enum.map(sorted_data, & &1.count),
            borderColor: "#10B981",
            backgroundColor: "#10B981",
            tension: 0.1,
            pointRadius: 4
          }
        ]
      },
      options: %{
        responsive: true,
        maintainAspectRatio: false,
        plugins: %{
          title: %{
            display: false
          },
          tooltip: %{
            mode: "index",
            intersect: false
          }
        },
        scales: %{
          y: %{
            beginAtZero: true,
            ticks: %{
              precision: 0
            }
          }
        }
      }
    })
  end
end
