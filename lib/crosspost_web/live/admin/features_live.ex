defmodule CrosspostWeb.Admin.FeaturesLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  import CrosspostWeb.AdminComponents

  @filters [
    key: [
      op: :ilike_and,
      label: "Key"
    ],
    name: [
      op: :ilike_and,
      label: "Name"
    ],
    type: [
      op: :==,
      label: "Type",
      type: "select",
      prompt: "Select Type",
      options: [
        [key: "Counter", value: "counter"],
        [key: "Limit", value: "limit"],
        [key: "Boolean", value: "boolean"]
      ]
    ],
    reset_period: [
      op: :==,
      label: "Reset Period",
      type: "select",
      prompt: "Select Reset Period",
      options: [
        [key: "Yearly", value: "yearly"],
        [key: "Monthly", value: "monthly"],
        [key: "Daily", value: "daily"]
      ]
    ]
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Admin.list_features(params) do
      {:ok, {features, meta}} ->
        features_in_use = Admin.features_in_use?(features)

        {:noreply,
         socket
         |> assign(:page_title, "Features")
         |> assign(:features, features)
         |> assign(:features_in_use, features_in_use)
         |> assign(:meta, meta)
         |> assign(:total_count, meta.total_count)
         |> assign(:filters, @filters)
         |> assign(:form, Phoenix.Component.to_form(meta))}

      {:error, meta} ->
        {:noreply,
         socket
         |> assign(:page_title, "Features")
         |> assign(:features, [])
         |> assign(:meta, meta)}
    end
  end

  @impl true
  def handle_event("update-filter", %{"reset" => "true"}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/features")}
  end

  @impl true
  def handle_event("update-filter", params, socket) do
    params = Map.delete(params, "_target")
    {:noreply, push_patch(socket, to: ~p"/admin/features?#{params}")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Features</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all features in the system. Total features: {@total_count}
          </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <.link
            navigate={~p"/admin/features/new"}
            class="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            New Feature
          </.link>
        </div>
      </div>

      <.filter_form meta={@meta} id="features-filter" form={@form} fields={@filters} />

      <div class="mt-8">
        <div class="admin-table-container">
          <Flop.Phoenix.table items={@features} meta={@meta} path={~p"/admin/features"}>
            <:col :let={feature} label="Key" field={:key}>
              <div class="font-medium text-gray-900">{feature.key}</div>
            </:col>
            <:col :let={feature} label="Name" field={:name}>
              <div class="text-gray-900">{feature.name}</div>
            </:col>
            <:col :let={feature} label="Type" field={:type}>
              <div class="text-gray-900">{String.capitalize(to_string(feature.type))}</div>
            </:col>
            <:col :let={feature} label="Reset Period" field={:reset_period}>
              <div class="text-gray-900">{String.capitalize(to_string(feature.reset_period))}</div>
            </:col>
            <:col :let={feature} label="Limit" field={:limit}>
              <div class="text-gray-900">{feature.limit}</div>
            </:col>
            <:col :let={feature} label="In Use">
              <%= if @features_in_use[feature.key] do %>
                <div class="text-green-600">
                  <.icon name="hero-check-circle" class="h-5 w-5" />
                </div>
              <% end %>
            </:col>
            <:action :let={feature}>
              <div class="flex justify-end gap-4">
                <.link
                  navigate={~p"/admin/features/#{feature.key}/edit"}
                  class="text-sm font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
                >
                  Edit
                </.link>
                <.link
                  navigate={~p"/admin/features/#{feature.key}"}
                  class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
                >
                  View →
                </.link>
              </div>
            </:action>
          </Flop.Phoenix.table>
        </div>
      </div>

      <.pagination_links meta={@meta} path={~p"/admin/features"} />
    </div>
    """
  end
end
