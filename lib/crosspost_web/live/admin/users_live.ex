defmodule CrosspostWeb.Admin.UsersLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Repo

  import CrosspostWeb.AdminComponents

  defp user_status_badge(user) do
    cond do
      user.status == "active" and user.customer ->
        assigns = %{
          text: "Customer",
          classes: "bg-green-50 text-green-700 ring-green-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.status == "trial" ->
        assigns = %{
          text: "Trial",
          classes: "bg-blue-50 text-blue-700 ring-blue-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      user.status == "trial_ended" ->
        assigns = %{
          text: "Trial Ended",
          classes: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        }

        ~H"""
        <span class={"inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset #{@classes}"}>
          {@text}
        </span>
        """

      true ->
        "—"
    end
  end

  @impl true
  def mount(_params, _session, socket) do
    case Admin.list_users_with_filters("", "", 1, 20) do
      {:ok, %{users: users, total_count: total_count, total_pages: total_pages}} ->
        {:ok,
         socket
         |> assign(:page_title, "Users")
         |> assign(:email_filter, "")
         |> assign(:name_filter, "")
         |> assign(:status_filter, [])
         |> assign(:created_after, "")
         |> assign(:created_before, "")
         |> assign(:page, 1)
         |> assign(:per_page, 20)
         |> assign(:users, users)
         |> assign(:total_count, total_count)
         |> assign(:total_pages, total_pages)
         |> assign(:has_error, false)
         |> assign(:selected_users, MapSet.new())
         |> assign(:show_tag_modal, false)
         |> assign(:new_tag, "")
         |> assign(:all_tags, get_all_tags(users))}

      {:error, reason} ->
        {:ok,
         socket
         |> assign(:page_title, "Users")
         |> assign(:email_filter, "")
         |> assign(:name_filter, "")
         |> assign(:status_filter, [])
         |> assign(:created_after, "")
         |> assign(:created_before, "")
         |> assign(:page, 1)
         |> assign(:per_page, 20)
         |> assign(:users, [])
         |> assign(:has_error, true)
         |> assign(:error_reason, reason)
         |> assign(:selected_users, MapSet.new())
         |> assign(:show_tag_modal, false)
         |> assign(:new_tag, "")
         |> assign(:all_tags, MapSet.new())}
    end
  end

  defp get_all_tags(users) do
    users
    |> Enum.flat_map(& &1.tags)
    |> MapSet.new()
  end

  @impl true
  def handle_params(params, _url, socket) do
    email_filter = params["email"] || ""
    name_filter = params["name"] || ""
    status_filter = params["status"] || []
    # Clean up empty status filters
    status_filter =
      if is_list(status_filter), do: Enum.reject(status_filter, &(&1 == "")), else: []

    created_after = params["created_after"] || ""
    created_before = params["created_before"] || ""
    page = String.to_integer(params["page"] || "1")
    per_page = String.to_integer(params["per_page"] || "20")

    # Map UI status filter values to DB values
    mapped_status_filter =
      Enum.map(status_filter, fn
        "customer" -> "active"
        "trial" -> "trial"
        "trial_ended" -> "trial_ended"
        other -> other
      end)

    case Admin.list_users_with_filters(
           email_filter,
           name_filter,
           page,
           per_page,
           created_after,
           created_before,
           mapped_status_filter
         ) do
      {:ok, %{users: users, total_count: total_count, total_pages: total_pages}} ->
        {:noreply,
         socket
         |> assign(:page_title, "Users")
         |> assign(:users, users)
         |> assign(:email_filter, email_filter)
         |> assign(:name_filter, name_filter)
         |> assign(:status_filter, status_filter)
         |> assign(:created_after, created_after)
         |> assign(:created_before, created_before)
         |> assign(:page, page)
         |> assign(:per_page, per_page)
         |> assign(:total_count, total_count)
         |> assign(:total_pages, total_pages)
         |> assign(:has_error, false)
         |> assign(:all_tags, get_all_tags(users))}

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:page_title, "Users")
         |> assign(:users, [])
         |> assign(:has_error, true)
         |> assign(:error_reason, reason)}
    end
  end

  @impl true
  def handle_event("filter", params, socket) do
    %{
      "email" => email,
      "name" => name,
      "status" => status,
      "created_after" => created_after,
      "created_before" => created_before
    } = params

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/users?#{%{email: email, name: name, status: status, created_after: created_after, created_before: created_before, page: 1, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("reset-filter", _, socket) do
    {:noreply,
     push_patch(socket,
       to: ~p"/admin/users?#{%{page: 1, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    page = String.to_integer(page)

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/users?#{%{email: socket.assigns.email_filter, name: socket.assigns.name_filter, status: socket.assigns.status_filter, created_after: socket.assigns.created_after, created_before: socket.assigns.created_before, page: page, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("toggle_status", %{"status" => status}, socket) do
    current_statuses = socket.assigns.status_filter
    current_statuses = if is_list(current_statuses), do: current_statuses, else: []

    # Use UI values for toggling
    new_statuses =
      cond do
        # Clicking "All" always clears the filter
        status == "" -> []
        # If status is already selected, remove it
        status in current_statuses -> List.delete(current_statuses, status)
        # If adding a new status when none were selected, just add this one
        current_statuses == [] -> [status]
        # Otherwise add the new status to the list
        true -> [status | current_statuses]
      end

    {:noreply,
     push_patch(socket,
       to:
         ~p"/admin/users?#{%{email: socket.assigns.email_filter, name: socket.assigns.name_filter, status: new_statuses, created_after: socket.assigns.created_after, created_before: socket.assigns.created_before, page: 1, per_page: socket.assigns.per_page}}"
     )}
  end

  @impl true
  def handle_event("toggle_select_all", %{"value" => value}, socket) do
    selected_users =
      case value do
        "on" ->
          # Select all users
          socket.assigns.users
          |> Enum.map(& &1.id)
          |> MapSet.new()

        _ ->
          # Deselect all users
          MapSet.new()
      end

    {:noreply, assign(socket, :selected_users, selected_users)}
  end

  @impl true
  def handle_event("toggle_select_user", %{"user-id" => user_id}, socket) do
    user_id = String.to_integer(user_id)
    selected_users = socket.assigns.selected_users

    updated_selected_users =
      if MapSet.member?(selected_users, user_id) do
        MapSet.delete(selected_users, user_id)
      else
        MapSet.put(selected_users, user_id)
      end

    {:noreply, assign(socket, :selected_users, updated_selected_users)}
  end

  @impl true
  def handle_event("show_tag_modal", _, socket) do
    {:noreply, assign(socket, :show_tag_modal, true)}
  end

  @impl true
  def handle_event("hide_tag_modal", _, socket) do
    {:noreply, assign(socket, :show_tag_modal, false)}
  end

  @impl true
  def handle_event("update_new_tag", %{"value" => value}, socket) do
    {:noreply, assign(socket, :new_tag, value)}
  end

  @impl true
  def handle_event("apply_tag", %{"tag" => tag}, socket) do
    selected_users = socket.assigns.selected_users
    users = socket.assigns.users

    # Update users with the new tag
    Enum.each(users, fn admin_user ->
      if MapSet.member?(selected_users, admin_user.id) do
        # Fetch the actual User record
        case Repo.get(Crosspost.Admin.Resources.User, admin_user.id) do
          nil ->
            nil

          user ->
            updated_tags = Enum.uniq(admin_user.tags ++ [tag])
            Admin.update_user(user, %{tags: updated_tags})
        end
      end
    end)

    # Reload the users list to get the updated data
    case Admin.list_users_with_filters(
           socket.assigns.email_filter,
           socket.assigns.name_filter,
           socket.assigns.page,
           socket.assigns.per_page,
           socket.assigns.created_after,
           socket.assigns.created_before,
           socket.assigns.status_filter
         ) do
      {:ok, %{users: updated_users, total_count: total_count, total_pages: total_pages}} ->
        {:noreply,
         socket
         |> assign(:users, updated_users)
         |> assign(:all_tags, get_all_tags(updated_users))
         |> assign(:show_tag_modal, false)
         |> assign(:new_tag, "")
         |> assign(:total_count, total_count)
         |> assign(:total_pages, total_pages)
         |> assign(:selected_users, MapSet.new())
         |> put_flash(:info, "Tags updated successfully")}

      {:error, _reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update tags")}
    end
  end

  @impl true
  def handle_event("remove_tag", %{"user-id" => user_id, "tag" => tag}, socket) do
    user_id = String.to_integer(user_id)

    # Fetch the actual User record and update tags
    case Repo.get(Crosspost.Admin.Resources.User, user_id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")}

      user ->
        # Get the admin user from the current list
        admin_user = Enum.find(socket.assigns.users, &(&1.id == user_id))
        updated_tags = Enum.reject(admin_user.tags, &(&1 == tag))
        Admin.update_user(user, %{tags: updated_tags})

        # Reload the users list to get the updated data
        case Admin.list_users_with_filters(
               socket.assigns.email_filter,
               socket.assigns.name_filter,
               socket.assigns.page,
               socket.assigns.per_page,
               socket.assigns.created_after,
               socket.assigns.created_before,
               socket.assigns.status_filter
             ) do
          {:ok, %{users: updated_users, total_count: total_count, total_pages: total_pages}} ->
            {:noreply,
             socket
             |> assign(:users, updated_users)
             |> assign(:all_tags, get_all_tags(updated_users))
             |> assign(:total_count, total_count)
             |> assign(:total_pages, total_pages)
             |> put_flash(:info, "Tag removed successfully")}

          {:error, _reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to remove tag")}
        end
    end
  end

  defp build_filter_params(email, name, status, created_after, created_before, page, per_page) do
    params = %{}

    params = if email && email != "", do: Map.put(params, "email", email), else: params
    params = if name && name != "", do: Map.put(params, "name", name), else: params
    params = if status && status != "", do: Map.put(params, "status", status), else: params

    params =
      if created_after && created_after != "",
        do: Map.put(params, "created_after", created_after),
        else: params

    params =
      if created_before && created_before != "",
        do: Map.put(params, "created_before", created_before),
        else: params

    params = Map.put(params, "page", page)
    params = Map.put(params, "per_page", per_page)

    params
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Users</h1>
          <p class="mt-2 text-sm text-gray-700">
            <%= if @has_error do %>
              Invalid filter parameters
            <% else %>
              A list of all users in the system. Total users: {@total_count}
            <% end %>
          </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-4">
          <%= if MapSet.size(@selected_users) > 0 do %>
            <button
              type="button"
              phx-click="show_tag_modal"
              class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Add Tags ({MapSet.size(@selected_users)})
            </button>
          <% end %>
          <.link
            href={
              ~p"/admin/users/export/csv?#{build_filter_params(@email_filter, @name_filter, @status_filter, @created_after, @created_before, @page, @per_page)}"
            }
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Download CSV
          </.link>
        </div>
      </div>
      
    <!-- Users filter and table section -->
      <div id="users-filter-container">
        <.users_filter_form
          email={@email_filter}
          name={@name_filter}
          status={@status_filter}
          created_after={@created_after}
          created_before={@created_before}
          on_filter="filter"
          on_reset="reset-filter"
        />

        <%= if @has_error do %>
          <div class="rounded-md bg-red-50 p-4 mt-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Invalid filter parameters
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>
                    {@error_reason || "Please check your filter settings and try again."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="mt-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="relative px-6 py-3">
                      <input
                        type="checkbox"
                        class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        checked={
                          MapSet.size(@selected_users) > 0 &&
                            MapSet.size(@selected_users) == length(@users)
                        }
                        phx-click="toggle_select_all"
                      />
                    </th>
                    <th
                      scope="col"
                      class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                    >
                      Name
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Email
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Plan
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Status
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Trial Ends
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Updated
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Tags
                    </th>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <%= for user <- @users do %>
                    <tr>
                      <td class="relative px-6 py-4">
                        <input
                          type="checkbox"
                          class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                          checked={MapSet.member?(@selected_users, user.id)}
                          phx-click="toggle_select_user"
                          phx-value-user-id={user.id}
                        />
                      </td>
                      <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                        {if user.name, do: user.name, else: "—"}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{user.email}</td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {if user.plan_name, do: user.plan_name, else: "—"}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {user_status_badge(user)}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        <%= if user.trial_end do %>
                          {Calendar.strftime(user.trial_end, "%Y-%m-%d %H:%M")}
                        <% else %>
                          —
                        <% end %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {Calendar.strftime(user.updated_at, "%Y-%m-%d %H:%M")}
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        <div class="flex flex-wrap gap-2">
                          <%= for tag <- user.tags do %>
                            <span class="inline-flex items-center gap-1 rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                              {tag}
                              <button
                                type="button"
                                phx-click="remove_tag"
                                phx-value-user-id={user.id}
                                phx-value-tag={tag}
                                class="text-gray-400 hover:text-gray-600"
                              >
                                <svg class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                                </svg>
                              </button>
                            </span>
                          <% end %>
                        </div>
                      </td>
                      <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium">
                        <.link
                          navigate={~p"/admin/users/#{user.id}"}
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          View<span class="sr-only">, <%= user.email %></span>
                        </.link>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>

          <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing <span class="font-medium">{(@page - 1) * @per_page + 1}</span>
              to <span class="font-medium">{min(@page * @per_page, @total_count)}</span>
              of <span class="font-medium">{@total_count}</span>
              users
            </div>
            <div class="flex space-x-2">
              <%= if @page > 1 do %>
                <button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page - 1}
                  class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                >
                  Previous
                </button>
              <% end %>
              <%= if @page < @total_pages do %>
                <button
                  type="button"
                  phx-click="paginate"
                  phx-value-page={@page + 1}
                  class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                >
                  Next
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>

      <.modal :if={@show_tag_modal} id="tag-modal" show on_cancel={JS.push("hide_tag_modal")}>
        <div class="space-y-4">
          <h3 class="text-lg font-medium leading-6 text-gray-900">
            Add Tags to Selected Users
          </h3>

          <div>
            <label class="block text-sm font-medium text-gray-700">New Tag</label>
            <div class="mt-1">
              <input
                type="text"
                phx-keyup="update_new_tag"
                value={@new_tag}
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Enter a new tag"
              />
            </div>
          </div>

          <%= if MapSet.size(@all_tags) > 0 do %>
            <div>
              <label class="block text-sm font-medium text-gray-700">Existing Tags</label>
              <div class="mt-2 flex flex-wrap gap-2">
                <%= for tag <- @all_tags do %>
                  <button
                    type="button"
                    phx-click="apply_tag"
                    phx-value-tag={tag}
                    class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10 hover:bg-gray-100"
                  >
                    {tag}
                  </button>
                <% end %>
              </div>
            </div>
          <% end %>

          <%= if String.trim(@new_tag) != "" do %>
            <div class="mt-5 sm:mt-6">
              <button
                type="button"
                phx-click="apply_tag"
                phx-value-tag={@new_tag}
                class="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Add New Tag
              </button>
            </div>
          <% end %>
        </div>
      </.modal>
    </div>
    """
  end
end
