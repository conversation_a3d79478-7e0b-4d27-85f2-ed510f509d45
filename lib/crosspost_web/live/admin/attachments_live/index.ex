defmodule CrosspostWeb.Admin.AttachmentsLive.Index do
  use CrosspostWeb, :live_view

  import CrosspostWeb.AdminComponents

  alias Crosspost.Admin

  @filters [
    id: [
      op: :==,
      label: "ID",
      type: "string"
    ],
    user_id: [
      op: :==,
      label: "User",
      type: "select",
      prompt: "Select User",
      options: []
    ],
    type: [
      op: :==,
      label: "Type",
      type: "select",
      prompt: "Select Type",
      options: [
        [key: "Image", value: "image"],
        [key: "Video", value: "video"],
        [key: "Link", value: "link"]
      ]
    ],
    status: [
      op: :==,
      label: "Status",
      type: "select",
      prompt: "Select Status",
      options: [
        [key: "Pending", value: "pending"],
        [key: "Completed", value: "completed"],
        [key: "Failed", value: "failed"]
      ]
    ]
  ]

  @impl true
  def mount(_params, _session, socket) do
    filters = update_user_filter_options(@filters)

    {:ok,
     socket
     |> assign(:page_title, "Attachments")
     |> assign(:filters, filters)
     |> assign(:meta, %Flop.Meta{})
     |> assign(:form, Phoenix.Component.to_form(%{}))}
  end

  defp update_user_filter_options(filters) do
    users = Admin.users_for_post_filter()

    user_options =
      users
      |> Enum.map(fn user ->
        display_name = if user.name, do: "#{user.name} (#{user.email})", else: user.email
        [key: display_name, value: user.id]
      end)

    put_in(filters[:user_id][:options], user_options)
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Admin.list_attachments(params) do
      {:ok, {attachments, meta}} ->
        {:noreply,
         socket
         |> stream(:attachments, attachments, reset: true)
         |> assign(:meta, meta)
         |> assign(:form, Phoenix.Component.to_form(meta))}

      {:error, meta} ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid parameters")
         |> assign(meta: meta)
         |> assign(:form, Phoenix.Component.to_form(meta))}
    end
  end

  @impl true
  def handle_event("update-filter", %{"reset" => "true"}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/attachments")}
  end

  @impl true
  def handle_event("update-filter", params, socket) do
    params = Map.delete(params, "_target")
    {:noreply, push_patch(socket, to: ~p"/admin/attachments?#{params}")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Attachments</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all attachments in the system
          </p>
        </div>
      </div>

      <.filter_form meta={@meta} id="attachments-filter" form={@form} fields={@filters} />

      <div class="mt-8">
        <div class="admin-table-container">
          <Flop.Phoenix.table items={@streams.attachments} meta={@meta} path={~p"/admin/attachments"}>
            <:col :let={{_id, attachment}} label="Preview">
              <div class="h-12 w-12 flex-shrink-0">
                <img
                  :if={attachment.type == :image}
                  src={attachment.preview_url || attachment.source_url}
                  alt=""
                  class="h-12 w-12 rounded-lg object-cover"
                />
                <div
                  :if={attachment.type == :video}
                  class="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center"
                >
                  <.icon name="hero-video-camera" class="h-6 w-6 text-gray-400" />
                </div>
              </div>
            </:col>
            <:col :let={{_id, attachment}} label="User" field={:user_id}>
              <div class="text-sm text-gray-900">
                {attachment.user.name || attachment.user.email}
              </div>
            </:col>
            <:col :let={{_id, attachment}} label="Filename" field={:filename}>
              <div class="text-gray-900 max-w-xs truncate" title={attachment.filename}>
                {attachment.filename}
              </div>
              <div class="text-gray-500">{attachment.content_type}</div>
            </:col>
            <:col :let={{_id, attachment}} label="Type" field={:type}>
              {attachment.type}
            </:col>
            <:col :let={{_id, attachment}} label="Status" field={:status}>
              <.status_badge status={attachment.status} />
            </:col>
            <:col :let={{_id, attachment}} label="Created" field={:inserted_at}>
              {Calendar.strftime(attachment.inserted_at, "%Y-%m-%d %H:%M")}
            </:col>
            <:action :let={{_id, attachment}}>
              <div class="flex justify-end">
                <.link
                  navigate={~p"/admin/attachments/#{attachment}"}
                  class="text-brand-600 hover:text-brand-900"
                >
                  View →
                </.link>
              </div>
            </:action>
          </Flop.Phoenix.table>
        </div>
      </div>

      <.pagination_links meta={@meta} path={~p"/admin/attachments"} />
    </div>
    """
  end

  defp status_badge(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
      status_colors(@status)
    ]}>
      {@status}
    </span>
    """
  end

  defp status_colors(status) do
    case status do
      :completed -> "bg-green-50 text-green-700 ring-green-600/20"
      :pending -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
      :failed -> "bg-red-50 text-red-700 ring-red-600/20"
      _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
    end
  end
end
