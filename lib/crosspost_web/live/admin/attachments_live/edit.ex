defmodule CrosspostWeb.Admin.AttachmentsLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Admin.Resources.Attachment

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_attachment(id) do
      {:ok, attachment} ->
        {:ok,
         socket
         |> assign(:page_title, "Edit Attachment")
         |> assign(:attachment, attachment)
         |> assign_form(attachment)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Attachment not found")
         |> redirect(to: ~p"/admin/attachments")}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  defp assign_form(socket, %Attachment{} = attachment) do
    form =
      attachment
      |> Admin.change_attachment()
      |> to_form()

    assign(socket, :form, form)
  end

  @impl true
  def handle_event("save", %{"attachment" => attachment_params}, socket) do
    case Admin.update_attachment(socket.assigns.attachment, attachment_params) do
      {:ok, attachment} ->
        {:noreply,
         socket
         |> put_flash(:info, "Attachment updated successfully")
         |> push_navigate(to: ~p"/admin/attachments/#{attachment}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/attachments/#{@attachment}"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to attachment
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Edit Attachment</h1>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <.form for={@form} phx-submit="save" class="space-y-8">
          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Preview</h3>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6">
                <div class="flex justify-center">
                  <div class="w-96">
                    <img
                      :if={@attachment.type == :image}
                      src={@attachment.source_url || @attachment.preview_url}
                      alt=""
                      class="rounded-lg object-cover"
                    />
                    <div
                      :if={@attachment.type == :video}
                      class="aspect-video rounded-lg bg-gray-100 flex items-center justify-center"
                    >
                      <.icon name="hero-video-camera" class="h-12 w-12 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="px-4 py-6 sm:px-6">
              <h3 class="text-base font-semibold leading-7 text-gray-900">Attachment Information</h3>
            </div>
            <div class="border-t border-gray-100">
              <div class="px-4 py-6 sm:px-6 space-y-6">
                <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                  <div class="sm:col-span-4">
                    <.input field={@form[:filename]} type="text" label="Filename" />
                  </div>

                  <div class="sm:col-span-4">
                    <.input field={@form[:content_type]} type="text" label="Content Type" />
                  </div>

                  <div class="sm:col-span-4">
                    <.input field={@form[:source_url]} type="text" label="Source URL" />
                  </div>

                  <div class="sm:col-span-4">
                    <.input field={@form[:preview_url]} type="text" label="Preview URL" />
                  </div>

                  <div class="sm:col-span-4">
                    <.input
                      field={@form[:type]}
                      type="select"
                      label="Type"
                      options={[
                        {"Image", :image},
                        {"Video", :video},
                        {"Link", :link}
                      ]}
                    />
                  </div>

                  <div class="sm:col-span-4">
                    <.input
                      field={@form[:status]}
                      type="select"
                      label="Status"
                      options={[
                        {"Pending", :pending},
                        {"Completed", :completed},
                        {"Failed", :failed}
                      ]}
                    />
                  </div>

                  <div class="col-span-full">
                    <label class="block text-sm font-medium leading-6 text-gray-900">
                      Metadata (JSON)
                    </label>
                    <div class="mt-2">
                      <textarea
                        name="attachment[metadata]"
                        rows="10"
                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 font-mono"
                      ><%= Jason.encode!(@attachment.metadata, pretty: true) %></textarea>
                    </div>
                  </div>
                </div>

                <div class="flex items-center justify-end gap-x-6">
                  <.link
                    navigate={~p"/admin/attachments/#{@attachment}"}
                    class="text-sm font-semibold leading-6 text-gray-900"
                  >
                    Cancel
                  </.link>
                  <.button type="submit" phx-disable-with="Saving...">
                    Save Changes
                  </.button>
                </div>
              </div>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
