defmodule CrosspostWeb.Admin.AttachmentsLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_attachment(id) do
      {:ok, attachment} ->
        {:ok,
         socket
         |> assign(:page_title, "Attachment Details")
         |> assign(:attachment, attachment)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Attachment not found")
         |> redirect(to: ~p"/admin/attachments")}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/attachments"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to attachments
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Attachment Details</h1>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <.link
            navigate={~p"/admin/attachments/#{@attachment}/edit"}
            class="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Edit Attachment
          </.link>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Preview</h3>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div class="flex justify-center">
                <div class="w-96">
                  <img
                    :if={@attachment.type == :image}
                    src={@attachment.source_url || @attachment.preview_url}
                    alt=""
                    class="rounded-lg object-cover"
                  />
                  <div
                    :if={@attachment.type == :video}
                    class="aspect-video rounded-lg bg-gray-100 flex items-center justify-center"
                  >
                    <.icon name="hero-video-camera" class="h-12 w-12 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Attachment Information</h3>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">ID</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@attachment.id}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Filename</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@attachment.filename}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Content Type</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@attachment.content_type}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Type</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@attachment.type}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Status</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <.status_badge status={@attachment.status} />
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@attachment.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Source URL</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @attachment.source_url do %>
                    <.link
                      href={@attachment.source_url}
                      target="_blank"
                      class="text-brand-600 hover:text-brand-900"
                    >
                      {@attachment.source_url}
                    </.link>
                  <% else %>
                    <span class="text-gray-500">Not set</span>
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Preview URL</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @attachment.preview_url do %>
                    <.link
                      href={@attachment.preview_url}
                      target="_blank"
                      class="text-brand-600 hover:text-brand-900"
                    >
                      {@attachment.preview_url}
                    </.link>
                  <% else %>
                    <span class="text-gray-500">Not set</span>
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Metadata</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <pre class="bg-gray-50 p-4 rounded-lg overflow-x-auto"><code>{Jason.encode!(@attachment.metadata, pretty: true)}</code></pre>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp status_badge(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
      status_colors(@status)
    ]}>
      {@status}
    </span>
    """
  end

  defp status_colors(status) do
    case status do
      "completed" -> "bg-green-50 text-green-700 ring-green-600/20"
      "pending" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
      "failed" -> "bg-red-50 text-red-700 ring-red-600/20"
      _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
    end
  end
end
