defmodule CrosspostWeb.Admin.JobsLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_job(id) do
      {:ok, job} ->
        {:ok,
         socket
         |> assign(:page_title, "Job Details")
         |> assign(:job, job)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Job not found")
         |> redirect(to: ~p"/admin/jobs")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/jobs"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to jobs
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Job Details</h1>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Job Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about this background job.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">ID</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@job.id}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">State</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <span class={[
                    "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
                    state_color(@job.state)
                  ]}>
                    {String.capitalize(to_string(@job.state))}
                  </span>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Queue</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {String.capitalize(@job.queue)}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Worker</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@job.worker}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Attempts</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@job.attempt} / {@job.max_attempts}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Priority</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@job.priority}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Tags</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div :if={length(@job.tags) == 0} class="text-gray-500">No tags</div>
                  <div :for={tag <- @job.tags} class="inline-flex items-center gap-2">
                    <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                      {tag}
                    </span>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@job.inserted_at, "%Y-%m-%d %H:%M:%S UTC")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Scheduled At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @job.scheduled_at,
                    do: Calendar.strftime(@job.scheduled_at, "%Y-%m-%d %H:%M:%S UTC"),
                    else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Attempted At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @job.attempted_at,
                    do: Calendar.strftime(@job.attempted_at, "%Y-%m-%d %H:%M:%S UTC"),
                    else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Completed At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @job.completed_at,
                    do: Calendar.strftime(@job.completed_at, "%Y-%m-%d %H:%M:%S UTC"),
                    else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Discarded At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @job.discarded_at,
                    do: Calendar.strftime(@job.discarded_at, "%Y-%m-%d %H:%M:%S UTC"),
                    else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Cancelled At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @job.cancelled_at,
                    do: Calendar.strftime(@job.cancelled_at, "%Y-%m-%d %H:%M:%S UTC"),
                    else: "—"}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Arguments</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Job arguments in JSON format.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <pre class="text-sm text-gray-700 whitespace-pre-wrap">{Jason.encode!(@job.args, pretty: true)}</pre>
            </div>
          </div>
        </div>

        <div :if={length(@job.errors) > 0} class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Errors</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Error history for this job.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div :for={{error, index} <- Enum.with_index(@job.errors)} class="mb-8 last:mb-0">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Error #{index + 1}</h4>
                <pre class="text-sm text-gray-700 whitespace-pre-wrap">{Jason.encode!(error, pretty: true)}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp state_color(state) do
    case state do
      :completed -> "bg-green-50 text-green-700 ring-green-600/20"
      :available -> "bg-blue-50 text-blue-700 ring-blue-600/20"
      :scheduled -> "bg-purple-50 text-purple-700 ring-purple-600/20"
      :executing -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
      :retryable -> "bg-orange-50 text-orange-700 ring-orange-600/20"
      :discarded -> "bg-red-50 text-red-700 ring-red-600/20"
      :cancelled -> "bg-gray-50 text-gray-700 ring-gray-600/20"
    end
  end
end
