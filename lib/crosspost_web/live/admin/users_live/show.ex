defmodule CrosspostWeb.Admin.UsersLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Features
  alias Crosspost.Repo

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_user(id) do
      {:ok, user} ->
        # Get all features to display their names
        features = Features.list_features() |> Enum.map(&{&1.key, &1}) |> Map.new()
        plans = Admin.list_active_plans()

        {:ok,
         socket
         |> assign(:page_title, "User Details")
         |> assign(:user, user)
         |> assign(:features, features)
         |> assign(:plans, plans)
         |> assign(:show_settings_modal, false)
         |> assign(:selected_connection, nil)}
    end
  end

  @impl true
  def handle_event("schedule-cleanup", _, socket) do
    user = socket.assigns.user |> Repo.preload(:enabled_features, force: true)

    case Features.schedule_cleanup(user) do
      {:ok, _user} ->
        {:noreply,
         socket
         |> put_flash(:info, "Feature usage cleanup has been scheduled")}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to schedule feature usage cleanup")}
    end
  end

  @impl true
  def handle_event("show-settings", %{"id" => connection_id}, socket) do
    connection =
      Enum.find(socket.assigns.user.connections, &(&1.id == String.to_integer(connection_id)))

    {:noreply,
     socket
     |> assign(:show_settings_modal, true)
     |> assign(:selected_connection, connection)}
  end

  def handle_event("hide-settings", _, socket) do
    {:noreply,
     socket
     |> assign(:show_settings_modal, false)
     |> assign(:selected_connection, nil)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/users"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to users
            </.link>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-base font-semibold leading-6 text-gray-900">User Details</h1>
            <div class="flex items-center gap-4">
              <button
                phx-click="schedule-cleanup"
                class="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
              >
                Schedule Feature Usage Cleanup
              </button>
              <.link
                navigate={~p"/admin/users/#{@user.id}/edit"}
                class="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
              >
                Edit User
              </.link>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">User Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Personal details and account information.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Name</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @user.name, do: @user.name, else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Email</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@user.email}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Plan</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div class="flex items-center gap-4">
                    <div>
                      {if @user.plan, do: @user.plan.name, else: "—"}
                    </div>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Trial End</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @user.trial_end do
                    Calendar.strftime(@user.trial_end, "%Y-%m-%d %H:%M")
                  else
                    "—"
                  end}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Email Confirmed</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @user.confirmed_at do
                    Calendar.strftime(@user.confirmed_at, "%Y-%m-%d %H:%M")
                  else
                    "Not confirmed"
                  end}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@user.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Updated</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@user.updated_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Timezone</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @user.timezone, do: @user.timezone, else: "—"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Tags</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div class="flex flex-wrap gap-2">
                    <%= if Enum.empty?(@user.tags) do %>
                      <span class="text-gray-500">No tags</span>
                    <% else %>
                      <%= for tag <- @user.tags do %>
                        <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                          {tag}
                        </span>
                      <% end %>
                    <% end %>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Settings</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div
                    id="settings-editor"
                    phx-hook="JsonEditor"
                    data-json={Jason.encode!(@user.settings)}
                  >
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Info</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div id="info-editor" phx-hook="JsonEditor" data-json={Jason.encode!(@user.info)}>
                  </div>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Connections</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Connected social media accounts.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div :if={length(@user.connections) == 0} class="text-sm text-gray-500">
                No connected accounts
              </div>
              <div :if={length(@user.connections) > 0} class="mt-4 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <table class="min-w-full divide-y divide-gray-300">
                      <thead>
                        <tr>
                          <th
                            scope="col"
                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                          >
                            Platform
                          </th>
                          <th
                            scope="col"
                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            User ID
                          </th>
                          <th
                            scope="col"
                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Connected At
                          </th>
                          <th
                            scope="col"
                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Expires At
                          </th>
                          <th
                            scope="col"
                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Settings
                          </th>
                        </tr>
                      </thead>
                      <tbody class="divide-y divide-gray-200">
                        <tr :for={connection <- @user.connections}>
                          <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                            {String.capitalize(connection.platform)}
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {connection.platform_user_id}
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {Calendar.strftime(connection.inserted_at, "%Y-%m-%d %H:%M")}
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {if connection.expires_at do
                              Calendar.strftime(connection.expires_at, "%Y-%m-%d %H:%M")
                            else
                              "Never"
                            end}
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            <button
                              type="button"
                              phx-click="show-settings"
                              phx-value-id={connection.id}
                              class="text-blue-600 hover:text-blue-900"
                            >
                              View Settings
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Features</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Available features and their status for this user.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div :if={Enum.empty?(@user.enabled_features)} class="text-sm text-gray-500">
                No features enabled
              </div>
              <div class="space-y-6">
                <div :for={feature <- @user.enabled_features} class="space-y-2">
                  <div class="flex items-center gap-2">
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset bg-green-50 text-green-700 ring-green-600/20">
                      {feature.feature_name}
                    </span>
                    <span class="text-xs text-gray-500">
                      ({feature.feature_type})
                    </span>
                    <span :if={feature.plan_key} class="text-xs text-gray-500">
                      via {feature.plan_key}
                    </span>
                  </div>

                  <div class="text-sm text-gray-500">
                    {feature.feature_description}
                  </div>

                  <div :if={feature.feature_type != :boolean} class="flex items-center gap-4">
                    <div class="text-sm text-gray-500">
                      Current value:
                      <span class="font-medium text-gray-900">{feature.current_usage}</span>
                    </div>

                    <div :if={feature.reset_period} class="text-sm text-gray-500">
                      Reset period:
                      <span class="font-medium text-gray-900">
                        {String.capitalize(to_string(feature.reset_period))}
                      </span>
                    </div>

                    <div :if={feature.period_end} class="text-sm text-gray-500">
                      Next reset:
                      <span class="font-medium text-gray-900">
                        {Calendar.strftime(feature.period_end, "%Y-%m-%d %H:%M")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Subscription Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about the user's subscription and billing status.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Status</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription.status do %>
                    {String.capitalize(@user.subscription.status)}
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Period Start</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.period_start do %>
                    {Calendar.strftime(@user.subscription.period_start, "%Y-%m-%d %H:%M")}
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Period End</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.period_end do %>
                    {Calendar.strftime(@user.subscription.period_end, "%Y-%m-%d %H:%M")}
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Stripe Customer</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.stripe_customer_id do %>
                    <%= if String.starts_with?(@user.subscription.stripe_customer_id, "admin_") do %>
                      {@user.subscription.stripe_customer_id}
                    <% else %>
                      <.link
                        href={"https://dashboard.stripe.com/customers/#{@user.subscription.stripe_customer_id}"}
                        target="_blank"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        {@user.subscription.stripe_customer_id}
                      </.link>
                    <% end %>
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Stripe Subscription</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.stripe_subscription_id do %>
                    <%= if String.starts_with?(@user.subscription.stripe_subscription_id, "admin_") do %>
                      {@user.subscription.stripe_subscription_id}
                    <% else %>
                      <.link
                        href={"https://dashboard.stripe.com/subscriptions/#{@user.subscription.stripe_subscription_id}"}
                        target="_blank"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        {@user.subscription.stripe_subscription_id}
                      </.link>
                    <% end %>
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Stripe Product</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.stripe_product_id do %>
                    <%= if String.starts_with?(@user.subscription.stripe_product_id, "admin_") do %>
                      {@user.subscription.stripe_product_id}
                    <% else %>
                      <.link
                        href={"https://dashboard.stripe.com/products/#{@user.subscription.stripe_product_id}"}
                        target="_blank"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        {@user.subscription.stripe_product_id}
                      </.link>
                    <% end %>
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Stripe Price</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.stripe_price_id do %>
                    <%= if String.starts_with?(@user.subscription.stripe_price_id, "admin_") do %>
                      {@user.subscription.stripe_price_id}
                    <% else %>
                      <.link
                        href={"https://dashboard.stripe.com/prices/#{@user.subscription.stripe_price_id}"}
                        target="_blank"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        {@user.subscription.stripe_price_id}
                      </.link>
                    <% end %>
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Subscription Features</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <%= if @user.subscription && @user.subscription.features do %>
                    <div class="flex flex-wrap gap-2">
                      <%= for feature <- @user.subscription.features do %>
                        <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                          {feature}
                        </span>
                      <% end %>
                    </div>
                  <% else %>
                    —
                  <% end %>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <.modal :if={@show_settings_modal} id="settings-modal" show on_cancel={JS.push("hide-settings")}>
        <div class="space-y-4">
          <h3 class="text-lg font-medium leading-6 text-gray-900">
            Connection Settings - {String.capitalize(@selected_connection.platform)}
          </h3>

          <div>
            <h4 class="text-sm font-medium text-gray-900">Settings</h4>
            <div class="mt-2">
              <div
                id="connection-settings-editor"
                phx-hook="JsonEditor"
                data-json={Jason.encode!(@selected_connection.settings)}
              />
            </div>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-900">Info</h4>
            <div class="mt-2">
              <div
                id="connection-info-editor"
                phx-hook="JsonEditor"
                data-json={Jason.encode!(@selected_connection.info)}
              />
            </div>
          </div>
        </div>
      </.modal>
    </div>
    """
  end
end
