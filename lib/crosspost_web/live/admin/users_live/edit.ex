defmodule CrosspostWeb.Admin.UsersLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  alias Crosspost.Features
  alias Crosspost.Admin.Resources.User

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_user(id) do
      {:ok, user} ->
        user = Crosspost.Repo.preload(user, [:features, :plan])
        features = Features.list_features() |> Enum.map(&{&1.key, &1}) |> Map.new()
        plans = Admin.list_active_plans()
        available_features = Features.features_for_plan(user.plan)

        {:ok,
         socket
         |> assign(:page_title, "Edit User")
         |> assign(:user, user)
         |> assign(:form, to_form(User.changeset(user, %{})))
         |> assign(:features, features)
         |> assign(:available_features, available_features)
         |> assign(:plans, plans)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "User not found")
         |> redirect(to: ~p"/admin/users")}
    end
  end

  @impl true
  def handle_event("toggle_feature", %{"key" => feature_key}, socket) do
    user = socket.assigns.user
    has_feature = Enum.any?(user.features, &(&1.key == feature_key))

    result =
      if has_feature do
        Admin.remove_user_feature(user, feature_key)
      else
        Admin.add_user_feature(user, feature_key)
      end

    case result do
      {:ok, updated_user} ->
        updated_user = Crosspost.Repo.preload(updated_user, [:features, :plan])

        {:noreply,
         socket
         |> assign(:user, updated_user)
         |> put_flash(:info, "User features updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update user features")}
    end
  end

  @impl true
  def handle_event("save", %{"user" => user_params}, socket) do
    user_params = parse_json_fields(user_params)
    user_params = maybe_set_confirmed_at(user_params)
    user_params = maybe_parse_subscription_fields(user_params)

    case Admin.update_user(socket.assigns.user, user_params) do
      {:ok, _user} ->
        {:noreply,
         socket
         |> put_flash(:info, "User updated successfully")
         |> redirect(to: ~p"/admin/users/#{socket.assigns.user.id}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  @impl true
  def handle_event("sync_subscription", _, socket) do
    case Admin.fetch_subscription_info(socket.assigns.user) do
      {:ok, subscription_data} ->
        changeset = User.changeset(socket.assigns.user, %{subscription: subscription_data})

        {:noreply,
         socket
         |> assign(:form, to_form(changeset))
         |> put_flash(:info, "Subscription data synced from Stripe")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, reason)}
    end
  end

  defp maybe_set_confirmed_at(%{"confirmed" => "true"} = params) do
    Map.put(params, "confirmed_at", DateTime.utc_now() |> DateTime.truncate(:second))
  end

  defp maybe_set_confirmed_at(params), do: params

  defp parse_json_fields(%{"settings" => settings, "info" => info} = params) do
    decoded_settings =
      case settings do
        "" -> %{}
        json -> Jason.decode!(json)
      end

    decoded_info =
      case info do
        "" -> %{}
        json -> Jason.decode!(json)
      end

    %{params | "settings" => decoded_settings, "info" => decoded_info}
  end

  defp maybe_parse_subscription_fields(%{"subscription" => subscription} = params)
       when is_map(subscription) do
    # Convert features from string to list
    subscription =
      case subscription["features"] do
        features when is_binary(features) and features != "" ->
          Map.put(
            subscription,
            "features",
            String.split(features, ",") |> Enum.map(&String.trim/1)
          )

        _ ->
          subscription
      end

    Map.put(params, "subscription", subscription)
  end

  defp maybe_parse_subscription_fields(params), do: params

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/users/#{@user.id}"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to user
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Edit User</h1>
        </div>
      </div>

      <div class="mt-8">
        <.form :let={f} for={@form} phx-submit="save" class="space-y-8">
          <div class="space-y-8 divide-y divide-gray-200">
            <div>
              <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div class="sm:col-span-3">
                  <.input field={f[:name]} type="text" label="Name" placeholder="User's name" />
                </div>

                <div class="sm:col-span-3">
                  <.input field={f[:email]} type="email" label="Email" placeholder="<EMAIL>" />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={f[:timezone]}
                    type="text"
                    label="Timezone"
                    placeholder="e.g. America/New_York"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={f[:plan_key]}
                    type="select"
                    label="Plan"
                    options={[{"No plan", nil} | Enum.map(@plans, &{&1.name, &1.key})]}
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    field={f[:trial_end]}
                    type="datetime-local"
                    label="Trial End"
                    placeholder="Trial end date"
                  />
                </div>

                <div class="sm:col-span-3">
                  <div class="relative flex items-start">
                    <div class="flex h-6 items-center">
                      <input type="hidden" name="user[confirmed]" value="false" />
                      <input
                        id="user-confirmed"
                        name="user[confirmed]"
                        type="checkbox"
                        value="true"
                        checked={not is_nil(@user.confirmed_at)}
                        class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                    </div>
                    <div class="ml-3 text-sm leading-6">
                      <label for="user-confirmed" class="font-medium text-gray-900">
                        Email Confirmed
                      </label>
                      <p class="text-gray-500">
                        {if @user.confirmed_at do
                          "Confirmed at " <> Calendar.strftime(@user.confirmed_at, "%Y-%m-%d %H:%M")
                        else
                          "Not confirmed"
                        end}
                      </p>
                    </div>
                  </div>
                </div>

                <div class="sm:col-span-6">
                  <h3 class="text-base font-semibold leading-6 text-gray-900 mb-4">
                    Subscription Details
                  </h3>
                  <div class="flex justify-end mb-4">
                    <button
                      type="button"
                      phx-click="sync_subscription"
                      class="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                    >
                      Sync from Stripe
                    </button>
                  </div>
                  <.inputs_for :let={fs} field={f[:subscription]}>
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                      <div class="sm:col-span-3">
                        <.input
                          field={fs[:status]}
                          type="select"
                          label="Status"
                          options={[
                            {"Active", "active"},
                            {"Trialing", "trialing"},
                            {"Canceled", "canceled"},
                            {"Trial Ended", "trial_ended"}
                          ]}
                        />
                      </div>

                      <div class="sm:col-span-3">
                        <.input field={fs[:period_start]} type="datetime-local" label="Period Start" />
                      </div>

                      <div class="sm:col-span-3">
                        <.input field={fs[:period_end]} type="datetime-local" label="Period End" />
                      </div>

                      <div class="sm:col-span-3">
                        <.input
                          field={fs[:stripe_customer_id]}
                          type="text"
                          label="Stripe Customer ID"
                        />
                      </div>

                      <div class="sm:col-span-3">
                        <.input
                          field={fs[:stripe_subscription_id]}
                          type="text"
                          label="Stripe Subscription ID"
                        />
                      </div>

                      <div class="sm:col-span-3">
                        <.input field={fs[:stripe_product_id]} type="text" label="Stripe Product ID" />
                      </div>

                      <div class="sm:col-span-3">
                        <.input field={fs[:stripe_price_id]} type="text" label="Stripe Price ID" />
                      </div>

                      <div class="sm:col-span-6">
                        <.input field={fs[:features]} type="text" label="Features (comma-separated)" />
                      </div>
                    </div>
                  </.inputs_for>
                </div>

                <div class="sm:col-span-6">
                  <label class="block text-sm font-medium text-gray-700">Settings</label>
                  <div class="mt-1">
                    <div
                      id="settings-editor"
                      phx-hook="JsonEditor"
                      data-json={Jason.encode!(@user.settings || %{})}
                      phx-update="ignore"
                    />
                    <input type="hidden" name="user[settings]" id="settings-input" />
                  </div>
                </div>

                <div class="sm:col-span-6">
                  <label class="block text-sm font-medium text-gray-700">Info</label>
                  <div class="mt-1">
                    <div
                      id="info-editor"
                      phx-hook="JsonEditor"
                      data-json={Jason.encode!(@user.info || %{})}
                      phx-update="ignore"
                    />
                    <input type="hidden" name="user[info]" id="info-input" />
                  </div>
                </div>

                <div class="sm:col-span-6">
                  <label class="block text-sm font-medium text-gray-700 mb-4">Features</label>
                  <div class="space-y-4">
                    <%= for feature <- @available_features do %>
                      <div class="relative flex items-start">
                        <div class="flex h-6 items-center">
                          <input
                            type="checkbox"
                            checked={Enum.any?(@user.features, &(&1.key == feature.key))}
                            phx-click="toggle_feature"
                            phx-value-key={feature.key}
                            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                          />
                        </div>
                        <div class="ml-3 text-sm leading-6">
                          <label class="font-medium text-gray-900">
                            {feature.name}
                          </label>
                          <p class="text-gray-500">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="pt-5">
            <div class="flex justify-end">
              <.button type="submit" phx-disable-with="Saving...">
                Save changes
              </.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
