defmodule CrosspostWeb.Admin.WaitlistLive do
  use CrosspostWeb, :live_view

  alias Crosspost.{Admin, AI, Repo}

  import CrosspostWeb.AdminComponents

  require Logger

  @filters [
    email: [
      op: :ilike_and,
      label: "Email",
      type: "text",
      placeholder: "Search by email"
    ],
    spam_status: [
      op: :==,
      label: "Spam Status",
      type: "select",
      prompt: "Select Status",
      options: [
        [key: "Spam", value: "spam"],
        [key: "Potential Spam", value: "potential_spam"]
      ]
    ],
    invitation_code: [
      op: :not_empty,
      label: "Invitation Status",
      type: "select",
      prompt: "Select Status",
      options: [
        [key: "Invited", value: "true"],
        [key: "Not Invited", value: "false"]
      ]
    ]
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:selected_action, nil)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Admin.list_waitlist(params) do
      {:ok, {waitlist, meta}} ->
        {:noreply,
         socket
         |> assign(:page_title, "Waitlist")
         |> assign(:waitlist, waitlist)
         |> assign(:meta, meta)
         |> assign(:filters, @filters)
         |> assign(:form, Phoenix.Component.to_form(meta))}

      {:error, meta} ->
        {:noreply,
         socket
         |> assign(:page_title, "Waitlist")
         |> assign(:waitlist, [])
         |> assign(:meta, meta)
         |> assign(:filters, @filters)
         |> assign(:form, Phoenix.Component.to_form(meta))}
    end
  end

  @impl true
  def handle_event("update-filter", %{"reset" => "true"}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/waitlist")}
  end

  def handle_event("update-filter", params, socket) do
    params = Map.delete(params, "_target")
    {:noreply, push_patch(socket, to: ~p"/admin/waitlist?#{params}")}
  end

  @impl true
  def handle_event("detect-spam", _, socket) do
    emails = Enum.map(socket.assigns.waitlist, & &1.email)

    Logger.debug("[WaitlistLive] Processing spam detection",
      email_count: length(emails),
      emails: emails,
      page_size: socket.assigns.meta.page_size,
      total_count: socket.assigns.meta.total_count
    )

    if length(emails) > 50 do
      {:noreply,
       socket
       |> put_flash(
         :error,
         "Please filter the list to 50 or fewer emails before running spam detection."
       )
       |> push_patch(to: ~p"/admin/waitlist")}
    else
      case AI.detect_spam_emails(emails) do
        {:ok, results} when is_map(results) ->
          Logger.info("[WaitlistLive] Spam detection results", results: inspect(results))

          updated_count =
            socket.assigns.waitlist
            |> Enum.count(fn entry ->
              if status = results[entry.email] do
                case Admin.update_waitlist_entry(entry, %{spam_status: status}) do
                  {:ok, _} -> true
                  {:error, _} -> false
                end
              else
                false
              end
            end)

          {:noreply,
           socket
           |> put_flash(:info, "Spam detection completed. Updated #{updated_count} entries.")
           |> push_patch(to: ~p"/admin/waitlist")}

        {:error, error_message} when is_binary(error_message) ->
          {:noreply,
           socket
           |> put_flash(:error, error_message)
           |> push_patch(to: ~p"/admin/waitlist")}

        {:error, error} ->
          Logger.error("[WaitlistLive] Spam detection error", error: inspect(error))

          {:noreply,
           socket
           |> put_flash(
             :error,
             "An unexpected error occurred during spam detection. Please try again."
           )
           |> push_patch(to: ~p"/admin/waitlist")}
      end
    end
  end

  @impl true
  def handle_event("select-action", %{"action" => action}, socket) do
    {:noreply, assign(socket, :selected_action, action)}
  end

  def handle_event("apply-action", %{"entries" => entries} = params, socket) do
    require Logger

    Logger.debug("Handling bulk action",
      action: socket.assigns.selected_action,
      params: inspect(params)
    )

    selected_ids = Map.keys(entries)
    entries_to_process = Enum.filter(socket.assigns.waitlist, &(to_string(&1.id) in selected_ids))

    Logger.debug("Processing entries",
      count: length(entries_to_process),
      ids: selected_ids
    )

    case socket.assigns.selected_action do
      "delete" ->
        # Delete each entry
        Enum.each(entries_to_process, &Admin.delete_waitlist_entry/1)

        {:noreply,
         socket
         |> assign(:selected_action, nil)
         |> put_flash(:info, "Selected entries deleted successfully")
         |> push_patch(to: ~p"/admin/waitlist")}

      "reset_spam" ->
        updated_count =
          Enum.count(entries_to_process, fn entry ->
            case Admin.update_waitlist_entry(entry, %{spam_status: nil}) do
              {:ok, _} -> true
              {:error, _} -> false
            end
          end)

        {:noreply,
         socket
         |> assign(:selected_action, nil)
         |> put_flash(:info, "Reset spam status for #{updated_count} entries")
         |> push_patch(to: ~p"/admin/waitlist")}

      "invite" ->
        results =
          Enum.reduce(entries_to_process, [], fn entry, acc ->
            if is_nil(entry.invitation_code) do
              invitation_code = generate_invitation_code()
              invitation_url = url(~p"/users/sign-up/#{invitation_code}")

              Logger.debug("Processing entry for bulk invite",
                entry_id: entry.id,
                email: entry.email
              )

              Repo.transaction(fn ->
                # First try to send the email
                case Crosspost.Emails.InvitationEmail.invitation_email(
                       entry.email,
                       invitation_url
                     )
                     |> Crosspost.Mailer.deliver() do
                  {:ok, _} ->
                    # Only update the database if email was sent successfully
                    case Admin.update_waitlist_entry(entry, %{
                           "invitation_code" => invitation_code,
                           "invited_at" => DateTime.utc_now()
                         }) do
                      {:ok, _updated_entry} ->
                        Logger.debug("Successfully sent invitation", email: entry.email)
                        :ok

                      {:error, changeset} ->
                        Logger.error("Failed to update entry",
                          errors: inspect(changeset.errors),
                          entry_id: entry.id
                        )

                        Repo.rollback({:error, entry.email, "Failed to update entry"})
                    end

                  {:error, error} ->
                    Logger.error("Failed to send invitation",
                      error: inspect(error),
                      email: entry.email
                    )

                    Repo.rollback({:error, entry.email, error})
                end
              end)
              |> case do
                {:ok, :ok} -> acc
                {:error, error} -> [error | acc]
              end
            else
              Logger.debug("Skipping already invited entry",
                entry_id: entry.id,
                email: entry.email
              )

              acc
            end
          end)

        case results do
          [] ->
            {:noreply,
             socket
             |> assign(:selected_action, nil)
             |> put_flash(:info, "Invitations sent successfully")
             |> push_patch(to: ~p"/admin/waitlist")}

          errors ->
            error_message =
              "Failed to send some invitations: " <>
                Enum.map_join(errors, ", ", fn {:error, email, _} -> email end)

            {:noreply,
             socket
             |> assign(:selected_action, nil)
             |> put_flash(:error, error_message)
             |> push_patch(to: ~p"/admin/waitlist")}
        end

      _ ->
        {:noreply, socket}
    end
  end

  defp generate_invitation_code do
    :crypto.strong_rand_bytes(16)
    |> Base.url_encode64(padding: false)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Waitlist</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all waitlist entries in the system. Total entries: {@meta.total_count}
          </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none flex items-center gap-4">
          <button
            phx-click="detect-spam"
            class="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Detect SPAM
          </button>
        </div>
      </div>

      <.filter_form meta={@meta} id="waitlist-filter" form={@form} fields={@filters} />

      <div class="mt-8">
        <.form for={%{}} as={:action} phx-submit="apply-action">
          <div class="mb-4 flex items-center gap-2">
            <select
              name="action"
              class="block rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
              phx-change="select-action"
            >
              <option value="" selected={is_nil(@selected_action)}>Select Action</option>
              <option value="delete" selected={@selected_action == "delete"}>Delete</option>
              <option value="invite" selected={@selected_action == "invite"}>Invite to Beta</option>
              <option value="reset_spam" selected={@selected_action == "reset_spam"}>
                Reset Spam Status
              </option>
            </select>
            <.button :if={@selected_action} type="submit">Apply</.button>
          </div>

          <div class="admin-table-container">
            <Flop.Phoenix.table items={@waitlist} meta={@meta} path={~p"/admin/waitlist"}>
              <:col :let={entry} label="">
                <input
                  type="checkbox"
                  name={"entries[#{entry.id}]"}
                  class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
              </:col>
              <:col :let={entry} label="Email" field={:email}>
                <div class="text-gray-900">{entry.email}</div>
              </:col>
              <:col :let={entry} label="Spam Status" field={:spam_status}>
                <.spam_badge status={entry.spam_status} />
              </:col>
              <:col :let={entry} label="Invitation Code" field={:invitation_code}>
                <div class="text-gray-900">
                  <.link
                    :if={entry.invitation_code}
                    href={~p"/users/sign-up/#{entry.invitation_code}"}
                    target="_blank"
                  >
                    {entry.invitation_code}
                  </.link>
                </div>
              </:col>
              <:col :let={entry} label="Invited At" field={:invited_at}>
                <div class="text-gray-900">
                  {if entry.invited_at,
                    do: Calendar.strftime(entry.invited_at, "%Y-%m-%d %H:%M"),
                    else: "-"}
                </div>
              </:col>
              <:col :let={entry} label="Used At" field={:used_at}>
                <div class="text-gray-900">
                  {if entry.used_at, do: Calendar.strftime(entry.used_at, "%Y-%m-%d %H:%M"), else: "-"}
                </div>
              </:col>
              <:col :let={entry} label="Created" field={:inserted_at}>
                {Calendar.strftime(entry.inserted_at, "%Y-%m-%d %H:%M")}
              </:col>
              <:action :let={entry}>
                <div class="flex justify-end">
                  <.link
                    navigate={~p"/admin/waitlist/#{entry.id}"}
                    class="text-brand-600 hover:text-brand-900"
                  >
                    View
                  </.link>
                </div>
              </:action>
            </Flop.Phoenix.table>
          </div>
        </.form>
      </div>

      <.pagination_links meta={@meta} path={~p"/admin/waitlist"} />
    </div>
    """
  end

  defp spam_badge(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
      spam_status_colors(@status)
    ]}>
      {if @status, do: String.replace(to_string(@status), "_", " "), else: "-"}
    </span>
    """
  end

  defp spam_status_colors(:spam), do: "bg-red-50 text-red-700 ring-red-600/20"
  defp spam_status_colors(:potential_spam), do: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
  defp spam_status_colors(_), do: "bg-gray-50 text-gray-700 ring-gray-600/20"
end
