defmodule CrosspostWeb.Admin.WorkspacesLive.Edit do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_workspace(id) do
      {:ok, workspace} ->
        users = Admin.list_users_for_select()

        {:ok,
         socket
         |> assign(:page_title, "Edit Workspace")
         |> assign(:workspace, workspace)
         |> assign(:users, users)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Workspace not found")
         |> redirect(to: ~p"/admin/workspaces")}
    end
  end

  @impl true
  def handle_event("save", %{"workspace" => workspace_params}, socket) do
    workspace_params = parse_json_fields(workspace_params)

    case Admin.update_workspace(socket.assigns.workspace, workspace_params) do
      {:ok, _workspace} ->
        {:noreply,
         socket
         |> put_flash(:info, "Workspace updated successfully")
         |> redirect(to: ~p"/admin/workspaces/#{socket.assigns.workspace.id}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp parse_json_fields(%{"settings" => settings} = params) do
    %{params | "settings" => Jason.decode!(settings)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/workspaces/#{@workspace.id}"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to workspace
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Edit Workspace</h1>
        </div>
      </div>

      <div class="mt-8">
        <.form for={%{}} phx-submit="save" class="space-y-8">
          <div class="space-y-8 divide-y divide-gray-200">
            <div>
              <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div class="sm:col-span-3">
                  <.input
                    type="text"
                    label="Name"
                    name="workspace[name]"
                    value={@workspace.name}
                    placeholder="Workspace name"
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    type="select"
                    label="Owner"
                    name="workspace[owner_id]"
                    value={@workspace.owner_id}
                    options={
                      Enum.map(@users, fn user ->
                        display_name =
                          if user.name, do: "#{user.name} (#{user.email})", else: user.email

                        {display_name, user.id}
                      end)
                    }
                  />
                </div>

                <div class="sm:col-span-3">
                  <.input
                    type="checkbox"
                    label="Default Workspace"
                    name="workspace[is_default]"
                    value={@workspace.is_default}
                  />
                </div>

                <div class="sm:col-span-6">
                  <label class="block text-sm font-medium text-gray-700">Settings</label>
                  <div class="mt-1">
                    <div
                      id="settings-editor"
                      phx-hook="JsonEditor"
                      data-json={Jason.encode!(@workspace.settings || %{})}
                      phx-update="ignore"
                    />
                    <input type="hidden" name="workspace[settings]" id="settings-input" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="pt-5">
            <div class="flex justify-end">
              <.button type="submit" phx-disable-with="Saving...">
                Save changes
              </.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
