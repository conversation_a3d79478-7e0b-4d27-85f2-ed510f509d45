defmodule CrosspostWeb.Admin.WorkspacesLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_workspace(id) do
      {:ok, workspace} ->
        {:ok,
         socket
         |> assign(:page_title, "Workspace Details")
         |> assign(:workspace, workspace)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Workspace not found")
         |> redirect(to: ~p"/admin/workspaces")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/workspaces"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to workspaces
            </.link>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Workspace Details</h1>
            <.link
              navigate={~p"/admin/workspaces/#{@workspace.id}/edit"}
              class="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
            >
              Edit Workspace
            </.link>
          </div>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Workspace Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about this workspace.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Name</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@workspace.name}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Slug</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@workspace.slug}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Owner</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @workspace.owner.name, do: @workspace.owner.name, else: @workspace.owner.email}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Default Workspace</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @workspace.is_default, do: "Yes", else: "No"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@workspace.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Updated</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@workspace.updated_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Settings</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div
                    id="settings-editor"
                    phx-hook="JsonEditor"
                    data-json={Jason.encode!(@workspace.settings || %{})}
                  >
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Invitations</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div :if={length(@workspace.invitations) == 0} class="text-gray-500">
                    No active invitations
                  </div>
                  <div :for={invitation <- @workspace.invitations} class="mb-4 last:mb-0">
                    <div class="flex items-center gap-2">
                      <span class="font-medium">{invitation["email"]}</span>
                      <span class="text-gray-500">
                        (invited at {invitation["invited_at"]}, valid until {invitation["valid_until"]})
                      </span>
                    </div>
                  </div>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Members</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Users who are members of this workspace.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <div :if={length(@workspace.workspace_users) == 0} class="text-sm text-gray-500">
                No members
              </div>
              <div :for={workspace_user <- @workspace.workspace_users} class="mb-4 last:mb-0">
                <div class="flex items-center gap-2">
                  <span class="font-medium">
                    {if workspace_user.user.name,
                      do: workspace_user.user.name,
                      else: workspace_user.user.email}
                  </span>
                  <span class="text-gray-500">
                    (joined at {Calendar.strftime(workspace_user.inserted_at, "%Y-%m-%d %H:%M")})
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Social Connections</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Connected social media accounts in this workspace.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <div class="px-4 py-6 sm:px-6">
              <%= if Enum.empty?(@workspace.connections) do %>
                <p class="text-sm text-gray-500">No social connections found in this workspace.</p>
              <% else %>
                <div class="flow-root">
                  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                      <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                          <tr>
                            <th
                              scope="col"
                              class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                            >
                              Platform
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              User
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Platform User ID
                            </th>
                            <th
                              scope="col"
                              class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Expires
                            </th>
                          </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                          <%= for connection <- @workspace.connections do %>
                            <tr>
                              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                {String.capitalize(connection.platform)}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {if connection.user.name,
                                  do: connection.user.name,
                                  else: connection.user.email}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {connection.platform_user_id}
                              </td>
                              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {if connection.expires_at,
                                  do: Calendar.strftime(connection.expires_at, "%Y-%m-%d %H:%M"),
                                  else: "Never"}
                              </td>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
