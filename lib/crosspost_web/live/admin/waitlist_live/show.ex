defmodule CrosspostWeb.Admin.WaitlistLive.Show do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    case Admin.get_waitlist_entry(id) do
      {:ok, entry} ->
        {:ok,
         socket
         |> assign(:page_title, "Waitlist Entry Details")
         |> assign(:entry, entry)}

      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Waitlist entry not found")
         |> redirect(to: ~p"/admin/waitlist")}
    end
  end

  @impl true
  def handle_event("clear_invitation", _, socket) do
    case Admin.update_waitlist_entry(socket.assigns.entry, %{
           "invitation_code" => nil,
           "invited_at" => nil,
           "used_at" => nil
         }) do
      {:ok, entry} ->
        {:noreply,
         socket
         |> assign(:entry, entry)
         |> put_flash(:info, "Invitation cleared successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to clear invitation")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-4">
            <.link
              navigate={~p"/admin/waitlist"}
              class="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
            >
              ← Back to waitlist
            </.link>
          </div>
          <h1 class="mt-4 text-base font-semibold leading-6 text-gray-900">Waitlist Entry Details</h1>
        </div>
      </div>

      <div class="mt-8 space-y-8">
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Entry Information</h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
              Details about this waitlist entry.
            </p>
          </div>
          <div class="border-t border-gray-100">
            <dl class="divide-y divide-gray-100">
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Email</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {@entry.email}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Spam Status</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <.spam_badge status={@entry.spam_status} />
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Invitation Code</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div class="flex items-center gap-2">
                    <span>{@entry.invitation_code || "-"}</span>
                    <.link
                      :if={@entry.invitation_code}
                      phx-click="clear_invitation"
                      data-confirm="Are you sure you want to clear the invitation? This will allow you to send a new invitation."
                      class="text-sm text-red-600 hover:text-red-900"
                    >
                      Clear invitation
                    </.link>
                  </div>
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Invited At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @entry.invited_at,
                    do: Calendar.strftime(@entry.invited_at, "%Y-%m-%d %H:%M"),
                    else: "-"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Used At</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {if @entry.used_at,
                    do: Calendar.strftime(@entry.used_at, "%Y-%m-%d %H:%M"),
                    else: "-"}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Created</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@entry.inserted_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>

              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-900">Updated</dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {Calendar.strftime(@entry.updated_at, "%Y-%m-%d %H:%M")}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp spam_badge(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset",
      spam_status_colors(@status)
    ]}>
      {if @status, do: String.replace(to_string(@status), "_", " "), else: "-"}
    </span>
    """
  end

  defp spam_status_colors(:spam), do: "bg-red-50 text-red-700 ring-red-600/20"
  defp spam_status_colors(:potential_spam), do: "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
  defp spam_status_colors(_), do: "bg-gray-50 text-gray-700 ring-gray-600/20"
end
