defmodule CrosspostWeb.Admin.JobsLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Admin
  import Flop.Phoenix
  import CrosspostWeb.AdminComponents

  @filters [
    queue: [
      op: :==,
      label: "Queue",
      type: "select",
      prompt: "Select Queue",
      options: [
        [key: "Default", value: "default"],
        [key: "Publishing", value: "publishing"],
        [key: "Mailer", value: "mailer"]
      ]
    ],
    state: [
      op: :==,
      label: "State",
      type: "select",
      prompt: "Select State",
      options: [
        [key: "Available", value: "available"],
        [key: "Scheduled", value: "scheduled"],
        [key: "Executing", value: "executing"],
        [key: "Retryable", value: "retryable"],
        [key: "Completed", value: "completed"],
        [key: "Discarded", value: "discarded"],
        [key: "Cancelled", value: "cancelled"]
      ]
    ],
    worker: [
      op: :ilike_and,
      label: "Worker",
      type: "text",
      placeholder: "Search by worker"
    ]
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Admin.list_jobs(params) do
      {:ok, {jobs, meta}} ->
        {:noreply,
         socket
         |> assign(:page_title, "Jobs")
         |> assign(:jobs, jobs)
         |> assign(:meta, meta)
         |> assign(:total_count, meta.total_count)
         |> assign(:filters, @filters)
         |> assign(:form, Phoenix.Component.to_form(meta))}

      {:error, meta} ->
        {:noreply,
         socket
         |> assign(:page_title, "Jobs")
         |> assign(:jobs, [])
         |> assign(:meta, meta)}
    end
  end

  @impl true
  def handle_event("update-filter", %{"reset" => "true"}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/jobs")}
  end

  def handle_event("update-filter", params, socket) do
    params = Map.delete(params, "_target")
    {:noreply, push_patch(socket, to: ~p"/admin/jobs?#{params}")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900">Jobs</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all jobs in the system. Total jobs: {@total_count}
          </p>
        </div>
      </div>

      <.filter_form meta={@meta} id="jobs-filter" form={@form} fields={@filters} />

      <div class="mt-8">
        <div class="admin-table-container">
          <Flop.Phoenix.table items={@jobs} meta={@meta} path={~p"/admin/jobs"}>
            <:col :let={job} label="Queue" field={:queue}>
              <div class="text-gray-900">{String.capitalize(job.queue)}</div>
            </:col>
            <:col :let={job} label="State" field={:state}>
              <div class="text-gray-900">{String.capitalize(to_string(job.state))}</div>
            </:col>
            <:col :let={job} label="Worker" field={:worker}>
              <div class="text-gray-900">{job.worker}</div>
            </:col>
            <:col :let={job} label="Attempt" field={:attempt}>
              <div class="text-gray-900">{job.attempt}</div>
            </:col>
            <:col :let={job} label="Max Attempts" field={:max_attempts}>
              <div class="text-gray-900">{job.max_attempts}</div>
            </:col>
            <:col :let={job} label="Scheduled At" field={:scheduled_at}>
              <div class="text-gray-900">
                {if job.scheduled_at,
                  do: Calendar.strftime(job.scheduled_at, "%Y-%m-%d %H:%M"),
                  else: "-"}
              </div>
            </:col>
            <:col :let={job} label="Attempted At" field={:attempted_at}>
              <div class="text-gray-900">
                {if job.attempted_at,
                  do: Calendar.strftime(job.attempted_at, "%Y-%m-%d %H:%M"),
                  else: "-"}
              </div>
            </:col>
            <:col :let={job} label="Created" field={:inserted_at}>
              {Calendar.strftime(job.inserted_at, "%Y-%m-%d %H:%M")}
            </:col>
            <:action :let={job}>
              <div class="flex justify-end">
                <.link
                  navigate={~p"/admin/jobs/#{job.id}"}
                  class="text-brand-600 hover:text-brand-900"
                >
                  View
                </.link>
              </div>
            </:action>
          </Flop.Phoenix.table>
        </div>
      </div>

      <div class="admin-pagination">
        <.pagination meta={@meta} path={~p"/admin/jobs"} />
      </div>
    </div>
    """
  end
end
