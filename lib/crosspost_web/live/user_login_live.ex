defmodule CrosspostWeb.UserLoginLive do
  use CrosspostWeb, :live_view

  alias CrosspostWeb.Live.Components.MastodonAuthButtonComponent

  def mount(_params, _session, socket) do
    email = Phoenix.Flash.get(socket.assigns.flash, :email)
    form = to_form(%{"email" => email}, as: "user")

    {:ok, assign(socket, form: form, email: nil, page_class: "home-page"),
     temporary_assigns: [form: form]}
  end

  def render(assigns) do
    ~H"""
    <div class="relative min-h-screen">
      <!-- Background gradient -->
      <div
        class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        aria-hidden="true"
      >
        <div
          class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
        >
        </div>
      </div>

      <div class="relative px-6 lg:px-8">
        <div class="mx-auto max-w-5xl pt-20 sm:pt-24 lg:pt-32">
          <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Welcome back
            </h1>
            <div class="mt-10">
              <div class="max-w-lg mx-auto bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
                <div class="space-y-6">
                  <div>
                    <.live_component module={MastodonAuthButtonComponent} id="mastodon-signin" />
                  </div>

                  <div>
                    <.link
                      href={~p"/auth/linkedin?intent=sign-in"}
                      class="flex w-full items-center justify-center gap-3 rounded-md bg-brand-network-linkedin px-3 py-2 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-linkedin"
                    >
                      <svg
                        class="h-5 w-5"
                        fill="currentColor"
                        role="img"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                      </svg>
                      <span class="text-sm font-semibold leading-6">Continue with LinkedIn</span>
                    </.link>
                  </div>

                  <div>
                    <.link
                      href={~p"/auth/x?intent=sign-in"}
                      class="flex w-full items-center justify-center gap-3 rounded-md bg-brand-network-x px-3 py-2 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-x"
                    >
                      <svg
                        class="h-5 w-5"
                        fill="currentColor"
                        role="img"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                      </svg>
                      <span class="text-sm font-semibold leading-6">Continue with X</span>
                    </.link>
                  </div>

                  <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                      <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                      <span class="bg-white/60 px-2 text-gray-500">Or continue with</span>
                    </div>
                  </div>

                  <.simple_form
                    for={@form}
                    id="login_form"
                    action={~p"/users/sign-in"}
                    phx-update="ignore"
                    class="space-y-6 text-left"
                  >
                    <.live_component
                      module={CrosspostWeb.FormProtectionComponent}
                      id="form-protection"
                    />
                    <.input
                      field={@form[:email]}
                      type="email"
                      placeholder="Email"
                      readonly={@email != nil}
                      value={@email}
                      autocomplete="username"
                      tabindex="1"
                      required
                    />
                    <.input
                      field={@form[:password]}
                      type="password"
                      placeholder="Password"
                      autocomplete="current-password"
                      tabindex="2"
                      required
                    />

                    <div class="flex items-center justify-between">
                      <.input
                        field={@form[:remember_me]}
                        type="checkbox"
                        label="Keep me logged in"
                        tabindex="3"
                      />
                      <.link
                        href={~p"/users/reset_password"}
                        class="text-sm font-medium text-blue-600 hover:text-blue-500"
                      >
                        Forgot password?
                      </.link>
                    </div>

                    <:actions>
                      <.button phx-disable-with="Signing in..." class="w-full" tabindex="4">
                        Sign in <span aria-hidden="true">→</span>
                      </.button>
                    </:actions>
                  </.simple_form>

                  <div class="mt-8 text-center text-sm text-gray-600">
                    <p class="mb-2">
                      Haven't confirmed your account yet?
                    </p>
                    <.link
                      navigate={~p"/users/confirm"}
                      class="font-medium text-blue-600 hover:text-blue-500"
                    >
                      Resend confirmation instructions
                    </.link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
