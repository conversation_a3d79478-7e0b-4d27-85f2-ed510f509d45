defmodule CrosspostWeb.WorkspaceHooks do
  import Phoenix.Component
  import Phoenix.LiveView

  alias Crosspost.Workspaces

  def on_mount(:default, _params, session, socket) do
    case socket.assigns.current_user do
      nil ->
        {:halt, socket}

      user ->
        workspace_id = session["workspace_id"] || user.default_workspace_id

        case Workspaces.resolve_workspace(user, workspace_id) do
          {:ok, workspace} ->
            {:cont,
             socket
             |> assign(:current_workspace, workspace)}

          {:error, _reason} ->
            {:halt,
             socket
             |> put_flash(:error, "Failed to load workspace")
             |> redirect(to: "/")}
        end
    end
  end
end
