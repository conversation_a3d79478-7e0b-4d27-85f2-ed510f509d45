defmodule CrosspostWeb.OnboardingComponent do
  use CrosspostWeb, :live_component

  alias Crosspost.Accounts

  @slides [
    %{
      title: "Welcome to JustCrossPost!",
      description:
        "Your all-in-one solution for seamless social media management. Let's get you started with a quick tour!",
      image: "/images/onboarding/welcome.png",
      color: "bg-indigo-600/90"
    },
    %{
      title: "Set Your Timezone",
      description:
        "First, head to settings and set your timezone. This ensures your posts go out at exactly the right time, every time.",
      image: "/images/onboarding/timezone.png",
      color: "bg-blue-600/90"
    },
    %{
      title: "Connect Your Networks",
      description:
        "Visit Social Connections in Settings to link your favorite social networks. The more you connect, the more reach your content will have!",
      image: "/images/onboarding/social-connections.png",
      color: "bg-purple-600/90"
    },
    %{
      title: "Start Crossposting",
      description:
        "You're almost there! Now you can create your first post and share it across all your connected networks with just one click.",
      image: "/images/onboarding/crossposting.png",
      color: "bg-emerald-700/90"
    },
    %{
      title: "We're Here to Help!",
      description:
        "Have questions or feedback? Use the feedback button in the bottom-left corner. We're always here to help you succeed!",
      image: "/images/onboarding/feedback.png",
      color: "bg-violet-700/90"
    }
  ]

  def mount(socket) do
    {:ok, assign(socket, current_slide: 0)}
  end

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:current_slide, fn -> 0 end)
     |> assign(:slides, @slides)}
  end

  def render(assigns) do
    ~H"""
    <div class="fixed inset-0 z-50 overflow-y-auto bg-gray-900/75">
      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-gray-800 px-8 pb-8 pt-12 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl sm:p-12">
          <div class="absolute right-0 top-0 pr-6 pt-6">
            <button
              type="button"
              class="rounded-md text-gray-400 hover:text-gray-300 focus:outline-none"
              phx-click="close"
              phx-target={@myself}
            >
              <span class="sr-only">Close</span>
              <.icon name="hero-x-mark-solid" class="h-6 w-6" />
            </button>
          </div>

          <div class="w-full">
            <div class="text-center sm:text-left">
              <div class="flex items-center justify-between mb-10">
                <div class="flex items-center space-x-3 pr-8">
                  <%= for index <- 0..4 do %>
                    <button
                      type="button"
                      class={[
                        "h-2 rounded-full transition-all duration-300",
                        if(@current_slide == index,
                          do: "w-8 #{Enum.at(@slides, index).color}",
                          else: "w-2 bg-gray-600"
                        )
                      ]}
                      phx-click="set-slide"
                      phx-value-slide={index}
                      phx-target={@myself}
                    >
                      <span class="sr-only">Slide {index + 1}</span>
                    </button>
                  <% end %>
                </div>

                <div class="text-sm text-gray-400">
                  {@current_slide + 1} of {length(@slides)}
                </div>
              </div>

              <div>
                <div class={[
                  "transition-opacity duration-300",
                  Enum.at(@slides, @current_slide).color,
                  "rounded-lg p-8 shadow-lg"
                ]}>
                  <h3 class="text-2xl font-bold text-white mb-4">
                    {Enum.at(@slides, @current_slide).title}
                  </h3>
                  <p class="text-white/90 text-lg">
                    {Enum.at(@slides, @current_slide).description}
                  </p>

                  <div class="mt-8 bg-black/20 rounded-lg p-4">
                    <div class="aspect-[16/9] rounded-lg overflow-hidden">
                      <img
                        src={Enum.at(@slides, @current_slide).image}
                        alt={"Screenshot: #{Enum.at(@slides, @current_slide).title}"}
                        class="w-full h-full object-cover object-center"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-8 flex justify-between">
                <button
                  :if={@current_slide > 0}
                  type="button"
                  class="inline-flex items-center rounded-md bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-100 shadow-sm ring-1 ring-inset ring-gray-600 hover:bg-gray-600"
                  phx-click="prev-slide"
                  phx-target={@myself}
                >
                  <.icon name="hero-arrow-left-solid" class="h-5 w-5 mr-2" /> Previous
                </button>
                <div :if={@current_slide == 0}></div>

                <button
                  :if={@current_slide < length(@slides) - 1}
                  type="button"
                  class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  phx-click="next-slide"
                  phx-target={@myself}
                >
                  Next <.icon name="hero-arrow-right-solid" class="h-5 w-5 ml-2" />
                </button>

                <button
                  :if={@current_slide == length(@slides) - 1}
                  type="button"
                  class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  phx-click="close"
                  phx-target={@myself}
                >
                  Get Started <.icon name="hero-arrow-right-solid" class="h-5 w-5 ml-2" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def handle_event("next-slide", _, socket) do
    {:noreply,
     assign(socket, :current_slide, min(socket.assigns.current_slide + 1, length(@slides) - 1))}
  end

  def handle_event("prev-slide", _, socket) do
    {:noreply, assign(socket, :current_slide, max(socket.assigns.current_slide - 1, 0))}
  end

  def handle_event("set-slide", %{"slide" => slide}, socket) do
    {:noreply, assign(socket, :current_slide, String.to_integer(slide))}
  end

  def handle_event("close", _, socket) do
    {:ok, user} =
      Accounts.update_user_settings(socket.assigns.current_user, %{"onboarding_completed" => true})

    send(self(), {:user_updated, user})
    send(self(), :close_onboarding)

    {:noreply, socket}
  end
end
