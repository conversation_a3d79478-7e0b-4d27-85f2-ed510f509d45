defmodule CrosspostWeb.Live.Components.MastodonAuthButtonComponent do
  use CrosspostWeb, :live_component

  alias Crosspost.Mastodon.Instances

  @default_instance "mastodon.social"

  @impl true
  def render(assigns) do
    ~H"""
    <div class="relative" id={"mastodon-auth-#{@id}"} phx-hook="MastodonInstanceDropdown">
      <div class="inline-flex w-full">
        <button
          type="button"
          phx-click="submit"
          phx-target={@myself}
          class="flex-1 flex items-center justify-center gap-3 rounded-l-md bg-brand-network-mastodon px-3 py-2 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-mastodon"
        >
          <svg
            class="h-5 w-5"
            fill="currentColor"
            role="img"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M23.268 5.313c-.35-2.578-2.617-4.61-5.304-5.004C17.51.242 15.792 0 11.813 0h-.03c-3.98 0-4.835.242-5.288.309C3.882.692 1.496 2.518.917 5.127.64 6.412.61 7.837.661 9.143c.074 1.874.088 3.745.26 5.611.118 1.24.325 2.47.62 3.68.55 2.237 2.777 4.098 4.96 4.857 2.336.792 4.849.923 7.256.38.265-.061.527-.132.786-.213.585-.184 1.27-.39 1.774-.753a.057.057 0 0 0 .023-.043v-1.809a.052.052 0 0 0-.02-.041.053.053 0 0 0-.046-.01 20.282 20.282 0 0 1-4.709.545c-2.73 0-3.463-1.284-3.674-1.818a5.593 5.593 0 0 1-.319-1.433.053.053 0 0 1 .066-.054c1.517.363 3.072.546 4.632.546.376 0 .75 0 1.125-.01 1.57-.044 3.224-.124 4.768-.422.038-.008.077-.015.11-.024 2.435-.464 4.753-1.92 4.989-5.604.008-.145.03-1.52.03-1.67.002-.512.167-3.63-.024-5.545zm-3.748 9.195h-2.561V8.29c0-1.309-.55-1.976-1.67-1.976-1.23 0-1.846.79-1.846 2.35v3.403h-2.546V8.663c0-1.56-.617-2.35-1.848-2.35-1.112 0-1.668.668-1.67 1.977v6.218H4.822V8.102c0-1.31.337-2.35 1.011-3.12.696-.77 1.608-1.164 2.74-1.164 1.311 0 2.302.5 2.962 1.498l.638 1.06.638-1.06c.66-.999 1.65-1.498 2.96-1.498 1.13 0 2.043.395 2.74 1.164.675.77 1.012 1.81 1.012 3.12z" />
          </svg>
          <span class="text-sm font-semibold leading-6">Continue with {@selected_instance}</span>
        </button>
        <button
          type="button"
          data-dropdown-toggle
          class="flex items-center justify-center rounded-r-md bg-brand-network-mastodon px-2 text-white border-l border-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-network-mastodon hover:bg-brand-network-mastodon/90"
        >
          <.icon name="hero-chevron-down" class="h-5 w-5" />
        </button>
      </div>

      <div
        data-dropdown
        class={"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 #{if @dropdown_visible, do: "", else: "hidden"}"}
      >
        <div class="p-2 bg-white dark:bg-gray-800">
          <.form
            for={@form}
            id={"mastodon-instance-form-#{@id}"}
            phx-change="search"
            phx-target={@myself}
          >
            <.input
              field={@form[:instance]}
              type="text"
              placeholder="Search instances..."
              phx-debounce="300"
              autocomplete="off"
              class="bg-white dark:bg-gray-800"
            />
          </.form>
        </div>

        <ul class="py-1 max-h-60 overflow-auto bg-white dark:bg-gray-800" role="listbox">
          <%= for instance <- @suggestions do %>
            <li
              class="px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              role="option"
              phx-click="select_instance"
              phx-value-instance={instance}
              phx-target={@myself}
            >
              {instance}
            </li>
          <% end %>
        </ul>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    form = to_form(%{"instance" => ""})

    {:ok,
     socket
     |> assign(assigns)
     |> assign(dropdown_visible: false)
     |> assign(
       form: form,
       suggestions: [],
       selected_instance: @default_instance
     )}
  end

  @impl true
  def handle_event("search", %{"instance" => query}, socket) when byte_size(query) > 0 do
    case Instances.list_instances(query) do
      {:ok, %{"instances" => instances}} ->
        {:noreply,
         socket
         |> assign(suggestions: instances)
         |> assign(dropdown_visible: true)}

      _ ->
        {:noreply, socket}
    end
  end

  def handle_event("search", _params, socket) do
    {:noreply,
     socket
     |> assign(suggestions: [])}
  end

  def handle_event("select_instance", %{"instance" => instance}, socket) do
    {:noreply,
     socket
     |> assign(
       selected_instance: instance,
       suggestions: [],
       dropdown_visible: false
     )}
  end

  def handle_event("submit", _params, socket) do
    instance = socket.assigns.selected_instance

    params = %{
      intent: socket.assigns[:intent] || "sign-in",
      instance: instance
    }

    {:noreply,
     socket
     |> redirect(to: ~p"/auth/mastodon?#{params}")}
  end
end
