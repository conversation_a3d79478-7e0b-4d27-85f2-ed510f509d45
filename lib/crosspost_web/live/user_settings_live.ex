defmodule CrosspostWeb.UserSettingsLive do
  use CrosspostWeb, :live_view

  require Logger

  # import CrosspostWeb.RootViewLive

  alias Crosspost.Repo
  alias Crosspost.Accounts
  alias Crosspost.Accounts.User
  alias Crosspost.Plans

  alias CrosspostWeb.UserSettingsLive.{
    ProfileInformation,
    SocialConnections,
    DeleteAccount,
    Password,
    Plan,
    Publishing
  }

  @impl true
  def mount(_params, session, socket) do
    user = socket.assigns.current_user
    current_workspace = socket.assigns.current_workspace |> Repo.preload(:connections)
    profile_changeset = Accounts.change_user_profile(user)

    # Get initial sidebar state from session, default to true if not set
    show_sidebar =
      case session["sidebar_state"] do
        "false" -> false
        _ -> true
      end

    user = Accounts.get_workspace_user!(user.id, current_workspace.id)

    socket =
      socket
      |> assign(:page_title, "Settings")
      |> assign(:current_user, user)
      |> assign(:current_workspace, current_workspace)
      |> assign(:profile_form, to_form(profile_changeset))
      |> assign(:profile_form_current_password, nil)
      |> assign(:current_password, nil)
      |> assign(:current_email, user.email)
      |> assign(:password_form, to_form(Accounts.change_user_password(user)))
      |> assign(:trigger_submit, false)
      |> assign(:active_section, "profile")
      |> assign(:plans, Plans.list_active_plans())
      |> assign(:show_sidebar, show_sidebar)

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    section = params["section"] || "profile"
    {:noreply, assign(socket, :active_section, section)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-[1024px] mx-auto">
      <div class="border-b border-gray-200 my-4 sm:my-8">
        <div class="overflow-x-auto hide-scrollbar -mb-px">
          <nav class="flex whitespace-nowrap min-w-full px-2 sm:px-0" aria-label="Settings sections">
            <.tab_link patch={~p"/settings/profile"} active={@active_section == "profile"}>
              Profile Information
            </.tab_link>
            <.tab_link patch={~p"/settings/plan"} active={@active_section == "plan"}>
              Plan & Usage
            </.tab_link>
            <.tab_link patch={~p"/settings/workspaces"} active={@active_section == "workspaces"}>
              Workspaces
            </.tab_link>
            <.tab_link patch={~p"/settings/publishing"} active={@active_section == "publishing"}>
              Publishing
            </.tab_link>
            <.tab_link
              patch={~p"/settings/social-connections"}
              active={@active_section == "social-connections"}
            >
              Social Connections
            </.tab_link>
            <.tab_link patch={~p"/settings/password"} active={@active_section == "password"}>
              Change Password
            </.tab_link>
            <.tab_link
              patch={~p"/settings/delete-account"}
              active={@active_section == "delete-account"}
            >
              Delete Account
            </.tab_link>
          </nav>
        </div>
      </div>

      <div class="mt-6">
        {render_section(assigns)}
      </div>
    </div>
    """
  end

  defp tab_link(assigns) do
    ~H"""
    <.link
      patch={@patch}
      class={[
        "whitespace-nowrap pb-2 sm:pb-4 px-2 sm:px-4 border-b-2 font-medium text-sm flex-none",
        @active && "border-blue-500 text-blue-400",
        !@active && "border-transparent text-gray-500 hover:text-blue-400 hover:border-blue-500"
      ]}
    >
      {render_slot(@inner_block)}
    </.link>
    """
  end

  defp render_section(%{active_section: "profile"} = assigns) do
    ~H"""
    <.live_component
      module={ProfileInformation}
      id="profile-information"
      current_user={@current_user}
      profile_form={@profile_form}
      profile_form_current_password={@profile_form_current_password}
    />
    """
  end

  defp render_section(%{active_section: "plan"} = assigns) do
    ~H"""
    <.live_component module={Plan} id="plan" current_user={@current_user} />
    """
  end

  defp render_section(%{active_section: "social-connections"} = assigns) do
    ~H"""
    <.live_component
      module={SocialConnections}
      id="social-connections"
      current_user={@current_user}
      current_workspace={@current_workspace}
    />
    """
  end

  defp render_section(%{active_section: "password"} = assigns) do
    ~H"""
    <.live_component
      module={Password}
      id="password"
      current_user={@current_user}
      password_form={@password_form}
      trigger_submit={@trigger_submit}
      current_password={@current_password}
      current_email={@current_email}
    />
    """
  end

  defp render_section(%{active_section: "delete-account"} = assigns) do
    ~H"""
    <.live_component module={DeleteAccount} id="delete-account" current_user={@current_user} />
    """
  end

  defp render_section(%{active_section: "workspaces"} = assigns) do
    ~H"""
    <.live_component
      module={CrosspostWeb.UserSettingsLive.Workspaces}
      id="workspaces"
      current_user={@current_user}
      current_workspace={@current_workspace}
      plans={@plans}
    />
    """
  end

  defp render_section(%{active_section: "publishing"} = assigns) do
    ~H"""
    <.live_component
      module={Publishing}
      id="publishing"
      current_user={@current_user}
      current_workspace={@current_workspace}
    />
    """
  end

  @impl true
  def handle_event("validate_password", params, socket) do
    %{"user" => user_params} = params

    password_form =
      socket.assigns.current_user
      |> Accounts.change_user_password(user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, password_form: password_form)}
  end

  @impl true
  def handle_event("update_password", params, socket) do
    %{"user" => user_params} = params
    user = socket.assigns.current_user

    case Accounts.update_user_password(user, user_params["current_password"], user_params) do
      {:ok, user} ->
        password_form =
          user
          |> Accounts.change_user_password(user_params)
          |> to_form()

        {:noreply,
         socket
         |> assign(trigger_submit: true, password_form: password_form)
         |> put_flash(:info, "Password updated successfully.")}

      {:error, changeset} ->
        {:noreply, assign(socket, password_form: to_form(changeset))}
    end
  end

  @impl true
  def handle_event("delete_account", params, socket) do
    user = socket.assigns.current_user
    feedback = params["feedback"]
    confirmation = params["confirmation"]

    if confirmation == "delete my account" do
      if user.hashed_password do
        case Accounts.get_user_by_email_and_password(user.email, params["current_password"]) do
          %User{} = user -> delete_user_account(socket, user, feedback)
          nil -> {:noreply, put_flash(socket, :error, "Invalid password. Please try again.")}
        end
      else
        delete_user_account(socket, user, feedback)
      end
    else
      {:noreply,
       put_flash(socket, :error, "Please type 'delete my account' to confirm deletion.")}
    end
  end

  @impl true
  def handle_event("validate_profile", %{"user" => user_params}, socket) do
    Logger.debug("Parent validate_profile called with params: #{inspect(user_params)}")

    profile_form =
      socket.assigns.current_user
      |> Accounts.change_user_profile(user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    Logger.debug("Parent validate_profile generated errors: #{inspect(profile_form.errors)}")

    {:noreply, assign(socket, profile_form: profile_form)}
  end

  @impl true
  def handle_event("update_profile", %{"user" => user_params} = params, socket) do
    user = socket.assigns.current_user
    password = if user.hashed_password, do: params["current_password"], else: nil

    case Accounts.update_user_profile(user, password, user_params) do
      {:ok, updated_user} ->
        Logger.debug("Profile update successful")

        {:noreply,
         socket
         |> assign(:current_user, updated_user)
         |> assign(:profile_form, to_form(Accounts.change_user_profile(updated_user)))
         |> assign(:profile_form_current_password, nil)
         |> put_flash(:info, "Profile updated successfully.")}

      {:error, changeset} ->
        Logger.debug("Profile update failed with errors: #{inspect(changeset.errors)}")
        {:noreply, assign(socket, :profile_form, to_form(Map.put(changeset, :action, :insert)))}
    end
  end

  def handle_event("show_modal", %{"network" => network}, socket) do
    case network do
      "bsky" ->
        {:noreply, assign(socket, show_bsky_modal: true)}

      "mastodon" ->
        {:noreply, assign(socket, show_mastodon_modal: true)}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:show_flash, {message, type}}, socket) do
    {:noreply, put_flash(socket, type, message)}
  end

  @impl true
  def handle_info({:reload_page, updated_user}, socket) do
    {:noreply,
     socket
     |> assign(:current_user, updated_user)
     |> put_flash(:info, "Publishing settings updated successfully.")
     |> redirect(to: ~p"/settings/publishing")}
  end

  @impl true
  def handle_info({:profile_updated, updated_user, :ok}, socket) do
    {:noreply,
     socket
     |> assign(:current_user, updated_user)}
  end

  @impl true
  def handle_info({:user_update_success, {updated_user, message}, :ok}, socket) do
    {:noreply,
     socket
     |> assign(:current_user, updated_user)
     |> put_flash(:info, message)}
  end

  def handle_info({:user_update_error, message, :error}, socket) do
    {:noreply, put_flash(socket, :error, message)}
  end

  def handle_info(:subscription_cancelled, socket) do
    user = socket.assigns.current_user

    {:noreply,
     socket
     |> assign(:current_user, user)
     |> put_flash(:info, "Your subscription has been cancelled.")
     |> push_navigate(to: ~p"/unsubscribed")}
  end

  defp delete_user_account(socket, user, feedback) do
    case Accounts.delete_user(user) do
      {:ok, _} ->
        if feedback && feedback != "" do
          Crosspost.Mailer.deliver_account_deletion_feedback(user.email, feedback)
        end

        {:noreply,
         socket
         |> put_flash(:info, "Your account has been successfully deleted.")
         |> redirect(to: ~p"/")}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to delete account. Please try again.")}
    end
  end
end
