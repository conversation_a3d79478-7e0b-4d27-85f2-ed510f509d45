defmodule CrosspostWeb.UserRegistrationLive do
  use CrosspostWeb, :live_view

  alias CrosspostWeb.Components.UserRegistrationForm

  require Logger

  def render(assigns) do
    ~H"""
    <div class="relative min-h-screen">
      <!-- Background gradient -->
      <div
        class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        aria-hidden="true"
      >
        <div
          class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
        >
        </div>
      </div>

      <div class="relative px-6 lg:px-8">
        <div class="mx-auto max-w-5xl pt-20 sm:pt-24 lg:pt-32">
          <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Join JustCrossPost Beta 🚀
            </h1>
            <div class="mt-4 flex flex-col items-center gap-2">
              <span class="inline-flex items-center gap-x-2 rounded-full bg-indigo-100 dark:bg-indigo-900/50 px-4 py-1 text-sm font-medium text-indigo-700 dark:text-indigo-200 ring-1 ring-inset ring-indigo-600/20 dark:ring-indigo-400/30">
                <.icon name="hero-sparkles" class="h-4 w-4" /> Beta Special: 25% off all plans
              </span>
              <span class="inline-flex items-center gap-x-2 rounded-full bg-green-100 dark:bg-green-900/50 px-4 py-1 text-sm font-medium text-green-700 dark:text-green-200 ring-1 ring-inset ring-green-600/20 dark:ring-green-400/30">
                <.icon name="hero-clock" class="h-4 w-4" />
                Start with a 14-day free trial (no credit card required)
              </span>
            </div>
            <div class="mt-10">
              <div class="max-w-lg mx-auto bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
                <.live_component module={UserRegistrationForm} id="registration-form" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(%{"invitation_code" => invitation_code}, _session, socket)
      when not is_nil(invitation_code) do
    {:ok, socket |> redirect(to: ~p"/users/sign-up")}
  end

  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  def handle_info({:registration_success, %{flash: flash, redirect_to: redirect_to}}, socket) do
    {:noreply,
     socket
     |> put_flash(:info, flash)
     |> redirect(to: redirect_to)}
  end

  def handle_info(:email, socket) do
    {:noreply, socket}
  end

  def handle_info(
        {:email, %Swoosh.Email{to: [{_name, to_email}], subject: subject} = _email},
        socket
      ) do
    Logger.info("Confirmation email sent", %{
      event: "accounts.confirmation_email.sent",
      to_email: to_email,
      subject: subject
    })

    {:noreply, socket}
  end
end
