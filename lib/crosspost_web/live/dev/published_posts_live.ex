defmodule CrosspostWeb.Dev.PublishedPostsLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Workers.TestPublishPostWorker

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      :timer.send_interval(1000, self(), :refresh)
    end

    {:ok, assign(socket, :posts, fetch_posts())}
  end

  @impl true
  def handle_event("clear", _params, socket) do
    TestPublishPostWorker.clear_published()
    {:noreply, assign(socket, :posts, [])}
  end

  @impl true
  def handle_info(:refresh, socket) do
    {:noreply, assign(socket, :posts, fetch_posts())}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-bold">Published Posts</h1>
        <button
          class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          phx-click="clear"
        >
          Clear All
        </button>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Post ID
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Network
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Content
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Meta
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Published At
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Attempt
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <%= for post <- @posts do %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {post[:post_id] || get_post_id(post)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {post[:network] || get_network(post)}
                </td>
                <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                  {format_content(post.content)}
                </td>
                <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                  {format_meta(post.content)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {Calendar.strftime(post.published_at, "%Y-%m-%d %H:%M:%S")}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {post[:attempt] || 1}
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
    """
  end

  defp fetch_posts do
    TestPublishPostWorker.get_published()
    |> Enum.sort_by(
      fn post ->
        network = get_in(post.content, [Access.at(0), :network])
        {DateTime.to_unix(post.published_at), network}
      end,
      fn {time1, network1}, {time2, network2} ->
        case time1 == time2 do
          true -> network1 <= network2
          false -> time1 >= time2
        end
      end
    )
  end

  defp format_content(content) when is_binary(content), do: content

  defp format_content(content) when is_list(content) do
    content
    |> Enum.map_join("\n", & &1.text)
  end

  defp format_content(_), do: "Invalid content format"

  defp get_network(post) do
    get_in(post.content, [Access.at(0), :network])
  end

  defp get_post_id(post) do
    get_in(post.content, [Access.at(0), :post_id])
  end

  defp format_meta(content) when is_list(content) do
    content
    |> Enum.map_join("\n", fn item ->
      Jason.encode!(item.meta, pretty: true)
    end)
  end

  defp format_meta(_), do: "Invalid meta format"
end
