defmodule CrosspostWeb.PostLive.EditorComponent do
  use CrosspostWeb, :live_component

  require Logger

  import CrosspostWeb.PostLive.Common
  import Crosspost, only: [broadcast: 2]

  alias Crosspost.Repo
  alias Crosspost.Publishing
  alias Crosspost.Features
  alias Crosspost.Mastodon.Instances

  @impl true
  def mount(socket) do
    {:ok,
     socket
     |> assign(:post, nil)
     |> assign(:selected_network, :canonical)
     |> assign(:show_preview, false)
     |> assign(:preview_network, nil)
     |> assign(:cloudinary, %{
       cloud_name: Application.get_env(:cloudex, :cloud_name),
       enabled: Application.get_env(:cloudex, :enabled)
     })}
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:current_post_id, fn ->
        case assigns[:post] do
          %{id: id} when not is_nil(id) -> id
          _ -> nil
        end
      end)
      |> assign(:user_connections, available_networks(assigns.current_user))
      |> assign(:current_network, :canonical)
      |> assign(:cloudinary, %{
        cloud_name: Application.get_env(:cloudex, :cloud_name),
        enabled: Application.get_env(:cloudex, :enabled)
      })

    {:ok, socket}
  end

  defp available_networks(user) do
    user.workspace_connections
    |> Enum.map(& &1.platform)
    |> Enum.reject(&(&1 == "twitter"))
    |> Enum.map(&String.to_existing_atom/1)
  end

  defp connection_data(_networks, user) do
    user.workspace_connections
    |> Enum.reject(&(&1.platform == "twitter"))
    |> Enum.map(fn connection ->
      base_data = %{
        network: String.to_existing_atom(connection.platform),
        avatar: get_connection_avatar(connection),
        settings: connection.settings
      }

      case connection.platform do
        "mastodon" ->
          instance_domain = connection.settings["instance"]

          case Repo.get(Crosspost.Mastodon.Instance, instance_domain) do
            %{
              info: %{
                "configuration" => %{
                  "statuses" => %{
                    "max_characters" => limit,
                    "characters_reserved_per_url" => url_length
                  }
                }
              }
            } = _instance ->
              base_data
              |> Map.put(:limit, limit)
              |> Map.put(:urlLength, url_length)

            _ ->
              case Instances.update_instance_info(instance_domain) do
                {:ok,
                 %{
                   info: %{
                     "configuration" => %{
                       "statuses" => %{
                         "max_characters" => limit,
                         "characters_reserved_per_url" => url_length
                       }
                     }
                   }
                 }} ->
                  base_data
                  |> Map.put(:limit, limit)
                  |> Map.put(:urlLength, url_length)

                _ ->
                  # Default Mastodon limit and URL length
                  base_data
                  |> Map.put(:limit, 500)
                  |> Map.put(:urlLength, 23)
              end
          end

        "x" ->
          limit =
            case connection.info["subscription_type"] do
              "Premium" -> 25_000
              "PremiumPlus" -> 25_000
              "Basic" -> 4_000
              _ -> 280
            end

          base_data
          |> Map.put(:limit, limit)
          |> Map.put(:urlLength, 23)

        _ ->
          base_data
      end
    end)
  end

  defp get_connection_avatar(connection) do
    case connection.platform do
      "linkedin" when is_map_key(connection.settings, "organization") ->
        connection.settings["organization"]["resolved_logo_urls"]["original"]

      _ ->
        connection.info["image"] || connection.info["avatar"]
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      id={@id}
      data-component="editor"
      class="w-full"
      phx-hook="PostForm"
      phx-target={@myself}
      data-connections={Jason.encode!(connection_data(@user_connections, @current_user))}
      data-post={post_data(@post)}
      data-cloudinary={Jason.encode!(@cloudinary)}
      data-schedule-slots={Jason.encode!(calculate_schedule_slots(@current_user))}
      data-future-schedules={
        Jason.encode!(
          Publishing.list_workspace_future_schedules(@current_workspace.id, @post && @post.id)
        )
      }
    >
      <%= if @current_user.workspace_connections == [] do %>
        <div class="mb-4 sm:mb-6 rounded-md bg-yellow-50 p-3 sm:p-4 max-w-2xl mx-auto">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fill-rule="evenodd"
                  d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">No social networks connected</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>
                  You haven't connected any social networks yet.
                  <.link
                    navigate={~p"/settings/social-connections"}
                    class="font-medium underline hover:text-yellow-600"
                  >
                    Go to settings
                  </.link>
                  to connect your accounts and start posting.
                </p>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="relative">
        <div class="w-full bg-white sm:border-b border-gray-200 sm:mb-8">
          <div
            id="editor-topbar"
            phx-update="ignore"
            class="flex items-center justify-between h-20 px-4 md:px-8 max-w-8xl mx-auto py-6"
          >
            <div class="network-toggles flex gap-2 sm:gap-3">
              <%= for network <- [:bsky, :mastodon, :linkedin, :x] do %>
                <%= if network in @user_connections do %>
                  <div data-network-button={network}></div>
                <% end %>
              <% end %>
            </div>
            <button
              type="button"
              class="flex items-center gap-2 px-3 py-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
              phx-click="toggle_preview"
              phx-target={@myself}
              phx-value-network=""
              data-preview-button
              data-hidden="true"
            >
              <span class="text-sm">Preview</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </button>
          </div>
        </div>

        <div class="w-full max-w-2xl mx-auto">
          <div phx-update="ignore" id="editor-wrapper" class="mb-6 sm:mt-10">
            <div id="tiptap-editor-container" data-content-editors></div>
          </div>

          <div class="flex flex-col mt-4 px-2 sm:px-0">
            <.live_component
              module={CrosspostWeb.PostLive.ScheduleInfoComponent}
              id="schedule-info"
              post={@post}
              current_user={@current_user}
            />

            <div class="flex justify-end">
              <div id="actions" phx-update="ignore">
                <div class="flex gap-2">
                  <button
                    type="button"
                    class="px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                    data-save-button
                  >
                    Save Draft
                  </button>

                  <button
                    type="button"
                    class="px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                    data-publish-button
                  >
                    Publish Now
                  </button>

                  <button
                    :if={Features.enabled?(@current_user, "auto_scheduler")}
                    type="button"
                    class="px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                    data-auto-schedule-button
                    data-feature="auto_scheduler"
                    data-feature-enabled={
                      if Features.enabled?(@current_user, "scheduled_posts") and
                           not Features.limit_reached?(@current_user, "scheduled_posts") do
                        "true"
                      else
                        "false"
                      end
                    }
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    Auto-schedule
                  </button>

                  <button
                    :if={!Features.enabled?(@current_user, "auto_scheduler")}
                    type="button"
                    class="px-2 py-1.5 bg-blue-600 hover:bg-blue-700 rounded-md focus:outline-none text-white"
                    data-open-scheduling
                    data-feature="scheduled_posts"
                    data-feature-enabled={
                      if Features.enabled?(@current_user, "scheduled_posts") and
                           not Features.limit_reached?(@current_user, "scheduled_posts") do
                        "true"
                      else
                        "false"
                      end
                    }
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          id="preview-panel"
          class={[
            "previews bg-white dark:bg-gray-800 fixed inset-0 shadow-lg transform transition-transform duration-300 z-50 md:inset-y-0 md:left-auto md:w-[600px]",
            if(@show_preview, do: "translate-x-0", else: "translate-x-full")
          ]}
          phx-window-keydown="close_preview"
          phx-key="escape"
          phx-target={@myself}
        >
          <.live_component
            module={CrosspostWeb.PostLive.PreviewComponent}
            id="preview"
            post={@post}
            current_user={@current_user}
            show_close_button={true}
            preview_network={@preview_network}
          />
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("switch_preview", %{"network" => network}, socket) do
    {:noreply, assign(socket, selected_network: String.to_existing_atom(network))}
  end

  @impl true
  def handle_event("toggle_preview", %{"network" => network}, socket) when network != "" do
    {:noreply,
     socket
     |> assign(:show_preview, !socket.assigns.show_preview)
     |> assign(:preview_network, String.to_existing_atom(network))}
  end

  def handle_event("toggle_preview", _params, socket) do
    {:noreply, assign(socket, :show_preview, !socket.assigns.show_preview)}
  end

  def handle_event("close_preview", %{"key" => "Escape"}, socket) do
    {:noreply, assign(socket, show_preview: false)}
  end

  def handle_event("save", params, socket) do
    Logger.metadata(
      action: "save",
      post_id: params["id"],
      user_id: socket.assigns.current_user.id,
      workspace_id: socket.assigns.current_workspace.id,
      publish_now: Map.get(params, "publish_now", false),
      auto_schedule: Map.get(params, "auto_schedule", false),
      auto_save: Map.get(params, "auto_save", false)
    )

    Logger.debug("Saving post", params: params)

    status =
      case params do
        %{"schedule" => true} -> "scheduled"
        %{"publish_now" => true} -> "publishing"
        %{"status" => status} -> status
        _ -> "draft"
      end

    # Filter out networks that don't have their crossposting feature enabled
    enabled_networks =
      (params["social_networks"] || [])
      |> Enum.filter(fn network ->
        feature_key = "crossposting_#{network}"
        Features.enabled?(socket.assigns.current_user, feature_key)
      end)

    attrs = %{
      status: status,
      content: format_content(params["content"]),
      social_networks: enabled_networks,
      workspace_id: socket.assigns.current_workspace.id,
      schedules:
        if(params["publish_now"]) do
          []
        else
          Publishing.format_schedules(params["schedules"], socket.assigns.current_user.timezone)
        end
    }

    result =
      case Publishing.get_post(params["id"], socket.assigns.current_user) do
        {:ok, post} ->
          case Publishing.update_post(post, attrs) do
            {:ok, updated_post} ->
              {:ok, updated_post}

            {:error, changeset} ->
              {:error, changeset}
          end

        {:error, :not_found} ->
          case Publishing.create_post(attrs, socket.assigns.current_user) do
            {:ok, post} ->
              {:ok, post}

            {:error, changeset} ->
              {:error, changeset}
          end
      end

    case result do
      {:ok, post} ->
        cond do
          params["auto_schedule"] ->
            Logger.debug("Auto-scheduling post", post_id: post.id)

            case Publishing.auto_schedule(post, socket.assigns.current_user) do
              {:ok, scheduled_post} ->
                Logger.info("Post auto-scheduled successfully", post_id: scheduled_post.id)

                {:noreply,
                 socket
                 |> put_flash(:info, "Post auto-scheduled successfully")
                 |> push_navigate(to: ~p"/posts/#{scheduled_post.id}")}

              {:error, message} ->
                Logger.error("Failed to auto-schedule post",
                  post_id: post.id,
                  error: message
                )

                {:noreply, put_flash(socket, :error, "Failed to auto-schedule post")}
            end

          params["publish_now"] ->
            case Publishing.publish_post_now(post, socket.assigns.current_user) do
              {:ok, published_post} ->
                Logger.info("Post scheduled for immediate publishing", post_id: published_post.id)

                {:noreply,
                 socket
                 |> put_flash(:info, "Post scheduled for publishing")
                 |> push_navigate(to: ~p"/posts/#{published_post.id}")}

              {:error, message} ->
                Logger.error("Failed to publish post immediately",
                  post_id: post.id,
                  error: message
                )

                {:noreply, put_flash(socket, :error, "Failed to publish post")}
            end

          params["schedule"] ->
            Logger.debug("Scheduling post", post_id: post.id)

            case Publishing.schedule_job(post, socket.assigns.current_user) do
              {:ok, _} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Post scheduled successfully")
                 |> push_navigate(to: ~p"/posts/#{post.id}")}

              {:error, reason} ->
                Logger.error("Failed to schedule post",
                  post_id: post.id,
                  error: inspect(reason)
                )

                {:noreply, put_flash(socket, :error, "Failed to schedule post")}
            end

          params["auto_save"] ->
            broadcast("posts:#{socket.assigns.current_user.id}", {:post_updated, post})

            {:reply, %{id: post.id}, socket |> assign(:post, post)}

          true ->
            {:noreply,
             socket
             |> put_flash(:info, "Post saved successfully")
             |> push_navigate(to: ~p"/posts/#{post.id}")}
        end

      {:error, changeset} ->
        Logger.error("Failed to save post",
          error: format_changeset_errors(changeset),
          changeset: inspect(changeset)
        )

        {:noreply,
         socket
         |> put_flash(:error, "Failed to save post")}
    end
  end

  defp post_data(post) do
    post = Repo.preload(post, [:schedules, content: [:content_attachments]])

    Jason.encode!(%{
      id: post.id,
      status: post.status,
      content:
        Enum.map(post.content, fn content ->
          %{
            id: content.id,
            text: content.text,
            network: content.network,
            order: content.order,
            meta: content.meta,
            sync: content.sync,
            attachments: content.content_attachments
          }
        end),
      social_networks: post.social_networks,
      schedules:
        Enum.map(post.schedules || [], fn schedule ->
          %{
            network: schedule.network,
            scheduled_at: schedule.scheduled_at
          }
        end)
    })
  end

  defp format_changeset_errors(changeset) do
    Enum.map(changeset.errors, fn
      {key, {msg, _}} -> "#{Phoenix.Naming.humanize(key)} #{msg}"
      {key, msg} -> "#{Phoenix.Naming.humanize(key)} #{msg}"
    end)
    |> Enum.join("; ")
  end

  defp calculate_schedule_slots(user) do
    # Get past schedules for the user
    past_schedules =
      Publishing.list_past_schedules(user)
      |> Enum.group_by(& &1.scheduled_at.hour)
      |> Enum.map(fn {hour, schedules} ->
        count = length(schedules)
        {hour, count}
      end)
      |> Enum.sort_by(fn {_hour, count} -> count end, :desc)
      # Take top 5 most used time slots
      |> Enum.take(5)

    # Calculate next 7 days of slots
    now = DateTime.utc_now()

    for day <- 0..6,
        {hour, _count} <- past_schedules do
      date = DateTime.add(now, day, :day)
      date = %{date | hour: hour, minute: 0, second: 0}
      DateTime.shift_zone!(date, user.timezone)
    end
    |> Enum.sort_by(& &1)
  end
end
