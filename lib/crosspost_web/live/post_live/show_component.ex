defmodule CrosspostWeb.PostLive.ShowComponent do
  use CrosspostWeb, :live_component

  import CrosspostWeb.StatusBadgeComponent
  import CrosspostWeb.DatetimeHelpers

  alias Crosspost.Repo

  @impl true
  def update(assigns, socket) do
    post = Repo.preload(assigns.post, :final_statuses)

    {:ok, socket |> assign(assigns) |> assign(:post, post)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="w-full max-w-2xl mx-auto mt-2 sm:mt-6" id="post-details">
      <.header class="mb-4 sm:mb-8">
        <div class="flex items-center gap-2 px-2 sm:px-0">
          <h1 class="text-lg sm:text-xl lg:text-2xl font-bold">Post Details</h1>
          <.status_badge status={@post.status} class="text-sm sm:text-base" />
        </div>
        <:actions>
          <div class="flex items-center gap-2 px-2 sm:px-0">
            <.link
              :if={@post.status != "published"}
              navigate={~p"/posts/#{@post.id}/edit"}
              class="text-white hover:text-blue-200"
              data-button-edit-post={@post.id}
            >
              <div class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center bg-blue-500 rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <.icon name="hero-pencil" class="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
            </.link>
            <.link
              :if={@post.status != "published"}
              phx-click="delete"
              phx-value-id={@post.id}
              data-confirm="Are you sure?"
              class="text-white hover:text-red-200"
            >
              <div class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center bg-red-500 rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                <.icon name="hero-trash" class="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
            </.link>
          </div>
        </:actions>
      </.header>

      <div class="space-y-3 sm:space-y-4">
        <div class="bg-white border-t border-b sm:border rounded-none sm:rounded-lg shadow-sm border-gray-200/60">
          <div class="divide-y divide-gray-600/30">
            <%= for network <- sort_networks_by_time(@post) do %>
              <div class="flex items-center justify-between p-4" data-network={network}>
                <div class="flex items-center gap-3">
                  <div data-status={network_status(@post, network)}>
                    <.network_status_button
                      network={network}
                      status={network_status(@post, network)}
                      post={@post}
                      current_user={@current_user}
                    />
                  </div>
                  <span class="font-medium">
                    {Phoenix.Naming.humanize(network)}
                  </span>
                </div>
                <div class="text-sm text-gray-600">
                  <%= case network_status(@post, network) do %>
                    <% :published -> %>
                      <div class="flex items-center gap-3">
                        <span>
                          Published {format_datetime(
                            get_network_published_at(@post, network),
                            @current_user.timezone
                          )}
                        </span>
                        <%= if url = get_network_post_url(@post, network) do %>
                          <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            class="inline-flex items-center text-blue-600 hover:text-blue-800"
                            data-network={network}
                          >
                            View post <span class="sr-only"></span>
                            <.icon name="hero-arrow-top-right-on-square" class="w-3 h-3 ml-1" />
                          </a>
                        <% end %>
                      </div>
                    <% status when status in [:scheduled, :pending, :publishing, :retrying] -> %>
                      <span>
                        Scheduled for {format_datetime(
                          get_network_scheduled_at(@post, network),
                          @current_user.timezone
                        )}
                      </span>
                    <% :failed -> %>
                      <div class="flex items-center gap-1">
                        <span class="text-red-600">Failed to publish</span>
                        <div class="group relative cursor-help">
                          <.icon name="hero-question-mark-circle" class="w-4 h-4 text-red-600" />
                          <div class="hidden group-hover:block absolute z-50 w-64 p-2 text-sm bg-gray-900 text-white rounded shadow-lg -left-1/2 bottom-full mb-1">
                            {network_status_message(@post, network, "Unexpected error occurred")}
                          </div>
                        </div>
                      </div>
                    <% _ -> %>
                      <span>Not scheduled</span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <div class="bg-white border-t border-b sm:border rounded-none sm:rounded-lg shadow-sm border-gray-200/60">
          <.live_component
            module={CrosspostWeb.PostLive.PreviewComponent}
            id={"preview-#{@post.id}"}
            post={@post}
            current_user={@current_user}
            show_close_button={false}
          />
        </div>
      </div>
    </div>
    """
  end

  # Sort networks by their status and timestamps
  defp sort_networks_by_time(post) do
    post.social_networks
    |> Enum.sort_by(
      fn network ->
        status = network_status(post, network)
        published_at = get_network_published_at(post, network)
        scheduled_at = get_network_scheduled_at(post, network)

        case status do
          :published when not is_nil(published_at) ->
            {0, published_at}

          status when status in [:scheduled, :pending, :publishing, :retrying] ->
            {1, scheduled_at || ~N[9999-12-31 23:59:59]}

          :failed ->
            {2, ~N[9999-12-31 23:59:59]}

          _ ->
            {3, ~N[9999-12-31 23:59:59]}
        end
      end,
      fn {order1, time1}, {order2, time2} ->
        case order1 == order2 do
          true -> NaiveDateTime.compare(time1, time2) != :gt
          false -> order1 < order2
        end
      end
    )
  end

  def network_status_button(assigns) do
    ~H"""
    <div class="relative">
      <div
        class={[
          "w-8 h-8 rounded-full flex items-center justify-center relative overflow-hidden",
          if(@status in [:publishing, :retrying], do: "animate-pulse"),
          status_ring_classes(@status)
        ]}
        data-network={@network}
      >
        <%= if avatar = get_network_avatar(@current_user, @network) do %>
          <div
            class="absolute inset-0"
            style={"background-image: url('#{avatar}'); background-size: cover; background-position: center;"}
          >
          </div>
        <% else %>
          <div class="absolute inset-0 bg-gray-200"></div>
        <% end %>
      </div>
    </div>
    """
  end

  defp status_ring_classes(status) do
    base_classes = "ring-2"

    case status do
      :published ->
        "#{base_classes} ring-green-500"

      status when status in [:scheduled, :pending, :publishing, :retrying] ->
        "#{base_classes} ring-purple-500"

      :failed ->
        "#{base_classes} ring-red-500"

      _ ->
        "#{base_classes} ring-gray-300"
    end
  end

  defp get_network_published_at(post, network) do
    post.final_statuses
    |> Enum.filter(&(&1.network == network and &1.status == :published))
    |> Enum.sort_by(
      & &1.updated_at,
      fn dt1, dt2 ->
        case {dt1, dt2} do
          {%DateTime{} = dt1, %DateTime{} = dt2} ->
            DateTime.compare(dt1, dt2) != :gt

          {%NaiveDateTime{} = dt1, %NaiveDateTime{} = dt2} ->
            NaiveDateTime.compare(dt1, dt2) != :gt

          {%NaiveDateTime{} = dt1, %DateTime{} = dt2} ->
            NaiveDateTime.compare(dt1, DateTime.to_naive(dt2)) != :gt

          {%DateTime{} = dt1, %NaiveDateTime{} = dt2} ->
            DateTime.compare(dt1, DateTime.from_naive!(dt2, "Etc/UTC")) != :gt
        end
      end
    )
    |> List.first()
    |> case do
      %{updated_at: updated_at} -> updated_at
      _ -> nil
    end
  end

  defp get_network_scheduled_at(post, network) do
    case post.schedules do
      %Ecto.Association.NotLoaded{} ->
        nil

      schedules when is_list(schedules) ->
        case Enum.find(schedules, &(&1.network == network)) do
          %{scheduled_at: scheduled_at} -> scheduled_at
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp network_status(post, network) do
    case Enum.find(post.final_statuses, &(&1.network == network)) do
      %{status: status} when is_atom(status) ->
        status

      %{status: status} when is_binary(status) ->
        String.to_atom(status)

      _ ->
        case post.status do
          "draft" -> :draft
          "scheduled" -> if get_network_scheduled_at(post, network), do: :scheduled, else: :draft
          "pending" -> :pending
          "published" -> :published
          _ -> :unknown
        end
    end
  end

  defp network_status_message(post, network, default_message) do
    case Enum.find(post.final_statuses, &(&1.network == network)) do
      %{outcome: %{"error" => %{"message" => message}}} when is_binary(message) -> message
      _ -> default_message
    end
  end

  defp get_network_post_url(post, network) do
    Crosspost.Posts.get_network_post_url(post, network)
  end

  defp get_network_avatar(%{workspace_connections: workspace_connections}, network) do
    case Enum.find(workspace_connections, &(&1.platform == Atom.to_string(network))) do
      nil ->
        nil

      connection ->
        case connection.platform do
          "linkedin" when is_map_key(connection.settings, "organization") ->
            connection.settings["organization"]["resolved_logo_urls"]["original"]

          _ ->
            connection.info["image"] || connection.info["avatar"]
        end
    end
  end
end
