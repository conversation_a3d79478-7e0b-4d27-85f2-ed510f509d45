<div class="max-w-6xl mx-auto pt-2 pb-2">
  <.header>
    <div class="mb-8 flex space-x-2 justify-center">
      <.link
        patch={~p"/posts?filter[status]=scheduled"}
        class={"px-3 py-1 text-sm rounded-full #{@filter=="scheduled" && "bg-blue-500 text-white" || "bg-gray-200 text-gray-700 hover:bg-gray-300" }"}
      >
        Scheduled
      </.link>
      <.link
        patch={~p"/posts?filter[status]=draft"}
        class={"px-3 py-1 text-sm rounded-full #{@filter=="draft" && "bg-blue-500 text-white" || "bg-gray-200 text-gray-700 hover:bg-gray-300" }"}
      >
        Drafts
      </.link>
      <.link
        patch={~p"/posts?filter[status]=published"}
        class={"px-3 py-1 text-sm rounded-full #{@filter=="published" && "bg-blue-500 text-white" || "bg-gray-200 text-gray-700 hover:bg-gray-300" }"}
      >
        Published
      </.link>
    </div>
  </.header>

  <%= if Enum.empty?(@posts) do %>
    <div class="bg-white shadow-md rounded-lg p-8 text-center">
      <h2 class="text-2xl font-semibold text-gray-800 mb-4">
        <%= if @filter == "published" do %>
          No published posts yet!
        <% else %>
          No posts yet!
        <% end %>
      </h2>
      <p class="text-gray-600 mb-6">
        <%= if @filter == "published" do %>
          You haven't published any posts yet. Start by creating a draft and then publish it when you're ready!
        <% else %>
          It looks like you haven't created any {@filter} posts.
          Why not start writing?
        <% end %>
      </p>
      <.link
        navigate={~p"/posts/new"}
        class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full transition duration-300 ease-in-out transform hover:scale-105"
      >
        Create a New Post
      </.link>
    </div>
  <% else %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-index="posts">
      <%= for post <- @posts do %>
        <CrosspostWeb.PostComponents.post_tile post={post} current_user={@current_user} />
      <% end %>
    </div>
  <% end %>
</div>
