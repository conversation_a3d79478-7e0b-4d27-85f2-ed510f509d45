defmodule CrosspostWeb.PostLive.PreviewComponent do
  use CrosspostWeb, :live_component

  alias CrosspostWeb.PostLive.Previews.CanonicalPreviewComponent
  alias CrosspostWeb.PostLive.Previews.BskyPreviewComponent
  alias CrosspostWeb.PostLive.Previews.XPreviewComponent
  alias CrosspostWeb.PostLive.Previews.MastodonPreviewComponent
  alias CrosspostWeb.PostLive.Previews.LinkedinPreviewComponent

  @impl true
  def render(assigns) do
    ~H"""
    <div class="h-full flex flex-col">
      <div class="flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 border-b border-gray-200">
        <div class="flex-1">
          <nav data-preview-tabs class="-mb-px flex space-x-1.5 sm:space-x-3" aria-label="Network previews">
            <button
              :for={network <- networks(@post)}
              type="button"
              phx-click="switch_preview"
              phx-value-network={network}
              phx-target={@myself}
              class={[
                "px-1.5 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm font-medium border-b-2 whitespace-nowrap",
                if(network == @network,
                  do: "border-blue-500 text-blue-600",
                  else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )
              ]}
            >
              {network_label(network)}
            </button>
          </nav>
        </div>
        <%= if @show_close_button do %>
          <button
            type="button"
            class="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 ml-4"
            phx-click="toggle_preview"
            phx-target="#post-editor"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        <% end %>
      </div>

      <div class="flex-1 overflow-y-auto sm:p-6" class="previews">
        <div data-network={@network} data-component="preview">
          <.live_component
            module={preview_module(@network)}
            id={"#{@network}-preview-#{@post.id}"}
            network={@network}
            post={@post}
            current_user={@current_user}
          />
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(socket) do
    {:ok, socket}
  end

  @impl true
  def update(%{post: post} = assigns, socket) do
    network = assigns[:network] || assigns[:preview_network] || default_network(post)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:network, network)
     |> assign_new(:show_close_button, fn -> false end)}
  end

  @impl true
  def handle_event("switch_preview", %{"network" => network}, socket) do
    {:noreply, assign(socket, :network, String.to_existing_atom(network))}
  end

  defp networks(post) do
    Enum.sort(post.social_networks)
  end

  defp default_network(post) do
    networks(post)
    |> List.first()
  end

  defp network_label(:bsky), do: "Bluesky"
  defp network_label(:x), do: "X (Twitter)"
  defp network_label(:mastodon), do: "Mastodon"
  defp network_label(:linkedin), do: "LinkedIn"

  defp preview_module(nil), do: CanonicalPreviewComponent
  defp preview_module(:bsky), do: BskyPreviewComponent
  defp preview_module(:x), do: XPreviewComponent
  defp preview_module(:mastodon), do: MastodonPreviewComponent
  defp preview_module(:linkedin), do: LinkedinPreviewComponent
end
