defmodule CrosspostWeb.PostLive.SidebarComponent do
  use CrosspostWeb, :live_component

  import CrosspostWeb.StatusBadgeComponent
  import CrosspostWeb.DatetimeHelpers

  alias Crosspost.Posts
  alias Crosspost.Accounts

  @impl true
  def render(assigns) do
    ~H"""
    <div id="sidebar" class="h-full flex flex-col">
      <div class="flex-none py-4">
        <div class="px-4 mb-4 flex items-center gap-2">
          <.link
            navigate={~p"/posts/new"}
            class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <.icon name="hero-plus-small" class="-ml-1 mr-2 h-5 w-5" /> New Post
          </.link>

          <.link
            navigate={~p"/settings"}
            class="inline-flex justify-center items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
            aria-label="Settings"
          >
            <.icon name="hero-cog-6-tooth" class="h-5 w-5" />
          </.link>

          <button
            :if={false}
            type="button"
            class="inline-flex justify-center items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
            phx-hook="SidebarToggle"
            id="toggle-sidebar-button"
            target={@myself}
            data-show-sidebar={
              Accounts.sidebar_hidden?(@current_user) |> if(do: "false", else: "true")
            }
          >
            <.icon name="hero-chevron-left" class="h-5 w-5" />
          </button>
        </div>
        <!-- Filters Section -->
        <div class="px-4 mb-6">
          <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Filters</h3>
          <nav class="space-y-1">
            <.filter_link
              filter="all"
              active={@selected_filter == "all"}
              icon="hero-inbox"
              count={length(@all_posts)}
              myself={@myself}
            >
              All Posts
            </.filter_link>

            <.filter_link
              filter="scheduled"
              active={@selected_filter == "scheduled"}
              icon="hero-clock"
              count={length(@scheduled_posts)}
              myself={@myself}
            >
              Scheduled
            </.filter_link>

            <.filter_link
              filter="draft"
              active={@selected_filter == "draft"}
              icon="hero-document"
              count={length(@draft_posts)}
              myself={@myself}
            >
              Drafts
            </.filter_link>

            <.filter_link
              filter="published"
              active={@selected_filter == "published"}
              icon="hero-check-circle"
              count={length(@published_posts)}
              myself={@myself}
            >
              Published
            </.filter_link>
          </nav>
        </div>
      </div>
      <!-- Posts List -->
      <div class="flex-1 flex flex-col px-4 pb-4 min-h-0 relative">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 sticky top-0 bg-white dark:bg-gray-800 py-2 flex-shrink-0">
          Posts
        </h3>
        <div class="absolute inset-x-4 top-10 bottom-4 overflow-y-auto">
          <nav class="space-y-1" id="posts-list">
            <div id="posts-container" phx-update="stream">
              <%= for {dom_id, post} <- @streams.posts do %>
                <.link
                  id={dom_id}
                  navigate={~p"/posts/#{post.id}"}
                  class={[
                    "group flex items-center px-3 py-3 text-sm font-medium rounded-md",
                    if(@current_post_id == post.id,
                      do: "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100",
                      else:
                        "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                    )
                  ]}
                >
                  <div class="flex flex-col flex-1 min-w-0 space-y-2">
                    <p
                      class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate px-2"
                      data-post={"post-#{post.id}"}
                      data-testid="sidebar-post"
                    >
                      {preview_post_content(post)}
                    </p>
                    <div class="flex items-center justify-between text-xs -pl-2 pt-2">
                      <.status_badge status={post.status} class="scale-90" />
                      <%= if time = get_post_time(post, @current_user.timezone) do %>
                        <p class="text-gray-500 dark:text-gray-400 ml-3">{time}</p>
                      <% end %>
                    </div>
                  </div>
                </.link>
              <% end %>
            </div>
          </nav>

          <%= if @has_more do %>
            <div class="mt-4 flex justify-center">
              <button
                phx-click="load_more"
                phx-target={@myself}
                phx-disable-with="Loading..."
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                data-testid="load-more-button"
              >
                Load more
              </button>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    current_user = assigns[:current_user] || socket.assigns[:current_user]
    current_workspace = assigns[:current_workspace] || socket.assigns[:current_workspace]
    selected_filter = assigns[:selected_filter] || "all"

    # Initialize page if not already set
    page = socket.assigns[:page] || 1
    per_page = 10

    # Reset page if filter changed
    reset_stream = socket.assigns[:selected_filter] != selected_filter
    page = if reset_stream, do: 1, else: page

    # Get posts with pagination
    {posts, has_more} =
      Posts.list_posts_for_sidebar(
        current_user,
        current_workspace,
        selected_filter,
        page,
        per_page
      )

    # Use PostOverview for counts to respect history limit
    alias Crosspost.Publishing.PostOverview

    # Get all posts for counts (respecting history limit)
    all_posts = PostOverview.get_all_for_counts(current_user, current_workspace)

    # Get scheduled posts (regardless of history limit)
    scheduled_posts = PostOverview.get_scheduled_for_counts(current_user, current_workspace)

    # Group posts by status
    grouped_posts = %{
      scheduled: Enum.filter(scheduled_posts, & &1.is_scheduled),
      draft: Enum.filter(all_posts, & &1.is_draft),
      published: Enum.filter(all_posts, & &1.is_published)
    }

    # Initialize or reset the stream
    socket =
      if !Map.has_key?(socket.assigns, :streams) ||
           !Map.has_key?(socket.assigns.streams, :posts) ||
           reset_stream do
        # For first load or filter change, reset the stream with all posts
        stream(socket, :posts, posts, reset: true)
      else
        # Otherwise, keep the existing stream
        socket
      end

    {:ok,
     socket
     |> assign(:current_user, current_user)
     |> assign(:current_workspace, current_workspace)
     |> assign(:current_post_id, assigns[:current_post_id])
     |> assign(:page, page)
     |> assign(:per_page, per_page)
     |> assign(:has_more, has_more)
     |> assign(:all_posts, all_posts)
     |> assign(:scheduled_posts, Map.get(grouped_posts, :scheduled, []))
     |> assign(:draft_posts, Map.get(grouped_posts, :draft, []))
     |> assign(:published_posts, Map.get(grouped_posts, :published, []))
     |> assign(:selected_filter, selected_filter)
     |> stream_posts(posts, reset_stream)}
  end

  @impl true
  def handle_event("filter_posts", %{"filter" => filter}, socket) do
    if socket.assigns.current_user && socket.assigns.current_workspace do
      # Reset to page 1 when changing filters
      {posts, has_more} =
        Posts.list_posts_for_sidebar(
          socket.assigns.current_user,
          socket.assigns.current_workspace,
          filter,
          1,
          socket.assigns.per_page
        )

      {:noreply,
       socket
       |> stream(:posts, posts, reset: true)
       |> assign(:selected_filter, filter)
       |> assign(:page, 1)
       |> assign(:has_more, has_more)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("load_more", _params, socket) do
    if socket.assigns.current_user && socket.assigns.current_workspace do
      # Increment page number
      next_page = socket.assigns.page + 1

      # Get next page of posts
      {new_posts, has_more} =
        Posts.list_posts_for_sidebar(
          socket.assigns.current_user,
          socket.assigns.current_workspace,
          socket.assigns.selected_filter,
          next_page,
          socket.assigns.per_page
        )

      # Stream in the new posts
      socket =
        Enum.reduce(new_posts, socket, fn post, acc ->
          stream_insert(acc, :posts, post, at: -1)
        end)

      {:noreply,
       socket
       |> assign(:page, next_page)
       |> assign(:has_more, has_more)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("toggle_sidebar", _params, socket) do
    current_user = socket.assigns.current_user
    new_state = !Accounts.sidebar_hidden?(current_user)

    case Accounts.set_sidebar_hidden(current_user, new_state) do
      {:ok, updated_user} ->
        {:noreply,
         socket
         |> assign(:current_user, updated_user)}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update settings.")}
    end
  end

  defp filter_link(assigns) do
    ~H"""
    <button
      phx-click="filter_posts"
      phx-value-filter={@filter}
      phx-target={@myself}
      class={[
        "flex items-center px-3 py-2 text-sm font-medium rounded-md w-full",
        if(@active,
          do: "bg-gray-100 text-gray-900",
          else: "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
        )
      ]}
    >
      <.icon name={@icon} class="mr-3 h-5 w-5 text-gray-400" />
      <span class="truncate">{render_slot(@inner_block)}</span>
      <span
        class="ml-auto bg-gray-100 text-gray-600 rounded-full px-2.5 py-0.5 text-xs"
        data-post-count={@filter}
      >
        {@count}
      </span>
    </button>
    """
  end

  # group_posts_by_status is no longer needed as we're using PostOverview

  defp preview_post_content(post) do
    preview_text =
      post.content
      |> Enum.sort_by(& &1.order)
      |> Enum.find_value("", fn content ->
        case {content.network, content.text} do
          {:canonical, text} when is_binary(text) -> text
          {_, text} when is_binary(text) -> text
          _ -> nil
        end
      end)
      |> String.trim()

    case preview_text do
      "" ->
        "No content"

      text ->
        if String.length(text) > 50 do
          String.slice(text, 0, 50) <> "..."
        else
          text
        end
    end
  end

  defp get_post_time(post, timezone) do
    case post do
      %{status: status} = post
      when status in ["scheduled", "pending"] and not is_nil(post.next_schedule) ->
        format_datetime(post.next_schedule, timezone)

      _ ->
        nil
    end
  end

  # Helper to stream posts to the component
  defp stream_posts(socket, posts, reset?) do
    # On first load or filter change, we've already set the stream in the update function
    if reset? || !Map.has_key?(socket.assigns, :page) || socket.assigns.page == 1 do
      # Already reset in the update function
      socket
    else
      # Only add new posts to the stream when loading more (page > 1)
      Enum.reduce(posts, socket, fn post, acc ->
        stream_insert(acc, :posts, post, at: -1)
      end)
    end
  end
end
