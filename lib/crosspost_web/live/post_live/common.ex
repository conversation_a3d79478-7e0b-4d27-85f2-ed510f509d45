defmodule CrosspostWeb.PostLive.Common do
  @moduledoc """
  Common functionality shared between post-related LiveViews.
  """
  require Logger

  alias Crosspost.Publishing

  def handle_publish(socket, id) do
    case Publishing.get_post(id, socket.assigns.current_user) do
      {:ok, post} ->
        # Schedule for 1 minute in the future
        scheduled_time = DateTime.utc_now() |> DateTime.add(1, :minute)

        case Publishing.update_post(post, %{
               scheduled_at: scheduled_time,
               status: "scheduled"
             }) do
          {:ok, updated_post} ->
            {:ok, updated_post}

          {:error, changeset} ->
            Logger.error("Failed to queue post for publishing: #{inspect(changeset)}")
            {:error, "Failed to queue post for publishing"}
        end

      {:error, :not_found} ->
        {:error, "Post not found"}
    end
  end

  def prepare_snappify_update(index, code) do
    {CrosspostWeb.PostLive.PostFormComponent,
     %{
       id: "post-form",
       action: :snappify_code,
       index: index,
       code: code
     }}
  end

  def format_content(blocks) when is_list(blocks) do
    blocks
    |> Enum.map(fn
      # Handle Ecto struct (PostContent)
      %{__struct__: Crosspost.Publishing.PostContent} = content ->
        %{
          id: content.id,
          text: content.text,
          order: content.order,
          meta: content.meta,
          network: content.network,
          sync: content.sync,
          attachments: Enum.map(content.media, &format_attachment/1)
        }

      # Handle map with string keys
      block when is_map(block) ->
        %{
          id: block["id"],
          text: block["text"],
          order: block["order"],
          meta: block["meta"],
          network: block["network"],
          sync: block["sync"],
          attachments: Enum.map(block["attachments"] || [], &format_attachment/1)
        }
    end)
  end

  def format_content(%{__struct__: Crosspost.Publishing.PostContent} = content) do
    %{
      text: content.text,
      order: content.order,
      meta: content.meta,
      network: content.network,
      sync: content.sync,
      attachments:
        Enum.map(content.attachments || [], fn attachment ->
          %{
            id: attachment.id,
            filename: attachment.filename,
            content_type: attachment.content_type
          }
        end)
    }
  end

  def format_content(%{} = content) do
    %{
      text: content.text || "",
      order: content.order || 0,
      meta: content.meta || %{},
      network: content.network || "canonical",
      sync: content.sync || false,
      attachments:
        Enum.map(content.attachments || [], fn attachment ->
          %{
            id: attachment.id,
            filename: attachment.filename,
            content_type: attachment.content_type
          }
        end)
    }
  end

  def format_content(content), do: content

  def format_attachment(%{__struct__: _} = attachment) do
    %{
      id: attachment.id,
      type: attachment.type,
      filename: attachment.filename,
      content_type: attachment.content_type,
      source_url: attachment.source_url,
      preview_url: attachment.preview_url,
      metadata: attachment.metadata
    }
  end

  def format_attachment(attachment) when is_map(attachment) do
    %{
      id: attachment["id"],
      type: attachment["type"],
      filename: attachment["filename"],
      content_type: attachment["content_type"],
      source_url: attachment["source_url"],
      preview_url: attachment["preview_url"],
      order: attachment["order"]
    }
  end

  def format_schedules(schedules, timezone) when is_list(schedules) do
    Enum.map(schedules, fn schedule ->
      %{
        network: schedule["network"],
        scheduled_at: schedule["scheduled_at"],
        timezone: timezone
      }
    end)
  end

  def convert_local_to_utc("", _timezone), do: nil
  def convert_local_to_utc(nil, _timezone), do: nil

  def convert_local_to_utc(local_datetime, timezone) when is_binary(timezone) do
    require Logger

    # Add seconds to the datetime string since datetime-local doesn't include them
    local_datetime_with_seconds = local_datetime <> ":00"

    Logger.debug("""
    Converting datetime:
    Input: #{inspect(local_datetime)}
    With seconds: #{inspect(local_datetime_with_seconds)}
    Timezone: #{inspect(timezone)}
    """)

    with {:ok, naive_dt} <- NaiveDateTime.from_iso8601(local_datetime_with_seconds),
         {:ok, local_dt} <- DateTime.from_naive(naive_dt, timezone),
         {:ok, utc_dt} <- DateTime.shift_zone(local_dt, "Etc/UTC") do
      Logger.debug("Conversion successful: #{inspect(utc_dt)}")
      utc_dt
    else
      error ->
        Logger.error("Conversion failed: #{inspect(error)}")
        nil
    end
  end

  # Add a catch-all clause for when timezone is nil or invalid
  def convert_local_to_utc(_local_datetime, _timezone), do: nil

  def format_datetime(datetime, timezone) when not is_nil(datetime) do
    datetime
    |> DateTime.shift_zone!(timezone)
    |> Calendar.strftime("%B %d, %Y at %I:%M %p")
  end

  def format_datetime(nil, _timezone), do: nil
end
