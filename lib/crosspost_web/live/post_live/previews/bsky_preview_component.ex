defmodule CrosspostWeb.PostLive.Previews.BskyPreviewComponent do
  use CrosspostWeb.PostLive.Previews.PostPreview, network: :bsky

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-900 font-inter space-y-4" data-preview-content="bsky">
      <%= if content_list = filter_content(@post.content, "bsky") do %>
        <%= for {content, index} <- Enum.with_index(Enum.sort_by(content_list, & &1.order)) do %>
          <div class="flex items-start gap-3 relative" data-post-item data-post-order={content.order}>
            <div class="flex flex-col items-center h-full z-10">
              <div class="p-1 bg-white dark:bg-gray-900" data-user-avatar>
                <%= if has_avatar?(@current_user) do %>
                  <div class="w-[40px] h-[40px] rounded-full overflow-hidden ring-1 ring-[#4A6179] ring-opacity-60">
                    <img
                      src={@connection.info["avatar"]}
                      alt="User profile"
                      class="w-full h-full object-cover"
                      draggable="false"
                      data-user-image
                    />
                  </div>
                <% else %>
                  <div class="w-[40px] h-[40px] rounded-full bg-[#1E2936] flex items-center justify-center ring-1 ring-[#4A6179] ring-opacity-60">
                    <svg class="h-5 w-5 text-[#788EA5]" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                <% end %>
              </div>
            </div>

            <div
              :if={index < Enum.count(content_list) - 1}
              class="absolute left-[23px] top-[42px] bottom-0 w-0.5 bg-gray-200"
              aria-hidden="true"
              data-thread-line
            >
            </div>

            <div class="flex-1 min-w-0 pb-3">
              <div class="flex items-center gap-1 pb-0.5 z-10" data-user-info>
                <div class="flex-shrink-1">
                  <div class="flex items-center gap-1 text-[13.125px] leading-[13.125px]">
                    <span
                      :if={String.length(@connection.info["display_name"]) > 0}
                      class="text-[15px] leading-5 font-semibold"
                      data-user-name
                    >
                      {@connection.info["display_name"]}
                    </span>
                    <span class="text-[15px] leading-5" data-user-handle>
                      @{@connection.info["handle"]}
                    </span>
                  </div>
                </div>
                <span class="text-[15px] leading-[15px]">·</span>
                <span class="text-[15px] leading-5" data-post-time>1d</span>
              </div>

              <div class="mt-1 leading-[1.2]">
                {render_content(content.text || "", content)}
              </div>

              <%= if attachments = content.content_attachments || [] do %>
                <%= if length(attachments) > 0 do %>
                  <.attachments_preview
                    attachments={attachments}
                    network="bsky"
                    hover_class="hover:bg-[#1E2936]"
                  />
                <% end %>
              <% end %>

              <div class="flex items-center justify-between mt-3 -ml-1.5" data-post-actions>
                <div class="flex-1 flex items-start">
                  <button
                    type="button"
                    class="flex items-center gap-1 p-1.5 rounded-full hover:bg-[#1E2936] transition-colors"
                    data-action="reply"
                  >
                    <svg fill="none" width="18" viewBox="0 0 24 24" height="18" class="text-[#788EA5]">
                      <path
                        fill="currentColor"
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M2.002 6a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H12.28l-4.762 2.858A1 1 0 0 1 6.002 21v-2h-1a3 3 0 0 1-3-3V6Zm3-1a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h2a1 1 0 0 1 1 1v1.234l3.486-2.092a1 1 0 0 1 .514-.142h7a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-14Z"
                      />
                    </svg>
                    <span class="text-sm text-[#788EA5] select-none" data-reply-count>0</span>
                  </button>
                </div>

                <div class="flex-1 flex items-start">
                  <button
                    type="button"
                    class="p-1.5 rounded-full hover:bg-[#1E2936] transition-colors"
                    data-action="repost"
                  >
                    <svg fill="none" width="18" viewBox="0 0 24 24" height="18" class="text-[#788EA5]">
                      <path
                        fill="currentColor"
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M17.957 2.293a1 1 0 1 0-1.414 1.414L17.836 5H6a3 3 0 0 0-3 3v3a1 1 0 1 0 2 0V8a1 1 0 0 1 1-1h11.836l-1.293 1.293a1 1 0 0 0 1.414 1.414l2.47-2.47a1.75 1.75 0 0 0 0-2.474l-2.47-2.47ZM20 12a1 1 0 0 1 1 1v3a3 3 0 0 1-3 3H6.164l1.293 1.293a1 1 0 1 1-1.414 1.414l-2.47-2.47a1.75 1.75 0 0 1 0-2.474l2.47-2.47a1 1 0 0 1 1.414 1.414L6.164 17H18a1 1 0 0 0 1-1v-3a1 1 0 0 1 1-1Z"
                      />
                    </svg>
                  </button>
                </div>

                <div class="flex-1 flex items-start">
                  <button
                    type="button"
                    class="p-1.5 rounded-full hover:bg-[#1E2936] transition-colors"
                    data-action="like"
                  >
                    <div class="relative">
                      <svg
                        fill="none"
                        width="18"
                        viewBox="0 0 24 24"
                        height="18"
                        class="text-[#788EA5]"
                      >
                        <path
                          fill="currentColor"
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.734 5.091c-1.238-.276-2.708.047-4.022 1.38a1 1 0 0 1-1.424 0C9.974 5.137 8.504 4.814 7.266 5.09c-1.263.282-2.379 1.206-2.92 2.556C3.33 10.18 4.252 14.84 12 19.348c7.747-4.508 8.67-9.168 7.654-11.7-.541-1.351-1.657-2.275-2.92-2.557Zm4.777 1.812c1.604 4-.494 9.69-9.022 14.47a1 1 0 0 1-.978 0C2.983 16.592.885 10.902 2.49 6.902c.779-1.942 2.414-3.334 4.342-3.764 1.697-.378 3.552.003 5.169 1.286 1.617-1.283 3.472-1.664 5.17-1.286 1.927.43 3.562 1.822 4.34 3.764Z"
                        />
                      </svg>
                    </div>
                  </button>
                </div>

                <div class="flex-1 flex items-start">
                  <button
                    type="button"
                    class="p-1.5 rounded-full hover:bg-[#1E2936] transition-colors"
                    data-action="more"
                  >
                    <svg fill="none" viewBox="0 0 24 24" width="20" height="20" class="text-[#788EA5]">
                      <path
                        fill="currentColor"
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M2 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm16 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm-6-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  defp filter_content(content, _network) do
    network_content = Enum.filter(content, &(&1.network == :bsky))

    if Enum.empty?(network_content) do
      Enum.filter(content, &(&1.network == :canonical))
    else
      network_content
    end
  end

  defp has_avatar?(user) do
    case get_network_avatar(user, :bsky) do
      nil -> false
      _ -> true
    end
  end

  defp render_content(text, content) do
    assigns = %{
      text: text,
      mentions: content.meta["mentions"] || [],
      network: "bsky"
    }

    ~H"""
    <.content_text text={@text} mentions={@mentions} network={@network} />
    """
  end
end
