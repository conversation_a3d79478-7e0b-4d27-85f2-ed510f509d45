defmodule CrosspostWeb.PostLive.Previews.LinkedinPreviewComponent do
  use CrosspostWeb.PostLive.Previews.PostPreview, network: :linkedin

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-4">
      <%= if content_list = filter_content(@post.content, "linkedin") do %>
        <%= for {content, index} <- Enum.with_index(Enum.sort_by(content_list, & &1.order)) do %>
          <div class="relative">
            <div class="pb-4">
              <div class="flex items-start justify-between">
                <div class="flex items-start flex-grow">
                  <div class="flex-shrink-0 z-10 p-1 bg-white dark:bg-gray-900">
                    <%= if @connection.info["image"] do %>
                      <img
                        src={@connection.info["image"]}
                        alt=""
                        class="w-10 h-10 rounded-full object-cover"
                      />
                    <% else %>
                      <div class="w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center">
                        <svg class="h-6 w-6 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                      </div>
                    <% end %>
                  </div>

                  <div class="ml-3 flex-grow">
                    <div class="flex items-center text-sm">
                      <span class="font-bold text-gray-900 dark:text-gray-100">
                        {@connection.info["name"]}
                      </span>
                      <span class="text-gray-500 dark:text-gray-400 ml-1">• You</span>
                    </div>
                    <div class="text-gray-500 dark:text-gray-400 text-sm">
                      {@connection.info["description"]}
                    </div>
                    <div class="text-gray-500 dark:text-gray-400 text-xs flex items-center mt-1">
                      <span>1d</span>
                      <span class="mx-1">•</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 16 16"
                        class="w-3 h-3"
                        fill="currentColor"
                      >
                        <path d="M8 1a7 7 0 107 7 7 7 0 00-7-7zM3 8a5 5 0 011-3l.55.55A1.5 1.5 0 015 6.62v1.07a.75.75 0 00.22.53l.56.56a.75.75 0 00.53.22H7v.69a.75.75 0 00.22.53l.56.56a.75.75 0 01.22.53V13a5 5 0 01-5-5zm6.24 4.83l2-2.46a.75.75 0 00.09-.8l-.58-1.16A.76.76 0 0010 8H7v-.19a.51.51 0 01.28-.45l.38-.19a.74.74 0 01.68 0L9 7.5l.38-.7a1 1 0 00.12-.48v-.85a.78.78 0 01.21-.53l1.07-1.09a5 5 0 01-1.54 9z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <%= if content.order > 0 && index < length(content_list) - 1 do %>
                <div
                  class="absolute left-[25px] top-[52px] bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"
                  aria-hidden="true"
                >
                </div>
              <% end %>

              <div class={[
                "mt-4 text-gray-900 dark:text-gray-100",
                if(content.order > 0, do: "ml-[58px]")
              ]}>
                {render_content(content)}

                <%= if attachments = content.content_attachments || [] do %>
                  <%= if length(attachments) > 0 do %>
                    <.attachments_preview
                      attachments={attachments}
                      network="linkedin"
                      hover_class="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    />
                  <% end %>
                <% end %>
              </div>

              <div class="mt-4 flex items-center justify-between text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-3">
                <button class="flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19.46 11l-3.91-3.91a7 7 0 01-1.69-2.74l-.49-1.47A2.76 2.76 0 0010.76 1 2.75 2.75 0 008 3.74v1.12a9.19 9.19 0 00.46 2.85L8.89 9H4.12A2.12 2.12 0 002 11.12a2.16 2.16 0 00.92 1.76A2.11 2.11 0 002 14.62a2.14 2.14 0 001.28 2 2 2 0 00-.28 1 2.12 2.12 0 002 2.12v.14A2.12 2.12 0 007.12 22h7.49a8.08 8.08 0 003.58-.84l.31-.16H21V11zM19 19h-1l-.73.37a6.14 6.14 0 01-2.69.63H7.72a1 1 0 01-1-.72l-.25-.87-.85-.41A1 1 0 015 17l.17-1-.76-.74A1 1 0 014.27 14l.66-1.09-.73-1.1a.49.49 0 01.08-.7.48.48 0 01.34-.11h7.05l-1.31-3.92A7 7 0 0110 4.86V3.75a.77.77 0 01.75-.75.75.75 0 01.71.51L12 5a9 9 0 002.13 3.5l4.5 4.5H19z" />
                  </svg>
                  <span>Like</span>
                </button>

                <button class="flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 9h10v1H7zm0 4h7v-1H7zm16-2a6.78 6.78 0 01-2.84 5.61L12 22v-4H8A7 7 0 018 4h8a7 7 0 017 7zm-2 0a5 5 0 00-5-5H8a5 5 0 000 10h6v2.28L19 15a4.79 4.79 0 002-4z" />
                  </svg>
                  <span>Comment</span>
                </button>

                <button class="flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13.96 5H6c-.55 0-1 .45-1 1v10H3V6c0-1.66 1.34-3 3-3h7.96L12 0h2.37L17 4l-2.63 4H12l1.96-3zm5.54 3H19v10c0 .55-.45 1-1 1h-7.96L12 16H9.63L7 20l2.63 4H12l-1.96-3H18c1.66 0 3-1.34 3-3V8h-1.5z" />
                  </svg>
                  <span>Repost</span>
                </button>

                <button class="flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21 3L0 10l7.66 4.26L16 8l-6.26 8.34L14 24l7-21z" />
                  </svg>
                  <span>Send</span>
                </button>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  defp filter_content(content, _network),
    do:
      Enum.filter(content, &(&1.network == :linkedin))
      |> Enum.sort_by(& &1.order)

  defp render_content(content) do
    assigns = %{
      text: content.text || "",
      mentions: content.meta["mentions"] || [],
      network: "linkedin"
    }

    ~H"""
    <.content_text text={@text} mentions={@mentions} network={@network} />
    """
  end
end
