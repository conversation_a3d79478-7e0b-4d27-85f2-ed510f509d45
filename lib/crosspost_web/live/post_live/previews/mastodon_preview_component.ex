defmodule CrosspostWeb.PostLive.Previews.MastodonPreviewComponent do
  use CrosspostWeb.PostLive.Previews.PostPreview, network: :mastodon

  def render(assigns) do
    ~H"""
    <div class="mastodon-preview space-y-4 border border-gray-200 dark:border-gray-700 rounded p-4">
      <%= if content_list = filter_content(@post.content, "mastodon") do %>
        <%= for {content, index} <- Enum.with_index(Enum.sort_by(content_list, & &1.order)) do %>
          <%= if content.order == 0 do %>
            <div class="status status-public">
              <div class="status__info flex items-center">
                <div class="flex items-center">
                  <div class="w-12 h-12 overflow-hidden p-1 bg-white dark:bg-gray-900">
                    <%= if @connection.info["image"] do %>
                      <img
                        src={@connection.info["image"]}
                        alt=""
                        class="w-full h-full object-cover rounded-full"
                      />
                    <% else %>
                      <div class="w-full h-full rounded-full bg-gray-600 flex items-center justify-center">
                        <svg class="h-6 w-6 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                      </div>
                    <% end %>
                  </div>
                  <div class="ml-3">
                    <div class="font-bold text-gray-900 dark:text-gray-100">
                      {@connection.info["name"]}
                    </div>
                    <div class="text-gray-500 dark:text-gray-400 text-sm">
                      @{@connection.info["nickname"]}
                    </div>
                  </div>
                </div>
              </div>

              <div class="status__content mt-4 text-gray-900 dark:text-gray-100">
                {render_content(content)}
              </div>

              <%= if content.content_attachments && length(content.content_attachments) > 0 do %>
                <div class="media-gallery mt-4">
                  <.attachments_preview
                    attachments={content.content_attachments}
                    network="mastodon"
                    hover_class="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  />
                </div>
              <% end %>

              <div class="status__meta mt-4 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <div class="flex items-center space-x-2 border-b border-gray-200 dark:border-gray-700 py-2">
                  <span>Dec 31, 2024, 01:59 PM</span>
                  <span>·</span>
                  <svg
                    height="18"
                    viewBox="0 -960 960 960"
                    width="18"
                    class="icon icon-globe text-gray-500 dark:text-gray-400"
                  >
                    <path
                      fill="currentColor"
                      d="M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm-40-82v-78q-33 0-56.5-23.5T360-320v-40L168-552q-3 18-5.5 36t-2.5 36q0 121 79.5 212T440-162Zm276-102q20-22 36-47.5t26.5-53q10.5-27.5 16-56.5t5.5-59q0-98-54.5-179T600-776v16q0 33-23.5 56.5T520-680h-80v80q0 17-11.5 28.5T400-560h-80v80h240q17 0 28.5 11.5T600-440v120h40q26 0 47 15.5t29 40.5Z"
                    />
                  </svg>
                </div>
                <div class="flex items-center space-x-2 pt-2 mt-2 px-2">
                  <a href="#" class="hover:underline">
                    <span>0</span>
                    <span>boosts</span>
                  </a>
                  <span>·</span>
                  <a href="#" class="hover:underline">
                    <span>0</span>
                    <span>favorites</span>
                  </a>
                </div>
              </div>

              <.engagement_buttons content={content} is_reply={false} />
            </div>
          <% else %>
            <div class="status-reply grid grid-cols-[48px,1fr] gap-3">
              <div class="relative flex justify-center">
                <%= if index < Enum.count(content_list) - 1 do %>
                  <div class="absolute inset-y-0 w-0.5 bg-gray-200 dark:bg-gray-700 -top-4" />
                <% end %>
                <div class="relative w-12 h-12 overflow-hidden p-1 bg-white dark:bg-gray-900 z-10">
                  <%= if @connection.info["image"] do %>
                    <img
                      src={@connection.info["image"]}
                      alt=""
                      class="w-full h-full object-cover rounded-full"
                    />
                  <% else %>
                    <div class="w-full h-full rounded-full bg-gray-600 flex items-center justify-center">
                      <svg class="h-6 w-6 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                  <% end %>
                </div>
              </div>

              <div>
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-bold text-gray-900 dark:text-gray-100">
                      {@connection.info["display_name"]}
                    </div>
                    <div class="text-gray-500 dark:text-gray-400 text-sm">
                      @{@connection.info["nickname"]}
                    </div>
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-2">
                    <span>Dec 31, 2024, 01:59 PM</span>
                    <span>·</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      height="18"
                      viewBox="0 -960 960 960"
                      width="18"
                      class="icon icon-globe text-gray-500 dark:text-gray-400"
                    >
                      <path
                        fill="currentColor"
                        d="M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm-40-82v-78q-33 0-56.5-23.5T360-320v-40L168-552q-3 18-5.5 36t-2.5 36q0 121 79.5 212T440-162Zm276-102q20-22 36-47.5t26.5-53q10.5-27.5 16-56.5t5.5-59q0-98-54.5-179T600-776v16q0 33-23.5 56.5T520-680h-80v80q0 17-11.5 28.5T400-560h-80v80h240q17 0 28.5 11.5T600-440v120h40q26 0 47 15.5t29 40.5Z"
                      />
                    </svg>
                  </div>
                </div>

                <div class="status__content mt-2 text-gray-900 dark:text-gray-100">
                  {render_content(content)}
                </div>

                <%= if content.content_attachments && length(content.content_attachments) > 0 do %>
                  <div class="media-gallery mt-4">
                    <.attachments_preview
                      attachments={content.content_attachments}
                      network="mastodon"
                      hover_class="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    />
                  </div>
                <% end %>

                <.engagement_buttons content={content} is_reply={true} />
              </div>
            </div>
          <% end %>
        <% end %>
      <% end %>
    </div>
    """
  end

  defp engagement_buttons(assigns) do
    ~H"""
    <div class={[
      "status__action-bar mt-4 flex items-center justify-between text-gray-500 dark:text-gray-400",
      if(!@is_reply, do: "border-t border-b border-gray-200 dark:border-gray-700 py-2 -mx-4 px-4")
    ]}>
      <button type="button" class="icon-button hover:text-blue-500 p-2" title="Reply">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 -960 960 960"
          width="24"
          class="icon icon-reply"
        >
          <path
            fill="currentColor"
            d="M760-200v-160q0-50-35-85t-85-35H273l144 144-57 56-240-240 240-240 57 56-144 144h367q83 0 141.5 58.5T840-360v160h-80Z"
          />
        </svg>
      </button>
      <button type="button" class="icon-button hover:text-green-500 p-2" title="Boost">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 -960 960 960"
          width="24"
          class="icon icon-retweet"
        >
          <path
            fill="currentColor"
            d="M280-80 120-240l160-160 56 58-62 62h406v-160h80v240H274l62 62-56 58Zm-80-440v-240h486l-62-62 56-58 160 160-160 160-56-58 62-62H280v160h-80Z"
          />
        </svg>
      </button>
      <button type="button" class="icon-button hover:text-yellow-500 p-2" title="Favorite">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 -960 960 960"
          width="24"
          class="icon icon-star"
        >
          <path
            fill="currentColor"
            d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143ZM233-120l65-281L80-590l288-25 112-265 112 265 288 25-218 189 65 281-247-149-247 149Zm247-350Z"
          />
        </svg>
      </button>
      <button type="button" class="icon-button hover:text-blue-500 p-2" title="Bookmark">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 -960 960 960"
          width="24"
          class="icon icon-bookmark"
        >
          <path
            fill="currentColor"
            d="M200-120v-640q0-33 23.5-56.5T280-840h400q33 0 56.5 23.5T760-760v640L480-240 200-120Zm80-122 200-86 200 86v-518H280v518Zm0-518h400-400Z"
          />
        </svg>
      </button>
      <button type="button" class="icon-button hover:text-blue-500 p-2" title="More">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 -960 960 960"
          width="24"
          class="icon icon-ellipsis-h"
        >
          <path
            fill="currentColor"
            d="M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z"
          />
        </svg>
      </button>
    </div>
    """
  end

  defp filter_content(content, _network),
    do:
      Enum.filter(content, &(&1.network == :mastodon))
      |> Enum.sort_by(& &1.order)

  defp render_content(content) do
    assigns = %{
      text: content.text || "",
      mentions: content.meta["mentions"] || [],
      network: "mastodon"
    }

    ~H"""
    <.content_text text={@text} mentions={@mentions} network={@network} />
    """
  end
end
