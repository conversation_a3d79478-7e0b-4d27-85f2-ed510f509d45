defmodule CrosspostWeb.PostLive.Previews.PostPreview do
  defmacro __using__(opts) do
    quote do
      use CrosspostWeb, :live_component

      import CrosspostWeb.PostLive.Previews.CoreComponents

      @impl true
      def update(assigns, socket) do
        current_user = assigns.current_user
        network = assigns.network

        connection = get_connection(current_user.workspace_connections, unquote(opts[:network]))

        {:ok,
         socket
         |> assign(assigns)
         |> assign(:connection, connection)}
      end

      def get_connection(connections, network) do
        Enum.find(connections, fn connection -> connection.platform == Atom.to_string(network) end)
      end
    end
  end
end
