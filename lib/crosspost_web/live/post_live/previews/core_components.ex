defmodule CrosspostWeb.PostLive.Previews.CoreComponents do
  use Phoenix.Component

  import Phoenix.HTML, only: [raw: 1]

  attr :attachment, :any, required: true

  def cover_image_preview(assigns) do
    ~H"""
    <div class="relative">
      <img
        src={@attachment.preview_url}
        alt={@attachment.metadata["alt"]}
        title={@attachment.filename}
        class="w-full h-full object-cover"
      />
    </div>
    """
  end

  attr :text, :string, required: true
  attr :mentions, :list, default: []
  attr :network, :string, required: true

  def content_text(assigns) do
    ~H"""
    <p data-post-content class="whitespace-pre-wrap text-[15px]">{raw(@text)}</p>
    """
  end

  attr :attachment, :any, required: true
  attr :idx, :integer, default: 0

  def video_preview(assigns) do
    ~H"""
    <div class="relative">
      <img
        src={@attachment.preview_url || @attachment.source_url}
        alt={@attachment.filename}
        class={[
          "rounded object-cover w-full",
          if(@idx == 0, do: "max-h-[500px]", else: "max-h-[200px]")
        ]}
      />
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="w-12 h-12 rounded-full bg-black/50 flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
      </div>
    </div>
    """
  end

  attr :attachment, :any, required: true
  attr :hover_class, :string, default: "hover:bg-gray-50 dark:hover:bg-gray-800/50"

  def link_preview(assigns) do
    ~H"""
    <div class="mt-2 border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
      <a
        href={@attachment.metadata["url"]}
        target="_blank"
        rel="noopener noreferrer"
        class={"flex items-start #{@hover_class} transition-colors"}
      >
        <%= if image_url = @attachment.metadata["image_url"] do %>
          <div class="flex-shrink-0">
            <img src={image_url} class="w-32 h-32 object-cover" alt={@attachment.metadata["title"]} />
          </div>
        <% end %>
        <div class="flex-1 p-3 min-w-0">
          <h3 class="font-medium text-gray-900 dark:text-gray-100 truncate">
            {@attachment.metadata["title"]}
          </h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
            {@attachment.metadata["description"]}
          </p>
          <p class="mt-2 text-xs text-gray-400 dark:text-gray-500">
            {URI.parse(@attachment.metadata["url"]).host}
          </p>
        </div>
      </a>
    </div>
    """
  end

  attr :attachments, :list, required: true
  attr :network, :string, required: true
  attr :hover_class, :string, default: "hover:bg-gray-50 dark:hover:bg-gray-800/50"

  def attachments_preview(assigns) do
    assigns = assign(assigns, :has_links?, Enum.any?(assigns.attachments, &(&1.type == :link)))

    assigns =
      assign(assigns, :media_attachments, Enum.filter(assigns.attachments, &(&1.type != :link)))

    ~H"""
    <div class="mt-3" data-attachments>
      <%= if @has_links? do %>
        <%= for attachment <- Enum.filter(@attachments, &(&1.type == :link)) do %>
          <.link_preview attachment={attachment} hover_class={@hover_class} />
        <% end %>
      <% end %>

      <%= if length(@media_attachments) > 0 do %>
        <.image_grid attachments={@media_attachments} network={@network} />
      <% end %>
    </div>
    """
  end

  # New components for image grid layouts
  attr :attachments, :list, required: true
  attr :network, :string, required: true

  def image_grid(assigns) do
    attachments = Enum.sort_by(assigns.attachments, & &1.order)
    count = length(attachments)

    radius_class =
      case assigns.network do
        # 12px
        "bsky" -> "rounded-xl"
        # 8px
        "mastodon" -> "rounded-lg"
        # 16px
        "twitter" -> "rounded-2xl"
        _ -> "rounded-xl"
      end

    assigns = assign(assigns, :attachments, attachments)
    assigns = assign(assigns, :count, count)
    assigns = assign(assigns, :radius_class, radius_class)

    ~H"""
    <div class={"mt-3 #{@radius_class} overflow-hidden w-full"}>
      <%= case @count do %>
        <% 1 -> %>
          <.single_image_layout attachment={List.first(@attachments)} />
        <% 2 -> %>
          <.two_images_layout attachments={@attachments} />
        <% 3 -> %>
          <.three_images_layout attachments={@attachments} />
        <% 4 -> %>
          <.four_images_layout attachments={@attachments} />
        <% _ -> %>
          <.single_image_layout attachment={List.first(@attachments)} />
      <% end %>
    </div>
    """
  end

  attr :attachment, :any, required: true

  def single_image_layout(assigns) do
    ~H"""
    <div class="relative w-full h-full">
      <%= if is_video?(@attachment) do %>
        <.video_preview attachment={@attachment} idx={0} />
      <% else %>
        <div class="aspect-[16/9] w-full h-full">
          <img
            src={@attachment.source_url}
            alt={@attachment.metadata["alt"] || @attachment.filename}
            class="w-full h-full object-cover"
          />
        </div>
      <% end %>
    </div>
    """
  end

  attr :attachments, :list, required: true

  def two_images_layout(assigns) do
    ~H"""
    <div class="grid grid-cols-2 gap-0.5 aspect-[16/9] w-full">
      <%= for {attachment, idx} <- Enum.with_index(@attachments) do %>
        <div class="relative w-full h-full bg-gray-100 dark:bg-gray-800">
          <%= if is_video?(attachment) do %>
            <.video_preview attachment={attachment} idx={idx} />
          <% else %>
            <img
              src={attachment.source_url}
              alt={attachment.metadata["alt"] || attachment.filename}
              class="w-full h-full object-cover"
            />
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  attr :attachments, :list, required: true

  def three_images_layout(assigns) do
    [main | rest] = assigns.attachments
    assigns = assign(assigns, :main, main)
    assigns = assign(assigns, :rest, rest)

    ~H"""
    <div class="relative aspect-[16/9] w-full">
      <div class="absolute inset-0 flex gap-0.5">
        <div class="w-1/2 h-full bg-gray-100 dark:bg-gray-800">
          <%= if is_video?(@main) do %>
            <.video_preview attachment={@main} idx={0} />
          <% else %>
            <img
              src={@main.source_url}
              alt={@main.metadata["alt"] || @main.filename}
              class="w-full h-full object-cover"
            />
          <% end %>
        </div>
        <div class="w-1/2 grid grid-rows-2 gap-0.5">
          <%= for {attachment, idx} <- Enum.with_index(@rest) do %>
            <div class="h-full bg-gray-100 dark:bg-gray-800">
              <%= if is_video?(attachment) do %>
                <.video_preview attachment={attachment} idx={idx + 1} />
              <% else %>
                <img
                  src={attachment.source_url}
                  alt={attachment.metadata["alt"] || attachment.filename}
                  class="w-full h-full object-cover"
                />
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  attr :attachments, :list, required: true

  def four_images_layout(assigns) do
    ~H"""
    <div class="grid grid-cols-2 grid-rows-2 gap-0.5 aspect-[16/9] w-full">
      <%= for {attachment, idx} <- Enum.with_index(@attachments) do %>
        <div class="relative w-full h-full bg-gray-100 dark:bg-gray-800">
          <%= if is_video?(attachment) do %>
            <.video_preview attachment={attachment} idx={idx} />
          <% else %>
            <img
              src={attachment.source_url}
              alt={attachment.metadata["alt"] || attachment.filename}
              class="w-full h-full object-cover"
            />
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  defp is_video?(attachment) do
    attachment.type == :video
  end

  # Add this helper function to be used by all preview components
  def get_network_avatar(user, network) do
    case Enum.find(user.workspace_connections, &(&1.platform == Atom.to_string(network))) do
      nil ->
        nil

      connection ->
        case connection.platform do
          "linkedin" when is_map_key(connection.settings, "organization") ->
            connection.settings["organization"]["resolved_logo_urls"]["original"]

          _ ->
            connection.info["image"] || connection.info["avatar"]
        end
    end
  end
end
