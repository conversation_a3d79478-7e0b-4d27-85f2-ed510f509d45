defmodule CrosspostWeb.PostLive.ScheduleInfoComponent do
  use CrosspostWeb, :live_component

  import CrosspostWeb.PostComponents, only: [network_icon: 1]

  @impl true
  def render(assigns) do
    ~H"""
    <div id="schedule-info">
      <%= if @post.schedules && length(@post.schedules) > 0 do %>
        <div class="grid grid-cols-2 mb-4 gap-2">
          <%= for schedule <- sort_schedules(@post.schedules) do %>
            <div class="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 px-3 py-1.5 rounded-md">
              <.network_icon network={schedule.network} status={:scheduled} />
              <span class="truncate">{format_datetime(schedule.scheduled_at, @timezone)}</span>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{post: _post} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:timezone, assigns.current_user.timezone)}
  end

  defp format_datetime(datetime, timezone) do
    CrosspostWeb.DatetimeHelpers.format_datetime(datetime, timezone)
  end

  defp sort_schedules(schedules) do
    Enum.sort_by(schedules, fn schedule ->
      {DateTime.to_unix(schedule.scheduled_at), schedule.network}
    end)
  end
end
