defmodule CrosspostWeb.UserRegistrationLive.OAuth do
  use CrosspostWeb, :live_view

  require Logger

  alias Crosspost.Accounts
  alias Crosspost.Accounts.User

  def mount(_params, session, socket) do
    case session["pending_oauth_auth"] do
      nil ->
        {:ok,
         socket
         |> put_flash(:error, "No pending authentication found")
         |> redirect(to: ~p"/")}

      oauth_auth ->
        provider = oauth_auth.provider
        # Try both string and atom keys since the data structure can vary
        suggested_email =
          get_in(oauth_auth, [:info, :email]) || get_in(oauth_auth, [:info, "email"])

        Logger.info("Setting up OAuth registration form", %{
          event: "oauth_registration.form_setup",
          provider: provider,
          suggested_email: suggested_email
        })

        changeset = User.oauth_registration_changeset(%User{}, %{email: suggested_email})

        {:ok,
         socket
         |> assign(:oauth_auth, oauth_auth)
         |> assign(:provider, provider)
         |> assign(:suggested_email, suggested_email)
         |> assign(:form, to_form(changeset))
         |> assign(:trigger_submit, false)
         |> assign(:check_errors, false)}
    end
  end

  def handle_event("validate", %{"user" => user_params}, socket) do
    changeset =
      %User{}
      |> User.oauth_registration_changeset(user_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, form: to_form(changeset))}
  end

  def handle_event("save", %{"user" => user_params}, socket) do
    oauth_auth = socket.assigns.oauth_auth

    case Accounts.register_and_enroll_user(%{
           oauth_provider: oauth_auth.provider,
           uid: oauth_auth.uid,
           email: user_params["email"],
           info: oauth_auth.info,
           credentials: oauth_auth.credentials
         }) do
      {:ok, user} ->
        Logger.info("Successfully completed OAuth registration", %{
          event: "oauth_registration.complete",
          provider: oauth_auth.provider,
          user_id: user.id,
          email: user_params["email"]
        })

        {:noreply,
         socket
         |> put_flash(
           :info,
           "Successfully registered with #{String.capitalize(oauth_auth.provider)}."
         )
         |> assign(:trigger_submit, true)
         |> assign(:user, user)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Logger.warning("Failed to complete OAuth registration", %{
          event: "oauth_registration.error",
          provider: oauth_auth.provider,
          error: inspect(changeset)
        })

        {:noreply, assign(socket, form: to_form(changeset), check_errors: true)}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="relative min-h-screen">
      <!-- Background gradient -->
      <div
        class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        aria-hidden="true"
      >
        <div
          class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
        >
        </div>
      </div>

      <div class="relative px-6 lg:px-8">
        <div class="mx-auto max-w-5xl pt-20 sm:pt-24 lg:pt-32">
          <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Complete your registration
            </h1>
            <p class="mt-6 text-lg leading-8 text-gray-600">
              <%= if @suggested_email do %>
                Please confirm your email address to complete registration with {String.capitalize(
                  @provider
                )}.
              <% else %>
                Please provide your email address to complete registration with {String.capitalize(
                  @provider
                )}.
              <% end %>
            </p>
            <div class="mt-10">
              <div class="max-w-lg mx-auto bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
                <.simple_form
                  for={@form}
                  id="registration_form"
                  phx-submit="save"
                  phx-change="validate"
                  phx-trigger-action={@trigger_submit}
                  action={~p"/users/sign-in/oauth?_action=registered"}
                  method="post"
                  class="space-y-6 text-left"
                >
                  <.live_component module={CrosspostWeb.FormProtectionComponent} id="form-protection" />

                  <.error :if={@check_errors}>
                    Oops, something went wrong! Please check the errors below.
                  </.error>

                  <.input field={@form[:email]} type="email" label="Email" required />
                  <input type="hidden" name="provider" value={@oauth_auth.provider} />
                  <input type="hidden" name="uid" value={@oauth_auth.uid} />
                  <:actions>
                    <.button phx-disable-with="Creating account..." class="w-full">
                      Complete registration
                    </.button>
                  </:actions>
                </.simple_form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
