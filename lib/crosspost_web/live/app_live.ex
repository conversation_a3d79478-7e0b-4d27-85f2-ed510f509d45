defmodule CrosspostWeb.AppLive do
  use CrosspostWeb, :live_view

  alias Crosspost.Publishing
  alias Crosspost.Publishing.Post
  alias Crosspost.Posts
  alias Crosspost.Accounts
  alias CrosspostWeb.OnboardingComponent

  require Logger

  @impl true
  def mount(_params, _session, socket) do
    current_user = socket.assigns.current_user
    current_workspace = socket.assigns.current_workspace

    if connected?(socket) do
      Phoenix.PubSub.subscribe(Crosspost.PubSub, "posts:#{current_user.id}")
      Phoenix.PubSub.subscribe(Crosspost.PubSub, "attachments:#{current_user.id}")

      send(self(), :after_reconnect)
    end

    current_user =
      Accounts.get_user_with_workspace_connections(current_user, current_workspace.id)

    payment_past_due = current_user.status == :past_due

    billing_portal_url =
      if (payment_past_due and current_user.subscription) &&
           current_user.subscription.stripe_customer_id do
        return_url = CrosspostWeb.Endpoint.url() <> "/dashboard"

        case Crosspost.Billing.Stripe.create_billing_portal_session(
               current_user.subscription.stripe_customer_id,
               return_url
             ) do
          {:ok, %{url: url}} -> url
          _ -> nil
        end
      else
        nil
      end

    {:ok,
     socket
     |> assign(:current_user, current_user)
     |> assign(:current_post_id, nil)
     |> assign(:show_editor, false)
     |> assign(:show_onboarding, !current_user.settings.onboarding_completed)
     |> assign(:current_workspace, current_workspace)
     |> assign(:selected_filter, "all")
     |> assign(:stats, get_dashboard_stats(current_user, current_workspace))
     |> assign(:payment_past_due, payment_past_due)
     |> assign(:billing_portal_url, billing_portal_url)}
  end

  @impl true
  def handle_params(_params, _url, %{socket: %{assigns: %{live_action: :calendar}}} = socket) do
    case Accounts.is_user_active?(socket.assigns.current_user) do
      {:ok, :active} ->
        {:noreply,
         socket
         |> assign(:page_title, "Calendar")
         |> assign(:post, nil)
         |> assign(:show_editor, false)}

      {:error, :trial_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?trial_end=true")}

      {:error, :subscription_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?subscription_ended=true")}

      _ ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans")}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    case Accounts.is_user_active?(socket.assigns.current_user) do
      {:ok, :active} ->
        filter = params["filter"] || socket.assigns.selected_filter || "all"
        socket = assign(socket, :selected_filter, filter)

        case socket.assigns.live_action do
          :new ->
            {:noreply,
             socket
             |> assign(:page_title, "New Post")
             |> assign(:current_post_id, nil)
             |> assign(:show_editor, true)
             |> assign(:post, %Post{content: [], schedules: []})}

          :edit ->
            case Publishing.get_post(params["id"], socket.assigns.current_user) do
              {:ok, post} ->
                {:noreply,
                 socket
                 |> assign(:page_title, "Edit Post")
                 |> assign(:current_post_id, post.id)
                 |> assign(:post, post)
                 |> assign(:show_editor, true)}

              {:error, :not_found} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Post not found")
                 |> push_navigate(to: ~p"/dashboard")}
            end

          :show ->
            if params["id"] do
              case Publishing.get_post(params["id"], socket.assigns.current_user) do
                {:ok, post} ->
                  {:noreply,
                   socket
                   |> assign(:page_title, "Post Details")
                   |> assign(:current_post_id, post.id)
                   |> assign(:post, post)
                   |> assign(:show_editor, false)}

                {:error, :not_found} ->
                  {:noreply,
                   socket
                   |> put_flash(:error, "Post not found")
                   |> push_navigate(to: ~p"/dashboard")}
              end
            else
              {:noreply,
               socket
               |> assign(:page_title, "Posts")
               |> assign(:post, nil)
               |> assign(:show_editor, false)}
            end

          _ ->
            {:noreply,
             socket
             |> assign(:page_title, "Posts")
             |> assign(:post, nil)
             |> assign(:show_editor, false)}
        end

      {:error, :trial_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?trial_end=true")}

      {:error, :subscription_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?subscription_ended=true")}

      _ ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="h-full flex"
      id="app-container"
      phx-hook="AppFeatures"
      data-features={Jason.encode!(format_features(Crosspost.Features.list_features()))}
    >
      <.live_component
        :if={@show_onboarding}
        module={OnboardingComponent}
        id="onboarding"
        current_user={@current_user}
      />
      <%= if @show_editor do %>
        <.live_component
          module={CrosspostWeb.PostLive.EditorComponent}
          id="post-editor"
          post={@post}
          current_user={@current_user}
          current_workspace={@current_workspace}
        />
      <% else %>
        <div class="flex-1 overflow-auto">
          <%= case @live_action do %>
            <% :calendar -> %>
              <.live_component
                module={CrosspostWeb.CalendarComponent}
                id="calendar"
                current_user={@current_user}
                current_workspace={@current_workspace}
              />
            <% _ -> %>
              <%= if @post do %>
                <.live_component
                  module={CrosspostWeb.PostLive.ShowComponent}
                  id="post-show"
                  post={@post}
                  current_user={@current_user}
                  current_workspace={@current_workspace}
                />
              <% else %>
                <.live_component
                  module={CrosspostWeb.DashboardComponent}
                  id="dashboard"
                  current_user={@current_user}
                  current_workspace={@current_workspace}
                  stats={@stats}
                  networks={[
                    {"bsky", "bg-blue-400"},
                    {"linkedin", "bg-blue-600"},
                    {"mastodon", "bg-purple-600"},
                    {"x", "bg-black"}
                  ]}
                />
              <% end %>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def handle_event("switch_preview", %{"network" => network}, socket) do
    {:noreply, assign(socket, :selected_network, network)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case Publishing.get_post(id, socket.assigns.current_user) do
      {:ok, post} ->
        case Publishing.delete_post(post) do
          {:ok, _} ->
            {:noreply,
             socket
             |> put_flash(:info, "Post deleted successfully")
             |> push_navigate(to: ~p"/dashboard")}

          {:error, _} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to delete post")
             |> push_navigate(to: ~p"/dashboard")}
        end

      {:error, :not_found} ->
        {:noreply,
         socket
         |> put_flash(:error, "Post not found")
         |> push_navigate(to: ~p"/dashboard")}
    end
  end

  @impl true
  def handle_event("theme_changed", %{"theme" => _theme}, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("update", %{"features" => features}, socket) do
    {:noreply, push_event(socket, "features:update", %{features: features})}
  end

  @impl true
  def handle_info({:attachment_updated, attachment}, socket) do
    {:noreply, push_event(socket, "attachment:uploaded", %{attachment: attachment})}
  end

  def handle_info({:attachment_error, error}, socket) do
    {:noreply, push_event(socket, "attachment:upload_failed", %{error: error})}
  end

  def handle_info({:post_updated, updated_post}, socket) do
    user = socket.assigns.current_user

    {:ok, post} = Publishing.get_post(updated_post.id, user)

    socket =
      if socket.assigns.current_post_id == updated_post.id and !socket.assigns.show_editor do
        assign(socket, :post, post)
      else
        socket
      end

    send_update(CrosspostWeb.PostLive.SidebarComponent,
      id: "sidebar"
    )

    if socket.assigns.post && socket.assigns.post.id == updated_post.id do
      send_update(CrosspostWeb.PostLive.PreviewComponent,
        id: "preview",
        post: post,
        current_user: socket.assigns.current_user
      )

      send_update(CrosspostWeb.PostLive.ScheduleInfoComponent,
        id: "schedule-info",
        post: post,
        current_user: socket.assigns.current_user
      )

      send_update(CrosspostWeb.PostLive.ShowComponent,
        id: "post-show",
        post: post,
        current_user: socket.assigns.current_user
      )
    end

    {:noreply, socket}
  end

  def handle_info(:after_reconnect, socket) do
    case socket.assigns.current_post_id do
      nil ->
        {:noreply, socket}

      post_id ->
        case Publishing.get_post(post_id, socket.assigns.current_user) do
          {:ok, post} ->
            {:noreply,
             socket
             |> assign(:post, post)
             |> assign(:show_editor, socket.assigns.show_editor)}

          {:error, _} ->
            {:noreply, socket}
        end
    end
  end

  @impl true
  def handle_info({:workspace_changed, workspace}, socket) do
    {:noreply, assign(socket, current_workspace: workspace)}
  end

  @impl true
  def handle_info(:close_onboarding, socket) do
    {:noreply, assign(socket, :show_onboarding, false)}
  end

  @impl true
  def handle_info({:user_updated, user}, socket) do
    case Accounts.is_user_active?(user) do
      {:ok, :active} ->
        {:noreply, assign(socket, :current_user, user)}

      {:error, :trial_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?trial_end=true")}

      {:error, :subscription_ended} ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans?subscription_ended=true")}

      _ ->
        {:noreply,
         socket
         |> redirect(to: ~p"/plans")}
    end
  end

  defp format_features(features) do
    features
    |> Enum.map(fn feature ->
      %{
        feature_key: feature.key,
        feature_type: feature.type,
        feature_name: feature.name,
        feature_description: feature.description,
        feature_coming_soon: feature.coming_soon
      }
    end)
    |> Enum.reduce(%{}, fn feature, acc ->
      Map.put(acc, feature.feature_key, feature)
    end)
  end

  defp get_dashboard_stats(user, workspace) do
    %{
      total_posts: Posts.count_user_posts(user, workspace),
      posts_this_month: Posts.count_user_posts_this_month(user, workspace),
      connected_platforms: Accounts.count_connected_platforms(user, workspace)
    }
  end
end
