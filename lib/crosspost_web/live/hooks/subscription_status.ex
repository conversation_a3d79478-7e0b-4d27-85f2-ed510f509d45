defmodule CrosspostWeb.Live.Hooks.SubscriptionStatus do
  import Phoenix.Component

  def on_mount(:default, _params, _session, %{assigns: %{current_user: user}} = socket) do
    payment_past_due = user && user.status == :past_due

    if payment_past_due do
      billing_portal_url = Crosspost.Accounts.billing_portal_url(user)

      {:cont,
       assign(socket,
         payment_past_due: payment_past_due,
         billing_portal_url: billing_portal_url
       )}
    else
      {:cont,
       assign(socket,
         payment_past_due: payment_past_due
       )}
    end
  end

  def on_mount(:default, _params, _session, socket) do
    {:cont, assign(socket, payment_past_due: false, billing_portal_url: nil)}
  end
end
