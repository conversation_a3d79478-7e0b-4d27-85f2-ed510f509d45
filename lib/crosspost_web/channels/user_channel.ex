defmodule CrosspostWeb.UserChannel do
  use Phoenix.Channel
  require Logger

  def join("user:" <> user_id, _params, socket) do
    socket_user_id =
      socket.assigns[:user_id] ||
        (socket.assigns[:current_user] && socket.assigns[:current_user].id) ||
        socket.assigns.session["user_id"]

    if to_string(socket_user_id) == user_id do
      {:ok, socket}
    else
      Logger.warning(
        "Unauthorized join attempt for user:#{user_id} by #{inspect(socket_user_id)}"
      )

      {:error, %{reason: "unauthorized"}}
    end
  end
end
