defmodule CrosspostWeb.UserSocket do
  use Phoenix.Socket

  # Channels
  channel "user:*", CrosspostWeb.UserChannel

  @impl true
  def connect(%{"user_id" => user_id}, socket, _connect_info) do
    # Simple authentication - just ensure user_id is present
    {:ok, assign(socket, :user_id, user_id)}
  end

  def connect(_params, _socket, _connect_info), do: :error

  @impl true
  def id(socket), do: "user_socket:#{socket.assigns.user_id}"
end
