defmodule CrosspostWeb.FallbackController do
  use CrosspostWeb, :controller

  def call(conn, {:error, %Ecto.Changeset{} = changeset}) do
    conn
    |> put_status(:unprocessable_entity)
    |> put_view(json: CrosspostWeb.ChangesetJSON)
    |> render(:error, changeset: changeset)
  end

  def call(conn, {:error, :not_found}) do
    conn
    |> put_status(:not_found)
    |> put_view(json: CrosspostWeb.ErrorJSON)
    |> render(:"404")
  end

  def call(conn, {:error, :unauthorized}) do
    conn
    |> put_status(:unauthorized)
    |> put_view(json: CrosspostWeb.ErrorJSON)
    |> render(:"401")
  end

  def call(conn, {:error, msg}) when is_binary(msg) do
    conn
    |> put_status(:internal_server_error)
    |> json(%{error: msg})
  end
end
