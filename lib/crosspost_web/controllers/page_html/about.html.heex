<div class="max-w-3xl mx-auto px-4 py-12 sm:py-16 lg:py-20">
  <div class="text-center mb-12">
    <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
      About JustCrossPost
    </h1>
    <p class="mt-6 text-lg leading-8 text-gray-600">
      The no fuss cross-posting app.
    </p>
  </div>

  <div class="space-y-12">
    <!-- Mission Section -->
    <section class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
      <div class="flex flex-col sm:flex-row items-center sm:items-start gap-6">
        <img
          src="https://gravatar.com/avatar/6a8272ae8f0af433dbf0c70dda702109?size=512"
          alt="Peter Solnica"
          class="w-24 h-24 sm:w-32 sm:h-32 rounded-full object-cover"
        />
        <div class="space-y-8 text-center sm:text-left">
          <div class="space-y-4">
            <p class="text-lg text-gray-900 leading-relaxed">
              Hi, I'm <PERSON>. I built JustCrossPost because I was tired of bloated tools that overcomplicate a simple thing: <span class="font-medium text-indigo-600">sharing your content</span>.
            </p>

            <p class="text-gray-600 leading-relaxed">
              As a solo dev, I wanted something clean, affordable, and focused — no dashboards full of features I'd never use, no confusing UX, and definitely no price tag aimed at marketing teams with deep pockets.
            </p>

            <p class="text-gray-600 leading-relaxed">
              JustCrossPost is for people like us — indie devs, creators, anyone who just wants to write and share without friction. It's built to give you a
              <span class="font-medium text-indigo-600">
                smooth, distraction-free writing experience
              </span>
              and help you get your content out there without the noise. No AI hype. No enterprise fluff. Just your voice, your words, everywhere they need to be.
            </p>

            <p class="text-gray-600 leading-relaxed">
              And if you're part of a team or company that just wants a straightforward tool that works — no complexity, no nonsense — you might find JustCrossPost
              <span class="font-medium text-indigo-600">refreshingly simple</span>
              too.
            </p>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Find me here:</h3>
            <div class="flex flex-wrap gap-4">
              <.social_link network="bsky" username="solnic.dev" />
              <.social_link network="mastodon" username="<EMAIL>" />
              <.social_link network="linkedin" username="solnic" />
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Features Grid -->
    <section class="grid gap-8 sm:grid-cols-2">
      <div class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
        <div class="flex items-center gap-4 mb-4">
          <div class="p-2 bg-indigo-100 rounded-lg">
            <svg
              class="w-6 h-6 text-indigo-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900">Effortless Publishing</h3>
        </div>
        <p class="text-gray-600">
          Write once and publish everywhere. Our platform automatically adapts your content for each social network,
          ensuring optimal presentation across all platforms.
        </p>
      </div>

      <div class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
        <div class="flex items-center gap-4 mb-4">
          <div class="p-2 bg-indigo-100 rounded-lg">
            <svg
              class="w-6 h-6 text-indigo-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900">Smart Scheduling</h3>
        </div>
        <p class="text-gray-600">
          Plan your content ahead with our intelligent scheduling system. Post at the perfect time for maximum engagement
          across different time zones and platforms.
        </p>
      </div>

      <div class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
        <div class="flex items-center gap-4 mb-4">
          <div class="p-2 bg-indigo-100 rounded-lg">
            <svg
              class="w-6 h-6 text-indigo-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
              />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900">Platform Optimization</h3>
        </div>
        <p class="text-gray-600">
          Each social network has its own quirks and requirements. We handle all the technical details,
          ensuring your content looks perfect everywhere.
        </p>
      </div>

      <div class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
        <div class="flex items-center gap-4 mb-4">
          <div class="p-2 bg-indigo-100 rounded-lg">
            <svg
              class="w-6 h-6 text-indigo-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900">Privacy First</h3>
        </div>
        <p class="text-gray-600">
          Your data security is our priority. We use industry-standard encryption and security practices
          to keep your accounts and content safe.
        </p>
      </div>
    </section>
    <!-- Supported Platforms -->
    <section class="bg-white rounded-2xl shadow-sm ring-1 ring-gray-900/5 p-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">Supported Platforms</h2>
      <div class="grid grid-cols-2 sm:grid-cols-4 gap-6">
        <div class="flex flex-col items-center">
          <CrosspostWeb.Components.SocialNetworkIcons.icon
            network="bsky"
            class="w-8 h-8 text-gray-600"
          />
          <span class="mt-2 text-sm font-medium text-gray-900">Bluesky</span>
        </div>
        <div class="flex flex-col items-center">
          <CrosspostWeb.Components.SocialNetworkIcons.icon
            network="mastodon"
            class="w-8 h-8 text-gray-600"
          />
          <span class="mt-2 text-sm font-medium text-gray-900">Mastodon</span>
        </div>
        <div class="flex flex-col items-center">
          <CrosspostWeb.Components.SocialNetworkIcons.icon
            network="linkedin"
            class="w-8 h-8 text-gray-600"
          />
          <span class="mt-2 text-sm font-medium text-gray-900">LinkedIn</span>
        </div>
        <div class="flex flex-col items-center">
          <CrosspostWeb.Components.SocialNetworkIcons.icon
            network="x"
            class="w-8 h-8 text-gray-600"
          />
          <span class="mt-2 text-sm font-medium text-gray-900">X</span>
        </div>
      </div>
      <div class="mt-8">
        <div class="bg-gradient-to-r from-[#ff80b5]/10 to-[#9089fc]/10 dark:from-[#ff80b5]/5 dark:to-[#9089fc]/5 rounded-xl p-6 border border-[#9089fc]/20">
          <div class="flex items-center justify-center gap-2 mb-3">
            <svg class="w-5 h-5 text-[#9089fc]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.75 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM7.5 15.75a.75.75 0 100-********* 0 000 1.5zM8.25 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM9.75 15.75a.75.75 0 100-********* 0 000 1.5zM10.5 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 15.75a.75.75 0 100-********* 0 000 1.5zM12.75 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM14.25 15.75a.75.75 0 100-********* 0 000 1.5zM15 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 15.75a.75.75 0 100-********* 0 000 1.5zM15 12.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM16.5 13.5a.75.75 0 100-********* 0 000 1.5z" />
              <path
                fill-rule="evenodd"
                d="M6.75 2.25A.75.75 0 017.5 3v1.5h9V3A.75.75 0 0118 3v1.5h.75a3 3 0 013 3v11.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V7.5a3 3 0 013-3H6V3a.75.75 0 01.75-.75zm13.5 9a1.5 1.5 0 00-1.5-1.5H5.25a1.5 1.5 0 00-1.5 1.5v7.5a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5v-7.5z"
                clip-rule="evenodd"
              />
            </svg>
            <span class="font-semibold text-[#6C467E] dark:text-[#9089fc]">Coming Soon</span>
          </div>
          <h4 class="text-center font-medium text-gray-900 dark:text-white mb-2">
            Expanding Our Platform Support
          </h4>
          <div class="text-center space-y-2">
            <p class="text-gray-600 dark:text-gray-300">
              We're working on adding support for:
            </p>
            <div class="flex flex-col items-center gap-1.5 text-gray-700 dark:text-gray-200 font-medium">
              <span>Blogging Platforms</span>
              <span>GitHub</span>
              <span>More Social Networks</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
              Want us to support your favorite platform?
              <.link
                navigate={~p"/contact"}
                class="text-[#9089fc] hover:text-[#ff80b5] font-medium"
              >
                Let us know!
              </.link>
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
