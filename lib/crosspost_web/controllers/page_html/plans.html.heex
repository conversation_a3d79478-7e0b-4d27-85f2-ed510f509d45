<div class="mx-auto px-4 py-12 sm:py-16 lg:py-20">
  <%= if @trial_end do %>
    <div class="max-w-3xl mx-auto mb-8">
      <div
        class="bg-yellow-50 dark:bg-yellow-900/50 border-l-4 border-yellow-400 p-4 rounded-lg"
        data-test-id="trial-end-notice"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <CrosspostWeb.CoreComponents.icon
              name="hero-exclamation-triangle"
              class="h-5 w-5 text-yellow-400"
            />
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700 dark:text-yellow-200 mb-2">
              Your trial period has ended. Please choose a plan to continue using JustCrossPost.
            </p>
            <p class="text-sm text-green-900 dark:text-green-200">
              Need more time? Just
              <.link navigate={~p"/contact"} class="font-bold underline">get in touch</.link>
              and we'll extend your trial.
            </p>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <%= if @subscription_ended do %>
    <div class="max-w-3xl mx-auto mb-8">
      <div
        class="bg-yellow-50 dark:bg-yellow-900/50 border-l-4 border-yellow-400 p-4 rounded-lg"
        data-test-id="subscription-ended-notice"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <CrosspostWeb.CoreComponents.icon
              name="hero-exclamation-triangle"
              class="h-5 w-5 text-yellow-400"
            />
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700 dark:text-yellow-200 mb-2">
              Your subscription has ended. Please choose a plan to continue using JustCrossPost.
            </p>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="max-w-3xl mx-auto text-center mb-12">
    <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
      Plans &amp; Pricing
    </h1>
    <p class="mt-6 text-lg leading-8 text-gray-600">
      Simple, transparent pricing that grows with you. All plans include a 14-day free trial.
    </p>
    <div class="mt-6">
      <span class="inline-flex items-center gap-x-2 rounded-full bg-indigo-100 dark:bg-indigo-900/50 px-4 py-1 text-sm font-medium text-indigo-700 dark:text-indigo-200 ring-1 ring-inset ring-indigo-600/20 dark:ring-indigo-400/30">
        <CrosspostWeb.CoreComponents.icon name="hero-sparkles" class="h-4 w-4" />
        Beta Special: 25% off all plans
      </span>
    </div>
  </div>

  <div class="max-w-7xl mx-auto">
    <div class="isolate grid max-w-md grid-cols-1 gap-y-8 mx-auto lg:max-w-none lg:grid-cols-3 lg:gap-x-8">
      <%= for plan <- @plans do %>
        <div class={[
          "rounded-3xl p-8 ring-1 xl:p-10 transition-all duration-200",
          !plan.enabled && "ring-gray-200 opacity-75",
          plan.enabled && "ring-gray-200 hover:scale-[1.02] hover:shadow-lg",
          plan.highlight && "ring-2 ring-indigo-600 scale-105 hover:scale-[1.07]"
        ]}>
          <div class="flex items-center justify-between gap-x-4">
            <h2 class="text-lg font-semibold leading-8 text-gray-900">
              {plan.name}
            </h2>
            <%= if plan.highlight do %>
              <span class="rounded-full bg-indigo-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-indigo-600">
                Popular
              </span>
            <% end %>
          </div>
          <p class="mt-4 text-sm leading-6 text-gray-600">
            {plan.description}
          </p>
          <p class="mt-6 flex flex-col items-center gap-y-1">
            <span class="text-sm text-gray-500 line-through">
              {Number.Currency.number_to_currency(plan.price / 100 / 0.75)}
            </span>
            <span class="flex items-baseline gap-x-1">
              <span class="text-4xl font-bold tracking-tight text-gray-900">
                {Number.Currency.number_to_currency(plan.price / 100)}
              </span>
              <span class="text-sm font-semibold leading-6 text-gray-600">/month</span>
            </span>
          </p>
          <%= if !plan.enabled do %>
            <button
              disabled
              class="mt-6 block w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 bg-gray-100 text-gray-500 cursor-not-allowed"
            >
              <div class="flex items-center justify-center gap-x-2">
                <span>Coming Soon</span>
                <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                  Beta
                </span>
              </div>
            </button>
          <% else %>
            <%= if @current_user do %>
              <%= if Crosspost.Accounts.subscribed?(@current_user) do %>
                <div class="mt-6">
                  <CrosspostWeb.Components.PlanChangeForm.plan_change_form
                    plan={plan}
                    highlight={plan.highlight}
                    cta={
                      if @current_user.plan.key == plan.key,
                        do: "Current Plan",
                        else: "Change to #{plan.name}"
                    }
                  />
                </div>
              <% else %>
                <form action={~p"/checkout"} method="post" class="mt-6">
                  <input type="hidden" name="price_id" value={plan.stripe_price_id} />
                  <input type="hidden" name="_csrf_token" value={get_csrf_token()} />
                  <button
                    type="submit"
                    class={[
                      "w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2",
                      plan.highlight &&
                        "bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600",
                      !plan.highlight &&
                        "bg-white text-indigo-600 ring-1 ring-inset ring-indigo-200 hover:ring-indigo-300"
                    ]}
                  >
                    {plan.cta || "Get started"}
                  </button>
                </form>
              <% end %>
            <% else %>
              <.link
                navigate={~p"/users/sign-in?_action=subscribe"}
                class={[
                  "mt-6 block w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2",
                  plan.highlight &&
                    "bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600",
                  !plan.highlight &&
                    "bg-white text-indigo-600 ring-1 ring-inset ring-indigo-200 hover:ring-indigo-300"
                ]}
              >
                Sign in to subscribe
              </.link>
            <% end %>
          <% end %>
          <ul role="list" class="mt-8 space-y-3 text-sm leading-6 text-gray-600">
            <%= for feature_key <- plan.features do %>
              <%= if feature = Map.get(@features, feature_key) do %>
                <li class="flex gap-x-3">
                  <svg
                    class="h-6 w-5 flex-none text-indigo-600"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="flex-grow">
                    <%= if feature.coming_soon do %>
                      <span class="inline-flex items-center rounded-md bg-indigo-50 px-1.5 py-0.5 text-xs font-medium text-indigo-700 ring-1 ring-inset ring-indigo-600/20 mr-1">
                        soon
                      </span>
                    <% end %>
                    {feature.description}
                  </span>
                </li>
              <% end %>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>

    <div class="mt-24">
      <div class="text-center mb-16">
        <h2 class="text-base font-semibold leading-7 text-indigo-600">Compare</h2>
        <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          Features comparison
        </p>
      </div>

      <div class="mt-8 flow-root">
        <div class="overflow-x-auto">
          <div class="inline-block min-w-full align-middle">
            <table class="min-w-full divide-y divide-gray-300">
              <thead>
                <tr class="bg-gray-50">
                  <th
                    scope="col"
                    class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8"
                  >
                    Feature
                  </th>
                  <th
                    scope="col"
                    class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                  >
                    Description
                  </th>
                  <%= for plan <- @plans do %>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                    >
                      {plan.name}
                    </th>
                  <% end %>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <%= for {_key, feature} <- @features do %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8">
                      {feature.name}
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-500">
                      {feature.description}
                    </td>
                    <%= for plan <- @plans do %>
                      <td class="px-3 py-4 text-center text-sm text-gray-500">
                        <%= if feature.key in plan.features do %>
                          <svg
                            class="h-5 w-5 text-indigo-600 mx-auto"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        <% else %>
                          —
                        <% end %>
                      </td>
                    <% end %>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
