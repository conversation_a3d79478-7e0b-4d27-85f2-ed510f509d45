<div class="relative min-h-screen mb-20">
  <!-- Background gradient -->
  <div
    class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
    aria-hidden="true"
  >
    <div
      class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
      style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
    >
    </div>
  </div>
  
<!-- Hero section -->
  <div class="relative isolate">
    <div class="mx-auto max-w-7xl px-6 py-10 sm:py-16 lg:px-8">
      <div class="mx-auto max-w-2xl text-center">
        <div class="flex justify-center mb-8">
          <img src={~p"/images/logo-hero-lm.png"} class="h-24 sm:h-32 w-auto light-mode-logo" />
          <img src={~p"/images/logo-hero.png"} class="h-24 sm:h-32 w-auto dark-mode-logo" />
        </div>
        <h1 class="mt-10 text-4xl font-bold tracking-tight text-brand-gray-600 dark:text-brand-white-600 sm:text-6xl font-russo">
          The
          <span class="bg-gradient-to-r from-[#ff80b5] to-[#9089fc] inline-block text-transparent bg-clip-text">
            no fuss
          </span>
          <br />cross-posting app
        </h1>
        <p class="mt-6 text-lg leading-8 text-brand-gray-600 dark:text-brand-white-600">
          Streamline your publishing. Write once, share everywhere. Support for Bluesky, Mastodon, LinkedIn, and X.
        </p>
        <div class="mt-10 flex items-center justify-center gap-x-6">
          <.link
            navigate={~p"/users/sign-up"}
            class="rounded-xl bg-[#6C467E] px-8 py-4 text-xl font-bold text-white shadow-lg hover:bg-[#6C467E]/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#6C467E] transition-all duration-200 hover:scale-105"
          >
            Get Started <span aria-hidden="true">→</span>
          </.link>
          <.link
            navigate={~p"/about"}
            class="text-lg font-semibold leading-6 text-brand-gray-600 dark:text-brand-white-600"
          >
            Learn more <span aria-hidden="true">→</span>
          </.link>
        </div>
      </div>

      <div class="mx-auto mt-16 sm:mt-24 max-w-3xl">
        <div class="rounded-xl bg-[#F3EFDC]/30 p-2 ring-1 ring-inset ring-[#6C467E]/10 lg:rounded-2xl lg:p-4">
          <img
            src={~p"/images/featured-screenshot-light.png"}
            alt="App screenshot"
            class="w-full rounded-md shadow-2xl ring-1 ring-[#6C467E]/10"
          />
        </div>
      </div>
    </div>
  </div>
  
<!-- CTA section -->
  <div class="relative isolate px-6 py-16 sm:py-24 lg:px-8">
    <div class="mx-auto max-w-2xl text-center">
      <h2 class="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
        Ready to streamline your publishing?
      </h2>
      <p class="mx-auto mt-6 max-w-xl text-xl leading-8 text-gray-600 dark:text-gray-300">
        Join our beta and be among the first to try out JustCrossPost!
      </p>
      <div class="mt-10 flex items-center justify-center">
        <.link
          navigate={~p"/users/sign-up"}
          class="rounded-xl bg-[#6C467E] px-10 py-5 text-2xl font-bold text-white shadow-lg hover:bg-[#6C467E]/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#6C467E] transition-all duration-200 hover:scale-105"
        >
          Get Started <span aria-hidden="true">→</span>
        </.link>
      </div>
    </div>
  </div>
  
<!-- Features section -->
  <div class="mx-auto mt-16 max-w-7xl px-6 sm:mt-24 lg:px-8">
    <div class="mx-auto max-w-2xl text-center">
      <h2 class="text-base font-semibold leading-7 text-[#6C467E]">Post Smarter</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-[#6C467E] dark:text-[#9089fc] sm:text-4xl">
        Everything you need to just crosspost and create more with ease
      </p>
    </div>

    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
      <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clip-rule="evenodd"
              />
            </svg>
            Schedule Posts
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              Plan your content ahead with our powerful scheduling system. Post at the perfect time for your audience.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-schedule.png"}
                alt="Post scheduling feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20">
              <text
                x="50%"
                y="50%"
                text-anchor="middle"
                dominant-baseline="central"
                font-size="20"
                font-weight="bold"
                fill="currentColor"
              >
                @
              </text>
            </svg>
            Cross-network mentions
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              Quickly create social profiles for multiple networks and use @-mentions that
              automatically link to the correct profile.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-social-profiles.png"}
                alt="Code images feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3.505 2.365A41.369 41.369 0 019 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 00-.577-.069 43.141 43.141 0 00-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 015 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914z" />
              <path d="M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 001.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0014 6z" />
            </svg>
            Thread & Long Form Modes
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              Create engaging thread-style content that automatically adapts to each platform's requirements. Have threads automatically expand to long-form version for platforms that support it.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-thread-mode.png"}
                alt="Thread Mode feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
      </dl>
    </div>
    <!-- Add a second row of features -->
    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
      <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <circle cx="10" cy="10" r="9" fill="none" stroke="currentColor" stroke-width="1.5" />
              <text
                x="10"
                y="11.5"
                font-size="6"
                fill="currentColor"
                text-anchor="middle"
                font-family="system-ui, -apple-system, sans-serif"
                font-weight="bold"
              >
                280
              </text>
            </svg>
            Smart Char Limits
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              Never worry about character limits again. Easily see when you're approaching the limit for each platform.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-char-limit.png"}
                alt="Smart character limits feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M1 5.25A2.25 2.25 0 013.25 3h13.5A2.25 2.25 0 0119 5.25v9.5A2.25 2.25 0 0116.75 17H3.25A2.25 2.25 0 011 14.75v-9.5zm1.5 5.81v3.69c0 .414.336.75.75.75h13.5a.75.75 0 00.75-.75v-2.69l-2.22-2.219a.75.75 0 00-1.06 0l-1.91 1.909.47.47a.75.75 0 11-1.06 1.06L6.53 8.091a.75.75 0 00-1.06 0l-2.97 2.97zM12 7a1 1 0 11-2 0 1 1 0 012 0z"
                clip-rule="evenodd"
              />
            </svg>
            Image & Video Attachments
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              Upload images once and we'll optimize them for each platform. Support for multiple images, alt text, and platform-specific optimizations.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-images.png"}
                alt="Image attachments feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
        <div class="flex flex-col">
          <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
            <svg class="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z" />
              <path
                fill-rule="evenodd"
                d="M.664 10.59a1.651 1.651 0 010-1.186A10.004 10.004 0 0110 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0110 17c-4.257 0-7.893-2.66-9.336-6.41zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"
              />
            </svg>
            Preview
          </dt>
          <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
            <p class="flex-auto">
              See exactly how your posts will look on each platform before publishing. No more formatting surprises.
            </p>
            <div class="mt-6 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10">
              <img
                src={~p"/images/feature-preview.png"}
                alt="Smart Preview feature screenshot"
                class="rounded-md shadow-2xl ring-1 ring-gray-900/10 w-full"
              />
            </div>
          </dd>
        </div>
      </dl>
    </div>
  </div>
  
<!-- Additional Features Section -->
  <div class="mx-auto mt-16 max-w-7xl px-6 sm:mt-24 lg:px-8">
    <div class="mx-auto max-w-2xl text-center">
      <h2 class="text-base font-semibold leading-7 text-[#6C467E]">And More</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-[#6C467E] dark:text-[#9089fc] sm:text-4xl">
        Packed with Features You'll Love
      </p>
    </div>

    <div class="mx-auto mt-16 max-w-5xl">
      <!-- Content Management -->
      <div class="mb-16">
        <h3 class="text-xl font-semibold text-[#6C467E] mb-6 text-center">Content Management</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="bg-white/50 dark:bg-white/5 rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
            <h4 class="font-medium text-gray-900 dark:text-white">Calendar View</h4>
            <p class="mt-2 text-gray-600 dark:text-gray-300">
              Get a bird's eye view of your content strategy with our intuitive calendar interface. Track published and scheduled posts across all platforms.
            </p>
          </div>
          <div class="bg-white/50 dark:bg-white/5 rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
            <h4 class="font-medium text-gray-900 dark:text-white">Workspace Collaboration</h4>
            <p class="mt-2 text-gray-600 dark:text-gray-300">
              Work seamlessly with your team using shared workspaces. Perfect for agencies, marketing teams, or anyone managing multiple accounts together.
            </p>
          </div>
        </div>
      </div>
      
<!-- Content Enrichment -->
      <div class="mb-16">
        <h3 class="text-xl font-semibold text-[#6C467E] mb-6 text-center">Content Enrichment</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="bg-white/50 dark:bg-white/5 rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
            <h4 class="font-medium text-gray-900 dark:text-white">Smart URL Cards</h4>
            <p class="mt-2 text-gray-600 dark:text-gray-300">
              Enhance your posts with rich URL previews. Add multiple links and decide which one you want to embed as a preview card.
            </p>
          </div>
          <div class="bg-white/50 dark:bg-white/5 rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
            <h4 class="font-medium text-gray-900 dark:text-white">Media Management</h4>
            <p class="mt-2 text-gray-600 dark:text-gray-300">
              Take control of your visual content with drag-and-drop media reordering. Perfect for creating the exact visual story you want to tell.
            </p>
          </div>
        </div>
      </div>
      
<!-- Thread Management -->
      <div>
        <h3 class="text-xl font-semibold text-[#6C467E] mb-6 text-center">
          Advanced Thread Features
        </h3>
        <div class="grid grid-cols-1 gap-8">
          <div class="bg-white/50 dark:bg-white/5 rounded-xl p-6 shadow-sm ring-1 ring-gray-900/5">
            <h4 class="font-medium text-gray-900 dark:text-white">Flexible Thread Editing</h4>
            <p class="mt-2 text-gray-600 dark:text-gray-300">
              Take full control of your threads with:
            </p>
            <ul class="mt-4 space-y-3 text-gray-600 dark:text-gray-300">
              <li class="flex items-start">
                <svg
                  class="h-6 w-6 flex-none text-[#9089fc]"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill-rule="evenodd"
                    d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="ml-3">Full or partial content sync between platforms</span>
              </li>
              <li class="flex items-start">
                <svg
                  class="h-6 w-6 flex-none text-[#9089fc]"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill-rule="evenodd"
                    d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="ml-3">Quick thread item reordering</span>
              </li>
              <li class="flex items-start">
                <svg
                  class="h-6 w-6 flex-none text-[#9089fc]"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill-rule="evenodd"
                    d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="ml-3">
                  Smart thread-to-article conversion for supported platforms
                </span>
              </li>
              <li class="flex items-start">
                <svg
                  class="h-6 w-6 flex-none text-[#9089fc]"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill-rule="evenodd"
                    d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="ml-3">
                  Platform-specific text customization for perfect messaging
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
