defmodule CrosspostWeb.UserSessionController do
  use CrosspostWeb, :controller

  alias Crosspost.Accounts
  alias CrosspostWeb.UserAuth

  require Logger

  def create(conn, %{"_action" => "registered"} = params) do
    create(conn, params, "Account created successfully!")
  end

  def create(conn, %{"_action" => "password_updated"} = params) do
    conn
    |> put_session(:user_return_to, ~p"/settings")
    |> create(params, "Password updated successfully!")
  end

  def create(conn, params) do
    create(conn, params, "Welcome back!")
  end

  defp create(conn, %{"user" => user_params}, info) do
    %{"email" => email, "password" => password} = user_params

    if user = Accounts.get_user_by_email_and_password(email, password) do
      if user.confirmed_at do
        conn
        |> put_flash(:info, info)
        |> UserAuth.log_in_user(user, user_params)
      else
        conn
        |> put_flash(:error, "You must confirm your email address before signing in.")
        |> redirect(to: ~p"/users/sign-in")
      end
    else
      # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
      conn
      |> put_flash(:error, "Invalid email or password")
      |> put_flash(:email, String.slice(email, 0, 160))
      |> redirect(to: ~p"/users/sign-in")
    end
  end

  # Add this new clause to handle password update case
  defp create(
         conn,
         %{"password" => _password, "password_confirmation" => _password_confirmation},
         info
       ) do
    conn
    |> put_flash(:info, info)
    |> redirect(to: ~p"/settings")
  end

  defp create(conn, _params, _info) do
    conn
    |> put_status(404)
    |> halt()
  end

  def delete(conn, _params) do
    conn
    |> put_flash(:info, "Logged out successfully.")
    |> UserAuth.log_out_user()
  end

  def create_oauth(conn, params) do
    provider = params["provider"]
    uid = params["uid"]

    cond do
      is_nil(provider) or is_nil(uid) ->
        conn
        |> delete_session("pending_oauth_auth")
        |> put_flash(:error, "Invalid params")
        |> redirect(to: ~p"/users/sign-in")

      true ->
        case Accounts.get_user_by_oauth(provider, uid) do
          {:ok, user} ->
            conn
            |> delete_session("pending_oauth_auth")
            |> put_flash(:info, "Successfully signed up with #{String.capitalize(provider)}.")
            |> UserAuth.log_in_user(user, %{})

          {:error, _error} ->
            conn
            |> delete_session("pending_oauth_auth")
            |> put_flash(:error, "Invalid credentials")
            |> redirect(to: ~p"/users/sign-in")
        end
    end
  end
end
