defmodule CrosspostWeb.SubscriptionController do
  use CrosspostWeb, :controller

  require Logger

  alias Crosspost.Plans
  alias Crosspost.Accounts

  def update(conn, %{"price_id" => price_id}) do
    user = conn.assigns.current_user

    Logger.metadata(user_id: user.id, price_id: price_id)

    with {:ok, plan} <- Plans.get_plan_by_stripe_price_id(price_id),
         {:ok, subscription} <-
           billing_provider().update_subscription(
             user.subscription.stripe_subscription_id,
             price_id
           ),
         {:ok, product} <-
           billing_provider().get_product(
             subscription.items.data
             |> List.first()
             |> Map.get(:price)
             |> Map.get(:product)
           ),
         {:ok, %{data: features}} <- billing_provider().get_features(product.id) do
      feature_keys =
        Enum.map(features, fn feature ->
          get_in(feature, [:entitlement_feature, :lookup_key])
        end)

      subscription_attrs = %{
        status: subscription.status,
        plan: plan.key,
        period_start: DateTime.from_unix!(subscription.created),
        period_end: DateTime.from_unix!(subscription.current_period_end),
        features: feature_keys,
        stripe_customer_id: user.subscription.stripe_customer_id,
        stripe_subscription_id: subscription.id,
        stripe_product_id: product.id,
        stripe_price_id: price_id
      }

      case Accounts.enroll(user, plan, subscription_attrs) do
        {:ok, _updated_user} ->
          conn
          |> put_flash(:info, "Successfully changed to #{plan.name} plan")
          |> redirect(to: ~p"/settings/plan")

        {:error, error} ->
          Logger.error("Failed to update user subscription", error: inspect(error))

          conn
          |> put_flash(:error, "Failed to update subscription")
          |> redirect(to: ~p"/settings/plan")
      end
    else
      {:error, error} ->
        Logger.error("Failed to update subscription", error: inspect(error))

        conn
        |> put_flash(:error, "Failed to update subscription")
        |> redirect(to: ~p"/settings/plan")
    end
  end

  defp billing_provider do
    Application.get_env(:crosspost, :billing_provider)
  end
end
