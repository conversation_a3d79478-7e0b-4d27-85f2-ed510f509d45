defmodule CrosspostWeb.Admin.ExportController do
  use CrosspostWeb, :controller

  alias Crosspost.Admin
  alias CSV

  def index(conn, _params) do
    render(conn, :index, layout: false)
  end

  def download_users_csv(conn, params) do
    # Extract filter parameters
    email_filter = params["email"] || ""
    name_filter = params["name"] || ""
    status_filter = params["status"] || []
    created_after = params["created_after"] || ""
    created_before = params["created_before"] || ""

    # Get all results without pagination
    case Admin.list_users_with_filters(
           email_filter,
           name_filter,
           # page (ignored when paginate is false)
           1,
           # per_page (ignored when paginate is false)
           20,
           created_after,
           created_before,
           status_filter,
           false
         ) do
      {:ok, %{users: users}} ->
        csv_data =
          users
          |> Enum.map(fn user ->
            trial_end =
              if user.trial_end,
                do: Calendar.strftime(user.trial_end, "%Y-%m-%d %H:%M:%S"),
                else: ""

            subscription_end =
              if user.subscription_period_end,
                do: Calendar.strftime(user.subscription_period_end, "%Y-%m-%d %H:%M:%S"),
                else: ""

            [
              user.name || "",
              user.email,
              user.status || "",
              user.plan_name || "",
              trial_end,
              subscription_end,
              Enum.join(user.tags || [], ", "),
              Calendar.strftime(user.inserted_at, "%Y-%m-%d %H:%M:%S"),
              Calendar.strftime(user.updated_at, "%Y-%m-%d %H:%M:%S")
            ]
          end)

        # Add header row
        csv_data = [
          [
            "Name",
            "Email",
            "Status",
            "Plan",
            "Trial Ends",
            "Subscription Ends",
            "Tags",
            "Created At",
            "Updated At"
          ]
          | csv_data
        ]

        csv_content = CSV.encode(csv_data) |> Enum.to_list() |> Enum.join()

        conn
        |> put_resp_content_type("text/csv")
        |> put_resp_header("content-disposition", "attachment; filename=\"users.csv\"")
        |> send_resp(200, csv_content)

      {:error, reason} ->
        conn
        |> put_flash(:error, "Invalid filter parameters: #{reason}")
        |> redirect(to: ~p"/admin/users")
    end
  end
end
