defmodule CrosspostWeb.WorkspaceController do
  use CrosspostWeb, :controller

  alias <PERSON><PERSON>.Workspaces

  def switch(conn, %{"workspace_slug" => "default"} = params) do
    user = conn.assigns.current_user
    workspace = Workspaces.get_workspace!(user.default_workspace_id)

    conn
    |> put_session(:workspace_id, workspace.id)
    |> redirect(to: params["return_to"] || "/")
  end

  def switch(conn, %{"workspace_slug" => slug} = params) do
    user = conn.assigns.current_user

    case Workspaces.get_workspace_by_slug(slug) do
      nil ->
        conn
        |> put_flash(:error, "Workspace not found")
        |> redirect(to: ~p"/")

      workspace ->
        if Workspaces.user_in_workspace?(user.id, workspace.id) do
          conn
          |> put_session(:workspace_id, workspace.id)
          |> redirect(to: params["return_to"] || "/")
        else
          conn
          |> put_flash(:error, "You don't have access to this workspace")
          |> redirect(to: ~p"/")
        end
    end
  end
end
