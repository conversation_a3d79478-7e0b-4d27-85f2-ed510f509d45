defmodule CrosspostWeb.PageController do
  use CrosspostWeb, :controller

  def index(conn, _params) do
    conn
    |> assign(:page_class, "home-page")
    |> render_static_page(:index)
  end

  def about(conn, _params) do
    render_static_page(conn, :about)
  end

  def tos(conn, _params) do
    render_static_page(conn, :tos)
  end

  def contact(conn, _params) do
    render_static_page(conn, :contact)
  end

  def privacy(conn, _params) do
    render_static_page(conn, :privacy)
  end

  def unsubscribed(conn, _params) do
    render_static_page(conn, :unsubscribed)
  end

  def plans(conn, params) do
    plans = Crosspost.Plans.list_active_plans()
    features = Crosspost.Features.list_features() |> Enum.map(&{&1.key, &1}) |> Map.new()

    conn
    |> assign(:plans, plans)
    |> assign(:features, features)
    |> assign(:trial_end, Map.get(params, "trial_end", false))
    |> assign(:subscription_ended, Map.get(params, "subscription_ended", false))
    |> render_static_page(:plans)
  end

  def home_or_dashboard(conn, params) do
    if conn.assigns.current_user do
      conn
      |> redirect(to: ~p"/dashboard")
    else
      index(conn, params)
    end
  end

  defp render_static_page(conn, template) do
    conn
    |> assign(:current_user, conn.assigns.current_user)
    |> assign(:current_workspace, conn.assigns.current_workspace)
    |> assign(:page_class, conn.assigns[:page_class])
    |> put_layout(html: {CrosspostWeb.Layouts, :static})
    |> render(template)
  end
end
