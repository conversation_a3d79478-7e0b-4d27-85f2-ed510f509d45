defmodule CrosspostWeb.PaymentController do
  use CrosspostWeb, :controller

  alias Crosspost.{Accounts, Plans}

  require Logger

  def success(conn, %{"session_id" => session_id}) do
    current_user = conn.assigns.current_user

    Logger.metadata(session_id: session_id, user_id: current_user.id)
    Logger.info("Processing successful payment")

    with {:ok, session} <- billing_provider().get_session(session_id),
         {:ok, updated_user} <- update_user_subscription(current_user, session) do
      Logger.info("Successfully updated user subscription",
        subscription: updated_user.subscription
      )

      conn
      |> put_root_layout(html: {CrosspostWeb.Layouts, :user})
      |> put_layout(html: {CrosspostWeb.Layouts, :static})
      |> render(:success)
    else
      {:error, error} ->
        Logger.error("Error processing payment", error: error)

        Sentry.capture_message("Error processing payment", extra: %{error: error})

        conn
        |> put_flash(:error, "Error processing subscription")
        |> redirect(to: ~p"/plans")
    end
  end

  defp update_user_subscription(
         user,
         %{customer: customer_id, subscription: subscription}
       ) do
    subscription_item = List.first(subscription.items.data)
    product_id = subscription_item.price.product
    price_id = subscription_item.price.id

    with {:ok, product} <- billing_provider().get_product(product_id),
         {:ok, %{data: features}} <- billing_provider().get_features(product.id),
         {:ok, plan} <- Plans.get_plan_by_stripe_price_id(price_id) do
      feature_keys =
        Enum.map(features, fn feature ->
          get_in(feature, [:entitlement_feature, :lookup_key])
        end)

      attrs = %{
        subscription: %{
          status: subscription.status,
          plan: plan.key,
          period_start: DateTime.from_unix!(subscription.created),
          period_end: DateTime.from_unix!(subscription.current_period_end),
          features: feature_keys,
          stripe_customer_id: customer_id,
          stripe_subscription_id: subscription.id,
          stripe_product_id: product_id,
          stripe_price_id: price_id
        },
        plan_key: plan.key,
        status: :active
      }

      Accounts.enroll(user, plan, attrs.subscription)
      |> case do
        {:ok, updated_user} ->
          Logger.info("Successfully updated user settings and plan",
            subscription: updated_user.subscription,
            plan_key: plan.key,
            features: feature_keys
          )

          Accounts.update_user(updated_user, %{trial_end: nil})

        {:error, error} ->
          Logger.error("Failed to update user", error: inspect(error))
          {:error, error}
      end
    else
      {:error, error} ->
        Logger.error("Failed to fetch plan or product data", error: error)
        Sentry.capture_message("Failed to fetch plan or product data", extra: %{error: error})
        {:error, error}
    end
  end

  defp billing_provider do
    Application.get_env(:crosspost, :billing_provider)
  end
end
