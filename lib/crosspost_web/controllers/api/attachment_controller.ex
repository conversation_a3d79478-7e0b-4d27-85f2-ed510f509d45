defmodule CrosspostWeb.API.AttachmentController do
  use CrosspostWeb, :controller

  alias Crosspost.Publishing
  alias Crosspost.Attachments
  alias Crosspost.Workspaces
  alias Crosspost.Features

  plug :ensure_workspace

  require Logger

  def show(conn, %{"id" => id}) do
    case Publishing.get_attachment(id, conn.assigns.current_user) do
      nil ->
        conn
        |> put_status(:not_found)
        |> text("Attachment not found")

      %{blob: blob, content_type: content_type} when not is_nil(blob) ->
        conn
        |> put_resp_content_type(content_type)
        |> send_resp(200, blob)

      _attachment ->
        conn
        |> put_status(:not_found)
        |> text("Attachment not found")
    end
  end

  # Handle link attachments - this needs to be first
  def create(conn, %{"type" => "link", "url" => url}) do
    current_user = conn.assigns.current_user
    workspace_id = conn.assigns.current_workspace.id

    case validate_url(url) do
      :ok ->
        case Crosspost.Websites.og_info(url) do
          {:ok, og_data} ->
            attrs = %{
              id: Ecto.UUID.generate(),
              source_url: og_data.image_url,
              filename: "link.json",
              content_type: "application/json",
              user_id: current_user.id,
              workspace_id: workspace_id,
              status: "pending",
              type: :link,
              metadata: %{
                "type" => "link",
                "url" => url,
                "title" => og_data.title,
                "description" => og_data.description,
                "image_url" => og_data.image_url
              }
            }

            case Publishing.create_attachment(attrs) do
              {:ok, attachment} ->
                case enqueue_upload(attachment) do
                  {:ok, _job} ->
                    conn
                    |> put_status(:created)
                    |> json(%{attachment: format_attachment(attachment)})

                  {:error, reason} ->
                    Logger.error("Failed to schedule upload", %{
                      event: "api.attachments.upload_scheduling_failed",
                      error: inspect(reason),
                      user_id: current_user.id,
                      workspace_id: workspace_id,
                      attachment_id: attachment.id
                    })

                    conn
                    |> put_status(:unprocessable_entity)
                    |> json(%{error: "Failed to create link attachment"})
                end

              {:error, changeset} ->
                errors = format_changeset_errors(changeset)

                Logger.error("Failed to create link attachment", %{
                  event: "api.attachments.link_creation_failed",
                  errors: errors,
                  user_id: current_user.id,
                  workspace_id: workspace_id,
                  url: url
                })

                conn
                |> put_status(:unprocessable_entity)
                |> json(%{error: errors})
            end

          {:error, reason} ->
            Logger.error("Failed to parse website", %{
              event: "api.attachments.og_info_failed",
              error: inspect(reason),
              user_id: current_user.id,
              workspace_id: workspace_id,
              url: url
            })

            conn
            |> put_status(:unprocessable_entity)
            |> json(%{error: "Failed to parse website"})
        end

      {:error, reason} ->
        Logger.error("Failed to validate URL", %{
          event: "api.attachments.url_validation_failed",
          url: url,
          error: inspect(reason),
          user_id: current_user.id,
          workspace_id: workspace_id
        })

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: "Failed to fetch website"})
    end
  end

  # Handle direct Cloudinary uploads with complete metadata
  def create(conn, %{"attachment" => attachment_params}) do
    current_user = conn.assigns.current_user
    workspace_id = conn.assigns.current_workspace.id

    attrs = %{
      id: attachment_params["id"],
      filename: attachment_params["filename"],
      content_type: attachment_params["content_type"],
      source_url: attachment_params["source_url"],
      preview_url: attachment_params["preview_url"],
      type: String.to_existing_atom(attachment_params["type"]),
      status: attachment_params["status"],
      metadata: attachment_params["metadata"],
      user_id: current_user.id,
      workspace_id: workspace_id
    }

    case Publishing.create_attachment(attrs) do
      {:ok, attachment} ->
        conn
        |> put_status(:created)
        |> json(%{attachment: format_attachment(attachment)})

      {:error, changeset} ->
        errors = format_changeset_errors(changeset)

        Logger.error("Failed to create attachment", %{
          event: "api.attachments.creation_failed",
          errors: errors,
          user_id: current_user.id,
          workspace_id: workspace_id
        })

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: errors})
    end
  end

  # Handle multiple files upload
  def create(conn, %{"files" => files} = params) when is_list(files) do
    handle_multiple_uploads(conn, files, params)
  end

  # Handle files coming as a map
  def create(conn, %{"files" => files} = params) when is_map(files) do
    handle_multiple_uploads(conn, Map.values(files), params)
  end

  # Handle files[] parameter
  def create(conn, %{"files[]" => files} = params) do
    handle_multiple_uploads(conn, List.wrap(files), params)
  end

  # Fallback for no files or invalid params
  def create(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: "Invalid parameters"})
  end

  def delete(conn, %{"id" => id}) do
    case Publishing.get_attachment(id) do
      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Attachment not found"})

      attachment ->
        current_user = conn.assigns.current_user

        if attachment.user_id == current_user.id do
          case Publishing.delete_attachment(attachment) do
            {:ok, _} ->
              if public_id = attachment.metadata["cloudinary"]["public_id"] do
                case storage().delete(public_id) do
                  {:ok, _} ->
                    Logger.info("Deleted attachment from Cloudinary", %{
                      attachment_id: attachment.id,
                      public_id: public_id
                    })

                    :ok

                  {:error, _} ->
                    Logger.error("Failed to delete attachment from Cloudinary", %{
                      error: "Failed to delete attachment from Cloudinary",
                      attachment_id: attachment.id,
                      public_id: public_id
                    })
                end
              end

              json(conn, %{success: true})

            {:error, _} ->
              conn
              |> put_status(:internal_server_error)
              |> json(%{error: "Failed to delete attachment"})
          end
        else
          conn
          |> put_status(:unauthorized)
          |> json(%{error: "Unauthorized"})
        end
    end
  end

  def update(conn, %{"id" => id, "attachment" => attachment_params}) do
    case Publishing.get_attachment(id) do
      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Attachment not found"})

      attachment ->
        current_user = conn.assigns.current_user

        if attachment.user_id == current_user.id do
          case Attachments.update_attachment(attachment, attachment_params) do
            {:ok, updated_attachment} ->
              json(conn, %{attachment: format_attachment(updated_attachment)})

            {:error, changeset} ->
              conn
              |> put_status(:unprocessable_entity)
              |> json(%{error: format_changeset_errors(changeset)})
          end
        else
          conn
          |> put_status(:unauthorized)
          |> json(%{error: "Unauthorized"})
        end
    end
  end

  defp handle_multiple_uploads(conn, files, params) do
    current_user = conn.assigns.current_user
    workspace_id = conn.assigns.current_workspace.id

    uuids = Map.get(params, "uuids", Enum.map(files, fn _ -> Ecto.UUID.generate() end))

    results =
      files
      |> Enum.zip(uuids)
      |> Enum.map(fn {upload, uuid} ->
        with {:ok, type} <- determine_attachment_type(upload.content_type),
             {:ok, metadata} <- validate_file(upload, type, current_user),
             {:ok, blob} <- get_blob(upload) do
          attrs = %{
            id: uuid,
            filename: upload.filename,
            content_type: upload.content_type,
            user_id: current_user.id,
            workspace_id: workspace_id,
            type: type,
            status: "pending",
            metadata: metadata,
            blob: blob
          }

          case Publishing.create_attachment(attrs) do
            {:ok, attachment} ->
              case enqueue_upload(attachment) do
                {:ok, _job} -> {:ok, attachment}
                {:error, reason} -> {:error, "Failed to schedule upload: #{inspect(reason)}"}
              end

            {:error, changeset} ->
              {:error, format_changeset_errors(changeset)}
          end
        end
      end)

    failed = Enum.filter(results, &(elem(&1, 0) == :error))
    succeeded = Enum.filter(results, &(elem(&1, 0) == :ok))

    cond do
      Enum.empty?(results) ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "No files provided"})

      Enum.empty?(failed) ->
        conn
        |> put_status(:created)
        |> json(%{
          attachments:
            Enum.map(succeeded, fn {:ok, attachment} -> format_attachment(attachment) end)
        })

      true ->
        error_messages = Enum.map(failed, fn {:error, msg} -> msg end)

        Logger.error("Failed to upload files", %{
          event: "api.attachments.upload_failed",
          errors: error_messages,
          user_id: current_user.id,
          workspace_id: workspace_id
        })

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: Enum.join(error_messages, "\n")})
    end
  end

  defp determine_attachment_type(content_type) do
    type =
      cond do
        String.starts_with?(content_type, "image/") -> :image
        String.starts_with?(content_type, "video/") -> :video
        true -> :file
      end

    {:ok, type}
  end

  defp validate_file(upload, :image, user) do
    with {:ok, %{size: size}} <- File.stat(upload.path),
         limit <- Features.get_limit(user, "image_size"),
         :ok <- validate_file_size(size, limit),
         image = Mogrify.open(upload.path),
         %{width: width, height: height} <- get_image_dimensions(image) do
      {:ok, %{"width" => width, "height" => height, "size" => size}}
    else
      {:error, :size_exceeded} ->
        limit_mb = trunc(Features.get_limit(user, "image_size") / (1024 * 1024))
        {:error, "Image size exceeds your plan's limit of #{limit_mb}MB"}

      _ ->
        {:error, "Invalid image file"}
    end
  end

  defp validate_file(upload, :video, user) do
    with {:ok, %{size: size}} <- File.stat(upload.path),
         limit <- Features.get_limit(user, "video_size"),
         :ok <- validate_file_size(size, limit) do
      {:ok, %{"size" => size}}
    else
      {:error, :size_exceeded} ->
        limit_mb = trunc(Features.get_limit(user, "video_size") / (1024 * 1024))
        {:error, "Video size exceeds your plan's limit of #{limit_mb}MB"}

      _ ->
        {:error, "Invalid video file"}
    end
  end

  defp validate_file_size(size, limit) when is_integer(size) and is_integer(limit) do
    if size <= limit, do: :ok, else: {:error, :size_exceeded}
  end

  defp get_image_dimensions(image) do
    case Mogrify.verbose(image) do
      %{width: width, height: height} -> %{width: width, height: height}
      _ -> %{width: 0, height: 0}
    end
  rescue
    _ -> %{width: 0, height: 0}
  end

  defp get_blob(upload) do
    case File.read(upload.path) do
      {:ok, blob} -> {:ok, blob}
      {:error, reason} -> {:error, reason}
    end
  end

  defp enqueue_upload(attachment) do
    %{attachment_id: attachment.id}
    |> worker_module().new()
    |> Oban.insert()
  end

  def worker_module,
    do: Application.get_env(:crosspost, :upload_worker, Crosspost.Workers.CloudinaryUploadWorker)

  defp format_attachment(attachment) do
    %{
      id: attachment.id,
      type: attachment.type,
      filename: attachment.filename,
      content_type: attachment.content_type,
      source_url: attachment.source_url,
      preview_url: attachment.preview_url,
      metadata: attachment.metadata,
      status: attachment.status
    }
  end

  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
        opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
      end)
    end)
    |> Enum.map_join("; ", fn {k, v} -> "#{Phoenix.Naming.humanize(k)} #{v}" end)
  end

  # Add this private function for URL validation
  defp validate_url(url) do
    uri = URI.parse(url)

    if uri.scheme in ["http", "https"] and not is_nil(uri.host) do
      :ok
    else
      {:error, "Invalid URL"}
    end
  end

  # Add helper function to ensure workspace is available
  defp ensure_workspace(conn, _opts) do
    workspace_id = get_workspace_id(conn)

    case Workspaces.get_workspace!(workspace_id) do
      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Workspace not found"})
        |> halt()

      workspace ->
        if Workspaces.user_in_workspace?(conn.assigns.current_user.id, workspace.id) do
          assign(conn, :current_workspace, workspace)
        else
          conn
          |> put_status(:unauthorized)
          |> json(%{error: "Unauthorized access to workspace"})
          |> halt()
        end
    end
  end

  # Helper to get workspace ID from params or session
  defp get_workspace_id(conn) do
    case conn.params do
      %{"workspace_id" => id} -> id
      _ -> get_session(conn, :workspace_id)
    end
  end

  defp storage do
    Application.get_env(:crosspost, :storage, Crosspost.Media.Storage)
  end

  # Add new action for signature generation
  def create_signature(conn, %{"type" => type} = _params) do
    upload_opts =
      %{
        resource_type: type,
        folder: conn.assigns.current_user.uid
      }
      |> Map.merge(cloudinary_opts(type))
      |> maybe_add_upload_preset()

    signature = storage().sign(upload_opts)

    conn
    |> put_status(:ok)
    |> json(%{signature: signature})
  end

  defp cloudinary_opts("image") do
    %{
      transformation: "f_auto,q_auto"
    }
  end

  defp cloudinary_opts("video") do
    %{
      eager: "f_auto,q_auto",
      eager_async: true
    }
  end

  defp maybe_add_upload_preset(opts) do
    case Application.get_env(:crosspost, :cloudinary_upload_preset) do
      preset when is_binary(preset) and preset != "" ->
        Map.put(opts, :upload_preset, preset)

      _ ->
        opts
    end
  end
end
