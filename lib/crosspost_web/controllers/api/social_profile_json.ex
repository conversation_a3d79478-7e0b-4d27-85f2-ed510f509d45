defmodule CrosspostWeb.API.SocialProfileJSON do
  alias Crosspost.Accounts.SocialProfile

  def index(%{profiles: profiles}) do
    %{data: for(profile <- profiles, do: data(profile))}
  end

  def show(%{profile: profile}) do
    %{data: data(profile)}
  end

  def search(%{profiles: profiles}) do
    %{data: profiles}
  end

  defp data(%SocialProfile{} = profile) do
    %{
      id: profile.id,
      name: profile.name,
      bsky_handle: profile.bsky_handle,
      x_handle: profile.x_handle,
      mastodon_handle: profile.mastodon_handle,
      type: "stored_profile"
    }
  end
end
