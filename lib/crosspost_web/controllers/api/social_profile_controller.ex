defmodule CrosspostWeb.API.SocialProfileController do
  use CrosspostWeb, :controller
  require Logger

  alias Crosspost.Accounts

  action_fallback CrosspostWeb.FallbackController

  def create(conn, %{"name" => _name} = params) do
    user = conn.assigns.current_user

    attrs = %{
      name: params["name"],
      user_id: user.id,
      bsky_handle: params["bsky_handle"],
      x_handle: params["x_handle"],
      mastodon_handle: params["mastodon_handle"]
    }

    with {:ok, profile} <- Accounts.create_social_profile(attrs) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/social-profiles/#{profile.id}")
      |> render(:show, profile: profile)
    end
  end

  def show(conn, %{"id" => id}) do
    user = conn.assigns.current_user

    with {:ok, profile} <- fetch_user_profile(user.id, id) do
      render(conn, :show, profile: profile)
    end
  end

  def update(conn, %{"id" => id} = params) do
    user = conn.assigns.current_user

    with {:ok, profile} <- fetch_user_profile(user.id, id),
         {:ok, updated_profile} <- Accounts.update_social_profile(profile, profile_params(params)) do
      render(conn, :show, profile: updated_profile)
    else
      {:error, %Ecto.Changeset{} = changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> put_view(json: CrosspostWeb.ChangesetJSON)
        |> render(:error, changeset: changeset)

      error ->
        error
    end
  end

  def delete(conn, %{"id" => id}) do
    user = conn.assigns.current_user

    with {:ok, profile} <- fetch_user_profile(user.id, id),
         {:ok, _deleted} <- Accounts.delete_social_profile(profile) do
      send_resp(conn, :no_content, "")
    end
  end

  def search(conn, %{"q" => query, "network" => network}) when byte_size(query) >= 3 do
    Logger.info("Profile search initiated with query: #{query}")

    user = conn.assigns.current_user
    connected_user = Accounts.get_connected_user!(user.id, conn.assigns.current_workspace.id)

    with {:ok, profiles} <- search_profiles(connected_user, query, network) do
      render(conn, :search, profiles: profiles)
    end
  end

  def search(conn, %{"q" => query}) when byte_size(query) < 3 do
    conn
    |> put_status(:bad_request)
    |> json(%{error: "Query must be at least 3 characters"})
  end

  def search(conn, %{"q" => _query}) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: "No network specified"})
  end

  # Private helpers

  defp fetch_user_profile(user_id, profile_id) do
    case Accounts.get_user_social_profile(user_id, profile_id) do
      nil -> {:error, :not_found}
      profile -> {:ok, profile}
    end
  end

  defp profile_params(params) do
    params
    |> Map.take(["name", "bsky_handle", "x_handle", "mastodon_handle"])
  end

  defp search_profiles(user, query, network) do
    case network do
      "bsky" -> search_bsky(user, query)
      "mastodon" -> search_mastodon(user, query)
      "x" -> search_x(user, query)
      _ -> {:error, "Invalid network"}
    end
  end

  defp search_mastodon(user, query) do
    case Accounts.get_connection(user, "mastodon") do
      nil ->
        {:ok, []}

      connection ->
        do_search_mastodon(
          connection.encrypted_access_token,
          connection.settings["instance"],
          query
        )
    end
  end

  defp do_search_mastodon(access_token, instance, query) do
    case mastodon_client().search_profiles(access_token, instance, query) do
      {:ok, profiles} ->
        {:ok, profiles}

      {:error, reason} ->
        Logger.error("Failed to search Mastodon profiles",
          event: "social_profiles.search",
          error: reason
        )

        {:ok, []}
    end
  end

  defp search_bsky(user, query) do
    Logger.debug("Searching Bluesky profiles for: #{query}")

    case Accounts.get_connection(user, "bsky") do
      nil ->
        {:ok, []}

      connection ->
        if System.get_env("E2E_TEST") == "true" do
          {:ok, []}
        else
          do_search_bsky(connection.encrypted_access_token, query, user)
        end
    end
  end

  defp do_search_bsky(access_token, query, user, tried_refresh \\ false) do
    case bsky_client().search_profiles(access_token, query) do
      {:ok, profiles} ->
        formatted_profiles =
          Enum.map(profiles, fn profile ->
            %{
              id: "bsky-#{profile["handle"]}",
              label: profile["handle"],
              network: "bsky",
              handle: profile["handle"],
              display_name: profile["displayName"] || profile["handle"],
              avatar: profile["avatar"] || "https://www.gravatar.com/avatar/0?d=mp",
              description: profile["description"]
            }
          end)

        {:ok, formatted_profiles}

      {:error, :expired_token} when not tried_refresh ->
        Logger.info("Bluesky token expired, attempting refresh")

        case Accounts.refresh_user_bsky_token(user) do
          {:ok, updated_user} ->
            # Get the fresh connection to get the new token
            case Accounts.get_connection(updated_user, "bsky") do
              nil ->
                {:error, "Failed to get refreshed connection"}

              refreshed_connection ->
                do_search_bsky(
                  refreshed_connection.encrypted_access_token,
                  query,
                  updated_user,
                  true
                )
            end

          {:error, reason} ->
            Logger.error("Failed to refresh Bluesky token: #{inspect(reason)}")
            {:error, "Failed to refresh token"}
        end

      {:error, reason} when is_binary(reason) ->
        {:error, reason}

      {:error, _reason} ->
        {:error, "Failed to search profiles"}
    end
  end

  def search_x(user, query) do
    case Accounts.get_connection(user, "twitter") do
      nil ->
        {:ok, []}

      connection ->
        # Use OAuth 1.0a tokens from the connection
        do_search_x(
          connection.encrypted_access_token,
          connection.encrypted_refresh_token,
          query
        )
    end
  end

  defp do_search_x(access_token, refresh_token, query) do
    case x_client().search_profiles(access_token, refresh_token, query) do
      {:ok, profiles} ->
        formatted_profiles =
          Enum.map(profiles, fn profile ->
            %{
              id: "x-#{profile["screen_name"]}",
              label: profile["screen_name"],
              network: "x",
              handle: profile["screen_name"],
              display_name: profile["name"] || profile["screen_name"],
              avatar: profile["profile_image_url_https"] || "/images/default_avatar.png",
              description: profile["description"]
            }
          end)

        {:ok, formatted_profiles}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp bsky_client do
    Application.get_env(:crosspost, :bsky_client, Crosspost.Accounts.Bsky.Client)
  end

  defp mastodon_client do
    Application.get_env(:crosspost, :mastodon_client, Crosspost.Accounts.Mastodon.Client)
  end

  defp x_client do
    Application.get_env(:crosspost, :x_client, Crosspost.Accounts.X.Client)
  end
end
