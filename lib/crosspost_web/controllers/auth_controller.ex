defmodule CrosspostWeb.AuthController do
  use CrosspostWeb, :controller

  plug :store_auth_intent
  plug Ueberauth

  alias <PERSON><PERSON>.Accounts

  import CrosspostWeb.UserAuth, only: [log_in_user: 2]

  require Logger

  defp store_auth_intent(conn, _opts) do
    conn =
      case conn.params do
        %{"intent" => intent} ->
          Logger.info("Storing auth intent",
            event: "auth.store_intent",
            intent: intent
          )

          put_session(conn, :auth_intent, intent)

        _ ->
          conn
      end

    # Store return_to parameter if present
    case conn.params do
      %{"return_to" => return_to} ->
        Logger.info("Storing return_to path",
          event: "auth.store_return_to",
          return_to: return_to
        )

        put_session(conn, :user_return_to, return_to)

      _ ->
        conn
    end
  end

  def callback(%{assigns: %{ueberauth_auth: auth, current_user: current_user}} = conn, %{
        "provider" => provider
      })
      when not is_nil(current_user) do
    current_workspace = conn.assigns[:current_workspace]
    provider = String.downcase(provider)

    Logger.info("Handling OAuth callback for connected user",
      event: "auth.callback.connected_user",
      provider: provider,
      user_id: current_user.id,
      workspace_id: current_workspace && current_workspace.id,
      auth_info: %{
        uid: auth.uid,
        provider: auth.provider,
        strategy: auth.strategy
      }
    )

    case Accounts.update_user_from_auth(provider, current_user, current_workspace, auth) do
      {:ok, _user} ->
        Logger.info("Successfully connected provider",
          event: "auth.connect.success",
          provider: provider,
          user_id: current_user.id,
          workspace_id: current_workspace && current_workspace.id
        )

        if provider == "x" do
          fetch_and_update_x_profile(current_user.id, current_workspace.id)
        end

        # Clear needs_reconnect flag from the connection's settings
        case Accounts.get_connection(current_user.id, provider, current_workspace.id) do
          nil ->
            Logger.error("Connection not found after update",
              event: "auth.connect.error.connection_not_found",
              provider: provider,
              user_id: current_user.id,
              workspace_id: current_workspace && current_workspace.id
            )

          connection ->
            updated_settings = Map.delete(connection.settings || %{}, "needs_reconnect")
            Accounts.update_connection(connection, %{settings: updated_settings})
        end

        # Get return_to path or default to social connections
        return_to = get_session(conn, :user_return_to) || ~p"/settings/social-connections"

        conn
        |> delete_session(:user_return_to)
        |> put_flash(:info, "Successfully connected with #{String.capitalize(provider)}.")
        |> redirect(to: return_to)

      {:error, %Ecto.Changeset{errors: [mastodon_id: {msg, _}]} = changeset}
      when provider == "mastodon" ->
        Logger.error("Failed to connect with Mastodon - invalid instance",
          event: "auth.connect.error.mastodon_invalid",
          provider: provider,
          error: inspect(changeset),
          user_id: current_user.id,
          workspace_id: current_workspace && current_workspace.id
        )

        conn
        |> put_flash(:error, msg)
        |> redirect(to: ~p"/settings/social-connections")

      {:error, %Ecto.Changeset{} = changeset} ->
        Logger.error("Failed to connect with provider - validation error",
          event: "auth.connect.error.validation",
          provider: provider,
          error: inspect(changeset),
          user_id: current_user.id,
          workspace_id: current_workspace && current_workspace.id
        )

        conn
        |> put_flash(:error, "Failed to connect with #{String.capitalize(provider)}.")
        |> redirect(to: ~p"/settings/social-connections")

      {:error, reason} ->
        Logger.error("Failed to connect with provider - unexpected error",
          event: "auth.connect.error.unexpected",
          provider: provider,
          error: inspect(reason),
          user_id: current_user.id,
          workspace_id: current_workspace && current_workspace.id
        )

        conn
        |> put_flash(:error, "Failed to connect with #{String.capitalize(provider)}.")
        |> redirect(to: ~p"/settings/social-connections")
    end
  end

  def callback(%{assigns: %{ueberauth_auth: auth}} = conn, %{"provider" => provider}) do
    auth_intent = get_session(conn, :auth_intent)
    provider = String.downcase(provider)

    Logger.info("Handling OAuth callback for new user",
      event: "auth.callback.new_user",
      provider: provider,
      auth_intent: auth_intent,
      auth_info: %{
        uid: auth.uid,
        provider: auth.provider,
        strategy: auth.strategy,
        info: %{
          email: auth.info.email,
          name: auth.info.name
        }
      }
    )

    case auth_intent do
      "sign-in" ->
        Logger.info("Attempting sign in with provider",
          event: "auth.sign_in.attempt",
          provider: provider
        )

        case Accounts.get_user_by_oauth(provider, auth.uid) do
          {:ok, user} ->
            Logger.info("Successfully signed in with provider",
              event: "auth.sign_in.success",
              provider: provider,
              user_id: user.id
            )

            conn
            |> delete_session(:auth_intent)
            |> put_flash(:info, "Successfully signed in with #{String.capitalize(provider)}.")
            |> log_in_user(user)

          {:error, :not_found} ->
            Logger.warning("No user found for OAuth credentials",
              event: "auth.sign_in.not_found",
              provider: provider,
              auth_uid: auth.uid
            )

            conn
            |> delete_session(:auth_intent)
            |> put_flash(
              :error,
              "No account found for this #{String.capitalize(provider)} account. Please sign up first."
            )
            |> redirect(to: ~p"/users/sign-in")
        end

      _ ->
        oauth_auth = %{
          provider: provider,
          uid: auth.uid,
          info: Map.from_struct(auth.info),
          credentials: Map.from_struct(auth.credentials)
        }

        Logger.info("Redirecting to OAuth registration completion",
          event: "auth.sign_up.email_completion",
          provider: provider,
          auth_info: auth.info,
          has_email: not is_nil(auth.info.email)
        )

        conn
        |> put_session(:pending_oauth_auth, oauth_auth)
        |> redirect(to: ~p"/users/sign-up/oauth")
    end
  end

  def callback(
        %{
          assigns: %{
            ueberauth_failure: %{
              errors: [%{message_key: "oauth2_error", message: "rate_limit_exceeded"}]
            }
          }
        } = conn,
        params
      ) do
    Logger.warning("OAuth rate limit exceeded",
      event: "auth.callback.rate_limit",
      provider: params["provider"]
    )

    conn
    |> put_flash(
      :error,
      "LinkedIn's rate limit has been reached. Please try again in a few minutes."
    )
    |> redirect(to: ~p"/users/sign-in")
  end

  def callback(%{assigns: %{ueberauth_failure: fails}} = conn, params) do
    error_message =
      case fails do
        %{errors: [%{message_key: "revoked_token", message: msg}]} ->
          msg

        %{errors: [%{message_key: "oauth_error", message: msg}]} ->
          msg

        %{errors: errors} ->
          error_messages =
            errors
            |> Enum.map(fn %{message: message, message_key: _key} ->
              case message do
                "Unknown error: " <> _ -> "Authentication failed. Please try again."
                message -> message
              end
            end)
            |> Enum.join(", ")

          error_messages
      end

    Logger.error("OAuth callback failed",
      event: "auth.callback.error",
      provider: params["provider"],
      error_details: %{
        message: error_message,
        errors: Enum.map(fails.errors, &Map.take(&1, [:message, :message_key]))
      }
    )

    conn
    |> put_flash(:error, error_message)
    |> redirect(to: ~p"/users/sign-in")
  end

  defp fetch_and_update_x_profile(user_id, workspace_id) do
    case Accounts.get_connection(user_id, "x", workspace_id) do
      nil ->
        Logger.error("Connection not found after creation",
          event: "auth.connect.error.connection_not_found",
          provider: "x",
          user_id: user_id,
          workspace_id: workspace_id
        )

      connection ->
        case x_client().get_profile(
               connection.encrypted_access_token,
               connection.platform_user_id
             ) do
          {:ok, profile} ->
            Accounts.update_connection(connection, %{
              info: Map.merge(connection.info, profile)
            })

          {:error, error} ->
            Logger.error("Failed to fetch X profile",
              event: "auth.connect.error.profile_fetch",
              error: error,
              provider: "x",
              user_id: user_id,
              workspace_id: workspace_id
            )
        end
    end
  end

  defp x_client do
    Application.get_env(:crosspost, :x_client, Crosspost.Accounts.X.Client)
  end
end
