defmodule CrosspostWeb.WorkspaceInvitationController do
  use CrosspostWeb, :controller

  require Logger

  alias Cross<PERSON>.Accounts
  alias Crosspost.Workspaces

  def join(conn, %{"slug" => slug, "code" => code}) do
    Logger.debug("Processing join request", slug: slug, code: code)

    workspace = Workspaces.get_workspace_by_slug!(slug)

    Logger.debug("Found workspace",
      workspace_id: workspace.id,
      workspace_name: workspace.name,
      invitations: workspace.invitations
    )

    case Accounts.Workspace.find_invitation(workspace, code) do
      nil ->
        Logger.debug("No valid invitation found", code: code)

        conn
        |> put_flash(:error, "Invalid or expired invitation")
        |> redirect(to: ~p"/")

      invitation ->
        Logger.debug("Found valid invitation",
          code: code,
          email: invitation["email"]
        )

        if conn.assigns.current_user do
          if Workspaces.user_in_workspace?(conn.assigns.current_user, workspace) do
            conn
            |> put_flash(:info, "You are already a member of #{workspace.name}")
            |> redirect(to: ~p"/dashboard")
          else
            conn
            |> put_root_layout(html: {CrosspostWeb.Layouts, :user})
            |> put_layout(html: {CrosspostWeb.Layouts, :static})
            |> render(:confirm,
              workspace: workspace,
              invitation: invitation,
              page_title: "Join #{workspace.name}"
            )
          end
        else
          conn
          |> put_session(:after_sign_in_path, ~p"/workspaces/#{slug}/join/#{code}")
          |> redirect(to: ~p"/users/sign-in")
        end
    end
  end

  def confirm(conn, %{"slug" => slug, "code" => code}) do
    workspace = Workspaces.get_workspace_by_slug!(slug)

    case Accounts.Workspace.find_invitation(workspace, code) do
      nil ->
        conn
        |> put_flash(:error, "Invalid or expired invitation")
        |> redirect(to: ~p"/")

      invitation ->
        handle_join_workspace(conn, workspace, invitation)
    end
  end

  defp handle_join_workspace(conn, workspace, invitation) do
    user = conn.assigns.current_user

    Logger.debug("Handling workspace join",
      user_id: user.id,
      workspace_id: workspace.id,
      invitation_code: invitation["code"]
    )

    case Workspaces.add_user_to_workspace(workspace, user) do
      {:ok, _workspace_user} ->
        case workspace
             |> Accounts.Workspace.remove_invitation(invitation["code"])
             |> Workspaces.update_workspace(%{}) do
          {:ok, _workspace} ->
            conn
            |> put_session(:workspace_id, workspace.id)
            |> put_flash(:info, "Successfully joined #{workspace.name}")
            |> redirect(to: ~p"/dashboard")

          {:error, _changeset} ->
            conn
            |> put_session(:workspace_id, workspace.id)
            |> put_flash(:info, "Successfully joined #{workspace.name}")
            |> redirect(to: ~p"/dashboard")
        end

      {:error, _changeset} ->
        if Workspaces.user_in_workspace?(user, workspace) do
          conn
          |> put_session(:workspace_id, workspace.id)
          |> put_flash(:info, "Successfully joined #{workspace.name}")
          |> redirect(to: ~p"/dashboard")
        else
          conn
          |> put_flash(:error, "Failed to join workspace")
          |> redirect(to: ~p"/")
        end
    end
  end
end
