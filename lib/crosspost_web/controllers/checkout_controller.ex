defmodule CrosspostWeb.CheckoutController do
  use CrosspostWeb, :controller

  def create(conn, %{"price_id" => price_id}) do
    # Get the current user from the connection
    current_user = conn.assigns.current_user

    # Base params for the checkout session
    params = %{
      payment_method_types: ["card"],
      line_items: [
        %{
          price: price_id,
          quantity: 1
        }
      ],
      mode: "subscription",
      success_url:
        "#{CrosspostWeb.Endpoint.url()}/payment/success?session_id={CHECKOUT_SESSION_ID}",
      cancel_url: "#{CrosspostWeb.Endpoint.url()}/plans",
      billing_address_collection: "required",
      payment_method_collection: "always"
    }

    # Add coupon for test users (with justcrosspost.app email domain)
    params =
      if is_test_user?(current_user) and not is_dev?() do
        Map.put(params, :discounts, [%{coupon: "6Lh2QGqw"}])
      else
        params
      end

    case Stripe.Checkout.Session.create(params) do
      {:ok, session} ->
        conn
        |> redirect(external: session.url)

      {:error, %Stripe.Error{} = error} ->
        conn
        |> put_flash(:error, "Error creating checkout session: #{error.message}")
        |> redirect(to: ~p"/plans")
    end
  end

  def create(conn, _params) do
    conn
    |> put_flash(:error, "No price selected")
    |> redirect(to: ~p"/plans")
  end

  # Helper function to check if a user is a test user
  defp is_test_user?(nil), do: false

  defp is_test_user?(user) do
    email = user.email
    String.contains?(email, "@justcrosspost.app")
  end

  defp is_dev? do
    Application.get_env(:crosspost, :env, :dev) in [:dev, :test]
  end
end
