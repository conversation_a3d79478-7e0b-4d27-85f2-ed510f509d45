defmodule CrosspostWeb.Endpoint do
  use Sentry.PlugCapture
  use Phoenix.Endpoint, otp_app: :crosspost

  plug Sentry.PlugContext

  # The session will be stored in the cookie and signed,
  # this means its contents can be read but not tampered with.
  # Set :encryption_salt if you would also like to encrypt it.
  @session_options [
    store: :cookie,
    key: "_just_crosspost_key",
    signing_salt: "JRXKz6NA",
    same_site: "Lax"
  ]

  socket "/live", Phoenix.LiveView.Socket,
    websocket: [
      connect_info: [session: @session_options],
      channel_routes: [
        "user:*": CrosspostWeb.UserChannel
      ]
    ],
    longpoll: [connect_info: [session: @session_options]]

  # Serve at "/" the static files from "priv/static" directory.
  #
  # You should set gzip to true if you are running phx.digest
  # when deploying your static files in production.
  plug Plug.Static,
    at: "/",
    from: :crosspost,
    gzip: true,
    only: CrosspostWeb.static_paths()

  # Code reloading can be explicitly enabled under the
  # :code_reloader configuration of your endpoint.
  if code_reloading? do
    socket "/phoenix/live_reload/socket", Phoenix.LiveReloader.Socket
    plug Phoenix.LiveReloader
    plug Phoenix.CodeReloader
    plug Phoenix.Ecto.CheckRepoStatus, otp_app: :crosspost
  end

  plug Phoenix.LiveDashboard.RequestLogger,
    param_key: "request_logger",
    cookie_key: "request_logger"

  plug Plug.RequestId
  plug Plug.Telemetry, event_prefix: [:phoenix, :endpoint]

  plug Stripe.WebhookPlug,
    at: "/webhooks/stripe",
    handler: CrosspostWeb.StripeHandler,
    secret: {Application, :get_env, [:crosspost, :stripe_webhook_secret]}

  plug Plug.Parsers,
    parsers: [:urlencoded, :multipart, :json],
    pass: ["*/*"],
    json_decoder: Phoenix.json_library(),
    length: 60_000_000,
    multipart_options: [
      max_file_size: 60_000_000
    ]

  plug Plug.MethodOverride
  plug Plug.Head
  plug Plug.Session, @session_options

  if Mix.env() == :test do
    plug Plug.Static,
      at: "/uploads/test",
      from: "priv/static/uploads/test",
      gzip: false
  end

  plug CrosspostWeb.Plugs.RateLimit
  plug CrosspostWeb.Plugs.FormProtectionPlug

  plug CrosspostWeb.Router
end
