defmodule CrosspostWeb.Router do
  use CrosspostWeb, :router

  import Plug.BasicAuth
  import Phoenix.Controller
  import Phoenix.LiveView.Router
  import Oban.Web.Router

  defimpl Plug.Exception, for: Phoenix.NotAcceptableError do
    def status(_exception), do: 404
    def actions(_exception), do: []
  end

  defimpl Plug.Exception, for: Plug.CSRFProtection.InvalidCSRFTokenError do
    def status(_exception), do: 404
    def actions(_exception), do: []
  end

  pipeline :admin_browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :protect_from_forgery
    plug :put_secure_browser_headers
  end

  pipeline :admin_auth do
    if Application.compile_env(:crosspost, :env) != :test do
      plug :basic_auth, username: "solnic_crosspost_admin", password: "8TjkMgg7xoVzHmnB"
    end

    plug :put_root_layout, {CrosspostWeb.Layouts, :admin}
  end

  import CrosspostWeb.UserAuth

  pipeline :browser do
    plug CrosspostWeb.Plugs.RedirectDomain
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
    plug :fetch_sidebar_state
  end

  pipeline :public_browser do
    plug CrosspostWeb.Plugs.RedirectDomain
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {CrosspostWeb.Layouts, :public}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
    plug :fetch_sidebar_state
  end

  pipeline :user_browser do
    plug CrosspostWeb.Plugs.RedirectDomain
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {CrosspostWeb.Layouts, :user}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
    plug :fetch_sidebar_state
  end

  pipeline :api do
    plug :accepts, ["json"]
    plug :fetch_session
    plug :protect_from_forgery
    plug :fetch_current_user
  end

  pipeline :workspace do
    plug CrosspostWeb.Plugs.WorkspacePlug
  end

  pipeline :auth_user do
    plug :require_authenticated_user
    plug CrosspostWeb.Plugs.CheckSubscription
  end

  # ============================================================
  # Admin routes
  # ============================================================

  scope "/admin", CrosspostWeb.Admin do
    pipe_through [:admin_browser, :admin_auth]

    oban_dashboard("/oban")

    get "/users/export/csv", ExportController, :download_users_csv

    live_session :admin, layout: {CrosspostWeb.Layouts, :admin_app} do
      live "/", DashboardLive, :index
      live "/users", UsersLive, :index
      live "/users/:id", UsersLive.Show, :show
      live "/users/:id/edit", UsersLive.Edit, :edit
      live "/waitlist", WaitlistLive, :index
      live "/waitlist/:id", WaitlistLive.Show, :show
      live "/features", FeaturesLive, :index
      live "/features/new", FeaturesLive.New, :new
      live "/features/:key", FeaturesLive.Show, :show
      live "/features/:key/edit", FeaturesLive.Edit, :edit
      live "/workspaces", WorkspacesLive, :index
      live "/workspaces/:id", WorkspacesLive.Show, :show
      live "/workspaces/:id/edit", WorkspacesLive.Edit, :edit
      live "/posts", PostsLive, :index
      live "/posts/:id", PostsLive.Show, :show
      live "/posts/:id/edit", PostsLive.Edit, :edit
      live "/jobs", JobsLive, :index
      live "/jobs/:id", JobsLive.Show, :show
      live "/plans", PlansLive, :index
      live "/plans/:key", PlansLive.Show, :show
      live "/plans/:key/edit", PlansLive.Edit, :edit
      live "/attachments", AttachmentsLive.Index, :index
      live "/attachments/:id", AttachmentsLive.Show, :show
      live "/attachments/:id/edit", AttachmentsLive.Edit, :edit
      live "/marketing", MarketingLive, :index
    end
  end

  # ============================================================
  # Public routes
  # ============================================================

  # Static pages - accessible to both logged-in and non-logged-in users
  scope "/", CrosspostWeb do
    pipe_through [:public_browser, :workspace]

    get "/", PageController, :home_or_dashboard
    get "/about", PageController, :about
    get "/tos", PageController, :tos
    get "/contact", PageController, :contact
    get "/privacy", PageController, :privacy
    get "/plans", PageController, :plans
    get "/unsubscribed", PageController, :unsubscribed

    scope "/workspaces" do
      get "/:slug/join/:code", WorkspaceInvitationController, :join
      get "/:slug/join/:code/confirm", WorkspaceInvitationController, :confirm
    end
  end

  scope "/auth", CrosspostWeb do
    pipe_through [:public_browser, :workspace]

    get "/:provider", AuthController, :request
    get "/:provider/callback", AuthController, :callback
  end

  scope "/", CrosspostWeb do
    pipe_through [:public_browser, :workspace, :redirect_if_user_is_authenticated]

    live_session :redirect_if_user_is_authenticated,
      on_mount: [{CrosspostWeb.UserAuth, :redirect_if_user_is_authenticated}],
      layout: {CrosspostWeb.Layouts, :static} do
      live "/users/sign-in", UserLoginLive, :new
      live "/users/reset_password", UserForgotPasswordLive, :new
      live "/users/reset_password/:token", UserResetPasswordLive, :edit
      live "/users/sign-up", UserRegistrationLive, :new
      live "/users/sign-up/oauth", UserRegistrationLive.OAuth, :new
      live "/users/sign-up/:invitation_code", UserRegistrationLive, :new
    end

    post "/users/sign-in", UserSessionController, :create
  end

  scope "/", CrosspostWeb do
    pipe_through [:public_browser, :workspace]

    get "/users/sign-out", UserSessionController, :delete

    live_session :confirmation,
      on_mount: [{CrosspostWeb.UserAuth, :mount_current_user}],
      layout: {CrosspostWeb.Layouts, :static} do
      live "/users/confirm/:token", UserConfirmationLive, :edit
      live "/users/confirm", UserConfirmationInstructionsLive, :new
    end
  end

  # ============================================================
  # Authorized routes
  # ============================================================

  # Authorized routes without workspace context (defaults to "default" workspace)
  scope "/", CrosspostWeb do
    pipe_through [:user_browser, :require_authenticated_user, :workspace]

    get "/payment/success", PaymentController, :success
    post "/subscription/update", SubscriptionController, :update

    resources "/checkout", CheckoutController, only: [:create]
  end

  scope "/", CrosspostWeb do
    pipe_through [:user_browser, :auth_user, :workspace]

    get "/workspace/:workspace_slug", WorkspaceController, :switch

    live_session :default_workspace,
      on_mount: [
        {CrosspostWeb.UserAuth, :ensure_authenticated},
        {CrosspostWeb.WorkspaceHooks, :default},
        {CrosspostWeb.Live.Hooks.SubscriptionStatus, :default}
      ],
      layout: {CrosspostWeb.Layouts, :app} do
      live "/dashboard", AppLive, :show
      live "/calendar", AppLive, :calendar

      live "/posts/new", AppLive, :new
      live "/posts/:id", AppLive, :show
      live "/posts/:id/edit", AppLive, :edit

      live "/settings", UserSettingsLive, :show
      live "/settings/:section", UserSettingsLive, :show
    end
  end

  scope "/api", CrosspostWeb.API do
    pipe_through [:api, :require_authenticated_api_user, :workspace]

    resources "/attachments", AttachmentController, only: [:create, :delete, :show, :update]
    post "/attachments/signature", AttachmentController, :create_signature

    get "/social-profiles/search", SocialProfileController, :search
    resources "/social-profiles", SocialProfileController, except: [:index, :new, :edit]

    post "/ai/posts/tags", AIController, :generate_tags
  end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:crosspost, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :user_browser

      live_session :dev,
        on_mount: [],
        layout: {CrosspostWeb.Layouts, :dev} do
        live "/published-posts", CrosspostWeb.Dev.PublishedPostsLive
      end

      live_dashboard "/dashboard", metrics: CrosspostWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end
  end

  defp fetch_sidebar_state(conn, _opts) do
    sidebar_state = conn.cookies["sidebar_state"]
    put_session(conn, :sidebar_state, sidebar_state)
  end

  scope "/users", CrosspostWeb do
    pipe_through [:public_browser, :redirect_if_user_is_authenticated]

    post "/sign-in/oauth", UserSessionController, :create_oauth
    post "/sign-in", UserSessionController, :create
  end

  pipeline :require_invitation do
    plug :validate_invitation_code
  end

  defp validate_invitation_code(conn, _opts) do
    conn
    |> redirect(to: "/users/sign-up")
    |> halt()
  end
end
