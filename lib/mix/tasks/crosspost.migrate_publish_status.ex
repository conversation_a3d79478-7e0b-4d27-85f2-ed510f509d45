defmodule Mix.Tasks.Crosspost.MigratePublishStatus do
  use Mix.Task
  import Ecto.Query
  require Logger

  alias Crosspost.Repo
  alias Crosspost.Publishing.{Post, PostStatus}

  @shortdoc "Migrates publish_status data from posts table to post_statuses table"

  def run(_) do
    [:postgrex, :ecto, :crosspost]
    |> Enum.each(&Application.ensure_all_started/1)

    migrate_publish_status()
  end

  defp migrate_publish_status do
    Logger.info("Starting publish status migration...")

    # Get all posts with publish_status
    posts_with_status =
      Post
      |> where([p], not is_nil(p.publish_status))
      |> select([p], {p.id, p.publish_status})
      |> Repo.all()

    Logger.info("Found #{length(posts_with_status)} posts with publish status")

    Repo.transaction(fn ->
      Enum.each(posts_with_status, fn {post_id, publish_status} ->
        migrate_post_status(post_id, publish_status)
      end)
    end)

    Logger.info("Migration completed successfully")
  end

  defp migrate_post_status(post_id, publish_status) when is_map(publish_status) do
    Enum.each(publish_status, fn {network, is_published} ->
      # Convert network string to atom and handle any network name changes
      network_atom = convert_network_name(network)

      attrs = %{
        post_id: post_id,
        network: network_atom,
        status: if(is_published, do: :published, else: :failed),
        outcome: nil
      }

      case Repo.get_by(PostStatus, post_id: post_id, network: network_atom) do
        nil ->
          %PostStatus{}
          |> PostStatus.changeset(attrs)
          |> Repo.insert!()

        existing ->
          existing
          |> PostStatus.changeset(attrs)
          |> Repo.update!()
      end
    end)
  end

  defp migrate_post_status(_post_id, nil), do: :ok

  # Handle network name conversions
  defp convert_network_name("twitter"), do: :x
  defp convert_network_name(network) when is_binary(network), do: String.to_existing_atom(network)
end
