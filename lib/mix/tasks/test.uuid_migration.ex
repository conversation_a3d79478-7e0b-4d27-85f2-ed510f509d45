defmodule Mix.Tasks.Test.UuidMigration do
  use Mix.Task

  alias Crosspost.Repo
  alias Crosspost.Publishing.{Post, PostContent, PostStatus, Attachment}
  import Ecto.Query

  @shortdoc "Tests the UUID migration by running migrations and verifying data integrity"
  # Version before UUID migrations started
  @last_regular_version 20_241_120_082_201

  def run(_) do
    # Start only Ecto repos
    Mix.Task.run("app.config")

    # Drop and recreate database
    Mix.Task.run("ecto.drop")
    Mix.Task.run("ecto.create")

    {:ok, _} = Application.ensure_all_started(:ecto_sql)

    # Step 1: Run migrations up to the last regular version
    {:ok, _, _} =
      Ecto.Migrator.with_repo(Repo, &Ecto.Migrator.run(&1, :up, to: @last_regular_version))

    # Step 2: Load test data
    Mix.Task.run("test.seed")

    # Step 3: Store counts and sample records before UUID migration
    pre_migration_state = capture_pre_migration_state()

    # Step 4: Run remaining migrations
    Mix.Task.run("ecto.migrate")

    # Step 5: Verify data integrity
    verify_data_integrity(pre_migration_state)
  end

  defp capture_pre_migration_state do
    %{
      counts: %{
        posts: Repo.aggregate(Post, :count),
        post_content: Repo.aggregate(PostContent, :count),
        post_statuses: Repo.aggregate(PostStatus, :count),
        attachments: Repo.aggregate(Attachment, :count)
      },
      sample_records: capture_sample_records(),
      relationships: capture_relationships()
    }
  end

  defp capture_sample_records do
    # Capture full records with associations for better comparison
    posts =
      Repo.all(
        from p in Post,
          preload: [:content, :post_statuses, :schedules],
          limit: 3
      )

    # Transform records to comparable format
    Enum.map(posts, fn post ->
      %{
        original_id: post.id,
        status: post.status,
        content_count: length(post.content),
        status_count: length(post.post_statuses),
        schedule_count: length(post.schedules),
        content_texts: Enum.map(post.content, & &1.text),
        status_networks: Enum.map(post.post_statuses, & &1.network)
      }
    end)
  end

  defp capture_relationships do
    # Capture a sample post with all its relationships
    post =
      Repo.one(
        from p in Post,
          where: p.status == "published",
          preload: [
            :content,
            :post_statuses,
            :schedules,
            content: :attachments
          ],
          limit: 1
      )

    case post do
      nil ->
        Mix.raise("No published post found in test data")

      post ->
        %{
          post_id: post.id,
          content:
            Enum.map(post.content, fn content ->
              %{
                id: content.id,
                text: content.text,
                attachment_count: length(content.attachments)
              }
            end),
          status_count: length(post.post_statuses),
          schedule_count: length(post.schedules)
        }
    end
  end

  defp verify_data_integrity(pre_migration_state) do
    # Verify counts
    verify_counts(pre_migration_state.counts)

    # Verify sample records maintained their relationships
    verify_sample_records(pre_migration_state.sample_records)

    # Verify specific relationships
    verify_relationships(pre_migration_state.relationships)

    Mix.shell().info([:green, "✓ UUID migration verification completed successfully"])
  end

  defp verify_counts(pre_counts) do
    current_counts = %{
      posts: Repo.aggregate(Post, :count),
      post_content: Repo.aggregate(PostContent, :count),
      post_statuses: Repo.aggregate(PostStatus, :count),
      attachments: Repo.aggregate(Attachment, :count)
    }

    Enum.each(pre_counts, fn {table, count} ->
      if current_counts[table] != count do
        Mix.raise("""
        Count mismatch for #{table}:
        - Before migration: #{count}
        - After migration: #{current_counts[table]}
        """)
      end
    end)

    Mix.shell().info("✓ Record counts verified")
  end

  defp verify_sample_records(pre_records) do
    current_records =
      Repo.all(
        from p in Post,
          preload: [:content, :post_statuses, :schedules]
      )
      |> Enum.map(fn post ->
        %{
          status: post.status,
          content_count: length(post.content),
          status_count: length(post.post_statuses),
          schedule_count: length(post.schedules),
          content_texts: Enum.map(post.content, & &1.text),
          status_networks: Enum.map(post.post_statuses, & &1.network)
        }
      end)

    # Verify that all pre-migration records exist in current records
    Enum.each(pre_records, fn pre_record ->
      unless Enum.any?(current_records, fn current ->
               current.content_texts == pre_record.content_texts &&
                 current.status_networks == pre_record.status_networks &&
                 current.status == pre_record.status
             end) do
        Mix.raise("Failed to find matching record after migration")
      end
    end)

    Mix.shell().info("✓ Sample records verified")
  end

  defp verify_relationships(pre_relations) do
    # Find a post with the same content text to verify relationships
    sample_content = hd(pre_relations.content)

    post =
      Repo.one(
        from p in Post,
          join: pc in assoc(p, :content),
          where: pc.text == ^sample_content.text,
          preload: [
            :content,
            :post_statuses,
            :schedules,
            content: :attachments
          ]
      )

    case post do
      nil ->
        Mix.raise("Failed to find post after migration")

      post ->
        verify_post_relationships(post, pre_relations)
    end

    Mix.shell().info("✓ Relationships verified")
  end

  defp verify_post_relationships(post, pre_relations) do
    # Verify content count and texts match
    current_content =
      Enum.map(post.content, &%{text: &1.text, attachment_count: length(&1.attachments)})

    pre_content =
      Enum.map(pre_relations.content, &%{text: &1.text, attachment_count: &1.attachment_count})

    unless Enum.sort_by(current_content, & &1.text) == Enum.sort_by(pre_content, & &1.text) do
      Mix.raise("Post content doesn't match after migration")
    end

    # Verify status counts match
    unless length(post.post_statuses) == pre_relations.status_count do
      Mix.raise("Post status count doesn't match after migration")
    end

    # Verify schedule counts match
    unless length(post.schedules) == pre_relations.schedule_count do
      Mix.raise("Post schedule count doesn't match after migration")
    end
  end
end
