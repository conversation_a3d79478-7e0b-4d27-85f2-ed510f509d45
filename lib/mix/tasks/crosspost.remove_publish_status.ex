defmodule Mix.Tasks.Crosspost.RemovePublishStatus do
  use Mix.Task
  require Logger

  @shortdoc "Removes publish_status column from posts table"

  def run(_) do
    [:postgrex, :ecto, :crosspost]
    |> Enum.each(&Application.ensure_all_started/1)

    Logger.info("Removing publish_status column from posts table...")

    sql = "ALTER TABLE posts DROP COLUMN IF EXISTS publish_status"

    case Crosspost.Repo.query(sql) do
      {:ok, _} ->
        Logger.info("Successfully removed publish_status column")

      {:error, error} ->
        Logger.error("Failed to remove publish_status column: #{inspect(error)}")
        exit({:shutdown, 1})
    end
  end
end
