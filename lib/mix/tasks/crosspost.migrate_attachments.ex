defmodule Mix.Tasks.Crosspost.MigrateAttachments do
  use Mix.Task
  require Logger

  alias Crosspost.Repo
  alias Crosspost.Publishing.PostContentAttachment

  @shortdoc "Migrates attachments to the new many-to-many structure"
  def run(_) do
    Logger.info("Starting attachment migration...")

    {:ok, _} = Application.ensure_all_started(:crosspost)

    # Get all attachments that have a post_content_id
    attachments_query = """
    SELECT id, post_content_id
    FROM attachments
    WHERE post_content_id IS NOT NULL
    """

    case Repo.query(attachments_query) do
      {:ok, %{rows: []}} ->
        Logger.info("No attachments found that need migration")

      {:ok, %{rows: attachments}} ->
        Logger.info("Found #{length(attachments)} attachments to migrate")

        Enum.each(attachments, fn [attachment_id, post_content_id] ->
          params = %{
            post_content_id: post_content_id,
            attachment_id: attachment_id
          }

          changeset = PostContentAttachment.changeset(%PostContentAttachment{}, params)

          case Repo.insert(changeset) do
            {:ok, _} ->
              Logger.info(
                "Migrated attachment #{attachment_id} for post_content #{post_content_id}"
              )

            {:error, changeset} ->
              Logger.error("""
              Failed to migrate attachment #{attachment_id} for post_content #{post_content_id}
              Errors: #{inspect(changeset.errors)}
              """)
          end
        end)

        Logger.info("Attachment migration completed")

      {:error, error} ->
        Logger.error("Failed to query attachments: #{inspect(error)}")
    end
  end
end
