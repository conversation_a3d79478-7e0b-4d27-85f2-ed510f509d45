defmodule Mix.Tasks.Crosspost.Gen.DataMigration do
  @moduledoc """
  Generates a new data migration file.

  ## Examples

      $ mix crosspost.gen.data_migration add_user_preferences
  """
  use Mix.Task

  @shortdoc "Generates a new data migration file"

  @impl Mix.Task
  def run([name | _]) do
    timestamp = timestamp()
    path = Path.join(["priv", "repo", "data_migrations"])
    base_name = "#{timestamp}_#{Macro.underscore(name)}.exs"
    file = Path.join(path, base_name)

    contents = """
    defmodule Crosspost.DataMigrations.#{Macro.camelize(name)} do
      use Ecto.Migration

      def up do
        # Add your data migration logic here
      end

      def down do
        # Add your rollback logic here
      end
    end
    """

    Mix.Generator.create_file(file, contents)
  end

  def run([]) do
    Mix.raise(
      "Expected data migration name to be given, please use \"mix crosspost.gen.data_migration NAME\""
    )
  end

  defp timestamp do
    {{y, m, d}, {hh, mm, ss}} = :calendar.universal_time()
    "#{y}#{pad(m)}#{pad(d)}#{pad(hh)}#{pad(mm)}#{pad(ss)}"
  end

  defp pad(i) when i < 10, do: <<?0, ?0 + i>>
  defp pad(i), do: to_string(i)
end
