defmodule Mix.Tasks.Crosspost.LoadFeatures do
  use Mix.Task

  require Logger

  alias Crosspost.Features

  @shortdoc "Loads features from plans.yml into the database"

  def run(_) do
    Mix.Task.run("app.start")
    Logger.info("Loading features from plans.yml...")

    {:ok, features} = Features.load_features()

    Mix.shell().info("""

    Feature loading completed:
      Successful: #{length(features)}
    """)
  end
end
