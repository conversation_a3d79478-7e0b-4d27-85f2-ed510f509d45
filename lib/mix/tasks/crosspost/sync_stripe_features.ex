defmodule Mix.Tasks.Crosspost.SyncStripeFeatures do
  use Mix.Task

  @shortdoc "Syncs plan and feature definitions with <PERSON>e"

  def run(_) do
    Mix.Task.run("app.start")

    case Crosspost.Features.StripeSync.sync_all() do
      {:ok, %{features: feature_results, products: product_results}} ->
        # Report feature sync results
        Enum.each(feature_results, fn
          {:ok, %{"name" => name}} ->
            Mix.shell().info("Created/updated feature: #{name}")

          {:ok, feature} when is_map(feature) ->
            Mix.shell().info("Created/updated feature: #{inspect(feature)}")

          {:error, {key, error}} ->
            Mix.shell().error("Failed to sync feature #{key}: #{inspect(error)}")
        end)

        # Report product sync results
        Enum.each(product_results, fn
          {:ok, %Stripe.Product{name: name}} ->
            Mix.shell().info("Created/updated product: #{name}")

          {:error, {id, error}} ->
            Mix.shell().error("Failed to sync product #{id}: #{inspect(error)}")

          {:skip, id} ->
            Mix.shell().info("Skipped product #{id} (no matching plan)")
        end)

      {:error, error} ->
        Mix.shell().error("Sync failed: #{inspect(error)}")
    end
  end
end
