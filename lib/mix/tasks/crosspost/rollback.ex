defmodule Mix.Tasks.Crosspost.Rollback do
  @moduledoc """
  Rolls back migrations.

  ## Examples

      $ mix crosspost.rollback -v 20240115000000
  """
  use Mix.Task

  @shortdoc "Rolls back migrations"

  @impl Mix.Task
  def run(args) do
    {opts, _, _} =
      OptionParser.parse(args,
        strict: [version: :string],
        aliases: [v: :version]
      )

    version =
      case opts[:version] do
        nil ->
          Mix.raise("The --version option is required. Please specify a version to rollback to.")

        version ->
          String.to_integer(version)
      end

    Mix.Task.run("app.start")
    Crosspost.Release.rollback(Crosspost.Repo, version)
  end
end
