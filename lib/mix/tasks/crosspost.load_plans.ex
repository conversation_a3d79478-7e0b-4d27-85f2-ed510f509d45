defmodule Mix.Tasks.Crosspost.LoadPlans do
  use Mix.Task
  alias Crosspost.Admin
  require Lo<PERSON>

  @shortdoc "Loads plans from plans.yml"

  def run(_) do
    Mix.Task.run("app.start")

    Logger.info("Loading plans from plans.yml")

    plans_path = Path.join(:code.priv_dir(:crosspost), "plans.yml")
    {:ok, plans_yaml} = YamlElixir.read_from_file(plans_path)

    Enum.each(plans_yaml["plans"], fn plan ->
      attrs = %{
        key: plan["key"],
        name: plan["name"],
        description: plan["description"],
        price: plan["price"],
        stripe_price_id: plan["stripe_price_id"],
        highlight: plan["highlight"] || false,
        enabled: plan["enabled"] || false,
        cta: plan["cta"],
        action: plan["action"],
        features: plan["features"] || []
      }

      case Admin.get_plan(plan["key"]) do
        {:ok, existing_plan} ->
          case Admin.update_plan(existing_plan, attrs) do
            {:ok, _updated_plan} ->
              Logger.info("Updated plan: #{plan["key"]}")

            {:error, changeset} ->
              Logger.error("Failed to update plan #{plan["key"]}: #{inspect(changeset.errors)}")
          end

        {:error, :not_found} ->
          case Admin.create_plan(attrs) do
            {:ok, _plan} ->
              Logger.info("Created plan: #{plan["key"]}")

            {:error, changeset} ->
              Logger.error("Failed to create plan #{plan["key"]}: #{inspect(changeset.errors)}")
          end
      end
    end)

    Logger.info("Finished loading plans")
  end
end
