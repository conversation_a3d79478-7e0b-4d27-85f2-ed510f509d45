defmodule Mix.Tasks.Test.E2e do
  use Mix.Task

  @shortdoc "Runs E2E tests with <PERSON><PERSON>"
  @moduledoc """
  Runs E2E tests with <PERSON><PERSON>.

  ## Usage

      mix test.e2e                    # runs all tests
      mix test.e2e post_form          # runs only post_form.spec.js
      mix test.e2e post_show:16       # runs test at line 16 in post_show.spec.js
      mix test.e2e post_show.spec.js  # runs post_show.spec.js (full filename also works)

  This task will:
  1. Reset the database
  2. Start the test server
  3. Run the specified Playwright tests
  """

  @impl true
  def run(args) do
    # Set test environment
    System.put_env("MIX_ENV", "test")
    System.put_env("E2E_TEST", "true")

    # Build the playwright command based on args
    playwright_cmd = build_playwright_cmd(args)

    # Run the tests
    {_, status} =
      System.cmd("sh", ["-c", "cd test/playwright && #{playwright_cmd}"],
        into: IO.stream(:stdio, :line),
        env: [{"MIX_ENV", "test"}, {"E2E_TEST", "true"}]
      )

    # Exit with the test status
    System.stop(status)
  end

  defp build_playwright_cmd([]) do
    "npx playwright test"
  end

  defp build_playwright_cmd([spec]) do
    case String.split(spec, ":") do
      [file, line, _column] ->
        # Handle line:column format
        file = ensure_spec_extension(file)
        "npx playwright test #{file}:#{line}"

      [file, line] ->
        # Handle just line number
        file = ensure_spec_extension(file)
        "npx playwright test #{file}:#{line}"

      [file] ->
        # Handle just the file
        file = ensure_spec_extension(file)
        "npx playwright test #{file}"
    end
  end

  defp ensure_spec_extension(file) do
    if String.ends_with?(file, ".spec.js") do
      file
    else
      "#{file}.spec.js"
    end
  end
end
