defmodule Mix.Tasks.Db.Seed do
  use Mix.Task

  alias Crosspost.Repo
  alias Crosspost.Accounts.{User, Workspace, WorkspaceUser}

  @shortdoc "Seeds the database with initial data"

  def run(_) do
    Mix.Task.run("app.start")
    create_qa_user()
  end

  defp create_qa_user do
    # First create a default workspace
    {:ok, workspace} =
      %Workspace{}
      |> Workspace.changeset(%{
        name: "Default",
        slug: "default-qa",
        settings: %{},
        is_default: true
      })
      |> Repo.insert()

    # Create QA user
    {:ok, user} =
      %User{}
      |> User.registration_changeset(%{
        email: "<EMAIL>",
        password: "supersecret123",
        confirmed_at: DateTime.utc_now()
      })
      |> Repo.insert()

    # Update user with default workspace
    {:ok, user} =
      user
      |> Ecto.Changeset.change(default_workspace_id: workspace.id)
      |> Repo.update()

    # Create workspace user association
    %WorkspaceUser{}
    |> WorkspaceUser.changeset(%{
      workspace_id: workspace.id,
      user_id: user.id
    })
    |> Repo.insert!()

    # Create LinkedIn connection
    %Crosspost.Accounts.Connection{}
    |> Crosspost.Accounts.Connection.changeset(%{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "linkedin",
      platform_user_id: "qa_linkedin_id",
      encrypted_access_token: "qa_linkedin_token",
      info: %{"name" => "QA User"}
    })
    |> Repo.insert!()

    # Create X connection
    %Crosspost.Accounts.Connection{}
    |> Crosspost.Accounts.Connection.changeset(%{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "x",
      platform_user_id: "qa_x_id",
      encrypted_access_token: "qa_x_token",
      info: %{"name" => "QA User"}
    })
    |> Repo.insert!()

    # Create Mastodon connection
    %Crosspost.Accounts.Connection{}
    |> Crosspost.Accounts.Connection.changeset(%{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "mastodon",
      platform_user_id: "qa_mastodon_id",
      encrypted_access_token: "qa_mastodon_token",
      info: %{"name" => "QA User"},
      settings: %{"instance" => "mastodon.social"}
    })
    |> Repo.insert!()

    # Create Bluesky connection
    %Crosspost.Accounts.Connection{}
    |> Crosspost.Accounts.Connection.changeset(%{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "bsky",
      platform_user_id: "qa_bsky_id",
      encrypted_access_token: "qa_bsky_token",
      info: %{"name" => "QA User"},
      settings: %{
        "username" => "qa.bsky.social",
        "app_password" => "qa_bsky_password"
      }
    })
    |> Repo.insert!()
  end
end
