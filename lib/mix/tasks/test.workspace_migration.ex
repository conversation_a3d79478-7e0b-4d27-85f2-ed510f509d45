defmodule Mix.Tasks.Test.WorkspaceMigration do
  use Mix.Task

  alias Crosspost.Repo
  alias Crosspost.Accounts.{User, Workspace, WorkspaceUser}
  alias Crosspost.Publishing.{Post, Attachment}

  import Ecto.Query

  @shortdoc "Tests the workspace migration by running migrations and verifying data integrity"

  def run(_) do
    Mix.Task.run("app.config")
    Mix.Task.run("ecto.drop")
    Mix.Task.run("ecto.create")
    {:ok, _} = Application.ensure_all_started(:ecto_sql)

    # Step 1: Run migrations up to post_network_statuses_view
    Mix.shell().info("Running migrations up to post_network_statuses_view...")

    {:ok, _, _} =
      Ecto.Migrator.with_repo(Repo, &Ecto.Migrator.run(&1, :up, to: 20_241_206_184_349))

    # Step 2: Create test data
    Mix.shell().info("Creating test data...")
    create_old_schema_data()

    # Step 3: Capture current state
    Mix.shell().info("Capturing current state...")
    pre_migration_state = capture_pre_migration_state()

    # Step 4: Run remaining migrations
    Mix.shell().info("Running remaining migrations...")
    Mix.Task.run("ecto.migrate")

    # Step 5: Verify data integrity
    Mix.shell().info("Verifying data integrity...")
    verify_data_integrity(pre_migration_state)
  end

  defp capture_pre_migration_state do
    %{
      counts: %{
        users: Repo.aggregate(User, :count),
        posts: Repo.aggregate(Post, :count),
        attachments: Repo.aggregate(Attachment, :count)
      },
      sample_records: capture_sample_records(),
      relationships: capture_relationships()
    }
  end

  defp capture_sample_records do
    # Capture full records with associations for better comparison
    users =
      Repo.all(
        from u in User,
          preload: [:posts, :connections],
          limit: 3
      )

    # Transform records to comparable format
    Enum.map(users, fn user ->
      %{
        id: user.id,
        email: user.email,
        post_count: length(user.posts),
        connection_count: length(user.connections),
        post_ids: Enum.map(user.posts, & &1.id),
        connection_platforms: Enum.map(user.connections, & &1.platform)
      }
    end)
  end

  defp capture_relationships do
    # Capture a sample user with all their relationships
    user =
      Repo.one(
        from u in User,
          where: fragment("EXISTS(SELECT 1 FROM posts WHERE user_id = ?)", u.id),
          preload: [
            :posts,
            :connections
          ],
          limit: 1
      )

    case user do
      nil ->
        Mix.raise("No user found with posts in test data")

      user ->
        %{
          user_id: user.id,
          posts: Enum.map(user.posts, &%{id: &1.id, status: &1.status}),
          connections: Enum.map(user.connections, &%{platform: &1.platform, user_id: &1.user_id})
        }
    end
  end

  defp verify_data_integrity(pre_migration_state) do
    # Verify counts
    verify_counts(pre_migration_state.counts)

    # Verify workspaces were created
    verify_workspaces()

    # Verify workspace assignments
    verify_workspace_assignments()

    # Verify sample records maintained their relationships
    verify_sample_records(pre_migration_state.sample_records)

    # Verify specific relationships
    verify_relationships(pre_migration_state.relationships)

    Mix.shell().info([:green, "✓ Workspace migration verification completed successfully"])
  end

  defp verify_counts(pre_counts) do
    current_counts = %{
      users: Repo.aggregate(User, :count),
      posts: Repo.aggregate(Post, :count),
      attachments: Repo.aggregate(Attachment, :count)
    }

    Enum.each(pre_counts, fn {table, count} ->
      if current_counts[table] != count do
        Mix.raise("""
        Count mismatch for #{table}:
        - Before migration: #{count}
        - After migration: #{current_counts[table]}
        """)
      end
    end)

    Mix.shell().info("✓ Record counts verified")
  end

  defp verify_workspaces do
    # Every user should have a default workspace
    users_without_workspace =
      Repo.all(
        from u in User,
          left_join: wu in WorkspaceUser,
          on: wu.user_id == u.id,
          left_join: w in Workspace,
          on: w.id == wu.workspace_id and w.is_default == true,
          where: is_nil(w.id),
          select: u.id
      )

    if users_without_workspace != [] do
      Mix.raise("""
      Found users without default workspace: #{inspect(users_without_workspace)}
      This likely means the default workspace migration didn't run correctly.
      """)
    end

    # Every workspace should have a user
    orphaned_workspaces =
      Repo.all(
        from w in Workspace,
          left_join: wu in WorkspaceUser,
          on: wu.workspace_id == w.id,
          where: is_nil(wu.user_id),
          select: w.id
      )

    if orphaned_workspaces != [] do
      Mix.raise("Orphaned workspaces found: #{inspect(orphaned_workspaces)}")
    end

    # Verify default workspace associations
    invalid_defaults =
      Repo.all(
        from u in User,
          left_join: w in assoc(u, :default_workspace),
          where:
            not is_nil(u.default_workspace_id) and
              (is_nil(w.id) or w.is_default != true),
          select: u.id
      )

    if invalid_defaults != [] do
      Mix.raise("""
      Found users with invalid default workspace associations: #{inspect(invalid_defaults)}
      Default workspaces must exist and have is_default = true
      """)
    end

    Mix.shell().info("✓ Workspace associations verified")
  end

  defp verify_workspace_assignments do
    # Check if all posts have workspace_id set
    posts_without_workspace =
      Repo.all(
        from p in Post,
          where: is_nil(p.workspace_id),
          select: p.id
      )

    if posts_without_workspace != [] do
      Mix.raise("""
      Found posts without workspace_id: #{inspect(posts_without_workspace)}
      This indicates the workspace assignment migration didn't complete properly.
      """)
    end

    # Check if all connections have workspace_id set
    connections_without_workspace =
      Repo.all(
        from c in Crosspost.Accounts.Connection,
          where: is_nil(c.workspace_id),
          select: c.id
      )

    if connections_without_workspace != [] do
      Mix.raise("""
      Found connections without workspace_id: #{inspect(connections_without_workspace)}
      This indicates the workspace assignment migration didn't complete properly.
      """)
    end

    Mix.shell().info("✓ Workspace assignments verified")
  end

  defp verify_sample_records(pre_records) do
    current_records =
      Repo.all(
        from u in User,
          preload: [:posts, :connections, :default_workspace]
      )
      |> Enum.map(fn user ->
        %{
          id: user.id,
          email: user.email,
          post_count: length(user.posts),
          connection_count: length(user.connections),
          post_ids: Enum.map(user.posts, & &1.id),
          connection_platforms: Enum.map(user.connections, & &1.platform)
        }
      end)

    # Verify that all pre-migration records exist in current records
    Enum.each(pre_records, fn pre_record ->
      unless Enum.any?(current_records, fn current ->
               current.id == pre_record.id &&
                 current.email == pre_record.email &&
                 current.post_count == pre_record.post_count &&
                 current.connection_count == pre_record.connection_count &&
                 current.post_ids == pre_record.post_ids &&
                 current.connection_platforms == pre_record.connection_platforms
             end) do
        Mix.raise("Failed to find matching record after migration")
      end
    end)

    Mix.shell().info(" Sample records verified")
  end

  defp verify_relationships(pre_relations) do
    # Debug output for user and workspace state
    IO.puts("\nChecking relationships for user #{pre_relations.user_id}")

    user =
      Repo.get!(User, pre_relations.user_id)
      |> Repo.preload([:posts, :connections, :default_workspace])

    IO.puts("""
    User state:
      ID: #{user.id}
      Default Workspace ID: #{user.default_workspace_id}
      Post count: #{length(user.posts)}
      Connection count: #{length(user.connections)}
    """)

    if is_nil(user.default_workspace_id) do
      # Get all workspaces for this user
      workspaces =
        Repo.all(
          from w in Workspace,
            join: wu in WorkspaceUser,
            on: wu.workspace_id == w.id,
            where: wu.user_id == ^user.id,
            select: %{id: w.id, slug: w.slug, is_default: w.is_default}
        )

      IO.puts("""
      Available workspaces for user:
      #{inspect(workspaces, pretty: true)}
      """)

      Mix.raise("""
      User #{user.id} has no default workspace set.
      This indicates the default workspace migration didn't complete properly.
      """)
    end

    # Verify all posts belong to user's default workspace
    posts_wrong_workspace =
      Repo.all(
        from p in Post,
          where:
            p.user_id == ^user.id and
              not is_nil(p.workspace_id) and
              p.workspace_id != ^user.default_workspace_id,
          select: %{id: p.id, workspace_id: p.workspace_id}
      )

    if posts_wrong_workspace != [] do
      Mix.raise("""
      Found posts not belonging to user's default workspace:
      User ID: #{user.id}
      Default Workspace ID: #{user.default_workspace_id}
      Problem Posts: #{inspect(posts_wrong_workspace)}
      """)
    end

    # Verify all connections belong to user's default workspace
    connections_wrong_workspace =
      Repo.all(
        from c in Crosspost.Accounts.Connection,
          where:
            c.user_id == ^user.id and
              not is_nil(c.workspace_id) and
              c.workspace_id != ^user.default_workspace_id,
          select: %{id: c.id, workspace_id: c.workspace_id}
      )

    if connections_wrong_workspace != [] do
      Mix.raise("""
      Found connections not belonging to user's default workspace:
      User ID: #{user.id}
      Default Workspace ID: #{user.default_workspace_id}
      Problem Connections: #{inspect(connections_wrong_workspace)}
      """)
    end

    Mix.shell().info("✓ Relationships verified")
  end

  defp create_old_schema_data do
    # Create a test user without default workspace
    {:ok, user} =
      %Crosspost.Accounts.User{}
      |> Crosspost.Accounts.User.registration_changeset(%{
        email: "<EMAIL>",
        password: "supersecret123",
        confirmed_at: DateTime.utc_now()
      })
      |> Repo.insert()

    # Create posts directly with SQL to bypass schema validations
    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

    # Create first post
    {:ok, %{rows: [[post1_id]]}} =
      Repo.query(
        """
          INSERT INTO posts (id, user_id, status, social_networks, inserted_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $5)
          RETURNING id
        """,
        [Ecto.UUID.dump!(Ecto.UUID.generate()), user.id, "published", ["x", "linkedin"], now]
      )

    # Create second post
    {:ok, %{rows: [[post2_id]]}} =
      Repo.query(
        """
          INSERT INTO posts (id, user_id, status, social_networks, inserted_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $5)
          RETURNING id
        """,
        [Ecto.UUID.dump!(Ecto.UUID.generate()), user.id, "draft", ["mastodon"], now]
      )

    # Add content to posts
    Repo.query!(
      """
        INSERT INTO post_content (id, post_id, text, network, order_num, meta, inserted_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
      """,
      [Ecto.UUID.dump!(Ecto.UUID.generate()), post1_id, "Test post 1", "canonical", 0, "{}", now]
    )

    Repo.query!(
      """
        INSERT INTO post_content (id, post_id, text, network, order_num, meta, inserted_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
      """,
      [Ecto.UUID.dump!(Ecto.UUID.generate()), post2_id, "Test post 2", "canonical", 0, "{}", now]
    )

    # Create connections
    Repo.query!(
      """
        INSERT INTO connections (user_id, platform, platform_user_id, info, inserted_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $5)
      """,
      [user.id, "x", "fake_x_id", "{}", now]
    )

    Repo.query!(
      """
        INSERT INTO connections (user_id, platform, platform_user_id, info, inserted_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $5)
      """,
      [user.id, "linkedin", "fake_linkedin_id", "{}", now]
    )
  end
end
