defmodule Mix.Tasks.Test.Seed do
  use Mix.Task

  alias Crosspost.Repo
  alias Crosspost.{Accounts, Features, Plans, Users}
  alias Crosspost.Accounts.{User, Waitlist, Connection, Workspace, WorkspaceUser}
  alias Crosspost.Publishing.{Post, PostStatus, Schedule}

  @tables [
    "users",
    "waitlist",
    "connections",
    "posts",
    "post_content",
    "post_statuses",
    "schedules",
    "attachments",
    "workspaces",
    "workspace_users",
    "user_features",
    "oban_jobs",
    "plans",
    "features",
    "feature_usages"
  ]

  @shortdoc "Seeds the test database with a user, waitlist entries, and sample posts"
  def run(_) do
    Mix.Task.run("app.start")

    # Truncate all tables
    truncate_tables()

    # Create waitlist entries
    create_waitlist_entries()

    create_plans()

    # Create a test user with connections to all networks
    _basic_user = create_test_user("<EMAIL>", "basic")
    _growth_user = create_test_user("<EMAIL>", "growth")
    pro_user = create_test_user("<EMAIL>", "pro")

    # Create various posts
    create_sample_posts(pro_user)

    IO.puts("Test database seeded successfully!")
  end

  defp create_plans do
    {:ok, _features} = Features.load_features()

    plans = Features.list_plans()

    Enum.each(plans, fn {key, plan} ->
      Repo.insert!(
        %Crosspost.Accounts.Plan{
          key: key,
          name: plan["name"],
          description: plan["description"],
          price: plan["price"],
          stripe_price_id: plan["stripe_price_id"],
          highlight: plan["highlight"] || false,
          enabled: plan["enabled"] || false,
          cta: plan["cta"],
          action: plan["action"],
          features: plan["features"],
          archived: false
        },
        on_conflict: :replace_all,
        conflict_target: :key
      )
    end)
  end

  defp truncate_tables do
    Repo.query!("SET CONSTRAINTS ALL DEFERRED")

    # Truncate each table
    Enum.each(@tables, fn table ->
      Repo.query!("TRUNCATE TABLE #{table} CASCADE")
    end)

    Repo.query!("SET CONSTRAINTS ALL IMMEDIATE")
  end

  defp create_waitlist_entries do
    # Create a waitlist entry with an invitation code (for testing signup flow)
    %Waitlist{
      email: "<EMAIL>",
      invitation_code: "test-invitation-code-123",
      invited_at: DateTime.utc_now() |> DateTime.truncate(:second)
    }
    |> Repo.insert!()

    # Create a waitlist entry without an invitation code
    %Waitlist{
      email: "<EMAIL>",
      invited_at: DateTime.utc_now() |> DateTime.truncate(:second)
    }
    |> Repo.insert!()

    # Create a waitlist entry with a used invitation code
    %Waitlist{
      email: "<EMAIL>",
      invitation_code: "used-invitation-code-456",
      invited_at: DateTime.utc_now() |> DateTime.truncate(:second),
      used_at: DateTime.utc_now() |> DateTime.truncate(:second)
    }
    |> Repo.insert!()

    # Export values for Playwright tests
    System.put_env("TEST_INVITATION_CODE", "test-invitation-code-123")
    System.put_env("TEST_USER_EMAIL", "<EMAIL>")
  end

  defp create_sample_posts(user) do
    # Create a draft post
    create_post(user, "This is a draft post", "draft", ["linkedin"])

    # Create a scheduled post
    scheduled_time = DateTime.utc_now() |> DateTime.add(1, :day) |> DateTime.truncate(:second)
    scheduled_post = create_post(user, "This is a scheduled post", "scheduled", ["x"])

    # Add schedule for the scheduled post
    %Schedule{
      post_id: scheduled_post.id,
      network: :x,
      scheduled_at: scheduled_time,
      timezone: user.timezone
    }
    |> Repo.insert!()

    # Create a published post with successful statuses
    published_time = DateTime.utc_now() |> DateTime.add(-1, :day) |> DateTime.truncate(:second)

    post =
      create_post(
        user,
        "This is a published post",
        "published",
        ["linkedin", "x"],
        published_time
      )

    # Add post statuses for published post
    Enum.each(["linkedin", "x"], fn network ->
      %PostStatus{
        post_id: post.id,
        network: String.to_existing_atom(network),
        status: :published,
        outcome: %{}
      }
      |> Repo.insert!()
    end)

    # Create a pending post
    create_post(
      user,
      "This is a pending post",
      "pending",
      ["linkedin", "x", "mastodon"]
    )

    # Create a partially published post with mixed statuses
    create_post(
      user,
      "This is a partially published post",
      "partially_published",
      ["linkedin", "x", "mastodon"],
      DateTime.utc_now() |> DateTime.add(-1, :hour) |> DateTime.truncate(:second)
    )

    # Add mixed statuses
    [
      %{network: :linkedin, status: :published},
      %{network: :x, status: :failed},
      %{network: :mastodon, status: :published}
    ]
    |> Enum.each(fn %{network: network, status: status} ->
      %PostStatus{
        post_id: post.id,
        network: network,
        status: status,
        outcome: %{}
      }
      |> Repo.insert!()
    end)
  end

  defp create_post(user, text, status, social_networks, published_at \\ nil) do
    workspace = ensure_default_workspace(user)

    content = [
      %{
        text: text,
        network: "canonical",
        order: 0,
        meta: %{}
      }
    ]

    attrs = %{
      user_id: user.id,
      workspace_id: workspace.id,
      content: content,
      social_networks: social_networks,
      status: status
    }

    # Add published_at if provided
    attrs =
      if published_at do
        Map.put(attrs, :published_at, DateTime.truncate(published_at, :second))
      else
        attrs
      end

    {:ok, post} = %Post{} |> Post.changeset(attrs) |> Repo.insert()
    post
  end

  defp create_test_user(email, plan_key) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    trial_end = DateTime.add(now, 30, :day)

    {:ok, user} =
      %User{}
      |> User.registration_changeset(%{
        email: email,
        password: "supersecret312",
        hashed_password: Bcrypt.hash_pwd_salt("supersecret312"),
        confirmed_at: now,
        trial_end: trial_end
      })
      |> Repo.insert()

    {:ok, _} = Accounts.update_user_settings(user, %{"onboarding_completed" => true})

    # Create default workspace for the user
    {:ok, workspace} =
      %Workspace{
        name: "Default",
        slug: "default-#{user.id}",
        settings: %{},
        is_default: true,
        owner_id: user.id
      }
      |> Repo.insert()

    # Associate user with workspace
    %WorkspaceUser{
      workspace_id: workspace.id,
      user_id: user.id
    }
    |> Repo.insert!()

    {:ok, user} = Users.update_user(user, %{default_workspace_id: workspace.id})

    # Enroll user in pro plan
    plan = Plans.get_plan!(plan_key)
    {:ok, user} = Accounts.enroll_in_trial(user, plan)

    {:ok, user} = Users.update_user(user, %{default_workspace_id: workspace.id})

    # Create connections for each platform (now with workspace_id)
    %Connection{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "linkedin",
      platform_user_id: "fake_linkedin_id",
      encrypted_access_token: "fake_linkedin_token",
      info: %{
        "birthday" => nil,
        "description" => "",
        "email" => "<EMAIL>",
        "first_name" => nil,
        "image" => "https://pbs.twimg.com/profile_images/1833895676238790656/oI9EqzeU_normal.jpg",
        "last_name" => nil,
        "location" => "",
        "name" => "Crossposting Testing",
        "nickname" => "crosspost_test",
        "phone" => nil,
        "urls" => %{
          "Website" => nil
        }
      }
    }
    |> Repo.insert!()

    # Update other connections to include workspace_id
    expires_at = now |> DateTime.add(3600, :second)

    %Connection{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "x",
      platform_user_id: "fake_x_id",
      encrypted_access_token: "fake_x_token",
      encrypted_refresh_token: "fake_x_refresh_token",
      expires_at: expires_at,
      info: %{
        "birthday" => nil,
        "description" => nil,
        "email" => nil,
        "first_name" => nil,
        "image" => "https://pbs.twimg.com/profile_images/1833895676238790656/oI9EqzeU_normal.jpg",
        "last_name" => nil,
        "location" => nil,
        "name" => "Crossposting Testing",
        "nickname" => "crosspost_test",
        "phone" => nil,
        "urls" => %{}
      }
    }
    |> Repo.insert!()

    %Connection{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "mastodon",
      platform_user_id: "fake_mastodon_id",
      encrypted_access_token: "fake_mastodon_token",
      encrypted_refresh_token: "fake_mastodon_refresh_token",
      expires_at: expires_at,
      settings: %{"instance" => "mastodon.social"},
      info: %{
        "birthday" => nil,
        "description" => nil,
        "email" => nil,
        "first_name" => nil,
        "image" =>
          "https://files.mastodon.social/accounts/avatars/113/605/935/015/324/727/original/b246122dd7ab64fb.png",
        "last_name" => nil,
        "location" => nil,
        "name" => "",
        "nickname" => "jc_tester",
        "phone" => nil,
        "urls" => %{
          "instance" => "https://mastodon.social",
          "profile" => "https://mastodon.social/@jc_tester"
        }
      }
    }
    |> Repo.insert!()

    %Connection{
      user_id: user.id,
      workspace_id: workspace.id,
      platform: "bsky",
      platform_user_id: "fake_bsky_id",
      encrypted_access_token: "fake_bsky_token",
      encrypted_refresh_token: "fake_bsky_refresh_token",
      settings: %{
        "username" => "testuser.bsky.social",
        "app_password" => "fake_bsky_app_password"
      },
      info: %{
        "avatar" =>
          "https://cdn.bsky.app/img/avatar/plain/did:plc:6k4tgyg7f2es7tiahpm6ro53/bafkreicnry5xjnfggnmmzzyo527eo7w3ltt3sm3ctf5lr2ri3fe5y3s6ei@jpeg",
        "description" => nil,
        "display_name" => "",
        "followers_count" => 4,
        "follows_count" => 1,
        "handle" => "jc-tester.bsky.social",
        "posts_count" => 179,
        "subscription_type" => "Basic"
      }
    }
    |> Repo.insert!()

    # Create past schedules for the user to provide suggested slots for auto-scheduler
    create_past_schedules(user, workspace.id)

    user
  end

  # Create past schedules for the user to provide suggested slots for auto-scheduler
  defp create_past_schedules(user, workspace_id) do
    # Create a variety of past schedules at different hours to simulate user posting patterns
    # These will be used by the auto-scheduler to suggest optimal posting times

    # Get a base time in the past
    past_base = DateTime.utc_now() |> DateTime.add(-30, :day) |> DateTime.truncate(:second)

    # Create schedules at peak hours with higher frequency to influence suggestions
    peak_hours = [9, 12, 15, 19, 21]
    networks = [:x, :linkedin, :bsky, :mastodon]

    # Create 50 past schedules across different days and times
    for day <- 1..10, hour <- peak_hours do
      # Create a post for this schedule
      {:ok, post} =
        %Post{}
        |> Post.changeset(%{
          user_id: user.id,
          workspace_id: workspace_id,
          status: "published",
          social_networks: [Enum.random(networks)],
          published_at: DateTime.add(past_base, day, :day) |> DateTime.add(hour, :hour)
        })
        |> Repo.insert()

      # Create a schedule for this post
      scheduled_at = DateTime.add(past_base, day, :day) |> DateTime.add(hour, :hour)

      %Schedule{
        post_id: post.id,
        network: Enum.random(networks),
        scheduled_at: scheduled_at,
        timezone: user.timezone
      }
      |> Repo.insert!()

      # Create a successful post status
      %PostStatus{
        post_id: post.id,
        network: Enum.random(networks),
        status: :published,
        is_final: true,
        outcome: %{}
      }
      |> Repo.insert!()
    end
  end

  defp get_default_workspace(user) do
    # Query through workspace_users join table
    import Ecto.Query

    from(w in Workspace,
      join: wu in WorkspaceUser,
      on: wu.workspace_id == w.id,
      where: wu.user_id == ^user.id and w.is_default == true,
      select: w,
      limit: 1
    )
    |> Repo.one()
  end

  defp create_default_workspace(user) do
    # Create default workspace
    {:ok, workspace} =
      %Workspace{}
      |> Workspace.changeset(%{
        name: "Default",
        slug: "default-#{System.unique_integer([:positive])}",
        settings: %{},
        is_default: true
      })
      |> Repo.insert()

    # Create workspace user association
    %WorkspaceUser{}
    |> WorkspaceUser.changeset(%{
      workspace_id: workspace.id,
      user_id: user.id
    })
    |> Repo.insert!()

    workspace
  end

  defp ensure_default_workspace(user) do
    case get_default_workspace(user) do
      nil -> create_default_workspace(user)
      workspace -> workspace
    end
  end
end
