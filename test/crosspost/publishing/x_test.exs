defmodule Crosspost.Publishing.XTest do
  use Crosspost.DataCase

  import Plug.Conn
  import Mox
  import Crosspost.Fixtures

  alias Crosspost.Publishing.X
  alias Crosspost.Publishing.Attachment

  setup :verify_on_exit!

  setup do
    user =
      user_fixture(%{
        encrypted_x_token: "fake_token",
        encrypted_x_refresh_token: "fake_refresh_token",
        x_expires_at: DateTime.utc_now() |> DateTime.truncate(:second) |> DateTime.add(3600),
        x_id: "fake_x_id"
      })

    connected_user = Crosspost.Accounts.get_connected_user!(user.id, user.default_workspace_id)

    Crosspost.Publishing.MediaHelperMock
    |> Mox.stub(:download_file, fn url ->
      cond do
        is_nil(url) -> {:error, "Invalid URL"}
        String.contains?(url, "cloudinary.com") -> {:ok, "downloaded_binary_data"}
        true -> {:error, "Invalid URL"}
      end
    end)

    {:ok, user: connected_user}
  end

  describe "refresh_connection/1" do
    test "returns :ok when token has not expired" do
      connection = %Crosspost.Accounts.Connection{
        encrypted_access_token: "fake_token",
        encrypted_refresh_token: "fake_refresh_token",
        # Token expires in 1 hour
        expires_at: DateTime.utc_now() |> DateTime.add(3600)
      }

      assert :ok == X.refresh_connection(connection)
    end

    test "refreshes token when token has expired" do
      connection = %Crosspost.Accounts.Connection{
        encrypted_access_token: "fake_token",
        encrypted_refresh_token: "fake_refresh_token",
        # Token expired 1 hour ago
        expires_at: DateTime.utc_now() |> DateTime.add(-3600)
      }

      expect(Ueberauth.Strategy.X.OAuthMock, :refresh_token, fn refresh_token ->
        assert refresh_token == "fake_refresh_token"
        {:ok, {"new_token", "new_refresh_token", ~U[2024-03-20 00:00:00Z]}}
      end)

      assert {:ok, {"new_token", "new_refresh_token", ~U[2024-03-20 00:00:00Z]}} ==
               X.refresh_connection(connection)
    end

    test "returns error when token refresh fails" do
      connection = %Crosspost.Accounts.Connection{
        encrypted_access_token: "fake_token",
        encrypted_refresh_token: "fake_refresh_token",
        # Token expired 1 hour ago
        expires_at: DateTime.utc_now() |> DateTime.add(-3600)
      }

      expect(Ueberauth.Strategy.X.OAuthMock, :refresh_token, fn refresh_token ->
        assert refresh_token == "fake_refresh_token"
        {:error, "Failed to refresh token"}
      end)

      assert {:error, "Failed to refresh token"} == X.refresh_connection(connection)
    end
  end

  describe "publish_content/3" do
    test "successfully publishes a single tweet with media", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Test tweet content",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        assert conn.request_path == "/2/tweets"
        assert conn.method == "POST"
        assert {"authorization", "Bearer fake_token"} in conn.req_headers
        assert {"content-type", "application/json"} in conn.req_headers

        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Test tweet content"
        assert payload["media"]["media_ids"] == ["media123"]

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      assert {:ok, %{"parent" => "12345", "root" => "12345"}} ==
               X.publish_content(content, user, media: [%{type: :image, id: "media123"}])
    end

    test "successfully publishes a tweet without media", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Test tweet content",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Test tweet content"
        refute Map.has_key?(payload, "media")

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      assert {:ok, %{"parent" => "12345", "root" => "12345"}} ==
               X.publish_content(content, user, media: [])
    end

    test "successfully publishes a tweet as a reply", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Reply tweet content",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        assert conn.request_path == "/2/tweets"
        assert conn.method == "POST"

        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Reply tweet content"
        assert payload["reply"]["in_reply_to_tweet_id"] == "previous123"

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      assert {:ok, %{"parent" => "12345", "root" => "previous123"}} ==
               X.publish_content(content, user,
                 previous: %{"root" => "previous123", "parent" => "previous123"},
                 media: []
               )
    end

    test "successfully publishes a reply with media", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Reply with media",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        assert conn.request_path == "/2/tweets"
        assert conn.method == "POST"

        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Reply with media"
        assert payload["reply"]["in_reply_to_tweet_id"] == "previous123"
        assert payload["media"]["media_ids"] == ["media123"]

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      assert {:ok, %{"parent" => "12345", "root" => "previous123"}} ==
               X.publish_content(content, user,
                 previous: %{"root" => "previous123", "parent" => "previous123"},
                 media: [%{type: :image, id: "media123"}]
               )
    end

    test "handles error when publishing a reply", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Reply tweet content",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        conn
        |> put_resp_content_type("application/json")
        |> send_resp(400, Jason.encode!(%{"errors" => [%{"message" => "Invalid reply ID"}]}))
      end)

      assert {:error, "Failed to post to X"} ==
               X.publish_content(content, user,
                 previous: %{"root" => "invalid_id", "parent" => "invalid_id"},
                 media: []
               )
    end

    test "handles daily limit exceeded", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Reply tweet content",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        conn
        |> put_resp_content_type("application/json")
        |> send_resp(429, Jason.encode!(%{"detail" => "Too Many Requests"}))
      end)

      assert {:error, "Daily limit exceeded on X API"} ==
               X.publish_content(content, user,
                 previous: %{"root" => "invalid_id", "parent" => "invalid_id"},
                 media: []
               )
    end

    test "successfully publishes first tweet in a thread", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "First tweet",
        meta: %{},
        attachments: [],
        order: 0
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "First tweet"
        refute Map.has_key?(payload, "reply")

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "tweet1"}}))
      end)

      assert {:ok, %{"parent" => "tweet1", "root" => "tweet1"}} ==
               X.publish_content(content, user, media: [])
    end

    test "successfully publishes subsequent tweet in thread", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Second tweet",
        meta: %{},
        attachments: [],
        order: 1
      }

      previous = %{
        "parent" => "tweet1",
        "root" => "tweet1"
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Second tweet"
        assert payload["reply"]["in_reply_to_tweet_id"] == "tweet1"

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "tweet2"}}))
      end)

      assert {:ok, %{"parent" => "tweet2", "root" => "tweet1"}} ==
               X.publish_content(content, user, previous: previous, media: [])
    end

    test "successfully publishes third tweet in thread", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Third tweet",
        meta: %{},
        attachments: [],
        order: 2
      }

      previous = %{
        "parent" => "tweet2",
        "root" => "tweet1"
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Third tweet"
        assert payload["reply"]["in_reply_to_tweet_id"] == "tweet2"

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "tweet3"}}))
      end)

      assert {:ok, %{"parent" => "tweet3", "root" => "tweet1"}} ==
               X.publish_content(content, user, previous: previous, media: [])
    end

    test "filters out non-image/video media when publishing", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Test tweet with mixed media",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Test tweet with mixed media"
        # Only image/video media included
        assert payload["media"]["media_ids"] == ["media123"]

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      # Pass a mix of different media types
      media = [
        %{type: :link, id: "link123"},
        %{type: :image, id: "media123"},
        %{type: :other, id: "other456"}
      ]

      assert {:ok, %{"parent" => "12345", "root" => "12345"}} ==
               X.publish_content(content, user, media: media)
    end

    test "filters out media with nil or invalid IDs", %{user: user} do
      content = %{
        id: UUID.uuid4(),
        text: "Test tweet with invalid media",
        meta: %{},
        attachments: []
      }

      Req.Test.stub(X, fn conn ->
        {:ok, body, conn} = Plug.Conn.read_body(conn)
        payload = Jason.decode!(body)
        assert payload["text"] == "Test tweet with invalid media"
        # Should only include valid media IDs
        assert payload["media"]["media_ids"] == ["valid123", "valid456"]

        conn
        |> put_resp_content_type("application/json")
        |> send_resp(201, Jason.encode!(%{"data" => %{"id" => "12345"}}))
      end)

      # Pass a mix of valid and invalid media
      media = [
        %{type: :image, id: "valid123"},
        %{type: :image, id: nil},
        %{type: :image, id: ""},
        %{type: :video, id: "valid456"},
        nil,
        %{type: :link, id: "link123"}  # Should be filtered out by type
      ]

      assert {:ok, %{"parent" => "12345", "root" => "12345"}} ==
               X.publish_content(content, user, media: media)
    end
  end

  describe "upload_media_file/2" do
    test "successfully uploads an image file", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :image,
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/mycloud/image/upload/v1/test.jpg",
        metadata: %{}
      }

      Req.Test.stub(X, fn conn ->
        assert conn.request_path == "/2/media/upload"
        assert conn.method == "POST"

        {:ok, body, _conn} = Plug.Conn.read_body(conn)

        assert String.contains?(body, "tweet_image")
        assert String.contains?(body, "media")

        conn
        |> send_resp(200, Jason.encode!(%{"id" => "12345678"}))
      end)

      assert {:ok, %{type: :image, id: "12345678"}} == X.upload_media_file(attachment, user)
    end

    test "successfully uploads a GIF file using chunked upload", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :image,
        filename: "animation.gif",
        content_type: "image/gif",
        source_url: "https://res.cloudinary.com/mycloud/image/upload/v1/animation.gif",
        metadata: %{}
      }

      # Stub for INIT request
      Req.Test.stub(X, fn conn ->
        case {conn.method, conn.request_path} do
          {"POST", "/2/media/upload"} ->
            {:ok, body, _conn} = Plug.Conn.read_body(conn)

            cond do
              String.contains?(body, "INIT") ->
                assert String.contains?(body, "tweet_gif")
                assert String.contains?(body, "image%2Fgif")

                conn
                |> put_resp_content_type("application/json")
                |> send_resp(200, Jason.encode!(%{"data" => %{"id" => "gif12345"}}))

              String.contains?(body, "APPEND") ->
                assert String.contains?(body, "gif12345")
                conn |> send_resp(200, Jason.encode!(%{"success" => true}))

              String.contains?(body, "FINALIZE") ->
                assert String.contains?(body, "gif12345")

                conn |> send_resp(200, Jason.encode!(%{"success" => true}))
            end

          {"GET", "/2/media/upload"} ->
            assert String.contains?(conn.query_string, "command=STATUS")
            assert String.contains?(conn.query_string, "gif12345")

            conn
            |> put_resp_content_type("application/json")
            |> send_resp(
              200,
              Jason.encode!(%{
                "data" => %{"processing_info" => %{"state" => "succeeded"}}
              })
            )
        end
      end)

      assert {:ok, %{type: :image, id: "gif12345"}} == X.upload_media_file(attachment, user)
    end

    test "handles failed video processing during chunked upload", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :video,
        filename: "failed.mp4",
        content_type: "video/mp4",
        source_url: "https://res.cloudinary.com/mycloud/video/upload/v1/failed.mp4",
        metadata: %{}
      }

      Req.Test.stub(X, fn conn ->
        case {conn.method, conn.request_path} do
          {"POST", "/2/media/upload"} ->
            {:ok, body, _conn} = Plug.Conn.read_body(conn)

            cond do
              String.contains?(body, "INIT") ->
                assert String.contains?(body, "tweet_video")

                conn
                |> put_resp_content_type("application/json")
                |> send_resp(200, Jason.encode!(%{"data" => %{"id" => "video_failed"}}))

              String.contains?(body, "APPEND") ->
                conn |> send_resp(200, Jason.encode!(%{"success" => true}))

              String.contains?(body, "FINALIZE") ->
                conn |> send_resp(200, Jason.encode!(%{}))
            end

          {"GET", "/2/media/upload"} ->
            conn
            |> put_resp_content_type("application/json")
            |> send_resp(
              200,
              Jason.encode!(%{"data" => %{"processing_info" => %{"state" => "failed"}}})
            )
        end
      end)

      {:error, msg} = X.upload_media_file(attachment, user)

      assert msg == "Media processing failed"
    end

    test "handles error in INIT phase of chunked upload", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :video,
        filename: "error.mp4",
        content_type: "video/mp4",
        source_url: "https://res.cloudinary.com/mycloud/video/upload/v1/error.mp4",
        metadata: %{}
      }

      Req.Test.stub(X, fn conn ->
        case {conn.method, conn.request_path} do
          {"POST", "/2/media/upload"} ->
            conn |> send_resp(400, Jason.encode!(%{"error" => "Invalid media type"}))

          _ ->
            conn |> send_resp(400, Jason.encode!(%{"error" => "Unexpected request"}))
        end
      end)

      {:error, msg} = X.upload_media_file(attachment, user)

      assert msg == "Error initiating chunked upload"
    end

    test "handles media download failure", %{user: user} do
      attachment = %Attachment{
        type: :image,
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "invalid_url",
        metadata: %{}
      }

      # Update the MediaHelperMock to return error for invalid URL
      Crosspost.Publishing.MediaHelperMock
      |> Mox.stub(:download_file, fn "invalid_url" ->
        {:error, "Failed to download file"}
      end)

      assert {:error, "Failed to download file"} == X.upload_media_file(attachment, user)
    end
  end

  describe "image optimization" do
    test "transforms Cloudinary URL for large images", %{user: user} do
      attachment = %Attachment{
        id: "att123",
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/test/image/upload/v1234567890/test.jpg",
        type: :image,
        metadata: %{
          "alt" => "Image alt text",
          "size" => 6_000_000
        }
      }

      Crosspost.Publishing.MediaHelperMock
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_2000,c_limit,q_auto,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 6_000_000)}
      end)
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_1600,c_limit,q_80,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 4_500_000)}
      end)

      Req.Test.stub(X, fn conn ->
        case conn.request_path do
          "/2/media/upload" ->
            conn
            |> Plug.Conn.put_resp_content_type("application/json")
            |> Plug.Conn.send_resp(201, Jason.encode!(%{"id" => "media123"}))
        end
      end)

      assert {:ok, %{type: :image, id: "media123"}} = X.upload_media_file(attachment, user)
    end

    test "tries multiple optimizations for very large images", %{user: user} do
      attachment = %Attachment{
        id: "att123",
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/test/image/upload/v1234567890/test.jpg",
        type: :image,
        metadata: %{
          "alt" => "Image alt text",
          "size" => 10_000_000
        }
      }

      # Initial attempt with default settings
      Crosspost.Publishing.MediaHelperMock
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_2000,c_limit,q_auto,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 10_000_000)}
      end)
      # First optimization attempt
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_1600,c_limit,q_80,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 8_000_000)}
      end)
      # Second optimization attempt
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_1200,c_limit,q_70,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 4_500_000)}
      end)

      Req.Test.stub(X, fn conn ->
        case conn.request_path do
          "/2/media/upload" ->
            conn
            |> Plug.Conn.put_resp_content_type("application/json")
            |> Plug.Conn.send_resp(201, Jason.encode!(%{"id" => "media123"}))
        end
      end)

      assert {:ok, %{type: :image, id: "media123"}} = X.upload_media_file(attachment, user)
    end

    test "returns error when image cannot be optimized enough", %{user: user} do
      attachment = %Attachment{
        id: "att123",
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/test/image/upload/v1234567890/test.jpg",
        type: :image,
        metadata: %{
          "alt" => "Image alt text",
          "size" => 20_000_000
        }
      }

      # Initial attempt with default settings
      Crosspost.Publishing.MediaHelperMock
      |> Mox.expect(:download_file, fn url ->
        assert String.contains?(url, "cloudinary.com")
        {:ok, :binary.copy(<<1>>, 20_000_000)}
      end)
      # All 8 optimization attempts
      |> Mox.expect(:download_file, 8, fn url ->
        assert String.contains?(url, "cloudinary.com")
        {:ok, :binary.copy(<<1>>, 6_000_000)}
      end)

      assert {:error, "Could not reduce image size enough"} =
               X.upload_media_file(attachment, user)
    end

    test "handles download errors during optimization", %{user: user} do
      attachment = %Attachment{
        id: "att123",
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/test/image/upload/v1234567890/test.jpg",
        type: :image,
        metadata: %{
          "alt" => "Image alt text",
          "size" => 6_000_000
        }
      }

      Crosspost.Publishing.MediaHelperMock
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_2000,c_limit,q_auto,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 6_000_000)}
      end)
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_1600,c_limit,q_80,f_auto/v1234567890/test.jpg"

        {:error, "Download failed"}
      end)
      |> Mox.expect(:download_file, fn url ->
        assert url ==
                 "https://res.cloudinary.com/test/image/upload/w_1200,c_limit,q_70,f_auto/v1234567890/test.jpg"

        {:ok, :binary.copy(<<1>>, 4_500_000)}
      end)

      Req.Test.stub(X, fn conn ->
        case conn.request_path do
          "/2/media/upload" ->
            conn
            |> Plug.Conn.put_resp_content_type("application/json")
            |> Plug.Conn.send_resp(201, Jason.encode!(%{"id" => "media123"}))
        end
      end)

      assert {:ok, %{type: :image, id: "media123"}} = X.upload_media_file(attachment, user)
    end

    test "handles X API v2 response format with data wrapper", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :image,
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/mycloud/image/upload/v1/test.jpg",
        metadata: %{}
      }

      Req.Test.stub(X, fn conn ->
        assert conn.request_path == "/2/media/upload"
        assert conn.method == "POST"

        conn
        |> send_resp(200, Jason.encode!(%{"data" => %{"id" => "v2_media_id"}}))
      end)

      assert {:ok, %{type: :image, id: "v2_media_id"}} == X.upload_media_file(attachment, user)
    end

    test "handles invalid media ID in response", %{user: user} do
      attachment = %Attachment{
        id: UUID.uuid4(),
        type: :image,
        filename: "test.jpg",
        content_type: "image/jpeg",
        source_url: "https://res.cloudinary.com/mycloud/image/upload/v1/test.jpg",
        metadata: %{}
      }

      Req.Test.stub(X, fn conn ->
        conn
        |> send_resp(200, Jason.encode!(%{"data" => %{"id" => nil}}))
      end)

      assert {:error, "Invalid media ID received from X API"} == X.upload_media_file(attachment, user)
    end
  end
end
