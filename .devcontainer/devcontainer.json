{"name": "jcp", "dockerComposeFile": "../docker-compose.yml", "service": "dev", "runServices": ["postgres", "dev"], "workspaceFolder": "/workspace/crosspost", "postCreateCommand": "apt update && apt install -y zsh less", "customizations": {"vscode": {"extensions": ["sleistner.vscode-fileutils", "kahole.magit", "GitHub.vscode-pull-request-github", "JakeBecker.elixir-ls", "ritvyk.heex-html"], "settings": {"terminal.integrated.shell.linux": "/usr/local/bin/zsh", "editor.formatOnSave": true}}}, "features": {"ghcr.io/devcontainers/features/node:1": {}}}