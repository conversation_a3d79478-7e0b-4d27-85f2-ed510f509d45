// See the Tailwind configuration guide for advanced usage
// https://tailwindcss.com/docs/configuration

const plugin = require("tailwindcss/plugin")
const fs = require("fs")
const path = require("path")

module.exports = {
  darkMode: 'class',
  content: [
    "./js/**/*.js",
    "./css/**/*.css",
    "../lib/crosspost_web.ex",
    "../lib/crosspost_web/**/*.*ex"
  ],
  theme: {
    extend: {
      colors: {
        brand: {
          white: {
            600: '#F3EFDC',
            700: '#F3EFDC'
          },
          gray: {
            600: '#65647C'
          },
          network: {
            x: '#000000',        // X (formerly Twitter)
            linkedin: '#0A66C2', // LinkedIn
            mastodon: '#6364FF', // Masto<PERSON>
            bsky: '#0085FF'      // Bluesky
          }
        },
        gray: {
          25: '#FEFEFE',
          700: '#263243',
          800: '#1F2937',
          900: '#111827'
        },
        red: {
          500: '#B06161',
          700: '#DC8686'
        },
        green: {
          100: '#14532d',
          500: '#3a9a41',
          700: '#80C97D'
        },
        blue: {
          600: '#2A528A',
          700: '#2f5c9b'
        },
        indigo: {
          600: '#4E31AA',
          700: '#6B46C1'
        },
        purple: {
          600: '#6C48C5',
          700: '#8A63D2'
        }
      }
    },
  },
  plugins: [
    require("@tailwindcss/forms"),
    // Allows prefixing tailwind classes with LiveView classes to add rules
    // only when LiveView classes are applied, for example:
    //
    //     <div class="phx-click-loading:animate-ping">
    //
    plugin(({ addVariant }) => addVariant("phx-click-loading", [".phx-click-loading&", ".phx-click-loading &"])),
    plugin(({ addVariant }) => addVariant("phx-submit-loading", [".phx-submit-loading&", ".phx-submit-loading &"])),
    plugin(({ addVariant }) => addVariant("phx-change-loading", [".phx-change-loading&", ".phx-change-loading &"])),

    // Embeds Heroicons (https://heroicons.com) into your app.css bundle
    // See your `CoreComponents.icon/1` for more information.
    //
    plugin(function ({ matchComponents, theme }) {
      let iconsDir = path.join(__dirname, "../deps/heroicons/optimized")
      let values = {}
      let icons = [
        ["", "/24/outline"],
        ["-solid", "/24/solid"],
        ["-mini", "/20/solid"],
        ["-micro", "/16/solid"]
      ]
      icons.forEach(([suffix, dir]) => {
        fs.readdirSync(path.join(iconsDir, dir)).forEach(file => {
          let name = path.basename(file, ".svg") + suffix
          values[name] = { name, fullPath: path.join(iconsDir, dir, file) }
        })
      })
      matchComponents({
        "hero": ({ name, fullPath }) => {
          let content = fs.readFileSync(fullPath).toString().replace(/\r?\n|\r/g, "")
          let size = theme("spacing.6")
          if (name.endsWith("-mini")) {
            size = theme("spacing.5")
          } else if (name.endsWith("-micro")) {
            size = theme("spacing.4")
          }
          return {
            [`--hero-${name}`]: `url('data:image/svg+xml;utf8,${content}')`,
            "-webkit-mask": `var(--hero-${name})`,
            "mask": `var(--hero-${name})`,
            "mask-repeat": "no-repeat",
            "background-color": "currentColor",
            "vertical-align": "middle",
            "display": "inline-block",
            "width": size,
            "height": size
          }
        }
      }, { values })
    })
  ]
}
