@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Editor container styles */
#tiptap-editor-container>[data-network] {
  @apply block;
}

#tiptap-editor-container>[data-network]>div.post-editor.is-focused {
  @apply bg-white dark:bg-gray-900;
}

#tiptap-editor-container>[data-network]>div.post-editor {
  @apply bg-white dark:bg-gray-800;
  @apply border-t border-gray-200 dark:border-gray-700;
  @apply sm:border-x;
  border-radius: 0;
}

#tiptap-editor-container>[data-network]>div.post-editor:last-child {
  @apply border-b;
}

@media (min-width: 640px) {
  #tiptap-editor-container>[data-network]>div.post-editor:first-child {
    @apply rounded-t-lg;
  }

  #tiptap-editor-container>[data-network]>div.post-editor:last-child {
    @apply rounded-b-lg;
  }
}

/* Character counter styles */
[data-char-counters] {
  @apply flex items-center gap-2;
}

#tiptap-editor-container .tiptap p.is-editor-empty:first-child::before {
  color: #98a3ae;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.font-inter {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Brand setup */
.text-brand-white-600 {
  color: theme('colors.brand.white.600');
}

.text-brand-white-700 {
  color: theme('colors.brand.white.700');
}

.text-brand-gray-600 {
  color: theme('colors.brand.gray.600');
}

.font-russo {
  font-family: "Russo One", sans-serif;
  font-weight: 400;
  font-style: normal;
  letter-spacing: 0.02em;
}

/* This file is for your main application CSS */

/* Add this to your app.css or create a new CSS file and import it */
.preview-entry {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.preview-entry:last-child {
  margin-bottom: 0;
}

.preview-entry p {
  margin-bottom: 0.10rem;
}

/* Add this to your existing CSS */
.flash-container {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flash-container .phx-flash {
  width: 100%;
  text-align: center;
  margin-bottom: 0.5rem;
}

.dark #flash-container p {
  color: theme('colors.gray.200');
}

/* Add these styles for mentions */
.mention {
  position: relative;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  background-color: #e5e7eb;
  font-weight: 500;
  color: #4b5563;
  white-space: nowrap;
  text-decoration: none;
}

.mention:hover {
  background-color: #d1d5db;
}

.mention .edit-button {
  display: none;
  position: absolute;
  top: -8px;
  right: -8px;
  padding: 2px;
  background-color: #fff;
  border: 1px solid #d1d5db;
  border-radius: 9999px;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.mention:hover .edit-button {
  display: flex;
}

.tippy-box {
  max-width: none !important;
}

/* Add this to your app.css */
#settings-modal-container {
  max-width: 1024px !important;
  width: 100% !important;
  margin: 0 auto;
}

#settings-modal-content {
  max-width: none !important;
}

.sortable-item {
  touch-action: none;
  user-select: none;
  transition: transform 0.15s ease;
}

.sortable-item.opacity-50 {
  background: white;
  z-index: 50;
}

.drag-handle {
  touch-action: none;
  user-select: none;
}

.drag-handle:hover {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing !important;
}

.cursor-grabbing .drag-handle {
  cursor: grabbing !important;
}

.tippy-box[data-theme~='light-border'] {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.tippy-box[data-theme~='light-border'] .tippy-content {
  padding: 0;
}

/* Add dark mode styles */
.dark {
  color-scheme: dark;
  color: theme('colors.gray.200');
}

.dark body {
  background-color: theme('colors.gray.900');
}

.dark .editor-content {
  color: theme('colors.gray.200');
}

.dark .ProseMirror p.is-editor-empty:first-child::before {
  color: theme('colors.gray.500');
}

/* Network toggles dark mode */
.dark #network-toggles button:not([data-active="true"]) {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

/* Preview panel dark mode */
.dark #preview-panel {
  border-color: theme('colors.gray.700');
}

/* Post entries dark mode */
.dark .preview-entry {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
}

/* Mentions dark mode update */
.dark .mention {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
}

.dark .mention:hover {
  background-color: theme('colors.gray.600');
}

/* Buttons and interactive elements dark mode */
.dark button[data-continue-block] {
  color: theme('colors.gray.300');
  background-color: theme('colors.gray.700');
}

.dark button[data-continue-block]:hover {
  color: theme('colors.gray.200');
  background-color: theme('colors.gray.600');
}

.dark button[data-remove-block] {
  color: theme('colors.gray.400');
}

.dark button[data-remove-block]:hover {
  color: theme('colors.red.400');
}

/* Move buttons dark mode */
.dark [data-move-up],
.dark [data-move-down] {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

.dark [data-move-up]:hover,
.dark [data-move-down]:hover {
  background-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

/* Upload button dark mode */
.dark .upload-button {
  color: theme('colors.gray.400');
}

.dark .upload-button:hover {
  color: theme('colors.gray.300');
  background-color: theme('colors.gray.700');
}

/* Tags button dark mode */
.dark [data-tags-button] {
  color: theme('colors.gray.300');
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
}

.dark [data-tags-button]:hover {
  color: theme('colors.gray.200');
  background-color: theme('colors.gray.600');
}

/* Tooltips dark mode */
.dark .tippy-box[data-theme~='light-border'] {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
}

/* Schedule button dark mode */
.dark [data-schedule-menu] {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

.dark [data-schedule-menu] button {
  color: theme('colors.gray.300');
}

.dark [data-schedule-menu] button:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
}

/* Attachments preview dark mode */
.dark .attachments-preview img {
  border-color: theme('colors.gray.700');
}

.dark [data-image-container] {
  background-color: theme('colors.gray.700');
}

/* Code block dark mode adjustments */
.dark .prose pre {
  background-color: theme('colors.gray.900');
  border-color: theme('colors.gray.700');
}

.dark .prose code {
  color: theme('colors.gray.200');
}

/* Character counter dark mode */
.dark [data-block-counters]>div {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

.dark [data-block-counters]>div.text-red-600 {
  background-color: theme('colors.red.900');
  color: theme('colors.red.300');
}

/* Transitions */
.dark-mode-transition {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Add these styles to your existing dark mode section */

/* Sidebar dark mode */
.dark .sidebar-filters a,
.dark .sidebar-filters button {
  color: theme('colors.gray.300');
}

.dark .sidebar-filters a:hover,
.dark .sidebar-filters button:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

/* Post list items dark mode */
.dark nav.space-y-1 a {
  color: theme('colors.gray.300');
}

.dark nav.space-y-1 a:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

.dark nav.space-y-1 a.bg-gray-100 {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

/* Post content in sidebar dark mode */
.dark nav.space-y-1 .text-gray-900 {
  color: theme('colors.gray.100');
}

.dark nav.space-y-1 .text-gray-600 {
  color: theme('colors.gray.400');
}

.dark nav.space-y-1 .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Filter section dark mode */
.dark .text-gray-500.uppercase {
  color: theme('colors.gray.400');
}

/* Count badges dark mode */
.dark .bg-gray-100.text-gray-600 {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

/* Active filter dark mode */
.dark button[class*="bg-gray-100"] {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

/* Filter icons dark mode */
.dark .text-gray-400 {
  color: theme('colors.gray.500');
}

/* Hover states for filter items */
.dark .text-gray-600:hover {
  color: theme('colors.gray.200');
}

/* Status badge dark mode adjustments */
.dark .bg-purple-100 {
  background-color: theme('colors.purple.900');
  color: theme('colors.purple.200');
}

.dark .bg-green-100 {
  background-color: theme('colors.green.900');
  color: theme('colors.green.200');
}

.dark .bg-yellow-100 {
  background-color: theme('colors.yellow.900');
  color: theme('colors.yellow.200');
}

/* Add these dark mode styles for preview */

/* Preview header dark mode */
.dark #preview-panel .border-b {
  border-color: theme('colors.gray.700');
}

/* Preview tabs dark mode */
.dark #preview-panel .border-gray-200 {
  border-color: theme('colors.gray.700');
}

.dark #preview-panel .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #preview-panel .text-gray-700 {
  color: theme('colors.gray.300');
}

.dark #preview-panel .hover\:text-gray-700:hover {
  color: theme('colors.gray.200');
}

.dark #preview-panel .hover\:border-gray-300:hover {
  border-color: theme('colors.gray.600');
}

/* Preview post cards dark mode */
.dark #preview-panel .bg-white {
  background-color: theme('colors.gray.800');
}

.dark #preview-panel .shadow-sm {
  --tw-shadow-color: rgba(0, 0, 0, 0.3);
}

/* Post header section dark mode */
.dark #preview-panel .bg-gray-50\/80 {
  background-color: theme('colors.gray.800');
}

.dark #preview-panel .border-gray-100 {
  border-color: theme('colors.gray.700');
}

/* User info dark mode */
.dark #preview-panel .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #preview-panel .bg-gray-200 {
  background-color: theme('colors.gray.700');
}

.dark #preview-panel .text-gray-400 {
  color: theme('colors.gray.500');
}

/* Thread badge dark mode */
.dark #preview-panel .bg-gray-100 {
  background-color: theme('colors.gray.700');
}

/* Post content dark mode */
.dark #preview-panel .text-gray-900 {
  color: theme('colors.gray.100');
}

.dark #preview-panel .post-content {
  color: theme('colors.gray.200');
}

/* Media attachments dark mode */
.dark #preview-panel .rounded-md {
  border: 1px solid theme('colors.gray.700');
}

/* Close button dark mode */
.dark #preview-panel button.text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #preview-panel button.hover\:text-gray-700:hover {
  color: theme('colors.gray.300');
}

.dark #preview-panel button.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.700');
}

/* Network tabs dark mode */
.dark #preview-panel .text-blue-600 {
  color: theme('colors.blue.400');
}

.dark #preview-panel .border-blue-500 {
  border-color: theme('colors.blue.400');
}

/* Add these dark mode styles for the show component */

/* Post details container dark mode */
.dark #post-details {
  color: theme('colors.gray.200');
}

/* Header section dark mode */
.dark #post-details .text-2xl {
  color: theme('colors.gray.100');
}

/* Status cards dark mode */
.dark #post-details .bg-white {
  background-color: theme('colors.gray.800');
}

.dark #post-details .border-gray-200\/60 {
  border-color: theme('colors.gray.700');
}

/* Network status section dark mode */
.dark #post-details .text-gray-600 {
  color: theme('colors.gray.400');
}

.dark #post-details .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #post-details .text-blue-600 {
  color: theme('colors.blue.400');
}

.dark #post-details .hover\:text-blue-800:hover {
  color: theme('colors.blue.300');
}

/* Preview tabs dark mode */
.dark #post-details .border-gray-200 {
  border-color: theme('colors.gray.700');
}

.dark #post-details .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #post-details .hover\:text-gray-700:hover {
  color: theme('colors.gray.200');
}

.dark #post-details .hover\:border-gray-300:hover {
  border-color: theme('colors.gray.600');
}

/* Post content section dark mode */
.dark #post-details .bg-gray-50\/80 {
  background-color: theme('colors.gray.800');
}

.dark #post-details .border-gray-100 {
  border-color: theme('colors.gray.700');
}

.dark #post-details .text-gray-900 {
  color: theme('colors.gray.100');
}

/* User info dark mode */
.dark #post-details .bg-gray-200 {
  background-color: theme('colors.gray.700');
}

.dark #post-details .text-gray-400 {
  color: theme('colors.gray.500');
}

/* Social actions dark mode */
.dark #post-details .border-gray-200 {
  border-color: theme('colors.gray.700');
}

.dark #post-details .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark #post-details .hover\:text-blue-600:hover {
  color: theme('colors.blue.400');
}

.dark #post-details .hover\:text-green-600:hover {
  color: theme('colors.green.400');
}

.dark #post-details .hover\:text-red-600:hover {
  color: theme('colors.red.400');
}

/* Network status icons dark mode */
.dark #post-details [data-status="published"] {
  color: theme('colors.green.400');
}

.dark #post-details [data-status="scheduled"],
.dark #post-details [data-status="pending"] {
  color: theme('colors.purple.400');
}

.dark #post-details [data-status="failed"] {
  color: theme('colors.red.400');
}

/* Action buttons dark mode */
.dark #post-details .bg-blue-500 {
  background-color: theme('colors.blue.600');
}

.dark #post-details .bg-blue-500:hover {
  background-color: theme('colors.blue.700');
}

/* Media attachments dark mode */
.dark #post-details .rounded-md {
  border: 1px solid theme('colors.gray.700');
}

/* Video overlay dark mode */
.dark #post-details .bg-black.bg-opacity-50 {
  background-color: theme('colors.black');
  opacity: 0.6;
}

/* Status badge colors dark mode */
.dark #post-details .ring-green-500 {
  --tw-ring-color: theme('colors.green.400');
}

.dark #post-details .ring-purple-500 {
  --tw-ring-color: theme('colors.purple.400');
}

.dark #post-details .ring-red-500 {
  --tw-ring-color: theme('colors.red.400');
}

.dark #post-details .ring-gray-300 {
  --tw-ring-color: theme('colors.gray.600');
}

/* Update the network status section in dark mode */
.dark #post-details .flex[data-network] {
  background-color: theme('colors.gray.800');
  /* Match container background */
}

/* Update the network status icon container in dark mode */
.dark #post-details [data-status] .rounded-full {
  background-color: theme('colors.gray.800');
  /* Match container background */
  border: 1px solid theme('colors.gray.700');
}

/* Update the ring color for different statuses in dark mode */
.dark #post-details [data-status="draft"] .ring-gray-300 {
  --tw-ring-color: theme('colors.gray.600');
  --tw-ring-opacity: 0.5;
}

/* Ensure the font color for the network name is visible */
.dark #post-details .flex[data-network] .font-medium {
  color: theme('colors.gray.200');
}

/* Ensure the status text is visible but muted */
.dark #post-details .flex[data-network] .text-gray-600 {
  color: theme('colors.gray.400');
}

/* Calendar and Time Picker Dark Mode */

/* Calendar container */
.dark .calendar-widget {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.200');
}

/* Calendar header */
.dark .calendar-widget button.text-gray-600 {
  color: theme('colors.gray.400');
}

.dark .calendar-widget button.hover\:text-gray-900:hover {
  color: theme('colors.gray.200');
}

.dark .calendar-widget button.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.700');
}

.dark .calendar-widget .text-lg.font-semibold.text-gray-900 {
  color: theme('colors.gray.100');
}

/* Calendar weekday headers */
.dark .calendar-widget .text-xs.font-medium.text-gray-500 {
  color: theme('colors.gray.300');
}

.dark .calendar-widget .bg-gray-50 {
  background-color: theme('colors.gray.800');
}

/* Calendar grid */
.dark .calendar-widget .bg-gray-200 {
  background-color: theme('colors.gray.700');
}

/* Calendar cells */
.dark .calendar-widget .bg-white {
  background-color: theme('colors.gray.800');
}

.dark .calendar-widget .text-gray-700 {
  color: theme('colors.gray.300');
}

.dark .calendar-widget .opacity-50 {
  color: theme('colors.gray.500');
}

/* Today's cell */
.dark .calendar-widget .bg-blue-50 {
  background-color: theme('colors.blue.900');
}

.dark .calendar-widget .text-blue-600 {
  color: theme('colors.blue.400');
}

/* Time picker */
.dark .time-hours,
.dark .time-minutes,
.dark .time-period {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

.dark .time-hours:focus,
.dark .time-minutes:focus,
.dark .time-period:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Time separator */
.dark .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Timezone display */
.dark .text-sm.text-gray-500.bg-gray-50 {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.400');
  border-color: theme('colors.gray.700');
}

/* Save button */
.dark .save-schedule {
  background-color: theme('colors.blue.600');
  color: theme('colors.white');
}

.dark .save-schedule:hover {
  background-color: theme('colors.blue.700');
}

/* Calendar event styles */
.dark .calendar-widget .bg-purple-50 {
  background-color: theme('colors.purple.900');
}

.dark .calendar-widget .text-purple-700 {
  color: theme('colors.purple.300');
}

.dark .calendar-widget .hover\:bg-purple-100:hover {
  background-color: theme('colors.purple.800');
}

.dark .calendar-widget .bg-green-50 {
  background-color: theme('colors.green.900');
}

.dark .calendar-widget .text-green-700 {
  color: theme('colors.green.300');
}

.dark .calendar-widget .hover\:bg-green-100:hover {
  background-color: theme('colors.green.800');
}

/* Show more button */
.dark .calendar-widget button[data-show-more] {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.300');
}

.dark .calendar-widget button[data-show-more]:hover {
  background-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

/* Calendar expanded view */
.dark #day-overlay-modal {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

.dark #day-overlay-modal .text-gray-900 {
  color: theme('colors.gray.100');
}

.dark #day-overlay-modal .border-b {
  border-color: theme('colors.gray.700');
}

/* Scheduling pane dark mode */
.dark .fixed h2.text-lg {
  color: theme('colors.gray.100');
}

.dark .fixed .border-b {
  border-color: theme('colors.gray.700');
}

/* Close button */
.dark .fixed .close-scheduling {
  color: theme('colors.gray.400');
}

.dark .fixed .close-scheduling:hover {
  color: theme('colors.gray.200');
  background-color: theme('colors.gray.700');
}

/* Calendar day buttons */
.dark .calendar-day.text-gray-900 {
  color: theme('colors.gray.200');
}

.dark .calendar-day.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.700');
}

/* Calendar header text */
.dark .calendar-widget .text-gray-900 {
  color: theme('colors.gray.100');
}

/* Calendar navigation buttons */
.dark .calendar-widget button.text-gray-600 {
  color: theme('colors.gray.400');
}

.dark .calendar-widget button.hover\:text-gray-900:hover {
  color: theme('colors.gray.200');
}

/* Time picker inputs dark mode */
.dark input[type="number"],
.dark select {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

.dark input[type="number"]:focus,
.dark select:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Timezone info */
.dark .bg-gray-50 {
  background-color: theme('colors.gray.800');
}

.dark .border-gray-200 {
  border-color: theme('colors.gray.700');
}

/* Save button - already styled in previous dark mode styles */
.dark .save-schedule {
  background-color: theme('colors.blue.600');
}

.dark .save-schedule:hover {
  background-color: theme('colors.blue.700');
}

/* Fix continue button plus icon in dark mode */
.dark button[data-continue-block] svg {
  color: theme('colors.gray.300');
}

.dark button[data-continue-block]:hover svg {
  color: theme('colors.gray.200');
}

/* Static pages dark mode */

/* Common text colors */
.dark .text-gray-900 {
  color: theme('colors.gray.100');
}

.dark .text-gray-600 {
  color: theme('colors.gray.400');
}

.dark .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Pricing page */
.dark .bg-white {
  background-color: theme('colors.gray.900');
}

/* Pricing cards */
.dark .ring-gray-200 {
  --tw-ring-color: theme('colors.gray.700');
}

/* Feature comparison table */
.dark .bg-gray-50 {
  background-color: theme('colors.gray.800');
}

.dark .divide-gray-300>*+* {
  border-color: theme('colors.gray.700');
}

.dark .divide-gray-200>*+* {
  border-color: theme('colors.gray.700');
}

/* Contact page */
.dark .ring-gray-900\/5 {
  --tw-ring-color: theme('colors.gray.700');
}

.dark .shadow-sm {
  --tw-shadow-color: rgba(0, 0, 0, 0.3);
}

/* About page */
.dark .prose {
  color: theme('colors.gray.300');
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4 {
  color: theme('colors.gray.100');
}

.dark .prose strong {
  color: theme('colors.gray.200');
}

.dark .prose a {
  color: theme('colors.blue.400');
}

.dark .prose a:hover {
  color: theme('colors.blue.300');
}

/* Privacy & Terms pages */
.dark .prose ul>li::before {
  background-color: theme('colors.gray.500');
}

.dark .prose ol>li::before {
  color: theme('colors.gray.400');
}

.dark .border-gray-200 {
  border-color: theme('colors.gray.700');
}

/* Contact cards */
.dark .bg-white.rounded-2xl {
  background-color: theme('colors.gray.800');
}

/* Social links */
.dark .text-indigo-600 {
  color: theme('colors.indigo.400');
}

.dark .text-indigo-600:hover {
  color: theme('colors.indigo.300');
}

/* Feature cards */
.dark .bg-indigo-100 {
  background-color: theme('colors.indigo.900');
}

.dark .text-indigo-600 {
  color: theme('colors.indigo.400');
}

/* Email links */
.dark .bg-gray-50.rounded-lg {
  background-color: theme('colors.gray.800');
}

/* Calendar day names and numbers visibility fix for dark mode */

/* Day names (Su, Mo, Tu, etc.) */
.dark .calendar-widget .text-xs.font-medium.text-gray-500 {
  color: theme('colors.gray.300') !important;
}

/* Current month day numbers */
.dark .calendar-day.text-gray-900 {
  color: theme('colors.gray.200') !important;
}

/* Previous/Next month day numbers - make them more muted but still visible */
.dark .calendar-day.text-gray-300 {
  color: theme('colors.gray.600') !important;
}

/* Disabled days (past dates) */
.dark .calendar-day[disabled] {
  color: theme('colors.gray.600') !important;
  opacity: 0.5;
}

/* Selected day */
.dark .calendar-day.bg-blue-600 {
  color: theme('colors.white') !important;
  background-color: theme('colors.blue.600') !important;
}

/* Today's date */
.dark .calendar-day.border-2.border-blue-600 {
  color: theme('colors.blue.400') !important;
  border-color: theme('colors.blue.500') !important;
}

/* Hover state for available days */
.dark .calendar-day:not([disabled]):hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100') !important;
}

/* Calendar container background */
.dark .calendar-widget {
  background-color: theme('colors.gray.800');
}

/* Month name and navigation */
.dark .calendar-widget .text-lg.font-semibold.text-gray-900 {
  color: theme('colors.gray.100') !important;
}

/* Calendar component dark mode */

/* Calendar container */
.dark #calendar-container {
  display: flex;
  flex-direction: column;
  background-color: theme('colors.gray.900');
  border: 1px solid theme('colors.gray.700');
  border-radius: 0.75rem;
  overflow: hidden;
}

/* Dark mode calendar container */
.dark #calendar-container {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

/* Week view specific styles */
.grid.grid-cols-8 {
  display: grid;
  grid-template-columns: auto repeat(7, 1fr);
  gap: 1px;
  background-color: theme('colors.gray.200');
}

.dark .grid.grid-cols-8 {
  background-color: theme('colors.gray.700');
}

/* Time column */
.grid.grid-cols-8>div:first-child {
  min-width: 4rem;
  width: 4rem;
}

/* Hour cells in week view */
.grid.grid-cols-8 .h-12 {
  height: 6rem !important;
}

/* Adjust post positioning for larger cells */
.grid.grid-cols-8 [data-post] {
  min-height: 1.5rem;
}

/* Header and cells */
.grid.grid-cols-8 .bg-gray-50 {
  background-color: theme('colors.gray.50');
}

.dark .grid.grid-cols-8 .bg-gray-50 {
  background-color: theme('colors.gray.800');
}

.grid.grid-cols-8 .bg-white {
  background-color: theme('colors.white');
}

.dark .grid.grid-cols-8 .bg-white {
  background-color: theme('colors.gray.800');
}

/* Calendar grid - both month and week views */
.grid.grid-cols-7,
.grid.grid-cols-8 {
  display: grid;
  gap: 1px;
  background-color: theme('colors.gray.200');
  width: 100%;
}

/* Grid columns setup */
.grid.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid.grid-cols-8 {
  grid-template-columns: 4rem repeat(7, minmax(0, 1fr));
}

/* Calendar cells - both month and week views */
.grid.grid-cols-7>div,
.grid.grid-cols-8>div {
  min-width: 0;
  /* Prevent cell expansion */
  background-color: theme('colors.white');
}

/* Header row height */
.grid.grid-cols-7>div:nth-child(-n+7),
.grid.grid-cols-8>div:nth-child(-n+8) {
  height: 2.5rem;
  padding: 0.5rem;
  background-color: theme('colors.gray.100');
}

/* Month view day cells */
.grid.grid-cols-7>div:nth-child(n+8) {
  height: 140px;
  overflow-y: auto;
}

/* Week view hour cells */
.grid.grid-cols-8 .h-12 {
  height: 6rem !important;
}

/* Dark mode adjustments */
.dark .grid.grid-cols-7,
.dark .grid.grid-cols-8 {
  background-color: theme('colors.gray.700');
}

.dark .grid.grid-cols-7>div,
.dark .grid.grid-cols-8>div {
  background-color: theme('colors.gray.800');
}

.dark .grid.grid-cols-7>div:nth-child(-n+7),
.dark .grid.grid-cols-8>div:nth-child(-n+8) {
  background-color: theme('colors.gray.700');
}

/* Calendar container */
#calendar-container {
  display: flex;
  flex-direction: column;
  background-color: theme('colors.white');
  border: 1px solid theme('colors.gray.200');
  border-radius: 0.75rem;
  overflow: hidden;
  width: 100%;
}

/* Dark mode calendar container */
.dark #calendar-container {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

/* Calendar grid - both month and week views */
.grid.grid-cols-7 {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: theme('colors.gray.200');
  width: 100%;
}

.dark .grid.grid-cols-7 {
  background-color: theme('colors.gray.700');
}

/* Calendar header row (day names) */
.grid.grid-cols-7>div:nth-child(-n+7),
.grid.grid-cols-8>div:nth-child(-n+8) {
  height: 2.5rem;
  padding: 0.5rem;
  background-color: theme('colors.gray.100');
  font-weight: 600;
}

/* Dark mode header row */
.dark .grid.grid-cols-7>div:nth-child(-n+7),
.dark .grid.grid-cols-8>div:nth-child(-n+8) {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

/* Day numbers */
.grid.grid-cols-7>div:nth-child(n+8) .text-sm.font-medium.text-gray-700,
.grid.grid-cols-8 .text-sm.font-medium.text-gray-700 {
  color: theme('colors.gray.800');
  font-weight: 600;
}

/* Dark mode day numbers */
.dark .grid.grid-cols-7>div:nth-child(n+8) .text-sm.font-medium.text-gray-700,
.dark .grid.grid-cols-8 .text-sm.font-medium.text-gray-700 {
  color: theme('colors.gray.200');
}

/* Today's date */
.text-blue-600 {
  font-weight: 700;
}

/* Dark mode today's date */
.dark .text-blue-600 {
  color: theme('colors.blue.600');
}

/* Time column in week view */
.grid.grid-cols-8>div:first-child {
  background-color: theme('colors.gray.100');
  font-weight: 500;
}

/* Dark mode time column */
.dark .grid.grid-cols-8>div:first-child {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.200');
}

/* Calendar post styles - light mode */
[data-network="canonical"] .bg-green-50 {
  background-color: theme('colors.green.50');
}

[data-network="canonical"] .text-green-700 {
  color: theme('colors.green.700');
}

[data-network="canonical"] .hover\:bg-green-100:hover {
  background-color: theme('colors.green.100');
}

/* Calendar post styles - dark mode */
.dark [data-network="canonical"] .bg-green-50 {
  background-color: theme('colors.green.950/50');
}

.dark [data-network="canonical"] .text-green-700 {
  color: theme('colors.green.400');
}

.dark [data-network="canonical"] .hover\:bg-green-100:hover {
  background-color: theme('colors.green.900/50');
}

/* Tooltip styles - light mode */
.tippy-box[data-theme~='light-border'] {
  background-color: theme('colors.white');
  border: 1px solid theme('colors.gray.200');
}

/* Tooltip styles - dark mode */
.dark .tippy-box[data-theme~='light-border'] {
  background-color: theme('colors.gray.800');
  border: 1px solid theme('colors.gray.700');
}

.dark .tippy-box[data-theme~='light-border'] .text-gray-900 {
  color: theme('colors.gray.100');
}

.dark .tippy-box[data-theme~='light-border'] .text-gray-600 {
  color: theme('colors.gray.400');
}

/* Remove truncation from tooltip content */
.tippy-box[data-theme~='light-border'] .truncate {
  text-overflow: unset;
  overflow: visible;
  white-space: normal;
}

/* Ensure the toggle button is visible in dark mode */
.dark .sidebar-toggle {
  color: theme('colors.gray.400');
}

.dark .sidebar-toggle:hover {
  color: theme('colors.gray.100');
  background-color: theme('colors.gray.700');
}

/* Settings UI Dark Mode - Styles only */

/* Modal and container backgrounds */
.dark #settings-modal {
  background-color: theme('colors.gray.900');
}

.dark #settings-modal .modal-content {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

/* Form inputs */
.dark #settings-modal input,
.dark #settings-modal select,
.dark #settings-modal textarea {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

.dark #settings-modal input:focus,
.dark #settings-modal select:focus,
.dark #settings-modal textarea:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Form labels and text */
.dark #settings-modal label {
  color: theme('colors.gray.300');
}

.dark #settings-modal .text-sm.text-gray-600 {
  color: theme('colors.gray.400');
}

/* Navigation links */
.dark #settings-modal nav a {
  color: theme('colors.gray.300');
}

.dark #settings-modal nav a:hover {
  background-color: theme('colors.gray.800');
}

.dark #settings-modal nav a.bg-blue-600 {
  color: theme('colors.white');
}

.dark #settings-modal nav a.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.800');
}

/* Section headers */
.dark #settings-modal h3 {
  color: theme('colors.gray.100');
}

/* Help text */
.dark #settings-modal .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Error messages */
.dark #settings-modal .invalid-feedback {
  color: theme('colors.red.400');
}

/* Delete account section */
.dark #settings-modal .delete-account {
  background-color: theme('colors.red.900/10');
  border-color: theme('colors.red.600');
}

.dark #settings-modal .delete-account .text-red-600 {
  color: theme('colors.red.400');
}

/* Social connection cards in Settings - Dark mode */
.dark #settings-modal .bg-white.shadow.rounded-lg {
  background-color: theme('colors.gray.900');
  border: 1px solid theme('colors.gray.700');
}

/* Card title */
.dark #settings-modal .text-lg.font-medium.text-gray-900 {
  color: theme('colors.gray.200');
}

/* Card description */
.dark #settings-modal .mt-1.text-sm.text-gray-600 {
  color: theme('colors.gray.400');
}

/* Connection status */
.dark #settings-modal .flex.items-center.gap-2 .text-gray-600 {
  color: theme('colors.gray.400');
}

/* Connected status */
.dark #settings-modal .text-green-600 {
  color: theme('colors.green.400');
}

/* Connect button */
.dark #settings-modal .bg-blue-600 {
  background-color: theme('colors.blue.600');
}

.dark #settings-modal .bg-blue-600:hover {
  background-color: theme('colors.blue.700');
}

/* Disconnect button */
.dark #settings-modal .bg-red-600 {
  background-color: theme('colors.red.600');
}

.dark #settings-modal .bg-red-600:hover {
  background-color: theme('colors.red.700');
}

/* Network icons */
.dark #settings-modal [data-network] {
  background-color: theme('colors.gray.800');
  border: 1px solid theme('colors.gray.700');
}

/* Individual network icon colors */
.dark #settings-modal [data-network="x"] svg {
  color: theme('colors.gray.600');
}

.dark #settings-modal [data-network="linkedin"] svg {
  color: theme('colors.blue.400');
}

.dark #settings-modal [data-network="mastodon"] svg {
  color: theme('colors.purple.400');
}

.dark #settings-modal [data-network="bsky"] svg {
  color: theme('colors.blue.400');
}

/* Social Connections Dark Mode */

/* Connected cards */
.dark .bg-emerald-50 {
  background-color: theme('colors.emerald.900/30');
}

.dark .border-emerald-200 {
  border-color: theme('colors.emerald.700');
}

.dark .hover\:bg-rose-50:hover {
  background-color: theme('colors.rose.900/30');
}

.dark .hover\:border-rose-200:hover {
  border-color: theme('colors.rose.700');
}

/* Unconnected cards */
.dark .border-gray-200 {
  border-color: theme('colors.gray.700');
}

.dark .hover\:bg-gray-50:hover {
  background-color: theme('colors.gray.800');
}

.dark .hover\:border-gray-300:hover {
  border-color: theme('colors.gray.600');
}

/* Text colors */
.dark .text-base.font-semibold {
  color: theme('colors.gray.200');
}

.dark .text-xs.text-gray-600 {
  color: theme('colors.gray.400');
}

/* Icon backgrounds */
.dark .bg-gray-200 {
  background-color: theme('colors.gray.700');
}

.dark .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Network icons */
.dark .bg-blue-400,
.dark .bg-blue-600,
.dark .group-hover\:bg-blue-400:hover {
  background-color: theme('colors.blue.600');
}

.dark .group-hover\:bg-purple-600:hover {
  background-color: theme('colors.purple.600');
}

.dark .group-hover\:bg-black:hover {
  background-color: theme('colors.gray.900');
}

/* Tooltip */
.dark .group\/tooltip .bg-gray-800 {
  background-color: theme('colors.gray.700');
}

/* Disconnect icon */
.dark .text-rose-500 {
  color: theme('colors.rose.400');
}

/* Login form dark mode */
.dark .bg-white\/60 {
  background-color: theme('colors.gray.800/60');
}

.dark .ring-gray-900\/5 {
  --tw-ring-color: theme('colors.gray.700/50');
}

/* Form inputs */
.dark input[type="email"],
.dark input[type="password"] {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.100');
}

.dark input[type="email"]:focus,
.dark input[type="password"]:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Labels */
.dark .text-zinc-800 {
  color: theme('colors.gray.200');
}

/* Checkbox */
.dark input[type="checkbox"] {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
}

.dark input[type="checkbox"]:checked {
  background-color: theme('colors.blue.600');
  border-color: theme('colors.blue.600');
}

.dark .text-zinc-600 {
  color: theme('colors.gray.400');
}

.dark .hover\:text-blue-500:hover {
  color: theme('colors.blue.300');
}

/* Help text */
.dark .text-gray-600 {
  color: theme('colors.gray.400');
}

/* Form background */
.dark .bg-white {
  background-color: transparent;
}

/* Main navigation styles */
header.bg-transparent .main-nav-light {
  @apply text-gray-800;
}

header.bg-transparent .main-nav-light a {
  @apply text-gray-700;
}

header.bg-transparent .main-nav-light a:hover {
  color: theme('colors.gray.900');
}

header.bg-transparent .main-nav-light #theme-toggle {
  @apply text-gray-700;
}

header.bg-transparent .main-nav-light #theme-toggle:hover {
  color: theme('colors.gray.900');
}

/* Dark header (logged in) navigation styles */
header.bg-gray-800 .main-nav-light a,
header.bg-gray-800 .main-nav-light #theme-toggle,
header.bg-gray-800 nav:not(.main-nav-light) a,
header.bg-gray-800 nav:not(.main-nav-light) #theme-toggle {
  @apply text-gray-300;
}

header.bg-gray-800 .main-nav-light a:hover,
header.bg-gray-800 .main-nav-light #theme-toggle:hover,
header.bg-gray-800 nav:not(.main-nav-light) a:hover,
header.bg-gray-800 nav:not(.main-nav-light) #theme-toggle:hover {
  color: theme('colors.white');
}

/* Dark mode overrides */
.dark header.bg-transparent .main-nav-light a,
.dark header.bg-transparent .main-nav-light #theme-toggle {
  color: theme('colors.white');
}

.dark header.bg-transparent .main-nav-light a:hover,
.dark header.bg-transparent .main-nav-light #theme-toggle:hover {
  color: theme('colors.blue.400');
}

/* Action button group styles */
.inline-flex button[data-schedule-button][disabled]+button[data-schedule-dropdown] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Dark mode adjustments */
.dark .inline-flex button[data-schedule-button][disabled]+button[data-schedule-dropdown] {
  background-color: theme('colors.blue.600/50');
  border-color: theme('colors.blue.500/10');
}

/* Ensure consistent border color when disabled */
.dark .inline-flex button[data-schedule-button][disabled] {
  border-right-color: theme('colors.blue.500/10');
}

/* Scheduling calendar dark mode */

/* Container and background */
.dark #scheduling-panel .bg-white {
  background-color: theme('colors.gray.900');
}

/* Header section */
.dark .fixed h2.text-lg {
  color: theme('colors.gray.100');
}

.dark .fixed .border-b {
  border-color: theme('colors.gray.700');
}

/* Close button */
.dark .close-scheduling {
  color: theme('colors.gray.400');
}

.dark .close-scheduling:hover {
  color: theme('colors.gray.200');
  background-color: theme('colors.gray.700');
}

/* Calendar navigation */
.dark .calendar-widget button.text-gray-600 {
  color: theme('colors.gray.400');
}

.dark .calendar-widget button.hover\:text-gray-900:hover {
  color: theme('colors.gray.200');
}

.dark .calendar-widget button.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.700');
}

/* Month text */
.dark .calendar-widget .text-lg.font-semibold.text-gray-900 {
  color: theme('colors.gray.100');
}

/* Day names */
.dark .calendar-widget .text-xs.font-medium.text-gray-500 {
  color: theme('colors.gray.400');
}

/* Calendar days */
.dark .calendar-day.text-gray-900 {
  color: theme('colors.gray.200');
}

.dark .calendar-day.text-gray-300 {
  color: theme('colors.gray.600');
}

.dark .calendar-day.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.700');
}

/* Selected day */
.dark .calendar-day.bg-blue-600 {
  background-color: theme('colors.blue.600');
}

.dark .calendar-day.bg-blue-600:hover {
  background-color: theme('colors.blue.700');
}

/* Time picker */
.dark .time-hours,
.dark .time-minutes,
.dark .time-period {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

.dark .time-hours:focus,
.dark .time-minutes:focus,
.dark .time-period:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Time separator */
.dark .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Timezone display */
.dark .text-sm.text-gray-500.bg-gray-50 {
  background-color: theme('colors.gray.800');
  color: theme('colors.gray.400');
  border-color: theme('colors.gray.700');
}

/* Save button */
.dark .save-schedule {
  background-color: theme('colors.blue.600');
}

.dark .save-schedule:hover {
  background-color: theme('colors.blue.700');
}

/* Calendar container */
.calendar-widget {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Calendar navigation buttons */
.calendar-widget button {
  border-radius: 0.375rem;
}

/* Calendar days */
.calendar-day {
  border-radius: 0.375rem;
}

/* Time picker inputs */
.time-hours,
.time-minutes,
.time-period {
  border-radius: 0.375rem;
}

/* Timezone display */
.text-sm.text-gray-500.bg-gray-50 {
  border-radius: 0.375rem;
}

/* Save button */
.save-schedule {
  border-radius: 0.375rem;
}

/* Dark mode adjustments */
.dark .fixed.bg-white {
  border: 1px solid theme('colors.gray.700');
}

/* Content area */
.fixed .flex-1 {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/* Mention UI Dark Mode */

/* Container */
.dark .mention-suggestions {
  color: theme('colors.gray.200');
}

.dark .mention-suggestions .bg-white {
  background-color: theme('colors.gray.800');
}

/* Input fields */
.dark .mention-suggestions input {
  background-color: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

.dark .mention-suggestions input:focus {
  border-color: theme('colors.blue.500');
  --tw-ring-color: theme('colors.blue.500');
}

/* Dividers */
.dark .mention-suggestions .divide-gray-100>*+* {
  border-color: theme('colors.gray.700');
}

/* Section backgrounds */
.dark .mention-suggestions .bg-gray-50 {
  background-color: theme('colors.gray.800');
}

/* User items */
.dark .mention-suggestions button.hover\:bg-gray-50:hover {
  background-color: theme('colors.gray.700');
}

/* Text colors */
.dark .mention-suggestions .text-gray-500 {
  color: theme('colors.gray.400');
}

.dark .mention-suggestions .text-gray-900 {
  color: theme('colors.gray.200');
}

/* Network labels */
.dark .mention-suggestions .text-blue-500 {
  color: theme('colors.blue.400');
}

.dark .mention-suggestions .text-purple-600 {
  color: theme('colors.purple.400');
}

/* Save button */
.dark .mention-suggestions [data-save-profile] {
  background-color: theme('colors.blue.600');
}

.dark .mention-suggestions [data-save-profile]:hover:not(:disabled) {
  background-color: theme('colors.blue.700');
}

/* Selected profile section */
.dark .mention-suggestions [data-selected-profile] {
  background-color: theme('colors.gray.800');
}

/* Mention UI styles */
.mention-suggestions {
  background-color: theme('colors.white');
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

/* Dark mode */
.dark .mention-suggestions {
  background-color: theme('colors.gray.800');
}

/* Container */
.mention-suggestions .bg-white {
  background-color: theme('colors.white');
}

.dark .mention-suggestions .bg-white {
  background-color: theme('colors.gray.800');
}

/* Make sure the tippy container is also properly styled */
.tippy-box[data-theme~='light-border'] {
  background-color: theme('colors.white') !important;
}

.dark .tippy-box[data-theme~='light-border'] {
  background-color: theme('colors.gray.800') !important;
}

/* User menu dropdown styles */
.dropdown-menu {
  background-color: theme('colors.white');
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid theme('colors.gray.200');
}

/* Dark mode adjustments */
.dark .dropdown-menu {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

/* Menu items */
.dark .dropdown-menu a {
  color: theme('colors.gray.300');
}

.dark .dropdown-menu a:hover {
  background-color: theme('colors.gray.700');
  color: theme('colors.gray.100');
}

/* Divider line */
.dark .dropdown-menu .border-gray-100 {
  border-color: theme('colors.gray.700');
}

/* Logout button */
.dark .dropdown-menu .text-red-600 {
  color: theme('colors.red.400');
}

.dark .dropdown-menu .hover\:bg-red-50:hover {
  background-color: theme('colors.red.900/30');
  color: theme('colors.red.300');
}

/* Modal overlay dark mode */
.dark #settings-modal-bg {
  background-color: theme('colors.gray.900/70');
  backdrop-filter: blur(4px);
}

/* Modal container dark mode */
.dark #settings-modal-container {
  background-color: theme('colors.gray.800/95');
  border-color: theme('colors.gray.700');
  backdrop-filter: blur(8px);
}

/* Modal content dark mode */
.dark #settings-modal-content {
  color: theme('colors.gray.200');
}

/* Close button dark mode */
.dark #settings-modal .opacity-20 {
  opacity: 0.4;
}

.dark #settings-modal .hover\:opacity-40:hover {
  opacity: 0.6;
}

/* Navigation links dark mode */
.dark #settings-modal nav a {
  color: theme('colors.gray.300');
}

.dark #settings-modal nav a:hover {
  background-color: theme('colors.gray.800');
}

.dark #settings-modal nav a.bg-blue-600 {
  color: theme('colors.white');
}

.dark #settings-modal nav a.hover\:bg-gray-100:hover {
  background-color: theme('colors.gray.800');
}

/* Section headers */
.dark #settings-modal h3 {
  color: theme('colors.gray.100');
}

/* Help text */
.dark #settings-modal .text-gray-500 {
  color: theme('colors.gray.400');
}

/* Error messages */
.dark #settings-modal .text-red-600 {
  color: theme('colors.red.400');
}

/* Add to your existing CSS */
@keyframes swipeHint {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  50% {
    opacity: 0.3;
    transform: translateX(10px);
  }

  100% {
    opacity: 0;
    transform: translateX(-20px);
  }
}

/* Add a subtle hint for swipe on mobile */
@media (max-width: 768px) {
  .swipe-hint::before {
    content: '';
    position: fixed;
    left: 0;
    top: 50%;
    width: 4px;
    height: 50px;
    background: theme('colors.blue.500');
    border-radius: 0 2px 2px 0;
    transform: translateY(-50%);
    opacity: 0;
    animation: swipeHint 2s ease-in-out infinite;
    pointer-events: none;
  }
}

/* Dark mode support */
.dark .swipe-hint::before {
  background: theme('colors.blue.400');
}

/* Content block toolbox styles */
.content-block-toolbox {
  @apply bg-gray-50 border-t border-gray-100;
  @apply rounded-none sm:rounded-b-lg;
}

/* Hide the tags section temporarily */
.content-block-toolbox {
  @apply hidden;
}

/* Logo visibility management */
.light-mode-logo {
  @apply block dark:hidden;
}

.dark-mode-logo {
  @apply hidden dark:block;
}

/* Override logo visibility when header is dark (logged in) */
header.bg-gray-800 .light-mode-logo {
  @apply hidden;
}

header.bg-gray-800 .dark-mode-logo {
  @apply block;
}

/* Update scheduling panel styles */
#scheduling-panel {
  position: fixed;
  inset: 0;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 50;
  transform: translateX(100%);
}

#scheduling-panel.transition-transform {
  transition: transform 300ms ease-in-out;
}

#scheduling-panel:not(.translate-x-full) {
  transform: translateX(0);
}

@media (min-width: 768px) {
  #scheduling-panel {
    inset: 0 0 0 auto;
    width: 400px;
  }
}

/* Dark mode support */
.dark #scheduling-panel {
  background-color: theme('colors.gray.900');
  border-left: 1px solid theme('colors.gray.700');
}

/* Network toggle dropdown styles - using workspace switcher styles */
[data-network-toggle-menu] {
  position: absolute;
  left: 0;
  z-index: 10;
  margin-top: 0.5rem;
  width: 14rem;
  transform-origin: top left;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark [data-network-toggle-menu] {
  background-color: rgb(31, 41, 55);
  border-color: rgb(55, 65, 81);
}

[data-network-toggle-menu] input[type="checkbox"] {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border-color: rgb(209, 213, 219);
  color: rgb(37, 99, 235);
}

.dark [data-network-toggle-menu] input[type="checkbox"] {
  border-color: rgb(75, 85, 99);
  background-color: rgb(55, 65, 81);
}

[data-network-toggle-menu] label[data-checked="true"] {
  background-color: rgb(243, 244, 246);
  color: rgb(17, 24, 39);
}

.dark [data-network-toggle-menu] label[data-checked="true"] {
  background-color: rgb(55, 65, 81);
  color: rgb(255, 255, 255);
}

/* Network toggle button styles */
/* Base overlay style - visible by default */
[data-avatar-overlay] {
  display: block;
}

/* Hide overlay when button is selected */
.network-toggle[data-selected="true"] [data-avatar-overlay] {
  display: none;
}

/* Hide icons when button is selected (except canonical) */
.network-toggle[data-selected="true"]:not([data-network="canonical"]) svg {
  display: none;
}

/* Ensure icons are white for contrast against the dark overlay */
.network-toggle svg {
  color: theme('colors.white') !important;
}

/* Network-specific background colors for selected state */
.network-toggle[data-selected="true"][data-network="mastodon"] {
  background-color: theme('colors.purple.500') !important;
}

.network-toggle[data-selected="true"][data-network="bsky"] {
  background-color: theme('colors.sky.500') !important;
}

.network-toggle[data-selected="true"][data-network="linkedin"] {
  background-color: theme('colors.blue.600') !important;
}

.network-toggle[data-selected="true"][data-network="x"] {
  background-color: theme('colors.black') !important;
}

.network-toggle[data-selected="true"][data-network="canonical"] {
  background-color: theme('colors.blue.700') !important;
}

/* Ensure text is white for all selected states */
.network-toggle[data-selected="true"] {
  color: theme('colors.white') !important;
}

/* Disabled network button styles */
.network-toggle[data-feature-enabled="false"] {
  opacity: 0.5;
  background-color: theme('colors.gray.100') !important;
  border-color: theme('colors.gray.300') !important;
}

.dark .network-toggle[data-feature-enabled="false"] {
  background-color: theme('colors.gray.800') !important;
  border-color: theme('colors.gray.700') !important;
}

/* Reconnect needed styles */
.network-toggle[data-needs-reconnect="true"] {
  opacity: 0.5;
  background-color: theme('colors.gray.300') !important;
  border-color: theme('colors.gray.400') !important;
}

.dark .network-toggle[data-needs-reconnect="true"] {
  background-color: theme('colors.gray.700') !important;
  border-color: theme('colors.gray.600') !important;
}

/* Hover effects for disabled buttons */
.network-toggle[data-connection-status]:not([data-connection-status="enabled"]):hover {
  background-color: rgb(229 231 235) !important;
  /* bg-gray-200 */
  border-color: rgb(156 163 175) !important;
  /* border-gray-400 */
  opacity: 0.8 !important;
}

.dark .network-toggle[data-connection-status]:not([data-connection-status="enabled"]):hover {
  background-color: rgb(55 65 81) !important;
  /* bg-gray-700 */
  border-color: rgb(75 85 99) !important;
  /* border-gray-600 */
  opacity: 0.8 !important;
}

/* Network button alert icon hover effect */
[data-network-button]:hover [data-reconnect-network],
[data-network-button]:hover [data-upgrade-required] {
  background-color: rgb(59 130 246) !important;
  /* bg-blue-500 */
  border-color: rgb(96 165 250) !important;
  /* border-blue-400 */
  color: white !important;
}

/* Bluesky preview dark mode */
.dark .bsky-preview {
  background-color: #16202B;
  border-color: #2E4052;
}

.dark .bsky-preview-hover:hover {
  background-color: #1E2936;
}

/* Bluesky preview styles */
.dark .bluesky-preview {
  background-color: #000;
  border-color: #2F3336;
}

.dark .bluesky-preview .post-content {
  color: #F1F3F5;
}

.dark .bluesky-preview .text-gray-500 {
  color: #AEBBCA;
}

.dark .bluesky-preview .text-gray-400 {
  color: #788EA5;
}

.dark .bluesky-preview .hover\:bg-gray-900:hover {
  background-color: #1E2936;
}

.dark .bluesky-preview .border-gray-800 {
  border-color: #2E4052;
}

.dark .bluesky-preview .bg-gray-800 {
  background-color: #1E2936;
}

.dark .bluesky-preview .hover\:text-blue-400:hover {
  color: #4A9EFF;
}

.dark .bluesky-preview .hover\:text-green-400:hover {
  color: #4CAF50;
}

.dark .bluesky-preview .hover\:text-red-400:hover {
  color: #FF4081;
}

.dropdown-menu {
  display: none;
}

.dropdown-menu.show {
  display: block;
}

.post-editor[data-synced="false"] .sync-tooltip {
  display: none;
}

.post-editor[data-synced="true"] .sync-tooltip {
  display: block;
}

.post-editor[data-synced="true"] .move-buttons {
  display: none;
}

[data-slot-cell][data-selected="true"] [data-checkbox] {
  opacity: 1 !important;
}

[data-slot-cell][data-selected="true"] [data-checkbox] input[type="checkbox"] {
  background-color: white;
  border-color: white;
}

[data-slot-cell][data-selected="true"] [data-checkbox] input[type="checkbox"]:checked {
  background-color: white;
  border-color: white;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%2322C55E' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

/* Network dropdown specific override */
.dark [data-network-dropdown] {
  background-color: theme('colors.gray.800') !important;
}

.dark [data-network-dropdown] .hover\:bg-gray-50:hover {
  background-color: theme('colors.gray.700') !important;
}

.dark [data-network-dropdown] .text-gray-700,
.dark [data-network-dropdown] .font-medium,
.dark [data-network-dropdown] .text-sm {
  color: theme('colors.gray.200') !important;
}

.dark [data-network-dropdown] .text-gray-900 {
  color: theme('colors.gray.200') !important;
}

/* Tippy dropdown styling in dark mode */
.dark .tippy-box[data-theme~='light'] {
  background-color: theme('colors.gray.800');
  border-color: theme('colors.gray.700');
}

/* Dropdown checkbox styling in dark mode */
.dark [data-network-dropdown] input[type="checkbox"] {
  border-color: theme('colors.gray.600');
  background-color: theme('colors.gray.700');
}

.dark [data-network-dropdown] input[type="checkbox"]:checked {
  background-color: theme('colors.green.600');
  border-color: theme('colors.green.600');
}

/* Dropdown trigger icon color in dark mode */
.dark [data-dropdown-trigger] svg {
  color: theme('colors.gray.400');
}

.dark [data-dropdown-trigger]:hover svg {
  color: theme('colors.gray.300');
}

/* Form background - more specific selector to avoid affecting dropdowns */
.dark form .bg-white {
  background-color: transparent;
}

/* Dropdown trigger icon color in dark mode */
.dark [data-dropdown-trigger] svg {
  color: theme('colors.gray.400');
}

.dark [data-dropdown-trigger]:hover svg {
  color: theme('colors.gray.300');
}

/* Time input styling for dark mode */
.dark [data-minute-input] {
  color: theme('colors.gray.200');
}

/* Hide default number input arrows and use custom styling */
[data-minute-input]::-webkit-inner-spin-button,
[data-minute-input]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

[data-minute-input] {
  -moz-appearance: textfield;
}

/* Slot background in dark mode */
.dark .slot-bg {
  background-color: theme('colors.green.900');
}

/* Time display styling for consistent spacing */
.time-container .flex {
  font-variant-numeric: tabular-nums;
}

.time-container [data-minute-display],
.time-container [data-minute-input] {
  font-feature-settings: "tnum";
  letter-spacing: -0.01em;
}

/* Custom dark mode striped background for past time slots */
.dark .dark\:bg-stripes-gray {
  background-image: repeating-linear-gradient(45deg,
      transparent,
      transparent 5px,
      rgba(75, 85, 99, 0.2) 5px,
      rgba(75, 85, 99, 0.2) 10px) !important;
}

/* Make past time slots visually distinct */
[data-past-time="true"] {
  position: relative;
  overflow: hidden;
}

/* Add a subtle overlay to past time slots */
[data-past-time="true"]::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 1;
}

.dark [data-past-time="true"]::after {
  background-color: rgba(0, 0, 0, 0.2);
}

.slot-bg {
  background-color: theme('colors.green.500') !important;
}

.time-container .flex {
  font-variant-numeric: tabular-nums;
}

.time-container [data-minute-display],
.time-container [data-minute-input] {
  font-feature-settings: "tnum";
  letter-spacing: -0.01em;
}

[data-selected="true"] .time-container .flex {
  color: white !important;
}

[data-selected="true"] .time-container .text-gray-500 {
  color: white !important;
}

[data-selected="true"] .drag-handle {
  color: white !important;
}

[data-selected="true"] .drag-handle:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Settings navigation mobile optimization */
.hide-scrollbar {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  -webkit-overflow-scrolling: touch;
  /* Smooth scrolling on iOS */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* Ensure active tab is visible on mobile */
@media (max-width: 640px) {
  [aria-label="Settings sections"] {
    scroll-padding: 0 1rem;
  }

  [aria-label="Settings sections"] a {
    scroll-snap-align: start;
  }
}

/* Auto-scheduler calendar cell states */
[data-slot-cell][data-past-time="true"] {
  @apply cursor-not-allowed opacity-60;
  background-color: rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.4) !important;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(0, 0, 0, 0.05) 5px, rgba(0, 0, 0, 0.05) 10px) !important;
}

.dark [data-slot-cell][data-past-time="true"] {
  background-color: rgba(75, 85, 99, 0.5) !important;
  color: theme('colors.gray.500') !important;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(75, 85, 99, 0.2) 5px, rgba(75, 85, 99, 0.2) 10px) !important;
}

[data-slot-cell][data-occupied="true"] {
  @apply cursor-not-allowed;
  background-color: rgb(31 41 55) !important;
}

[data-slot-cell][data-suggested="true"] {
  background-color: #148d391a !important;
}

[data-slot-cell][data-selected="true"] {
  /* Selected styling will be handled by schedule controls */
}

/* Auto-scheduler Today button */
[data-today-button] {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background-color: theme('colors.blue.500');
  color: theme('colors.white');
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

[data-today-button]:hover {
  background-color: theme('colors.blue.600');
}

[data-today-button]:focus {
  outline: none;
  box-shadow: 0 0 0 2px theme('colors.blue.500'), 0 0 0 4px theme('colors.blue.500/20');
}

.dark [data-today-button] {
  background-color: theme('colors.blue.600');
}

.dark [data-today-button]:hover {
  background-color: theme('colors.blue.700');
}

.dark [data-today-button]:focus {
  box-shadow: 0 0 0 2px theme('colors.blue.600'), 0 0 0 4px theme('colors.blue.600/20');
}
