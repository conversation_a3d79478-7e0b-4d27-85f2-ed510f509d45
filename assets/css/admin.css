@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer components {

  /* Admin table styles */
  .admin-table-container {
    @apply ring-1 ring-gray-300 rounded-lg overflow-hidden;
  }

  table {
    @apply min-w-full divide-y divide-gray-300;
  }

  thead {
    @apply bg-gray-50;
  }

  th {
    @apply px-3 py-3.5 text-left text-sm font-semibold text-gray-900;
  }

  th a {
    @apply text-gray-900 hover:text-gray-700 font-semibold;
  }

  th span.order-direction {
    @apply text-blue-600 ml-2;
  }

  tbody {
    @apply divide-y divide-gray-200 bg-white;
  }

  tbody tr {
    @apply hover:bg-gray-50;
  }

  td {
    @apply whitespace-nowrap px-3 py-4 text-sm text-gray-500;
  }

  /* Admin form styles */
  .admin-form-container {
    @apply bg-white shadow sm:rounded-lg p-6 mt-4;
  }

  .admin-form-group {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-3;
  }

  .admin-input {
    @apply block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6;
  }

  .admin-label {
    @apply block text-sm font-medium leading-6 text-gray-900;
  }

  /* Admin pagination styles */
  .admin-pagination {
    @apply mt-4;
  }

  .admin-pagination nav {
    @apply flex items-center justify-center gap-2;
  }

  .admin-pagination .pagination-list {
    @apply flex items-center gap-2 list-none m-0 p-0;
  }

  .admin-pagination .pagination-link {
    @apply inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50;
  }

  .admin-pagination .pagination-link.is-current {
    @apply z-10 bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
  }

  .admin-pagination .pagination-previous,
  .admin-pagination .pagination-next {
    @apply inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50;
  }

  .admin-pagination .pagination-previous.disabled,
  .admin-pagination .pagination-next.disabled {
    @apply opacity-50 cursor-not-allowed hover:bg-white;
  }

  .admin-pagination-mobile {
    @apply flex flex-1 justify-between sm:hidden;
  }

  .admin-pagination-desktop {
    @apply hidden sm:flex sm:flex-1 sm:items-center sm:justify-between;
  }

  .admin-pagination-button {
    @apply relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline-offset-0;
  }

  /* Admin layout styles */
  .admin-sidebar {
    @apply bg-white rounded-lg shadow;
  }

  .admin-sidebar-link {
    @apply text-gray-700 flex items-center px-4 py-2 text-sm font-medium rounded-md;
  }

  .admin-sidebar-link:hover {
    @apply text-gray-900 bg-gray-50;
  }

  .admin-sidebar-link.active {
    @apply bg-gray-50 text-blue-600;
  }

  .admin-content {
    @apply bg-white rounded-lg shadow;
  }

  .admin-header {
    @apply bg-gray-800 pb-32;
  }

  .admin-header-content {
    @apply mx-auto max-w-7xl py-4 px-4 sm:px-6 lg:px-8;
  }

  .admin-main {
    @apply -mt-32;
  }

  .admin-main-content {
    @apply mx-auto max-w-7xl px-4 pb-12 sm:px-6 lg:px-8;
  }

  .admin-table-container p:only-child {
    @apply text-center py-8 text-sm text-gray-500;
  }

  .vega-embed {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
  }

  .vega-embed canvas {
    width: 100% !important;
    height: auto !important;
  }

  #posts-chart,
  #networks-chart {
    min-height: 300px;
    width: 100%;
  }

  /* JSON Editor styles */
  .jse-theme-light {
    @apply border border-gray-300 rounded-md;
    --jse-background-color: theme('colors.white');
    --jse-main-border: none;
    min-height: 200px;
  }
}
