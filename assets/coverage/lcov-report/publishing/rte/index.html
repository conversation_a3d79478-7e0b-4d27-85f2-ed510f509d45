
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for publishing/rte</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> publishing/rte</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.06% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>63/121</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.5% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>23/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.38% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>15/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.25% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>58/111</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="content_parser.js"><a href="content_parser.js.html">content_parser.js</a></td>
	<td data-value="92.3" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.3" class="pct high">92.3%</td>
	<td data-value="65" class="abs high">60/65</td>
	<td data-value="71.87" class="pct medium">71.87%</td>
	<td data-value="32" class="abs medium">23/32</td>
	<td data-value="86.66" class="pct high">86.66%</td>
	<td data-value="15" class="abs high">13/15</td>
	<td data-value="93.22" class="pct high">93.22%</td>
	<td data-value="59" class="abs high">55/59</td>
	</tr>

<tr>
	<td class="file low" data-value="social_profile.js"><a href="social_profile.js.html">social_profile.js</a></td>
	<td data-value="5.35" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.35" class="pct low">5.35%</td>
	<td data-value="56" class="abs low">3/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="12.5" class="pct low">12.5%</td>
	<td data-value="16" class="abs low">2/16</td>
	<td data-value="5.76" class="pct low">5.76%</td>
	<td data-value="52" class="abs low">3/52</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-23T11:50:28.144Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    