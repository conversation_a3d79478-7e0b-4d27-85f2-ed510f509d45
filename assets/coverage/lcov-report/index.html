
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.12% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>327/535</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.19% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>174/299</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>78/150</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>313/489</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="extensions"><a href="extensions/index.html">extensions</a></td>
	<td data-value="66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66" class="pct medium">66%</td>
	<td data-value="100" class="abs medium">66/100</td>
	<td data-value="56.38" class="pct medium">56.38%</td>
	<td data-value="94" class="abs medium">53/94</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="18" class="abs high">15/18</td>
	<td data-value="67.7" class="pct medium">67.7%</td>
	<td data-value="96" class="abs medium">65/96</td>
	</tr>

<tr>
	<td class="file medium" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="61.34" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 61%"></div><div class="cover-empty" style="width: 39%"></div></div>
	</td>
	<td data-value="61.34" class="pct medium">61.34%</td>
	<td data-value="238" class="abs medium">146/238</td>
	<td data-value="57.69" class="pct medium">57.69%</td>
	<td data-value="130" class="abs medium">75/130</td>
	<td data-value="42.3" class="pct low">42.3%</td>
	<td data-value="78" class="abs low">33/78</td>
	<td data-value="65.42" class="pct medium">65.42%</td>
	<td data-value="214" class="abs medium">140/214</td>
	</tr>

<tr>
	<td class="file medium" data-value="publishing/rte"><a href="publishing/rte/index.html">publishing/rte</a></td>
	<td data-value="52.06" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 52%"></div><div class="cover-empty" style="width: 48%"></div></div>
	</td>
	<td data-value="52.06" class="pct medium">52.06%</td>
	<td data-value="121" class="abs medium">63/121</td>
	<td data-value="57.5" class="pct medium">57.5%</td>
	<td data-value="40" class="abs medium">23/40</td>
	<td data-value="48.38" class="pct low">48.38%</td>
	<td data-value="31" class="abs low">15/31</td>
	<td data-value="52.25" class="pct medium">52.25%</td>
	<td data-value="111" class="abs medium">58/111</td>
	</tr>

<tr>
	<td class="file medium" data-value="stores"><a href="stores/index.html">stores</a></td>
	<td data-value="68.42" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.42" class="pct medium">68.42%</td>
	<td data-value="76" class="abs medium">52/76</td>
	<td data-value="65.71" class="pct medium">65.71%</td>
	<td data-value="35" class="abs medium">23/35</td>
	<td data-value="65.21" class="pct medium">65.21%</td>
	<td data-value="23" class="abs medium">15/23</td>
	<td data-value="73.52" class="pct medium">73.52%</td>
	<td data-value="68" class="abs medium">50/68</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-23T11:50:28.144Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    