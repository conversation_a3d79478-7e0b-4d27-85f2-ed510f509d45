
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for stores</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> stores</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">68.42% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>52/76</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">65.71% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>23/35</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">65.21% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>15/23</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.52% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>50/68</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="post_store.js"><a href="post_store.js.html">post_store.js</a></td>
	<td data-value="68.42" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.42" class="pct medium">68.42%</td>
	<td data-value="76" class="abs medium">52/76</td>
	<td data-value="65.71" class="pct medium">65.71%</td>
	<td data-value="35" class="abs medium">23/35</td>
	<td data-value="65.21" class="pct medium">65.21%</td>
	<td data-value="23" class="abs medium">15/23</td>
	<td data-value="73.52" class="pct medium">73.52%</td>
	<td data-value="68" class="abs medium">50/68</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-23T11:50:28.144Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    