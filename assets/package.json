{"name": "crosspost", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"deploy": "cd .. && mix assets.deploy && rm -f _build/esbuild", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@tiptap/core": "^2.1.13", "@tiptap/extension-code-block": "^2.9.1", "@tiptap/extension-link": "^2.10.2", "@tiptap/extension-mention": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/pm": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/suggestion": "^2.9.1", "chart.js": "^4.4.1", "fast-deep-equal": "^3.1.3", "flatpickr": "^4.6.13", "interactjs": "^1.10.27", "lodash": "^4.17.21", "mobx": "^6.13.5", "sortablejs": "^1.15.6", "tippy.js": "^6.3.7", "twitter-text": "^3.1.0", "uuid": "^11.0.3", "vanilla-jsoneditor": "^2.3.3", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "babel-jest": "^29.7.0", "jest": "^29.7.0"}}