import "phoenix_html"
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"
import { Chart } from 'chart.js/auto'
import { JsonEditor } from './hooks/json_editor'
import Sortable from "sortablejs"

const Hooks = {
  ChartHook: {
    mounted() {
      this.chart = null
      this.renderChart()

      // Handle window resize
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    },

    updated() {
      this.renderChart()
    },

    destroyed() {
      if (this.chart) {
        this.chart.destroy()
      }
    },

    renderChart() {
      const spec = JSON.parse(this.el.dataset.spec)

      if (this.chart) {
        this.chart.destroy()
      }

      this.chart = new Chart(this.el, spec)
    }
  },
  JsonEditor,
  Flash: {
    mounted() {
      // Basic flash message handling
      let hide = () => {
        this.el.style.opacity = '0'
        setTimeout(() => this.el.remove(), 150)
      }

      // Auto-hide after 5 seconds
      setTimeout(hide, 5000)

      // Allow manual close
      this.el.addEventListener('click', () => hide())
    }
  }
}

Hooks.FeatureList = {
  mounted() {
    const el = this.el
    new Sortable(el, {
      animation: 150,
      ghostClass: "bg-gray-100",
      onEnd: (evt) => {
        const features = Array.from(el.children).map(item => item.dataset.featureKey)
        this.pushEvent("reorder_features", { features })
      }
    })
  }
}

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  params: { _csrf_token: csrfToken },
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket
