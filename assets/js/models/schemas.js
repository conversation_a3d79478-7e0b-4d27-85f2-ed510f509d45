import { z } from 'zod'

const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
const URL_REGEX = /^(?:https?:\/\/|\/)\S+/i

export const NetworkEnum = z.enum(['canonical', 'bsky', 'x', 'mastodon', 'linkedin'])
export const PostStatusEnum = z.enum(['draft', 'published', 'partially_published', 'failed', 'pending', 'scheduled'])
export const AttachmentTypeEnum = z.enum(['image', 'video', 'link'])
export const FeatureTypeEnum = z.enum(['counter', 'limit', 'boolean'])
export const ResetPeriodEnum = z.enum(['yearly', 'monthly', 'daily'])

export const FeatureSchema = z.object({
  featureKey: z.string(),
  featureType: FeatureTypeEnum,
  featureName: z.string(),
  featureDescription: z.string(),
  featureComingSoon: z.boolean().default(false),
  planKey: z.string().nullable(),
  usageKey: z.string().nullable(),
  currentUsage: z.number().int().default(0),
  limit: z.number().int().nullable(),
  resetPeriod: ResetPeriodEnum.nullable(),
  periodStart: z.date().nullable(),
  periodEnd: z.date().nullable()
})

export const ConnectionSchema = z.object({
  network: NetworkEnum,
  avatar: z.string().nullable(),
  limit: z.number().int().optional(),
  urlLength: z.number().int().optional(),
  settings: z.record(z.any()).default({})
})

export const SocialProfileSchema = z.object({
  id: z.number().int().positive().nullable().optional(),
  name: z.string(),
  bskyHandle: z.string().nullable(),
  xHandle: z.string().nullable(),
  mastodonHandle: z.string().nullable(),
  networks: z.array(z.object({
    id: NetworkEnum,
    handle: z.string()
  })).default([])
})

export const UserSchema = z.object({
  uid: z.string().regex(UUID_REGEX, 'Must be a valid UUID'),
  email: z.string().email(),
  name: z.string().nullable(),
  settings: z.record(z.any()).default({}),
  info: z.record(z.any()).default({}),
  timezone: z.string().default('Etc/UTC'),
  connections: z.array(ConnectionSchema).default([])
})

export const AttachmentSchema = z.object({
  id: z.string().regex(UUID_REGEX, 'Must be a valid UUID'),
  type: AttachmentTypeEnum,
  sourceUrl: z.string().regex(URL_REGEX, 'Must be a valid URL or path').nullable(),
  previewUrl: z.string().regex(URL_REGEX, 'Must be a valid URL or path').nullable(),
  metadata: z.record(z.any()).default({}),
  filename: z.string().nullable().optional(),
  isLoading: z.boolean().default(false),
  order: z.number().int().min(0).default(0).optional()
})

export const ScheduleSchema = z.object({
  network: NetworkEnum,
  timezone: z.string(),
  scheduledAt: z.date().nullable()
})

export const PostContentSchema = z.object({
  text: z.string().default(''),
  network: NetworkEnum,
  order: z.number().int().default(0),
  meta: z.object({
    mentions: z.array(z.any()).default([]),
    tags: z.array(z.string()).default([])
  }).default({ mentions: [], tags: [] }),
  attachments: z.array(AttachmentSchema).default([]),
  sync: z.boolean().default(true)
})

export const PostSchema = z.object({
  id: z.string().regex(UUID_REGEX, 'Must be a valid UUID'),
  content: z.array(PostContentSchema).default([]),
  socialNetworks: z.array(NetworkEnum).default([]),
  schedules: z.array(ScheduleSchema).default([]),
  status: PostStatusEnum.default('draft')
})

export function validatePost(data) {
  return PostSchema.safeParse(data)
}

export function validatePostContent(data) {
  return PostContentSchema.safeParse(data)
}

export function validateAttachment(data) {
  return AttachmentSchema.safeParse(data)
}

export function validateSchedule(data) {
  return ScheduleSchema.safeParse(data)
}
