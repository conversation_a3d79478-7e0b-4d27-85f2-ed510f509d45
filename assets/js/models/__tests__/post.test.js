import { Post, PostContent, Attachment, Schedule, Networks, PostStatuses } from '../post'

describe('Post', () => {
  describe('constructor', () => {
    it('creates a new post with default values', () => {
      const post = new Post()
      expect(post.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)
      expect(post.content).toEqual([])
      expect(post.socialNetworks).toEqual([])
      expect(post.schedules).toEqual([])
      expect(post.status).toBe('draft')
    })

    it('creates a post with provided values', () => {
      const post = new Post({
        id: '123e4567-e89b-42d3-a456-************',
        content: [{
          network: Networks.bsky,
          text: 'Hello',
          id: '123e4567-e89b-42d3-a456-************'
        }],
        social_networks: [Networks.bsky, Networks.linkedin],
        schedules: [{
          network: Networks.bsky,
          timezone: 'UTC',
          scheduledAt: new Date()
        }],
        status: PostStatuses.scheduled
      })

      expect(post.id).toBe('123e4567-e89b-42d3-a456-************')
      expect(post.content[0]).toBeInstanceOf(PostContent)
      expect(post.socialNetworks).toEqual([Networks.bsky, Networks.linkedin])
      expect(post.schedules[0]).toBeInstanceOf(Schedule)
      expect(post.status).toBe('scheduled')
    })

    it('throws error for invalid data', () => {
      expect(() => new Post({
        id: 'invalid-uuid',
        status: 'invalid-status'
      })).toThrow('Invalid post data')
    })
  })

  describe('getContentForNetwork', () => {
    it('returns content for specific network', () => {
      const post = new Post({
        content: [
          {
            id: '123e4567-e89b-42d3-a456-************',
            network: Networks.bsky,
            text: 'post',
            order: 0
          },
          {
            id: '123e4567-e89b-42d3-a456-************',
            network: Networks.linkedin,
            text: 'post',
            order: 0
          }
        ]
      })
      const bskyContent = post.getContentForNetwork(Networks.bsky)
      expect(bskyContent).toHaveLength(1)
      expect(bskyContent[0].text).toBe('post')
    })

    it('returns content for specific network and order', () => {
      const post = new Post({
        content: [
          {
            id: '123e4567-e89b-42d3-a456-************',
            network: Networks.bsky,
            text: 'post 1',
            order: 0
          },
          {
            id: '123e4567-e89b-42d3-a456-************',
            network: Networks.bsky,
            text: 'post 2',
            order: 1
          }
        ]
      })
      const content = post.getContentForNetwork(Networks.bsky, 1)
      expect(content.text).toBe('post 2')
    })
  })

  describe('hasContent', () => {
    it('returns true when post has content', () => {
      const post = new Post({
        content: [{
          id: '123e4567-e89b-42d3-a456-************',
          network: Networks.bsky,
          text: 'test'
        }]
      })
      expect(post.hasContent()).toBe(true)
      expect(post.hasContent(Networks.bsky)).toBe(true)
    })

    it('returns false when post has no content', () => {
      const post = new Post()
      expect(post.hasContent()).toBe(false)
      expect(post.hasContent(Networks.bsky)).toBe(false)
    })
  })
})

describe('PostContent', () => {
  describe('constructor', () => {
    it('creates content with default values', () => {
      const content = new PostContent({
        id: '123e4567-e89b-42d3-a456-************',
        network: Networks.bsky
      })
      expect(content.text).toBe('')
      expect(content.network).toBe(Networks.bsky)
      expect(content.order).toBe(0)
      expect(content.meta).toEqual({ mentions: [], tags: [] })
      expect(content.sync).toBe(true)
      expect(content.attachments).toEqual([])
    })

    it('creates content with provided values', () => {
      const data = {
        id: '123e4567-e89b-42d3-a456-************',
        text: 'test',
        network: Networks.bsky,
        order: 1,
        meta: { mentions: [{ id: '1', name: 'test' }], tags: ['test'] },
        attachments: [{
          id: '123e4567-e89b-42d3-a456-************',
          type: 'image',
          sourceUrl: 'https://example.com/image.jpg',
          previewUrl: null
        }]
      }
      const content = new PostContent(data)
      expect(content.text).toBe('test')
      expect(content.network).toBe(Networks.bsky)
      expect(content.order).toBe(1)
      expect(content.meta.mentions).toHaveLength(1)
      expect(content.meta.tags).toHaveLength(1)
      expect(content.attachments[0]).toBeInstanceOf(Attachment)
    })

    it('throws error for invalid data', () => {
      expect(() => new PostContent({
        id: 'invalid-uuid',
        network: 'invalid-network'
      })).toThrow('Invalid post content data')
    })
  })

  describe('attachment management', () => {
    let content

    beforeEach(() => {
      content = new PostContent({
        id: '123e4567-e89b-42d3-a456-************',
        network: Networks.bsky
      })
    })

    it('adds new attachment', () => {
      const attachment = {
        id: '123e4567-e89b-42d3-a456-************',
        type: 'image',
        sourceUrl: 'https://example.com/image.jpg',
        previewUrl: null
      }
      content.addAttachment(attachment)
      expect(content.attachments).toHaveLength(1)
      expect(content.attachments[0]).toBeInstanceOf(Attachment)
    })

    it('updates existing attachment', () => {
      const attachment = {
        id: '123e4567-e89b-42d3-a456-************',
        type: 'image',
        sourceUrl: 'https://example.com/image.jpg',
        previewUrl: null,
        filename: 'test.jpg'
      }
      content.addAttachment(attachment)
      content.updateAttachment('123e4567-e89b-42d3-a456-************', {
        ...attachment,
        filename: 'updated.jpg'
      })
      expect(content.attachments[0].filename).toBe('updated.jpg')
    })

    it('removes attachment', () => {
      const attachment = {
        id: '123e4567-e89b-42d3-a456-************',
        type: 'image',
        sourceUrl: 'https://example.com/image.jpg',
        previewUrl: null
      }
      content.addAttachment(attachment)
      content.removeAttachment('123e4567-e89b-42d3-a456-************')
      expect(content.attachments).toHaveLength(0)
    })

    describe('updateLinkAttachment', () => {
      it('replaces existing link attachment with new one', () => {
        const oldLink = {
          id: '123e4567-e89b-42d3-a456-************',
          type: 'link',
          metadata: { url: 'https://example.com/old' }
        }
        const newLink = {
          id: '123e4567-e89b-42d3-a456-556642440002',
          type: 'link',
          metadata: { url: 'https://example.com/new' }
        }

        content.addAttachment(oldLink)
        content.updateLinkAttachment(newLink)

        expect(content.attachments).toHaveLength(1)
        expect(content.attachments[0].id).toBe(newLink.id)
        expect(content.attachments[0].metadata.url).toBe(newLink.metadata.url)
      })

      it('adds link attachment if none exists', () => {
        const link = {
          id: '123e4567-e89b-42d3-a456-************',
          type: 'link',
          metadata: { url: 'https://example.com' }
        }

        content.updateLinkAttachment(link)

        expect(content.attachments).toHaveLength(1)
        expect(content.attachments[0].id).toBe(link.id)
      })

      it('preserves non-link attachments when updating link', () => {
        const image = {
          id: '123e4567-e89b-42d3-a456-************',
          type: 'image',
          sourceUrl: 'https://example.com/image.jpg'
        }
        const link = {
          id: '123e4567-e89b-42d3-a456-556642440002',
          type: 'link',
          metadata: { url: 'https://example.com' }
        }

        content.addAttachment(image)
        content.updateLinkAttachment(link)

        expect(content.attachments).toHaveLength(2)
        expect(content.attachments.find(a => a.type === 'image')).toBeTruthy()
        expect(content.attachments.find(a => a.type === 'link')).toBeTruthy()
      })

      it('removes link attachment when passing null', () => {
        const link = {
          id: '123e4567-e89b-42d3-a456-************',
          type: 'link',
          metadata: { url: 'https://example.com' }
        }

        content.addAttachment(link)
        content.updateLinkAttachment(null)

        expect(content.attachments).toHaveLength(0)
      })
    })
  })
})

describe('PostContent static methods', () => {
  describe('removeUrlFromText', () => {
    it('removes a URL from the end of text', () => {
      const text = 'Check out this link https://example.com'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check out this link')
    })

    it('removes a URL with trailing whitespace', () => {
      const text = 'Check out this link https://example.com  '
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check out this link')
    })

    it('keeps a URL that is not at the end', () => {
      const text = 'Check out https://example.com for more information'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check out https://example.com for more information')
    })

    it('keeps a URL at the beginning', () => {
      const text = 'https://example.com is a great resource'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('https://example.com is a great resource')
    })

    it('handles URLs with special regex characters', () => {
      const text = 'Check out this special URL https://example.com/page?query=test+value'
      const url = 'https://example.com/page?query=test+value'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check out this special URL')
    })

    it('keeps a URL inside parentheses', () => {
      const text = 'Check this out (https://example.com) for more details'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check this out (https://example.com) for more details')
    })

    it('keeps a URL with no spaces before or after', () => {
      const text = 'Check this out(https://example.com)for more details'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check this out(https://example.com)for more details')
    })

    it('removes a URL at the end, even after punctuation if it has space before', () => {
      const text = 'Check this out: https://example.com'
      const url = 'https://example.com'
      const result = PostContent.removeUrlFromText(text, url)
      expect(result).toBe('Check this out:')
    })
  })
})
