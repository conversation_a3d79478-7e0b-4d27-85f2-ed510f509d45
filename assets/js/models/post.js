import { v4 as uuidv4 } from 'uuid'
import {
  validatePost,
  validatePostContent,
  validateAttachment,
  validateSchedule,
  NetworkEnum,
  PostStatusEnum,
  AttachmentTypeEnum
} from './schemas'

export class Post {
  id = null
  content = []
  socialNetworks = []
  schedules = []
  status = 'draft'

  static fromJSON(data = {}, availableNetworks = []) {
    const post = new Post({
      ...data,
      social_networks: data.social_networks?.length > 0 ?
        data.social_networks :
        availableNetworks || []
    })
    return post
  }

  constructor(data = {}) {
    const validationData = {
      id: data.id || uuidv4(),
      content: (data.content || []).map(c => {
        if (c instanceof PostContent) return c
        return new PostContent(c)
      }),
      socialNetworks: data.social_networks || [],
      schedules: (data.schedules || []).map(s => {
        if (s instanceof Schedule) return s
        return new Schedule(s)
      }),
      status: data.status || 'draft'
    }

    const result = validatePost(validationData)

    if (!result.success) {
      throw new Error(`Invalid post data: ${result.error.message}`)
    }

    const validData = result.data
    this.id = validData.id
    this.content = validData.content.map(c => {
      if (c instanceof PostContent) return c
      return new PostContent(c)
    })
    this.socialNetworks = validData.socialNetworks
    this.schedules = validData.schedules.map(s => {
      if (s instanceof Schedule) return s
      return new Schedule(s)
    })
    this.status = validData.status
  }

  addContent(contents) {
    this.content = this.content.concat(contents)
    return this
  }

  getContentForNetwork(network, order = undefined) {
    if (!network) return []
    const networkContent = this.content.filter(c => c.network === network)
    if (order !== undefined) {
      return networkContent.find(c => c.order === order)
    }
    return networkContent
  }

  hasContent(network) {
    const content = network ?
      this.content.filter(c => c.network === network) :
      this.content

    return content.some(block => block.hasContent())
  }

  allNetworksHaveContent() {
    if (this.socialNetworks.length > 0) {
      return this.socialNetworks.every(network => {
        const networkContent = this.getContentForNetwork(network)
        const allHasContent = networkContent.every(item => item.hasContent())

        return networkContent.length > 0 && allHasContent
      })
    }

    return false
  }

  hasAttachments(network) {
    const content = network ?
      this.content.filter(c => c.network === network) :
      this.content

    return content.some(block => block.hasAttachments())
  }

  setSchedules(schedules) {
    this.schedules = schedules.map(s => {
      if (s instanceof Schedule) return s
      return new Schedule(s)
    })
  }

  toJSON() {
    return {
      id: this.id,
      content: this.content.map(c => c.toJSON()),
      social_networks: this.socialNetworks,
      schedules: this.schedules.map(s => s.toJSON()),
      status: this.status
    }
  }

  isPublishable() {
    return this.allNetworksHaveContent() &&
      this.socialNetworks.length > 0 &&
      !this.hasAnyCharLimitExceeded()
  }

  isSchedulable() {
    return this.allNetworksHaveContent() &&
      this.socialNetworks.length > 0 &&
      this.schedules.length > 0 &&
      !this.hasAnyCharLimitExceeded()
  }

  isSaveable() {
    return this.allNetworksHaveContent() &&
      this.socialNetworks.length > 0
  }

  hasAnyCharLimitExceeded() {
    // First check canonical content since it has counters for all networks
    const canonicalContent = this.getContentForNetwork('canonical')
    if (canonicalContent.some(content => content.charLimitExceeded)) {
      return true
    }

    // Then check network-specific content
    return this.socialNetworks.some(network => {
      const networkContent = this.getContentForNetwork(network)
      return networkContent.some(content => content.charLimitExceeded)
    })
  }
}

export class PostContent {
  text = ''
  network = null
  order = 0
  meta = { mentions: [], tags: [] }
  attachments = []
  sync = true
  charCount = 0
  charLimitExceeded = false

  static fromCanonical(network, canonicalContent) {
    return new PostContent({
      network,
      order: canonicalContent.order,
      meta: canonicalContent.meta,
      text: PostContent.processCanonicalText(network, canonicalContent),
      attachments: canonicalContent.attachments.map(a => new Attachment(a)),
    })
  }

  static removeUrlFromText(text, url) {
    // If the URL is surrounded by other text or punctuation, don't remove it
    // This regex checks if the URL is at the end of the text (possibly with whitespace after)
    const endsWithUrlRegex = new RegExp(`${this.escapeRegExp(url)}\\s*$`);

    // This regex checks if the URL is standalone (has space or start of text before it)
    const standaloneUrlRegex = new RegExp(`(^|\\s)${this.escapeRegExp(url)}($|\\s)`);

    // Only remove the URL if it's at the end of the text AND it's a standalone URL
    // This prevents removing URLs that are part of a sentence or in parentheses
    if (endsWithUrlRegex.test(text) && standaloneUrlRegex.test(text)) {
      return text.replace(endsWithUrlRegex, '').trim();
    }

    return text;
  }

  // Helper method to escape special characters in regex
  static escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  static shouldRemoveUrl(network) {
    return network === 'bsky' || network === 'linkedin'
  }

  static processCanonicalText(network, canonicalContent) {
    const meta = { ...canonicalContent.meta }
    let text = canonicalContent.text

    text = PostContent.processMentionsForNetwork(text, network, meta.mentions)
    text = PostContent.processTextForNetwork(text, network, canonicalContent.attachments)

    return text
  }

  static processMentionsForNetwork(text, network, mentions) {
    let sortedMentions = [...mentions].sort((a, b) => b.name.length - a.name.length)

    sortedMentions.forEach(mention => {
      const originalMention = `@${mention.name}`
      const escapedOriginalMention = originalMention.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const regex = new RegExp(`(^|\\s)(${escapedOriginalMention})(?=\\s|$)`, 'g')

      if (network === 'linkedin') {
        text = text.replace(regex, (match, prefix) => `${prefix}${mention.name}`)
      } else {
        const networkMentionData = mention.networks?.find(n => n.id === network)
        if (networkMentionData?.handle) {
          const networkMentionText = `@${networkMentionData.handle}`
          text = text.replace(regex, (match, prefix) => `${prefix}${networkMentionText}`)
        }
      }
    })

    return text
  }

  static processTextForNetwork(text, network, attachments) {
    if (PostContent.shouldRemoveUrl(network)) {
      const linkAttachment = attachments?.find(a => a.type === 'link')

      if (linkAttachment?.metadata?.url) {
        const result = PostContent.removeUrlFromText(text, linkAttachment.metadata.url)
        return result
      }
    }
    return text
  }

  constructor(data = {}) {
    const validationData = {
      text: data.text || '',
      network: data.network,
      order: data.order || 0,
      sync: data.sync,
      meta: data.meta || { mentions: [], tags: [] },
      attachments: (data.attachments || []).map(a => {
        if (a instanceof Attachment) return a
        return new Attachment(a)
      }),
      charCount: data.charCount || 0,
      charLimitExceeded: data.charLimitExceeded || false
    }

    const result = validatePostContent(validationData)

    if (!result.success) {
      throw new Error(`Invalid post content data: ${result.error.message}`)
    }

    const validData = result.data
    this.id = validData.id
    this.text = validData.text
    this.network = validData.network
    this.order = validData.order
    this.sync = validData.sync
    this.meta = validData.meta
    this.charCount = validData.charCount
    this.charLimitExceeded = validData.charLimitExceeded

    this.attachments = validData.attachments.map(a => {
      if (a instanceof Attachment) return a
      return new Attachment(a)
    })
  }

  isEditable() {
    return this.sync !== true || this.isCanonical()
  }

  isCanonical() {
    return this.network === "canonical"
  }

  withOrder(order) {
    this.order = order
    return this
  }

  withText(text) {
    this.text = text
    return this
  }

  withAttachments(attachments) {
    this.attachments = attachments.map((a, index) => {
      const attachment = a instanceof Attachment ? a : new Attachment(a)
      attachment.order = index
      return attachment
    })
    return this
  }

  addAttachment(attachment) {
    const existingIndex = this.attachments.findIndex(a => a.id === attachment.id)
    if (existingIndex !== -1) {
      this.attachments[existingIndex] = new Attachment({
        ...attachment,
        order: existingIndex
      })
    } else {
      this.attachments.push(new Attachment({
        ...attachment,
        order: this.attachments.length
      }))
    }
    return this
  }

  removeAttachment(id) {
    const index = this.attachments.findIndex(a => a.id === id)
    if (index !== -1) {
      this.attachments.splice(index, 1)
      // Update order of remaining attachments
      this.attachments.forEach((a, i) => {
        a.order = i
      })
    }
    return this
  }

  updateAttachment(id, attachment) {
    const index = this.attachments.findIndex(a => a.id === id)

    if (index !== -1) {
      this.attachments[index] = new Attachment({
        ...attachment,
        order: index
      })
    }

    return this
  }

  updateLinkAttachment(attachment) {
    const index = this.attachments.findIndex(a => a.type === 'link')

    if (index !== -1) {
      this.attachments.splice(index, 1)
      this.attachments.forEach((a, i) => {
        a.order = i
      })
    }

    if (attachment) {
      this.attachments.push(new Attachment({
        ...attachment,
        order: this.attachments.length
      }))
    }
  }

  updateCharCount(count, isExceeded) {
    this.charCount = count
    this.charLimitExceeded = isExceeded

    return this
  }

  hasContent() {
    const text = this.text?.trim() || ''
    return text.length > 0 || this.hasAttachments()
  }

  hasAttachments() {
    return this.attachments.length > 0
  }

  toJSON() {
    return {
      text: this.text,
      network: this.network,
      order: this.order,
      meta: this.meta,
      attachments: this.attachments
        .sort((a, b) => a.order - b.order)
        .map(a => a.toJSON()),
      sync: this.sync
    }
  }
}

export class Attachment {
  id = null
  type = null
  sourceUrl = null
  previewUrl = null
  metadata = {}
  filename = null
  isLoading = false
  order = 0

  constructor(data = {}) {
    const result = validateAttachment({
      id: data.id || uuidv4(),
      type: data.type,
      sourceUrl: data.sourceUrl || data.source_url || null,
      previewUrl: data.previewUrl || data.preview_url || null,
      metadata: data.metadata || {},
      filename: data.filename,
      isLoading: data.isLoading || false,
      order: data.order || 0
    })

    if (!result.success) {
      throw new Error(`Invalid attachment data: ${result.error.message}`)
    }

    const validData = result.data
    this.id = validData.id
    this.type = validData.type
    this.sourceUrl = validData.sourceUrl
    this.previewUrl = validData.previewUrl
    this.metadata = validData.metadata
    this.filename = validData.filename
    this.isLoading = validData.isLoading
    this.order = validData.order
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      sourceUrl: this.sourceUrl,
      previewUrl: this.previewUrl,
      metadata: this.metadata,
      filename: this.filename,
      isLoading: this.isLoading,
      order: this.order
    }
  }
}

export class Schedule {
  network = null
  timezone = null
  scheduledAt = null

  constructor(data = {}) {
    const scheduledAt = data.scheduled_at || data.scheduledAt

    const result = validateSchedule({
      network: data.network,
      timezone: data.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : null
    })

    if (!result.success) {
      throw new Error(`Invalid schedule data: ${result.error.message}`)
    }

    const validData = result.data
    this.network = validData.network
    this.timezone = validData.timezone
    this.scheduledAt = validData.scheduledAt
  }

  toJSON() {
    return {
      network: this.network,
      timezone: this.timezone,
      scheduled_at: this.scheduledAt
    }
  }
}

// Export enums for use in other parts of the application
export const Networks = NetworkEnum.enum
export const PostStatuses = PostStatusEnum.enum
export const AttachmentTypes = AttachmentTypeEnum.enum
