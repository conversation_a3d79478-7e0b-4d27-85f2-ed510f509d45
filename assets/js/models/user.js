import { UserSchema, FeatureSchema, SocialProfileSchema } from './schemas'

export class Feature {
  featureKey = null
  featureType = null
  featureName = null
  featureDescription = null
  featureComingSoon = false
  planKey = null
  currentUsage = 0
  usageKey = null
  limit = null
  resetPeriod = null
  periodStart = null
  periodEnd = null

  constructor(data = {}) {
    const validationData = {
      featureKey: data.feature_key,
      featureType: data.feature_type,
      featureName: data.feature_name,
      featureDescription: data.feature_description,
      featureComingSoon: data.feature_coming_soon,
      planKey: data.plan_key,
      currentUsage: data.current_usage,
      usageKey: data.usage_key,
      limit: data.limit,
      resetPeriod: data.reset_period,
      periodStart: data.period_start ? new Date(data.period_start) : null,
      periodEnd: data.period_end ? new Date(data.period_end) : null
    }

    const result = FeatureSchema.safeParse(validationData)

    if (!result.success) {
      throw new Error(`Invalid feature data: ${result.error.message}`)
    }

    Object.assign(this, result.data)
  }

  isEnabled() {
    return !this.featureComingSoon &&
      (this.featureType == "boolean" ||
        (this.limit === -1 || this.currentUsage < this.limit))
  }

  isUnlimited() {
    return this.limit === -1
  }

  remainingUsage() {
    if (this.isUnlimited) return Infinity
    return Math.max(0, this.limit - this.currentUsage)
  }

  usagePercentage() {
    if (this.isUnlimited() || !this.limit) return 0
    return Math.round((this.currentUsage / this.limit) * 100)
  }

  toJSON() {
    return {
      feature_key: this.featureKey,
      feature_type: this.featureType,
      feature_name: this.featureName,
      feature_description: this.featureDescription,
      feature_coming_soon: this.featureComingSoon,
      plan_key: this.planKey,
      current_usage: this.currentUsage,
      limit: this.limit,
      reset_period: this.resetPeriod,
      period_start: this.periodStart?.toISOString(),
      period_end: this.periodEnd?.toISOString()
    }
  }
}

export class SocialProfile {
  id = null
  name = null
  bskyHandle = null
  xHandle = null
  mastodonHandle = null
  networks = []

  constructor(data = {}) {
    const validationData = {
      id: data.id,
      name: data.name,
      bskyHandle: data.bsky_handle,
      mastodonHandle: data.mastodon_handle,
      xHandle: data.x_handle,
      networks: this.mapNetworks(data)
    }

    const result = SocialProfileSchema.safeParse(validationData)

    if (!result.success) {
      throw new Error(`Invalid social profile data: ${result.error.message}`)
    }

    Object.assign(this, result.data)
  }

  mapNetworks(data) {
    const mapping = { bsky: "bsky_handle", mastodon: "mastodon_handle", x: "x_handle" }
    const networks = []

    Object.keys(mapping).forEach(network => {
      const handle = data[mapping[network]]

      if (handle) networks.push({ id: network, handle })
    })

    return networks
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      bsky_handle: this.bskyHandle,
      mastodon_handle: this.mastodonHandle,
      x_handle: this.xHandle,
      networks: this.networks
    }
  }
}

export class User {
  uid = null
  email = null
  name = null
  settings = {}
  info = {}
  timezone = 'Etc/UTC'
  enabledFeatures = []
  connections = []
  socialProfiles = []
  workspace_settings = {}

  constructor(data = {}) {
    const validationData = {
      uid: data.uid,
      email: data.email,
      name: data.name,
      settings: data.settings || {},
      info: data.info || {},
      timezone: data.timezone || 'Etc/UTC',
      connections: data.connections
    }

    const result = UserSchema.safeParse(validationData)

    if (!result.success) {
      throw new Error(`Invalid user data: ${result.error.message}`)
    }

    Object.assign(this, result.data)
    this.workspace_settings = data.workspace_settings || {}

    this.enabledFeatures = (data.enabled_features).map(f => new Feature(f))
    this.socialProfiles = (data.social_profiles).map(p => new SocialProfile(p))
  }

  getFeature(key) {
    return this.enabledFeatures.find(f => f.usageKey === key || f.featureKey === key)
  }

  hasFeature(key) {
    const feature = this.getFeature(key)
    return feature && feature.isEnabled() ? true : false
  }

  toJSON() {
    return {
      id: this.id,
      email: this.email,
      name: this.name,
      settings: this.settings,
      info: this.info,
      timezone: this.timezone,
      workspace_settings: this.workspace_settings,
      enabled_features: this.enabledFeatures.map(f => f.toJSON())
    }
  }
}
