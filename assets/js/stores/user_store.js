import { makeAutoObservable } from 'mobx'
import { User } from '../models/user'

export class UserStore {
  user = null

  constructor() {
    makeAutoObservable(this)
  }

  loadFromJSON(data = {}) {
    console.log('UserStore loadFromJSON data:', data)
    this.user = new User(data)
  }

  getFeature(usageKey) {
    const feature = this.user.getFeature(usageKey)
    console.log('UserStore getFeature:', usageKey, feature)
    return feature
  }

  hasFeature(usageKey) {
    return this.user.hasFeature(usageKey)
  }
}
