import { PostStore } from '../post_store'
import { Networks } from '../../models/post'

describe('PostStore', () => {
  let store

  beforeEach(() => {
    store = new PostStore()
    store.loadFromJSON({
      id: '123e4567-e89b-42d3-a456-556642440000',
      content: [],
      social_networks: [Networks.x],
      schedules: [],
      status: 'draft'
    })
  })

  describe('getContentForNetwork', () => {
    it('returns empty array when no content exists', () => {
      expect(store.getContentForNetwork(Networks.x)).toEqual([])
    })

    it('returns content for specific network', () => {
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440001',
        network: Networks.x,
        text: 'test',
        order: 0
      })
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(1)
      expect(content[0].text).toBe('test')
    })
  })

  describe('addContent', () => {
    it('adds new content', () => {
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440001',
        network: Networks.x,
        text: 'test',
        order: 0
      })
      expect(store.post.content).toHaveLength(1)
      expect(store.post.content[0].text).toBe('test')
    })

    it('reorders content when inserting in the middle', () => {
      store.addContent({
        network: Networks.x,
        text: 'first',
        order: 0
      })
      store.addContent({
        network: Networks.x,
        text: 'third',
        order: 1
      })

      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440003',
        network: Networks.x,
        text: 'second'
      }, 1)

      const networkContent = store.getContentForNetwork(Networks.x)

      expect(networkContent).toHaveLength(3)
      expect(networkContent[0].text).toBe('first')
      expect(networkContent[0].order).toBe(0)
      expect(networkContent[1].text).toBe('second')
      expect(networkContent[1].order).toBe(1)
      expect(networkContent[2].text).toBe('third')
      expect(networkContent[2].order).toBe(2)
    })

    it('maintains order uniqueness when inserting content', () => {
      store.addContent({
        network: Networks.x,
        text: 'first',
        order: 0
      })
      store.addContent({
        network: Networks.x,
        text: 'second',
        order: 1
      })

      store.addContent({
        network: Networks.x,
        text: 'middle',
        order: 2
      })

      const networkContent = store.getContentForNetwork(Networks.x)
      const orders = networkContent.map(c => c.order)
      const uniqueOrders = [...new Set(orders)]

      // Verify orders are unique
      expect(orders).toHaveLength(uniqueOrders.length)
      // Verify orders are sequential
      expect(orders.sort((a, b) => a - b)).toEqual([0, 1, 2])
    })

    it('does not affect content from other networks when inserting', () => {
      store.addContent({
        network: Networks.bsky,
        text: 'bsky post',
        order: 0
      })

      store.addContent({
        network: Networks.x,
        text: 'first',
        order: 0
      })
      store.addContent({
        network: Networks.x,
        text: 'second',
        order: 1
      })

      store.addContent({
        network: Networks.x,
        text: 'third',
        order: 2
      })

      const bskyContent = store.getContentForNetwork(Networks.bsky)
      const xContent = store.getContentForNetwork(Networks.x)

      expect(bskyContent).toHaveLength(1)
      expect(bskyContent[0].text).toBe('bsky post')
      expect(bskyContent[0].order).toBe(0)

      expect(xContent).toHaveLength(3)
      expect(xContent[0].text).toBe('first')
      expect(xContent[0].order).toBe(0)
      expect(xContent[1].text).toBe('second')
      expect(xContent[1].order).toBe(1)
      expect(xContent[2].text).toBe('third')
      expect(xContent[2].order).toBe(2)

    })
  })

  describe('updateContent', () => {
    it('updates existing content', () => {
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440001',
        network: Networks.x,
        text: 'test 1',
        order: 0
      })
      store.updateContent(Networks.x, 0, { text: 'updated' })
      expect(store.post.content[0].text).toBe('updated')
    })

    it('adds new content if none exists', () => {
      store.updateContent(Networks.x, 0, {
        id: '123e4567-e89b-42d3-a456-556642440001',
        text: 'test'
      })
      expect(store.post.content).toHaveLength(1)
      expect(store.post.content[0].text).toBe('test')
    })
  })

  describe('removeContent', () => {
    beforeEach(() => {
      // Add multiple content blocks for testing
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440001',
        network: Networks.x,
        text: 'first',
        order: 0
      })
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440002',
        network: Networks.x,
        text: 'second',
        order: 1
      })
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440003',
        network: Networks.x,
        text: 'third',
        order: 2
      })
    })

    it('removes content for specific network and order', () => {
      store.removeContent(Networks.x, 0)
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(2)
      expect(content[0].text).toBe('second')
      expect(content[0].order).toBe(0)
      expect(content[1].text).toBe('third')
      expect(content[1].order).toBe(1)
    })

    it('updates order values after removing content from the middle', () => {
      store.removeContent(Networks.x, 1)
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(2)
      expect(content[0].text).toBe('first')
      expect(content[0].order).toBe(0)
      expect(content[1].text).toBe('third')
      expect(content[1].order).toBe(1)
    })

    it('updates order values after removing last content', () => {
      store.removeContent(Networks.x, 2)
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(2)
      expect(content[0].text).toBe('first')
      expect(content[0].order).toBe(0)
      expect(content[1].text).toBe('second')
      expect(content[1].order).toBe(1)
    })

    it('maintains correct order values when removing multiple content items', () => {
      store.removeContent(Networks.x, 0)
      store.removeContent(Networks.x, 0) // Remove what was originally at order 1
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(1)
      expect(content[0].text).toBe('third')
      expect(content[0].order).toBe(0)
    })

    it('does nothing if content does not exist', () => {
      store.removeContent(Networks.x, 5)
      const content = store.getContentForNetwork(Networks.x)
      expect(content).toHaveLength(3)
      expect(content[0].order).toBe(0)
      expect(content[1].order).toBe(1)
      expect(content[2].order).toBe(2)
    })

    it('does not affect content from other networks when removing content', () => {
      // Add content for another network
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440004',
        network: Networks.bsky,
        text: 'bsky post',
        order: 0
      })

      store.removeContent(Networks.x, 1)

      // Check X network content is reordered
      const xContent = store.getContentForNetwork(Networks.x)
      expect(xContent).toHaveLength(2)
      expect(xContent[0].text).toBe('first')
      expect(xContent[0].order).toBe(0)
      expect(xContent[1].text).toBe('third')
      expect(xContent[1].order).toBe(1)

      // Check Bluesky content remains unchanged
      const bskyContent = store.getContentForNetwork(Networks.bsky)
      expect(bskyContent).toHaveLength(1)
      expect(bskyContent[0].text).toBe('bsky post')
      expect(bskyContent[0].order).toBe(0)
    })
  })

  describe('reorder', () => {
    beforeEach(() => {
      // Add multiple content blocks for testing
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440001',
        network: Networks.x,
        text: 'first',
        order: 0
      })
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440002',
        network: Networks.x,
        text: 'second',
        order: 1
      })
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440003',
        network: Networks.x,
        text: 'third',
        order: 2
      })
    })

    it('returns same content array if order has not changed', () => {
      const content = store.getContentForNetwork(Networks.x, 1)
      const result = store.reorder(content, 1)

      expect(result).toHaveLength(3)
      expect(result[0].text).toBe('first')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('second')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('third')
      expect(result[2].order).toBe(2)
    })

    it('moves content to a higher order and shifts others down', () => {
      const content = store.getContentForNetwork(Networks.x, 0)
      const result = store.reorder(content, 2)

      expect(result).toHaveLength(3)
      expect(result[0].text).toBe('second')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('third')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('first')
      expect(result[2].order).toBe(2)

      // Verify store state is also updated
      const networkContent = store.getContentForNetwork(Networks.x)
      expect(networkContent).toEqual(result)
    })

    it('moves content to a lower order and shifts others up', () => {
      const content = store.getContentForNetwork(Networks.x, 2)
      const result = store.reorder(content, 0)

      expect(result).toHaveLength(3)
      expect(result[0].text).toBe('third')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('first')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('second')
      expect(result[2].order).toBe(2)

      // Verify store state is also updated
      const networkContent = store.getContentForNetwork(Networks.x)
      expect(networkContent).toEqual(result)
    })

    it('maintains order uniqueness during reordering', () => {
      // Get all orders before reordering
      const content = store.getContentForNetwork(Networks.x, 0)
      const result = store.reorder(content, 2)

      // Check that orders are unique
      const orders = result.map(c => c.order)
      const uniqueOrders = [...new Set(orders)]
      expect(orders).toHaveLength(uniqueOrders.length)

      // Check that orders are sequential
      expect(orders.sort((a, b) => a - b)).toEqual([0, 1, 2])
    })

    it('handles reordering in the middle of the list', () => {
      const content = store.getContentForNetwork(Networks.x, 0)
      const result = store.reorder(content, 1)

      expect(result).toHaveLength(3)
      expect(result[0].text).toBe('second')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('first')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('third')
      expect(result[2].order).toBe(2)
    })

    it('handles multiple reorders in sequence', () => {
      // First reorder: move 'first' to the end
      let content = store.getContentForNetwork(Networks.x, 0)
      let result = store.reorder(content, 2)

      expect(result[2].text).toBe('first')
      expect(result[2].order).toBe(2)

      // Second reorder: move 'second' to the front
      content = result.find(c => c.text === 'second')  // Get 'second' by text instead of order
      result = store.reorder(content, 0)

      expect(result[0].text).toBe('second')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('third')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('first')
      expect(result[2].order).toBe(2)

      // Verify all orders remain unique and sequential
      const orders = result.map(c => c.order)
      const uniqueOrders = [...new Set(orders)]
      expect(orders).toHaveLength(uniqueOrders.length)
      expect(orders.sort((a, b) => a - b)).toEqual([0, 1, 2])
    })

    it('does not affect content from other networks', () => {
      // Add content for another network
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440004',
        network: Networks.bsky,
        text: 'bsky post',
        order: 0
      })

      const content = store.getContentForNetwork(Networks.x, 0)
      const result = store.reorder(content, 2)

      // Check X network content is reordered
      expect(result).toHaveLength(3)
      expect(result[0].text).toBe('second')
      expect(result[0].order).toBe(0)
      expect(result[1].text).toBe('third')
      expect(result[1].order).toBe(1)
      expect(result[2].text).toBe('first')
      expect(result[2].order).toBe(2)

      // Check Bluesky content remains unchanged
      const bskyContent = store.getContentForNetwork(Networks.bsky)
      expect(bskyContent).toHaveLength(1)
      expect(bskyContent[0].text).toBe('bsky post')
      expect(bskyContent[0].order).toBe(0)
    })

    it('maintains correct order after multiple network reorders', () => {
      // Add content for another network
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440004',
        network: Networks.bsky,
        text: 'bsky first',
        order: 0
      })
      store.addContent({
        id: '123e4567-e89b-42d3-a456-556642440005',
        network: Networks.bsky,
        text: 'bsky second',
        order: 1
      })

      // Reorder X network content
      let content = store.getContentForNetwork(Networks.x, 0)
      let result = store.reorder(content, 2)

      // Verify X network order
      expect(result.map(c => c.order)).toEqual([0, 1, 2])

      // Reorder Bluesky network content
      content = store.getContentForNetwork(Networks.bsky, 0)
      result = store.reorder(content, 1)

      // Verify Bluesky network order
      expect(result.map(c => c.order)).toEqual([0, 1])

      // Verify both networks maintain correct order
      const xContent = store.getContentForNetwork(Networks.x)
      const bskyContent = store.getContentForNetwork(Networks.bsky)

      expect(xContent.map(c => c.order)).toEqual([0, 1, 2])
      expect(bskyContent.map(c => c.order)).toEqual([0, 1])
    })
  })
})
