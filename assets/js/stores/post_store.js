import { makeAutoObservable } from 'mobx'
import { Post, PostContent } from '../models/post'

export class PostStore {
  post = null
  form = null

  constructor() {
    makeAutoObservable(this)
  }

  setForm(form) {
    this.form = form
  }

  getConnection(network) {
    return this.form?.getConnection(network)
  }

  loadFromJSON(data = {}, availableNetworks = []) {
    this.post = Post.fromJSON(data, availableNetworks)
  }

  syncFromCanonical(network, order) {
    const canonicalContent = this.getContentForNetwork("canonical", order)

    if (!canonicalContent) return

    const processedText = PostContent.processTextForNetwork(
      canonicalContent.text,
      network,
      canonicalContent.attachments
    )

    let content = this.updateContent(network, order, {
      text: processedText,
      meta: canonicalContent.meta,
    })

    content.withAttachments(canonicalContent.attachments)

    return content
  }

  getContentForNetwork(network, order = undefined) {
    const networkContent = this.post.content
      .filter(c => c.network === network)
      .sort((a, b) => a.order - b.order)

    if (order !== undefined) {
      return networkContent.find(c => c.order === order)
    }

    return networkContent
  }

  disableNetwork(network) {
    this.post.content = this.post.content.filter(c => c.network !== network)
    this.post.socialNetworks = this.post.socialNetworks.filter(n => n !== network)
    this.post.schedules = this.post.schedules.filter(s => s.network !== network)
    return this.post
  }

  addContent(data, index = undefined) {
    const content = new PostContent(data)

    this.post.content.push(content)

    const networkContent = this.getContentForNetwork(content.network)
    content.order = networkContent.length - 1

    if (index !== undefined) {
      this.reorder(content, index)
    }

    return content
  }

  updateContent(network, order, data) {
    if (!this.post) return null

    const existingIndex = this.post.content.findIndex(
      c => c.network === network && c.order === order
    )

    if (existingIndex !== -1) {
      const content = this.post.content[existingIndex]

      if (data.text !== undefined) {
        const processedText = PostContent.processTextForNetwork(data.text, network, content.attachments)
        content.text = processedText
      }

      // Update meta if provided (replace the entire object, don't merge)
      if (data.meta !== undefined) {
        content.meta = { ...data.meta }
      }

      // Update sync if provided
      if (data.sync !== undefined) {
        content.sync = data.sync
      }

      // Update character count and limit exceeded status
      if (data.charCount !== undefined) {
        content.charCount = data.charCount
      }
      if (data.charLimitExceeded !== undefined) {
        content.charLimitExceeded = data.charLimitExceeded
      }

      // Update other properties as needed
      if (data.order !== undefined) {
        content.order = data.order
      }

      return content
    } else {
      return this.addContent({
        ...data,
        network,
        order
      })
    }
  }

  removeContent(network, order = undefined) {
    if (order === undefined) {
      this.post.content = this.post.content.filter(c => c.network !== network)
    } else {
      const index = this.post.content.findIndex(
        c => c.network === network && c.order === order
      )

      if (index !== -1) {
        const content = this.post.content[index]
        this.post.content.splice(index, 1)

        // Update order values for remaining content in the same network
        const networkContent = this.getContentForNetwork(network)
        networkContent.forEach((c, i) => {
          c.order = i
        })

        return content
      }
      return null
    }
  }

  reorder(content, newOrder) {
    const networkContent = this.getContentForNetwork(content.network)
    const currentIndex = networkContent.findIndex(c => c.order === content.order)

    // Remove the content from its current position
    const [movedContent] = networkContent.splice(currentIndex, 1)

    // Insert it at the new position
    networkContent.splice(newOrder, 0, movedContent)

    // Update all orders to be sequential
    networkContent.forEach((c, i) => {
      c.order = i
    })

    return networkContent
  }
}
