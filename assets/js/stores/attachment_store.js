import { makeAutoObservable } from 'mobx'
import { Attachment } from '../models/post'

export class AttachmentStore {
  attachments = new Map()

  static fromPost(post, events) {
    const store = new AttachmentStore(events)
    post.content.forEach(content => {
      content.attachments.forEach(attachment => store.addAttachment(attachment))
    })
    return store
  }

  constructor(events) {
    makeAutoObservable(this)
    this.events = events
  }

  addAttachment(attachment) {
    const modelAttachment = attachment instanceof Attachment ?
      attachment : new Attachment(attachment)
    this.attachments.set(modelAttachment.id, modelAttachment)
    return modelAttachment
  }

  updateAttachment(id, data) {
    const attachment = this.attachments.get(id)

    const transformedData = {
      ...data,
      type: data.content_type?.startsWith('video/') ? 'video' : 'image',
      sourceUrl: data.source_url || null,
      previewUrl: data.preview_url || null
    }

    Object.assign(attachment, transformedData)

    return attachment
  }

  setMetadata(id, metadata) {
    const attachment = this.attachments.get(id)
    attachment.metadata = { ...attachment.metadata, ...metadata }
    return attachment
  }

  removeAttachment(id) {
    const attachment = this.attachments.get(id)

    if (this.attachments.delete(id)) {
      console.debug('Attachment removed', id)
    } else {
      console.debug('Attachment not found', id)
    }

    return attachment
  }

  finalize(attachment) {
    return this.updateAttachment(attachment.id, { ...attachment, isLoading: false })
  }

  getAttachment(id) {
    return this.attachments.get(id)
  }

  getAllAttachments() {
    return Array.from(this.attachments.values())
  }

  clear() {
    this.attachments.clear()
  }
}
