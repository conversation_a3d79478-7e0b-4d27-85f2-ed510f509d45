// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"

import PostForm from "./hooks/post_form"
import ActivityChart from "./hooks/activity_chart"
import CalendarTooltip from "./hooks/calendar_tooltip"
import CalendarExpand from "./hooks/calendar_expand"
import MobileMenuHook from "./hooks/mobile_menu_hook"
import UserMenuHook from "./hooks/user_menu_hook"
import Workspace<PERSON><PERSON><PERSON><PERSON><PERSON> from "./hooks/workspace_switcher_hook"
import MastodonInstanceDropdown from "./hooks/mastodon_instance_dropdown"
import { FeatureButtons } from "./views/feature_buttons"
import AppFeatures from "./hooks/app_features"

// Initialize feature buttons handler
window.JCP = window.JCP || {}
window.JCP.Views = window.JCP.Views || {}
window.JCP.Views.FeatureButtons = new FeatureButtons()

// Get CSRF token from meta tag
let csrfToken = document.querySelector("meta[name='csrf-token']")?.getAttribute("content")

// Simple mobile sidebar toggle
document.addEventListener('click', (e) => {
  if (e.target.closest('[data-mobile-sidebar-toggle]') ||
    e.target.closest('#mobile-sidebar-close') ||
    e.target.closest('#sidebar-backdrop')) {
    document.getElementById('sidebar-container').classList.toggle('-translate-x-full');
    document.getElementById('sidebar-backdrop').classList.toggle('hidden');
  }
});

// Create hooks object combining all hooks
const Hooks = {
  ...PostForm,
  ...ActivityChart,
  ...MastodonInstanceDropdown,
  ...AppFeatures,
  CalendarTooltip,
  CalendarExpand,
  MobileMenu: MobileMenuHook,
  UserMenu: UserMenuHook,
  WorkspaceSwitcher: WorkspaceSwitcherHook,
  SidebarToggle: {
    mounted() {
      this.el.addEventListener('click', () => {
        const sidebarContainer = document.getElementById('sidebar-container');
        const button = document.getElementById('toggle-sidebar-button');

        const isShown = button.dataset.showSidebar === 'true';

        // Toggle visibility classes
        if (isShown) {
          sidebarContainer.classList.remove('translate-x-0', 'md:block');
          sidebarContainer.classList.add('-translate-x-full', 'md:hidden');
        } else {
          sidebarContainer.classList.remove('-translate-x-full', 'md:hidden');
          sidebarContainer.classList.add('translate-x-0', 'md:block');
        }

        // Update data attribute
        sidebarContainer.dataset.showSidebar = (!isShown).toString();

        // Notify server
        this.pushEventTo('#sidebar', 'toggle_sidebar');
      });
    }
  },
  Flash: {
    mounted() {
      // Basic flash message handling
      let hide = () => {
        this.el.style.opacity = '0'
        setTimeout(() => this.el.remove(), 150)
      }

      // Auto-hide after 5 seconds
      setTimeout(hide, 5000)

      // Allow manual close
      this.el.addEventListener('click', () => hide())
    }
  },
  ModalCloser: {
    mounted() {
      this.handleEvent("hide_modal", () => {
        this.el.dispatchEvent(new Event("click", { bubbles: true }));
      });
    },
  },
}

// Add dark mode handling
const setupDarkMode = () => {
  // Check for saved theme preference or system preference
  const isDark = localStorage.theme === 'dark' ||
    (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // Apply initial theme
  document.documentElement.classList.toggle('dark', isDark);

  // Listen for system theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
    if (!('theme' in localStorage)) {
      document.documentElement.classList.toggle('dark', e.matches);
    }
  });

  // Add hook for theme toggle button
  const ThemeToggle = {
    mounted() {
      this.el.addEventListener('click', () => {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.theme = isDark ? 'dark' : 'light';

        // Only try to notify server if we have a LiveView connection
        try {
          if (this.el.closest('[data-phx-main]')?.__view) {
            this.pushEvent('theme_changed', { theme: isDark ? 'dark' : 'light' });
          }
        } catch (e) {
          console.debug('Theme changed without LiveView connection');
        }
      });
    }
  };

  // Add to existing Hooks object
  Object.assign(Hooks, { ThemeToggle });
};

// Initialize LiveSocket with channel configuration
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {
    _csrf_token: csrfToken,
    user_id: document.querySelector('body').dataset.userId
  },
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Add after LiveSocket initialization
setupDarkMode();

// Handle sidebar cookie state
window.addEventListener("app:set_sidebar_cookie", (e) => {
  localStorage.setItem('showSidebar', e.detail.show);
});

// Handle clicks on disabled feature buttons
document.addEventListener("click", (e) => {
  const target = e.target.closest("[data-feature]");
  if (target && target.hasAttribute("disabled")) {
    e.preventDefault();
    e.stopPropagation();

    const feature = target.dataset.feature;
    const liveView = target.closest("[data-phx-main]").__view;
    liveView.pushEvent("show_feature_upgrade_modal", { feature });
  }
});

// Mastodon instance dropdown
window.addEventListener("click", (e) => {
  const dropdowns = document.querySelectorAll("[id^='mastodon-instance-dropdown-']")
  dropdowns.forEach((dropdown) => {
    const button = dropdown.previousElementSibling.querySelector("[phx-click^='show_dropdown']")
    if (!dropdown.contains(e.target) && !button.contains(e.target)) {
      dropdown.classList.add("hidden")
    }
  })
})

window.showDropdown = (id) => {
  const dropdown = document.getElementById(id)
  if (dropdown.classList.contains("hidden")) {
    dropdown.classList.remove("hidden")
  } else {
    dropdown.classList.add("hidden")
  }
}
