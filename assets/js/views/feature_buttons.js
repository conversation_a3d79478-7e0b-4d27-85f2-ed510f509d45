import { UpgradeModal } from '../publishing/upgrade_modal'

export class FeatureButtons {
  constructor() {
    this.upgradeModal = new UpgradeModal()
    this.setupEventListeners()
  }

  setupEventListeners() {
    document.addEventListener('click', (e) => {
      const button = e.target.closest('[data-feature]')
      if (!button) return

      if (button.dataset.featureEnabled === 'false') {
        e.preventDefault()
        e.stopPropagation()

        const featureKey = button.dataset.feature
        const feature = JCP.user.getFeature(featureKey)

        if (feature) {
          this.upgradeModal.show(feature)
        } else {
          console.warn(`Feature ${featureKey} not found`)
        }
      }
    })
  }

  destroy() {
    this.upgradeModal.close()
  }
}
