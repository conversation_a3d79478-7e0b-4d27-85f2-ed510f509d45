const COOKIE_CONSENT_STATUS_KEY = 'cookie_consent_status';
const COOKIE_CONSENT_VERSION = '1';

class CookieConsent {
  constructor() {
    this.init();
  }

  init() {
    const consentStatus = localStorage.getItem(COOKIE_CONSENT_STATUS_KEY);

    if (!consentStatus || !consentStatus.endsWith(`_v${COOKIE_CONSENT_VERSION}`)) {
      this.showConsentBanner();
    }

    this.setupEventListeners();
  }

  showConsentBanner() {
    const banner = document.querySelector('[data-cookie-consent]');
    if (banner) {
      requestAnimationFrame(() => {
        banner.classList.remove('translate-y-full');
        banner.classList.add('translate-y-0');
      });
    }
  }

  hideConsentBanner() {
    const banner = document.querySelector('[data-cookie-consent]');
    if (banner) {
      banner.classList.remove('translate-y-0');
      banner.classList.add('translate-y-full');
    }
  }

  setupEventListeners() {
    // Accept button
    const acceptBtn = document.querySelector('[data-cookie-accept]');
    if (acceptBtn) {
      acceptBtn.addEventListener('click', () => {
        localStorage.setItem(COOKIE_CONSENT_STATUS_KEY, `accepted_v${COOKIE_CONSENT_VERSION}`);
        this.hideConsentBanner();
      });
    }

    // Decline button
    const declineBtn = document.querySelector('[data-cookie-decline]');
    if (declineBtn) {
      declineBtn.addEventListener('click', () => {
        localStorage.setItem(COOKIE_CONSENT_STATUS_KEY, `declined_v${COOKIE_CONSENT_VERSION}`);
        this.hideConsentBanner();
      });
    }
  }
}

// Initialize cookie consent when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new CookieConsent();
});
