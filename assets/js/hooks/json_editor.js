import { createJSONEditor } from 'vanilla-jsoneditor'

export const JsonEditor = {
  mounted() {
    try {
      const json = JSON.parse(this.el.dataset.json || 'null');
      const inputId = this.el.id.replace('-editor', '-input');
      const input = document.getElementById(inputId);

      this.editor = createJSONEditor({
        target: this.el,
        props: {
          content: { json },
          mode: 'tree',
          readOnly: false,
          navigationBar: false,
          mainMenuBar: false,
          statusBar: false,
          onChange: (updatedContent) => {
            if (input) {
              input.value = JSON.stringify(updatedContent.json);
            }
          }
        }
      });

      // Set initial value
      if (input) {
        input.value = JSON.stringify(json);
      }
    } catch (err) {
      console.error('Failed to initialize editor:', err);
      this.el.innerHTML = '<div class="text-sm text-red-500">Error loading JSON viewer</div>';
    }
  },

  destroyed() {
    if (this.editor) {
      this.editor.destroy();
    }
  }
};
