import Chart from 'chart.js/auto'

export default {
  ActivityChart: {
    mounted() {
      const labels = this.el.dataset.labels ? JSON.parse(this.el.dataset.labels) : [];
      const datasets = this.el.dataset.datasets ? JSON.parse(this.el.dataset.datasets) : [];

      this.chart = new Chart(this.el, {
        type: 'bar',
        data: {
          labels,
          datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            x: {
              stacked: true,
              grid: {
                display: false
              }
            },
            y: {
              stacked: true,
              beginAtZero: true,
              ticks: {
                stepSize: 1
              }
            }
          }
        }
      });
    },

    destroyed() {
      this.chart?.destroy();
    }
  }
};
