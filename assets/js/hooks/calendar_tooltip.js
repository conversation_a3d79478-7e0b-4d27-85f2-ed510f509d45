import tippy from 'tippy.js';

export default {
  mounted() {
    // Only initialize tooltip if the element is visible
    if (this.el.offsetParent === null) {
      return; // Element is hidden, don't create tooltip
    }

    let postData;
    const dayCell = this.el.closest('[data-posts]');

    try {
      if (dayCell) {
        const postsStr = dayCell.dataset.posts;
        if (postsStr) {
          const posts = JSON.parse(postsStr);
          const postId = this.el.id.replace('calendar-post-', '').split('-')[0];
          postData = posts.find(p => p.id.toString() === postId);
        }
      } else if (this.el.dataset.post) {
        postData = JSON.parse(this.el.dataset.post);
      }
    } catch (e) {
      console.warn('Failed to parse post data:', e);
      return; // Exit early if we can't parse the data
    }

    // If no post data, check for inline content
    if (!postData) {
      const contentEl = this.el.querySelector('[data-tooltip-content]');
      if (contentEl) {
        tippy(this.el, {
          content: contentEl.innerHTML,
          allowHTML: true,
          placement: this.el.dataset.tooltipPlacement || 'top',
          delay: [100, 0],
          interactive: true,
          theme: 'light-border',
          appendTo: document.body
        });
      }
      return;
    }

    const content = `
      <div class="p-2">
        <div class="text-sm text-gray-900 font-medium mb-1">
          ${postData.datetime}
        </div>
        <div class="flex items-center gap-2 mb-2">
          ${postData.networks.map(network => `
            <div class="network-icon ${network} ${postData.post_type === 'published' ? 'published' : 'scheduled'}"></div>
          `).join('')}
        </div>
        <div class="text-sm text-gray-600">
          ${postData.text}
        </div>
      </div>
    `;

    tippy(this.el, {
      content,
      allowHTML: true,
      placement: this.el.dataset.tooltipPlacement || 'top',
      delay: [100, 0],
      interactive: true,
      theme: 'light-border',
      appendTo: document.body
    });
  },

  destroyed() {
    if (this.el._tippy) {
      this.el._tippy.destroy();
    }
  }
}
