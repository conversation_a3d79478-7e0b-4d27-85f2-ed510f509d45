import { Form } from '../publishing/form'

let Hooks = {}

Hooks.PostForm = {
  mounted() {
    this.form = new Form(this.el, {
      hooks: {
        pushEvent: (event, payload, callback) =>
          this.pushEventTo('#post-editor', event, payload, callback)
      }
    })

    this.handleEvent("attachment:uploaded", ({ attachment }) => {
      this.form.events.emit('attachment:uploaded', attachment)
    })

    this.handleEvent("attachment:upload_failed", ({ error }) => {
      this.form.events.emit('attachment:upload_failed', { id: error.id, message: error.message })
    })
  },

  destroyed() {
    this.form.destroy()
  }
}

export default Hooks
