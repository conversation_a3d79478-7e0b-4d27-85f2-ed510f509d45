const UserMenuHook = {
  mounted() {
    const button = this.el.querySelector('#desktop-avatar-button');
    const menu = this.el.querySelector('#desktop-dropdown-menu');

    if (!button || !menu) return;

    const toggleMenu = () => {
      if (window.innerWidth < 1024) return; // Only handle desktop clicks
      menu.classList.toggle('hidden');
    };

    const closeMenu = (e) => {
      if (window.innerWidth < 1024) return; // Only handle desktop clicks
      if (!button.contains(e.target) && !menu.contains(e.target)) {
        menu.classList.add('hidden');
      }
    };

    // Add event listeners
    button.addEventListener('click', toggleMenu);
    document.addEventListener('click', closeMenu);

    // Clean up
    this.cleanup = () => {
      button.removeEventListener('click', toggleMenu);
      document.removeEventListener('click', closeMenu);
    };
  },

  destroyed() {
    if (this.cleanup) {
      this.cleanup();
    }
  }
};

export default UserMenuHook;
