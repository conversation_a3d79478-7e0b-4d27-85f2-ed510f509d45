const MobileMenuHook = {
  mounted() {
    const menu = this.el
    const backdrop = menu.querySelector('.bg-gray-600')
    const panel = menu.querySelector('.transform')
    const closeButton = menu.querySelector('#close-mobile-menu')
    const avatarButton = document.getElementById('desktop-avatar-button')

    const showMenu = () => {
      menu.classList.remove('hidden')
      panel.classList.remove('translate-x-full')
      document.body.style.overflow = 'hidden'
    }

    const hideMenu = () => {
      panel.classList.add('translate-x-full')
      setTimeout(() => {
        menu.classList.add('hidden')
        document.body.style.overflow = ''
      }, 300)
    }

    // Handle avatar button click
    const handleAvatarClick = () => {
      if (window.innerWidth >= 1024) return // Only handle mobile clicks
      showMenu()
    }

    // Handle backdrop click
    const handleBackdropClick = (e) => {
      if (e.target === backdrop) {
        hideMenu()
      }
    }

    // Handle escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape' && !menu.classList.contains('hidden')) {
        hideMenu()
      }
    }

    // Handle navigation link clicks
    const handleNavClick = (e) => {
      if (e.target.closest('a')) {
        hideMenu()
      }
    }

    // Add event listeners
    avatarButton.addEventListener('click', handleAvatarClick)
    backdrop.addEventListener('click', handleBackdropClick)
    closeButton.addEventListener('click', hideMenu)
    document.addEventListener('keydown', handleEscape)
    menu.addEventListener('click', handleNavClick)

    // Clean up
    this.cleanup = () => {
      avatarButton.removeEventListener('click', handleAvatarClick)
      backdrop.removeEventListener('click', handleBackdropClick)
      closeButton.removeEventListener('click', hideMenu)
      document.removeEventListener('keydown', handleEscape)
      menu.removeEventListener('click', handleNavClick)
    }
  },

  destroyed() {
    if (this.cleanup) {
      this.cleanup()
    }
  }
}

export default MobileMenuHook
