const WorkspaceSwitcherHook = {
  mounted() {
    const id = this.el.id
    const button = document.getElementById(`${id}-button`)
    const menu = document.getElementById(`${id}-dropdown`)

    if (!button || !menu) return

    const toggleMenu = () => {
      menu.classList.toggle('show')
    }

    const closeMenu = (e) => {
      if (!button.contains(e.target) && !menu.contains(e.target)) {
        menu.classList.remove('show')
      }
    }

    // Toggle menu on button click
    button.addEventListener('click', toggleMenu)

    // Close menu when clicking outside
    document.addEventListener('click', closeMenu)

    // Clean up
    this.toggleMenu = toggleMenu
    this.closeMenu = closeMenu
  },

  destroyed() {
    const id = this.el.id
    const button = document.getElementById(`${id}-button`)

    if (button) {
      button.removeEventListener('click', this.toggleMenu)
    }

    document.removeEventListener('click', this.closeMenu)
  }
}

export default WorkspaceSwitcherHook

