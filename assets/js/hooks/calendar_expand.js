export default {
  mounted() {
    this.el.addEventListener('click', e => {
      const showMoreBtn = e.target.closest('[data-show-more]');
      if (!showMoreBtn) return;

      e.preventDefault();
      const date = showMoreBtn.dataset.date;

      const overlay = document.createElement('div');
      overlay.id = `day-overlay-${date}`;
      overlay.className = 'fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center';

      const modal = document.createElement('div');
      modal.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 transform transition-all';

      const content = document.getElementById(`expanded-posts-${date}`);
      if (content) {
        modal.innerHTML = `
          <div class="flex justify-between items-center px-6 py-4 border-b dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
              ${content.getAttribute('data-date-display')}
            </h3>
            <button type="button" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
            ${content.innerHTML}
          </div>
        `;

        // Add click handlers
        overlay.addEventListener('click', e => {
          if (e.target === overlay) {
            overlay.remove();
          }
        });

        modal.querySelector('button').addEventListener('click', () => {
          overlay.remove();
        });

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Initialize tooltips for the expanded posts
        const expandedPosts = modal.querySelectorAll('[phx-hook="CalendarTooltip"]');
        expandedPosts.forEach(post => {
          const tooltipContent = post.querySelector('[data-tooltip-content]');
          if (tooltipContent) {
            tippy(post, {
              content: tooltipContent.innerHTML,
              allowHTML: true,
              placement: post.dataset.tooltipPlacement || 'right',
              delay: [100, 0],
              interactive: true,
              theme: 'light-border',
              appendTo: document.body
            });
            tooltipContent.remove();
          }
        });

        // Add animation classes after a brief delay
        requestAnimationFrame(() => {
          modal.classList.add('translate-y-0', 'opacity-100');
        });
      }
    });
  }
}
