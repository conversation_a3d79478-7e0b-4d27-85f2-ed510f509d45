const MastodonInstanceDropdown = {
  mounted() {
    // Get the toggle button and dropdown elements
    this.toggleButton = this.el.querySelector("[data-dropdown-toggle]")
    this.dropdown = this.el.querySelector("[data-dropdown]")
    this.searchInput = this.el.querySelector("input[type='text']")

    // <PERSON>le clicks outside the dropdown
    document.addEventListener("click", (e) => {
      // Don't close if clicking inside the dropdown content or the toggle button
      if (this.dropdown.contains(e.target) || this.toggleButton.contains(e.target)) {
        return
      }
      this.hideDropdown()
    })

    // Add click handler for the toggle button
    this.toggleButton.addEventListener("click", (e) => {
      e.preventDefault()
      this.toggleDropdown()
    })
  },

  toggleDropdown() {
    if (this.dropdown.classList.contains("hidden")) {
      this.showDropdown()
    } else {
      this.hideDropdown()
    }
  },

  showDropdown() {
    this.dropdown.classList.remove("hidden")
    setTimeout(() => this.searchInput?.focus(), 50)
  },

  hideDropdown() {
    this.dropdown.classList.add("hidden")
  }
}

export default { MastodonInstanceDropdown }
