export class Attachments {
  constructor({ topic, events, store, postStore }) {
    this.topic = topic
    this.events = events
    this.store = store
    this.postStore = postStore

    this.MAX_VIDEO_SIZE = 50 * 1024 * 1024
    this.CLOUDINARY_UPLOAD_URL = 'https://api.cloudinary.com/v1_1'
    this.cloudinaryConfig = JCP.getConfig('cloudinary')
    this.cloudinaryEnabled = this.cloudinaryConfig?.enabled

    this.setupEventHandlers()
  }

  setupEventHandlers() {
    this.topic.on('attachments:upload_requested', this.onAttachmentsUploadRequested.bind(this))
    this.topic.on('attachments:remove_requested', this.onAttachmentsRemoveRequested.bind(this))
  }

  destroy() {
    this.topic.unregister(this)
  }

  async onAttachmentsUploadRequested(files) {
    const validFiles = this.validateFiles(files)

    if (validFiles.length > 0) {
      await this.uploadFiles(validFiles)
    }
  }

  async onAttachmentsRemoveRequested(attachmentId) {
    try {
      console.debug('Attachments onAttachmentsRemoveRequested', attachmentId)

      const isStillReferenced = this.postStore.post.content.some(content =>
        content.attachments.some(a => a.id === attachmentId)
      )

      if (!isStillReferenced) {
        const response = await fetch(`/api/attachments/${attachmentId}`, {
          method: 'DELETE',
          headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to remove attachment')
        }

        // Only remove from store if successfully deleted from server
        this.store.removeAttachment(attachmentId)
        console.log(`Attachment ${attachmentId} removed successfully`)
      }
    } catch (error) {
      console.error('Failed to remove attachment:', error)
      alert('Failed to remove attachment. Please try again.')
    }
  }

  async updateAttachment(attachmentId, updates) {
    try {
      console.debug('Attachments updateAttachment', attachmentId, updates)

      const response = await fetch(`/api/attachments/${attachmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ attachment: updates })
      })

      if (response.ok) {
        const { attachment } = await response.json()

        this.events.emit('attachment:updated', attachment)

        console.log('Attachment updated', attachment)
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update attachment')
      }
    } catch (error) {
      console.error('Failed to update attachment:', error)
      alert('Failed to update attachment. Please try again.')
      throw error
    }
  }

  async uploadFiles(files) {
    const attachments = []
    const validFiles = []

    // Add to store first for UI feedback and validate files
    for (const { id, file } of files) {
      const type = file.type.startsWith('video/') ? 'video' : 'image'

      // Check file size before upload
      const maxSize = type === 'video' ? 100 * 1024 * 1024 : 10 * 1024 * 1024 // 100MB for video, 10MB for images
      if (file.size > maxSize) {
        const sizeMB = Math.round(maxSize / (1024 * 1024))
        this.topic.emit('editor:show_error', `The file "${file.name}" is too large. Maximum size allowed is ${sizeMB}MB.`)
        continue
      }

      const attachment = {
        id,
        type,
        sourceUrl: null,
        previewUrl: null,
        metadata: {},
        filename: file.name,
        isLoading: true
      }

      this.store.addAttachment(attachment)
      attachments.push(attachment)
      validFiles.push({ id, file })
    }

    if (attachments.length > 0) {
      this.topic.emit('attachments:updated', attachments)
    }

    if (validFiles.length === 0) {
      return
    }

    try {
      if (this.cloudinaryEnabled) {
        // Upload directly to Cloudinary first
        await Promise.all(
          validFiles.map(async ({ id, file }) => {
            const type = file.type.startsWith('video/') ? 'video' : 'image'

            // Get signature for Cloudinary upload
            const signatureResponse = await fetch('/api/attachments/signature', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
              },
              body: JSON.stringify({ type })
            })

            const { signature } = await signatureResponse.json()

            // Upload to Cloudinary with progress tracking
            const cloudinaryData = await new Promise((resolve, reject) => {
              const xhr = new XMLHttpRequest()
              const cloudinaryFormData = new FormData()

              Object.entries(signature).forEach(([key, value]) => {
                cloudinaryFormData.append(key, value)
              })
              cloudinaryFormData.append('file', file)

              xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                  const progress = (event.loaded / event.total) * 100
                  this.events.emit('attachment:progress', { id, progress })
                }
              })

              xhr.addEventListener('load', (args) => {
                if (xhr.status >= 200 && xhr.status < 300) {
                  resolve(JSON.parse(xhr.response))
                } else {
                  reject(new Error(xhr.response?.error?.message || 'Failed to upload to Cloudinary'))
                }
              })

              xhr.addEventListener('error', (args) => {
                // Check if the error is likely due to file size (ERR_FAILED with 413)
                if (file.size > 100 * 1024 * 1024) { // 100MB general limit
                  reject(new Error(`The file "${file.name}" is too large for upload. Please try a smaller file.`))
                } else {
                  reject(new Error('Network error during upload. Please try again.'))
                }
              })

              xhr.open('POST', `${this.CLOUDINARY_UPLOAD_URL}/${this.cloudinaryConfig.cloud_name}/${type}/upload`)
              xhr.send(cloudinaryFormData)
            })

            // Create attachment in our backend with Cloudinary data
            const response = await fetch('/api/attachments', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                attachment: {
                  id,
                  filename: file.name,
                  content_type: file.type,
                  type: type,
                  source_url: cloudinaryData.secure_url,
                  preview_url: type === 'video' ? cloudinaryData.secure_url.replace(/\.[^.]+$/, '.jpg') : null,
                  status: 'completed',
                  metadata: {
                    size: cloudinaryData.bytes,
                    width: cloudinaryData.width,
                    height: cloudinaryData.height,
                    cloudinary: {
                      public_id: cloudinaryData.public_id,
                      version: cloudinaryData.version,
                      resource_type: cloudinaryData.resource_type,
                      size: cloudinaryData.bytes,
                      width: cloudinaryData.width,
                      height: cloudinaryData.height
                    }
                  }
                }
              })
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.error || 'Failed to create attachment')
            }

            const { attachment } = await response.json()

            // Update store with the complete attachment data
            this.store.updateAttachment(id, {
              ...attachment,
              isLoading: false
            })

            // Emit events to update UI
            this.events.emit('attachment:uploaded', attachment)
            this.topic.emit('attachments:updated', [attachment])
          })
        )
      } else {
        // If Cloudinary is disabled, upload files to our backend
        const formData = new FormData()
        validFiles.forEach(({ id, file }) => {
          formData.append('files[]', file)
          formData.append('uuids[]', id)
        })

        const response = await fetch('/api/attachments', {
          method: 'POST',
          body: formData,
          headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
          }
        })

        const data = await response.json()

        if (!response.ok) {
          const error = response.status === 422 ? data.error : 'Failed to upload attachments'
          this.topic.emit('editor:show_error', error)

          attachments.forEach(attachment => {
            this.store.removeAttachment(attachment.id)
            this.events.emit('attachment:removed', attachment)
          })

          throw new Error(data.error)
        }

        data.attachments.forEach(attachment => {
          this.store.updateAttachment(attachment.id, {
            ...attachment,
            isLoading: false
          })
        })
      }
    } catch (error) {
      console.error('Upload failed:', error)
      this.topic.emit('editor:show_error', error.message)

      attachments.forEach(attachment => {
        this.store.removeAttachment(attachment.id)
        this.events.emit('attachment:removed', attachment)
      })

      throw error
    }
  }

  async createLink(url) {
    try {
      const response = await fetch('/api/attachments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
          type: 'link',
          url
        })
      })

      if (response.ok) {
        const { attachment } = await response.json()
        this.topic.emit('links:attached', this.store.addAttachment(attachment))
      } else {
        this.topic.emit('links:error', { error: 'Failed to create link preview.' })
      }
    } catch (error) {
      this.topic.emit('links:error', { error: 'Failed to create link preview.' })
    }
  }

  getImageSizeLimit() {
    return JCP.user.getFeature('image_size').limit
  }

  getVideoSizeLimit() {
    return JCP.user.getFeature('video_size').limit
  }

  validateFiles(fileData) {
    const validFiles = []
    const errors = []

    fileData.forEach(({ id, file }) => {
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        errors.push(`File "${file.name}" has an unsupported type`)
        return
      }
      validFiles.push({ id, file })
    })

    if (errors.length > 0) {
      this.topic.emit('editor:show_error', errors.join('\n'))
    }

    return validFiles
  }

  // Add accept attribute to file input to restrict file types
  setupFileInput() {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = 'image/*,video/*'
    input.style.display = 'none'

    input.addEventListener('change', (event) => {
      const files = Array.from(event.target.files)
      const validFiles = this.validateFiles(files.map((file, id) => ({ id: `temp-${id}`, file })))
      if (validFiles.length > 0) {
        this.handleFileSelect(event)
      }
      // Reset input so the same file can be selected again
      input.value = ''
    })

    document.body.appendChild(input)
    return input
  }
}
