import { BaseCharCounter } from './base'

export class MastodonCharCounter extends BaseCharCounter {
  constructor(options) {
    const connection = JCP.getConnection('mastodon')
    const limit = connection.limit
    const urlLength = connection.urlLength

    super({ ...options, network: 'mastodon', limit, urlLength })

    this.urlPlaceholder = '.'.repeat(urlLength)
  }

  getCount() {
    const text = this.getText()
    const urlRegex = /(https?:\/\/[^\s]+)/g
    const urls = text.match(urlRegex) || []

    let processedText = text

    urls.forEach(url => {
      processedText = processedText.replace(url, this.urlPlaceholder)
    })

    return processedText.length
  }
}
