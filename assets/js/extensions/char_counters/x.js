import { BaseCharCounter } from './base'
import twttr from 'twitter-text'

export class XCharCounter extends BaseCharCounter {
  constructor(options) {
    let limit = 280; // Default X character limit

    try {
      const conn = JCP.getConnection("x");
      if (conn && conn.limit) {
        limit = conn.limit;
      }
    } catch (error) {
      console.warn("Failed to get X connection limit, using default:", error);
    }

    super({ ...options, network: 'x', limit: limit })
  }

  getCount() {
    const text = this.getText()

    if (!text) return 0

    const parsedTweet = twttr.parseTweet(text)

    return parsedTweet.weightedLength
  }
}
