import { NETWORKS } from '../networks'

export class BaseCharCounter {
  constructor(options = {}) {
    this.events = options.events
    this.network = options.network
    this.order = options.order
    this.store = options.store
    this.limit = options.limit
    this.urlLength = options.urlLength
    this.count = 0
    this.container = options.container
    this.editorId = options.editorId

    this.events.topic(this.network).on('content:edit', this.onContentEdit.bind(this), this)
    this.events.topic(this.network).on('content:sync_toggled', this.onContentEdit.bind(this), this)

    this.updateCount()
    this.rerender()
  }

  onContentEdit(content) {
    if (content.order === this.order) {
      this.updateCount()
      this.rerender()
    }
  }

  updateCount() {
    this.count = this.getCount()

    const isExceeded = this.isExceeded()
    const content = this.getContent()

    if (content) {
      this.store.updateContent(this.network, this.order, {
        charCount: this.count,
        charLimitExceeded: isExceeded
      })
    }

    this.events.topic(this.network).emit('content:char_count_updated', {
      count: this.count,
      network: this.network,
      isExceeded: isExceeded
    })
  }

  getContent() {
    return this.store.getContentForNetwork(this.network, this.order)
  }

  getText() {
    return this.getContent()?.text || ""
  }

  isExceeded() {
    return this.getRemainingChars() < 0
  }

  getCount() {
    return this.getText().length
  }

  getRemainingChars() {
    return this.limit - this.count
  }

  getProgressPercentage() {
    return Math.min(100, (this.count / this.limit) * 100)
  }

  getNetworkIcon() {
    const network = NETWORKS.find(n => n.id === this.network)
    if (!network) return ''

    return `
      <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="${network.viewBox}">
        <path d="${network.icon}" />
      </svg>
    `
  }

  getProgressColor(percentage) {
    if (percentage >= 100) return 'from-rose-400 to-rose-500'
    if (percentage >= 80) return 'from-orange-300 to-orange-400'
    return 'from-blue-500 to-blue-600'
  }

  rerender() {
    if (this.container) {
      this.container.innerHTML = this.renderHTML()
    }
  }

  renderHTML() {
    const count = this.count
    const limit = this.limit
    const remaining = this.getRemainingChars()
    const percentage = this.getProgressPercentage()
    const isOverLimit = count > limit
    const isApproachingLimit = percentage >= 80

    return `
      <div class="relative flex items-center h-7 rounded-md bg-gray-50/80 dark:bg-gray-700/50 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r ${this.getProgressColor(percentage)} opacity-10"
             style="width: ${percentage}%">
        </div>
        <div class="relative flex items-center justify-between w-full gap-1.5 px-1.5 text-sm">
          <div class="flex items-center shrink-0">
            ${this.getNetworkIcon()}
          </div>
          <span
            data-network="${this.network}"
            data-chars-left="${remaining}"
            class="font-medium tabular-nums text-center ${isOverLimit ? 'text-rose-600 dark:text-rose-400' :
        isApproachingLimit ? 'text-orange-600 dark:text-orange-400' :
          'text-gray-600 dark:text-gray-400'
      }">${remaining}</span>
        </div>
      </div>
    `
  }

  render() {
    return this.renderHTML()
  }

  destroy() {
    if (this.events) {
      this.events.unregister(this)
    }
    if (this.container) {
      this.container.innerHTML = ''
    }
  }
}
