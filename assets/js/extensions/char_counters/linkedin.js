import { BaseCharCounter } from './base'

export class LinkedInCharCounter extends BaseCharCounter {
  constructor(options) {
    super({ ...options, network: 'linkedin', limit: 3000 })
  }

  getCount(content) {
    const text = this.getText()
    return text.length
  }

  getText() {
    return this.getContent()?.text || ""
  }

  getContent() {
    return this.store.getContentForNetwork('linkedin')[0]
  }
}
