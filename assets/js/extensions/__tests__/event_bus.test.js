import { EventBus } from '../event_bus'

describe('EventBus', () => {
  let events

  beforeEach(() => {
    events = new EventBus()
  })

  describe('basic event handling', () => {
    it('should handle events with callbacks', () => {
      const callback = jest.fn()
      events.on('test', callback)
      events.emit('test', 'data')
      expect(callback).toHaveBeenCalledWith('data')
    })

    it('should handle events with object owners', () => {
      const obj = {}
      const callback = jest.fn()
      events.on('test', callback, obj)
      events.emit('test', 'data')
      expect(callback).toHaveBeenCalledWith('data')
    })

    it('should remove specific event handlers', () => {
      const callback1 = jest.fn()
      const callback2 = jest.fn()
      events.on('test', callback1)
      events.on('test', callback2)
      events.off('test', callback1)
      events.emit('test', 'data')
      expect(callback1).not.toHaveBeenCalled()
      expect(callback2).toHaveBeenCalledWith('data')
    })
  })

  describe('topic handling', () => {
    it('should handle events in topics', () => {
      const callback = jest.fn()
      events.topic('mytopic').on('test', callback)
      events.topic('mytopic').emit('test', 'data')
      expect(callback).toHaveBeenCalledWith('data')
    })

    it('should isolate events between topics', () => {
      const callback1 = jest.fn()
      const callback2 = jest.fn()
      events.topic('topic1').on('test', callback1)
      events.topic('topic2').on('test', callback2)
      events.topic('topic1').emit('test', 'data')
      expect(callback1).toHaveBeenCalledWith('data')
      expect(callback2).not.toHaveBeenCalled()
    })
  })

  describe('object ownership and unregistration', () => {
    it('should unregister all handlers for an object', () => {
      const obj = {}
      const callback1 = jest.fn()
      const callback2 = jest.fn()
      const callback3 = jest.fn()

      events.on('test1', callback1, obj)
      events.on('test2', callback2, obj)
      events.on('test3', callback3) // No owner

      events.unregister(obj)

      events.emit('test1', 'data')
      events.emit('test2', 'data')
      events.emit('test3', 'data')

      expect(callback1).not.toHaveBeenCalled()
      expect(callback2).not.toHaveBeenCalled()
      expect(callback3).toHaveBeenCalledWith('data')
    })

    it('should unregister handlers for an object in a specific topic', () => {
      const obj = {}
      const callback1 = jest.fn()
      const callback2 = jest.fn()

      events.topic('topic1').on('test', callback1, obj)
      events.topic('topic2').on('test', callback2, obj)

      events.unregister(obj, 'topic1')

      events.topic('topic1').emit('test', 'data')
      events.topic('topic2').emit('test', 'data')

      expect(callback1).not.toHaveBeenCalled()
      expect(callback2).toHaveBeenCalledWith('data')
    })

    it('should handle multiple handlers with the same callback but different owners', () => {
      const obj1 = {}
      const obj2 = {}
      const callback = jest.fn()

      events.on('test', callback, obj1)
      events.on('test', callback, obj2)

      events.unregister(obj1)
      events.emit('test', 'data')

      expect(callback).toHaveBeenCalledTimes(1)
    })
  })

  describe('error handling', () => {
    it('should handle errors in callbacks', () => {
      const errorCallback = () => { throw new Error('test error') }
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => { })

      events.on('test', errorCallback)
      events.emit('test', 'data')

      expect(consoleError).toHaveBeenCalled()
      consoleError.mockRestore()
    })

    it('should throw error for invalid topic names', () => {
      expect(() => events.topic(null)).toThrow('Topic name must be a string')
      expect(() => events.on('test', () => { }, null, { topic: null })).toThrow('Topic name must be a string')
    })
  })

  describe('cleanup', () => {
    it('should remove all listeners from a topic', () => {
      const callback1 = jest.fn()
      const callback2 = jest.fn()

      events.topic('topic1').on('test1', callback1)
      events.topic('topic1').on('test2', callback2)

      events.removeAllListeners('topic1')

      events.topic('topic1').emit('test1', 'data')
      events.topic('topic1').emit('test2', 'data')

      expect(callback1).not.toHaveBeenCalled()
      expect(callback2).not.toHaveBeenCalled()
    })

    it('should remove all listeners from all topics', () => {
      const callback1 = jest.fn()
      const callback2 = jest.fn()

      events.topic('topic1').on('test', callback1)
      events.topic('topic2').on('test', callback2)

      events.removeAllListeners()

      events.topic('topic1').emit('test', 'data')
      events.topic('topic2').emit('test', 'data')

      expect(callback1).not.toHaveBeenCalled()
      expect(callback2).not.toHaveBeenCalled()
    })
  })
})
