export class EventBus {
  constructor(options = {}) {
    // Map of topic => Map of event => Set of {callback, obj} pairs
    this.topics = new Map()
    // Initialize the global topic
    this.topics.set('global', new Map())

    // Debug mode configuration
    this.debug = options.debug || false
    this.debugPrefix = options.debugPrefix || '[EventBus]'
  }

  topic(name) {
    if (typeof name !== 'string') {
      throw new Error('Topic name must be a string')
    }

    if (!this.topics.has(name)) {
      this.topics.set(name, new Map())
      if (this.debug) {
        console.log(`${this.debugPrefix} Created new topic: ${name}`)
      }
    }

    return {
      name: name,
      emit: (event, data) => this.emit(event, data, name),
      on: (event, callback, obj) => this.on(event, callback, obj, { topic: name }),
      off: (event, callback, obj) => this.off(event, callback, obj, { topic: name }),
      unregister: (obj) => this.unregister(obj, name)
    }
  }

  on(event, callback, obj = null, options = { topic: 'global' }) {
    if (typeof options.topic !== 'string') {
      throw new Error('Topic name must be a string')
    }

    // Ensure topic exists
    if (!this.topics.has(options.topic)) {
      this.topics.set(options.topic, new Map())
      if (this.debug) {
        console.log(`${this.debugPrefix} Created new topic: ${options.topic}`)
      }
    }

    // Debug: Log information about the callback
    if (this.debug) {
      const fnName = callback.name || '<anonymous>'
      console.log(`${this.debugPrefix} Registering function '${fnName}' for event '${event}' in topic '${options.topic}'${obj ? ' with owner object' : ''}`)
    }

    const topicListeners = this.topics.get(options.topic)
    if (!topicListeners.has(event)) {
      topicListeners.set(event, new Set())
    }

    // Store callback with its owner object
    topicListeners.get(event).add({ callback, obj })

    return () => this.off(event, callback, obj, options)
  }

  off(event, callback, obj = null, options = { topic: 'global' }) {
    if (typeof options.topic !== 'string') {
      throw new Error('Topic name must be a string')
    }

    if (!this.topics.has(options.topic)) {
      if (this.debug) {
        console.warn(`${this.debugPrefix} Attempted to remove listener from non-existent topic: ${options.topic}`)
      }
      return
    }

    const topicListeners = this.topics.get(options.topic)
    const callbacks = topicListeners.get(event)

    if (callbacks) {
      // Find and remove the specific callback-object pair
      callbacks.forEach(handler => {
        if (handler.callback === callback && handler.obj === obj) {
          callbacks.delete(handler)
        }
      })

      if (callbacks.size === 0) {
        topicListeners.delete(event)
      }

      if (this.debug) {
        const fnName = callback.name || '<anonymous>'
        console.log(`${this.debugPrefix} Removed listener '${fnName}' for '${event}' in topic '${options.topic}'`)
      }
    }
  }

  emit(event, data, topic = 'global') {
    if (typeof topic !== 'string') {
      throw new Error('Topic name must be a string')
    }

    if (!this.topics.has(topic)) {
      if (this.debug) {
        console.warn(`${this.debugPrefix} Attempted to emit event on non-existent topic: ${topic}`)
      }
      return
    }

    const topicListeners = this.topics.get(topic)
    const callbacks = topicListeners.get(event)

    if (this.debug) {
      const callbackCount = callbacks?.size || 0
      console.group(`${this.debugPrefix} Emitting '${event}' in topic '${topic}' (${callbackCount} listeners)`)
      if (data !== undefined) {
        console.log('Data:', data)
      }
    }

    if (callbacks) {
      callbacks.forEach(handler => {
        try {
          if (this.debug) {
            const fnName = handler.callback.name || '<anonymous>'
            console.log(`${this.debugPrefix} Executing listener: ${fnName}`)
          }
          handler.callback(data)
        } catch (error) {
          console.error(`${this.debugPrefix} Error in listener for '${event}' in topic '${topic}':`, error)
        }
      })
    } else if (this.debug) {
      console.log(`${this.debugPrefix} No listeners found`)
    }

    if (this.debug) {
      console.groupEnd()
    }
  }

  unregister(obj, topic = null) {
    if (!obj) return

    if (this.debug) {
      console.log(`${this.debugPrefix} Unregistering all handlers for object${topic ? ` in topic '${topic}'` : ''}`)
    }

    // Iterate through all topics (or just the specified topic)
    const topics = topic ? [topic] : Array.from(this.topics.keys())

    topics.forEach(topicName => {
      const topicListeners = this.topics.get(topicName)
      if (!topicListeners) return

      // For each event in the topic
      topicListeners.forEach((callbacks, event) => {
        // Find and remove callbacks bound to this object
        const toRemove = Array.from(callbacks).filter(handler => handler.obj === obj)
        toRemove.forEach(handler => {
          callbacks.delete(handler)
        })

        // Clean up empty event listeners
        if (callbacks.size === 0) {
          topicListeners.delete(event)
        }
      })
    })
  }

  removeAllListeners(topic = null) {
    if (topic) {
      if (typeof topic !== 'string') {
        throw new Error('Topic name must be a string')
      }

      const topicListeners = this.topics.get(topic)
      if (topicListeners) {
        if (this.debug) {
          console.log(`${this.debugPrefix} Removing all listeners for topic '${topic}'`)
        }
        topicListeners.clear()
      }
    } else {
      if (this.debug) {
        console.log(`${this.debugPrefix} Removing all listeners from all topics`)
      }
      this.topics.clear()
      this.topics.set('global', new Map())
    }
  }
}
