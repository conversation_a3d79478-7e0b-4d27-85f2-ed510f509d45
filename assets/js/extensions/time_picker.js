export class TimePicker {
  constructor(options = {}) {
    this.selectedTime = options.selectedTime || new Date();
    this.minTime = options.minTime;
    this.onChange = options.onChange;
    this.element = null;

    this.render();
    this.setupEventListeners();
  }

  render() {
    this.element = document.createElement('div');
    this.element.className = 'flex gap-2 items-center';

    const hours = this.selectedTime.getHours();
    const minutes = this.selectedTime.getMinutes();
    const isPM = hours >= 12;
    const displayHours = isPM ? (hours === 12 ? 12 : hours - 12) : (hours === 0 ? 12 : hours);

    this.element.innerHTML = `
      <div class="flex gap-2 items-center">
        <input
          type="number"
          class="time-hours w-16 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          value="${displayHours}"
          step="1"
          onwheel="this.blur()"
        />
        <span class="text-gray-500">:</span>
        <input
          type="number"
          class="time-minutes w-16 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          value="${minutes.toString().padStart(2, '0')}"
          step="1"
          onwheel="this.blur()"
        />
        <select class="time-period w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
          <option value="AM" ${!isPM ? 'selected' : ''}>AM</option>
          <option value="PM" ${isPM ? 'selected' : ''}>PM</option>
        </select>
      </div>
    `;
  }

  setupEventListeners() {
    const hoursInput = this.element.querySelector('.time-hours');
    const minutesInput = this.element.querySelector('.time-minutes');
    const periodSelect = this.element.querySelector('.time-period');

    // Add click handler to prevent event propagation
    this.element.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    const updateTime = () => {
      let hours = parseInt(hoursInput.value, 10) || 12;
      let minutes = parseInt(minutesInput.value, 10) || 0;
      const isPM = periodSelect.value === 'PM';

      // Handle wrapping for hours (1-12)
      if (hours > 12) hours = 1;
      if (hours < 1) hours = 12;

      // Handle wrapping for minutes (0-59)
      if (minutes > 59) minutes = 0;
      if (minutes < 0) minutes = 59;

      // Update input values to show valid numbers
      hoursInput.value = hours;
      minutesInput.value = minutes.toString().padStart(2, '0');

      // Convert to 24-hour format
      if (isPM && hours !== 12) hours += 12;
      if (!isPM && hours === 12) hours = 0;

      const newDate = new Date(this.selectedTime);
      newDate.setHours(hours);
      newDate.setMinutes(minutes);

      this.selectedTime = newDate;
      this.onChange(this.selectedTime);
    };

    // Handle spinner clicks (up/down arrows in number input)
    const handleSpinnerClick = (input, isHours = false) => {
      const oldValue = parseInt(input.value, 10) || (isHours ? 12 : 0);

      // Use a MutationObserver to detect value changes from spinner clicks
      const observer = new MutationObserver(() => {
        const newValue = parseInt(input.value, 10);
        if (newValue !== oldValue) {
          updateTime();
        }
      });

      observer.observe(input, { attributes: true, attributeFilter: ['value'] });

      // Cleanup observer after a short delay
      setTimeout(() => observer.disconnect(), 100);
    };

    hoursInput.addEventListener('mouseup', () => handleSpinnerClick(hoursInput, true));
    minutesInput.addEventListener('mouseup', () => handleSpinnerClick(minutesInput, false));

    // Add input event listeners
    hoursInput.addEventListener('input', updateTime);
    minutesInput.addEventListener('input', updateTime);
    periodSelect.addEventListener('change', updateTime);

    // Add keyboard event listeners for up/down arrows
    hoursInput.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        let value = parseInt(hoursInput.value, 10) || 12;
        value += e.key === 'ArrowUp' ? 1 : -1;

        // Wrap hours
        if (value > 12) value = 1;
        if (value < 1) value = 12;

        hoursInput.value = value;
        updateTime();
      }
    });

    minutesInput.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        let value = parseInt(minutesInput.value, 10) || 0;
        value += e.key === 'ArrowUp' ? 1 : -1;

        // Wrap minutes
        if (value > 59) value = 0;
        if (value < 0) value = 59;

        minutesInput.value = value.toString().padStart(2, '0');
        updateTime();
      }
    });

    // Add blur event listeners to format numbers
    hoursInput.addEventListener('blur', () => {
      let value = parseInt(hoursInput.value, 10) || 12;
      if (value > 12) value = 1;
      if (value < 1) value = 12;
      hoursInput.value = value;
      updateTime();
    });

    minutesInput.addEventListener('blur', () => {
      let value = parseInt(minutesInput.value, 10) || 0;
      if (value > 59) value = 0;
      if (value < 0) value = 59;
      minutesInput.value = value.toString().padStart(2, '0');
      updateTime();
    });
  }

  setTime(date) {
    this.selectedTime = new Date(date);

    // Get existing inputs
    const hoursInput = this.element.querySelector('.time-hours');
    const minutesInput = this.element.querySelector('.time-minutes');
    const periodSelect = this.element.querySelector('.time-period');

    // Calculate new values
    const hours = this.selectedTime.getHours();
    const minutes = this.selectedTime.getMinutes();
    const isPM = hours >= 12;
    const displayHours = isPM ? (hours === 12 ? 12 : hours - 12) : (hours === 0 ? 12 : hours);

    // Update input values
    hoursInput.value = displayHours;
    minutesInput.value = minutes.toString().padStart(2, '0');
    periodSelect.value = isPM ? 'PM' : 'AM';
  }

  getElement() {
    return this.element;
  }

  destroy() {
    this.element.remove();
  }
}
