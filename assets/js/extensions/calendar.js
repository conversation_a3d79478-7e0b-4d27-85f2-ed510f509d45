export class Calendar {
  constructor(options = {}) {
    const today = new Date();
    this.selectedDate = options.selectedDate ? new Date(options.selectedDate) : new Date(today);
    this.minDate = options.minDate ? new Date(options.minDate) : new Date(today);
    this.onChange = options.onChange || (() => { });
    this.element = null;

    this.selectedDate.setHours(0, 0, 0, 0);
    this.minDate.setHours(0, 0, 0, 0);

    if (!options.selectedDate) {
      this.onChange(this.selectedDate);
    }

    this.render();
    this.setupEventListeners();
  }

  render() {
    this.element = document.createElement('div');
    this.element.className = 'calendar-widget bg-white';
    this.renderCalendar();
  }

  renderCalendar() {
    const year = this.selectedDate.getFullYear();
    const month = this.selectedDate.getMonth();
    const today = new Date();

    // Get first day of month and total days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDay = firstDay.getDay(); // 0 = Sunday

    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'];

    this.element.innerHTML = `
      <div class="flex items-center justify-between">
        <button type="button" data-prev-month class="prev-month p-2 m-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <div class="text-base font-medium text-gray-900">
          ${monthNames[month]} ${year}
        </div>
        <button type="button" data-next-month class="next-month p-2 mx-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      <div class="grid grid-cols-7 gap-1 text-center">
        ${['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']
        .map(day => `<div class="text-xs font-medium text-gray-500 py-2">${day}</div>`)
        .join('')}
      </div>

      <div class="grid grid-cols-7 gap-1 p-2">
        ${this.generateDays(year, month, daysInMonth, startingDay, today)}
      </div>
    `;
  }

  generateDays(year, month, daysInMonth, startingDay, today) {
    let days = '';
    let dayCount = 1;
    const rows = Math.ceil((daysInMonth + startingDay) / 7);
    const totalCells = rows * 7;

    // Get next month's details
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextMonthYear = month === 11 ? year + 1 : year;
    let nextMonthDay = 1;

    for (let i = 0; i < totalCells; i++) {
      if (i < startingDay) {
        // Previous month's days (we'll skip these for simplicity)
        days += '<div class="w-full h-9"></div>';
      } else if (dayCount <= daysInMonth) {
        // Current month's days
        const date = new Date(year, month, dayCount);
        const isSelected = this.isSameDay(date, this.selectedDate);
        const isToday = this.isSameDay(date, today);
        const isPast = date < this.minDate;

        days += `
          <button
            type="button"
            class="calendar-day ${this.getDayClasses(isSelected, isToday, isPast)}"
            data-date="${date.toISOString()}"
            ${isPast ? 'disabled' : ''}
          >
            ${dayCount}
          </button>
        `;
        dayCount++;
      } else {
        // Next month's days
        const date = new Date(nextMonthYear, nextMonth, nextMonthDay);
        days += `
          <button
            type="button"
            class="calendar-day w-full h-9 rounded-md text-sm flex items-center justify-center transition-colors text-gray-300"
            disabled
          >
            ${nextMonthDay}
          </button>
        `;
        nextMonthDay++;
      }
    }

    return days;
  }

  getDayClasses(isSelected, isToday, isPast) {
    const baseClasses = 'w-full h-9 rounded-md text-sm flex items-center justify-center transition-colors';

    if (isPast) {
      return `${baseClasses} text-gray-300 cursor-not-allowed`;
    }
    if (isSelected) {
      return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 font-medium`;
    }
    if (isToday) {
      return `${baseClasses} border-2 border-blue-600 text-blue-600 hover:bg-gray-100 font-medium`;
    }
    return `${baseClasses} text-gray-700 hover:bg-gray-100`;
  }

  isSameDay(date1, date2) {
    return date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear();
  }

  setupEventListeners() {
    this.element.addEventListener('click', (e) => {
      // Prevent event from bubbling up to document
      e.stopPropagation();

      const dayButton = e.target.closest('.calendar-day');
      if (dayButton && !dayButton.disabled) {
        this.selectedDate = new Date(dayButton.dataset.date);
        this.renderCalendar();
        this.onChange(this.selectedDate);
      }

      if (e.target.closest('.prev-month')) {
        e.preventDefault();
        this.selectedDate.setMonth(this.selectedDate.getMonth() - 1);
        this.renderCalendar();
      }

      if (e.target.closest('.next-month')) {
        e.preventDefault();
        this.selectedDate.setMonth(this.selectedDate.getMonth() + 1);
        this.renderCalendar();
      }
    });
  }

  setDate(date) {
    this.selectedDate = new Date(date);
    this.renderCalendar();
  }

  getElement() {
    return this.element;
  }

  destroy() {
    this.element.remove();
  }
}
