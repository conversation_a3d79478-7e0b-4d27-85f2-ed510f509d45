import { Calendar } from './calendar';
import { TimePicker } from './time_picker';
import { NETWORKS } from './networks';

class NetworkScheduler {
  constructor(container, options = {}) {
    this.container = container;
    this.network = options.network;
    this.selectedTime = options.selectedTime;
    this.minTime = options.minTime || new Date();
    this.onChange = options.onChange;
    this.section = this.container.closest(`.network-schedule, .network-schedule-${this.network}`);
    this.content = this.section.querySelector('.schedule-content');
    this.arrow = this.section.querySelector('svg');

    this.calendar = null;
    this.timePicker = null;

    this.initialize();

    // Add transition classes to content
    this.content.classList.add('transition-all', 'duration-200', 'ease-in-out', 'overflow-hidden');

    // Initialize height as 0
    this.content.style.height = '0px';
    this.content.style.opacity = '0';
  }

  initialize() {
    this.container.innerHTML = `
      <div class="calendar-container mb-4"></div>
      <div class="time-picker-container"></div>
    `;

    const calendarContainer = this.container.querySelector('.calendar-container');
    const timePickerContainer = this.container.querySelector('.time-picker-container');

    this.calendar = new Calendar({
      selectedDate: this.selectedTime,
      minDate: this.minTime,
      onChange: (date) => {
        const newDate = new Date(date);
        newDate.setHours(this.selectedTime.getHours());
        newDate.setMinutes(this.selectedTime.getMinutes());
        this.selectedTime = newDate;
        this.onChange(this.network, this.selectedTime);
      }
    });

    this.timePicker = new TimePicker({
      selectedTime: this.selectedTime,
      minTime: this.minTime,
      onChange: (time) => {
        const newDate = new Date(this.selectedTime);
        newDate.setHours(time.getHours());
        newDate.setMinutes(time.getMinutes());
        this.selectedTime = newDate;
        this.onChange(this.network, this.selectedTime);
      }
    });

    calendarContainer.appendChild(this.calendar.getElement());
    timePickerContainer.appendChild(this.timePicker.getElement());
  }

  setTime(time) {
    this.selectedTime = new Date(time);
    this.calendar.setDate(this.selectedTime);
    this.timePicker.setTime(this.selectedTime);
  }

  open() {
    this.content.classList.remove('hidden');
    const height = this.content.scrollHeight;

    requestAnimationFrame(() => {
      this.content.style.height = `${height}px`;
      this.content.style.opacity = '1';
    });

    this.opened = true;
  }

  close() {
    const height = this.content.scrollHeight;
    this.content.style.height = `${height}px`;
    this.content.offsetHeight;

    requestAnimationFrame(() => {
      this.content.style.height = '0px';
      this.content.style.opacity = '0';
    });

    this.content.addEventListener('transitionend', () => {
      if (!this.opened) {
        this.content.classList.add('hidden');
      }
    }, { once: true });

    this.opened = false;
  }

  toggle() {
    this.opened ? this.close() : this.open();
  }

  destroy() {
    this.calendar?.destroy();
    this.timePicker?.destroy();
    this.container.remove();
  }

  // Add method to get current selected time
  getCurrentTime() {
    return this.selectedTime;
  }
}

export class Scheduling {
  constructor({ events, post }) {
    this.events = events;
    this.post = post;
    this.currentSchedules = new Map();
    this.updatedSchedules = new Map();
    this.isOpen = false;
    this.container = null;
    this.networkSchedulers = new Map();

    this.initializeFromPostData(this.post.schedules);

    // Add listener for network changes
    this.events.on('network:enabled', ({ network }) => {
      if (!this.networkSchedulers.has(network)) {
        this.initializeNetworkScheduler(network);
      }
    });

    this.container = document.createElement('div');
    this.container.id = 'scheduling-panel';
    this.container.className = `
      fixed inset-0 bg-white shadow-lg transform transition-transform duration-300 z-50
      md:inset-y-0 md:left-auto md:w-[400px] translate-x-full
    `;

    this.createContainer();
    this.setupEventListeners();
  }

  createContainer() {
    this.container.innerHTML = `
      <div class="h-full flex flex-col">
        <div class="flex items-center justify-between px-6 py-4 border-b">
          <h2 class="text-lg font-medium">Schedule Post</h2>
          <button type="button" class="close-scheduling p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <div class="network-schedules space-y-2" data-testid="network-schedulers">
              <!-- All Networks Section -->
              ${this.createNetworkElement('canonical').outerHTML}

              <!-- Individual Network Sections -->
              <div class="network-schedule-list space-y-2">
                ${Array.from(this.post.socialNetworks)
        .map(network => this.createNetworkElement(network).outerHTML)
        .join('')}
              </div>
            </div>

            <div class="text-sm text-gray-500 bg-gray-50 p-2 rounded-md border border-gray-200">
              ${Intl.DateTimeFormat().resolvedOptions().timeZone.replace(/_/g, ' ')}
            </div>
          </div>
        </div>

        <div class="border-t px-6 py-4">
          <button type="button" class="save-schedule w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" data-button="save-schedules">
            Save
          </button>
        </div>
      </div>
    `;

    // Set initial state with transition disabled
    this.container.classList.add('translate-x-full');
    this.container.style.transition = 'none';
    document.body.appendChild(this.container);

    // Force reflow and restore transition
    this.container.offsetHeight;
    this.container.style.removeProperty('transition');
  }

  getNetworkIcon(network) {
    if (network === 'canonical') {
      return `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>`;
    }

    const networkConfig = NETWORKS.find(n => n.id === network);
    if (!networkConfig) return '';

    return `<svg class="w-4 h-4" fill="currentColor" viewBox="${networkConfig.viewBox}">
      <path d="${networkConfig.icon}" />
    </svg>`;
  }

  initializeNetworkScheduler(network) {
    if (this.networkSchedulers.has(network)) return this.networkSchedulers.get(network);

    // Get or create the container
    let container = this.container.querySelector(`.schedule-content[data-network="${network}"]`);

    if (!container) {
      // Create the network section if it doesn't exist
      const networkList = this.container.querySelector('.network-schedule-list');
      if (!networkList) return;

      const networkElement = this.createNetworkElement(network);
      networkList.appendChild(networkElement);

      // Get the newly created container
      container = networkElement.querySelector('.schedule-content');
    }

    const displayTime = this.getDisplayTime(network);

    const scheduler = new NetworkScheduler(container, {
      network,
      selectedTime: displayTime,
      minTime: new Date(),
      onChange: (network, date) => {
        if (network === 'canonical') {
          Array.from(this.post.socialNetworks).forEach(net => {
            this.initializeNetworkScheduler(net).setTime(date);
            this.updatedSchedules.set(net, date);
          });
        } else {
          this.updatedSchedules.set(network, date);
        }
      }
    });

    this.networkSchedulers.set(network, scheduler);

    return scheduler;
  }

  removeNetworkScheduler(network) {
    if (!this.networkSchedulers.has(network)) return

    this.networkSchedulers.get(network).destroy();
    this.networkSchedulers.delete(network);
    this.container.querySelector(`.network-schedule[data-network="${network}"]`).remove();
  }

  closeAllNetworkSchedulers() {
    this.networkSchedulers.forEach(scheduler => scheduler.close());
  }

  open(network = 'canonical') {
    // Prevent multiple opens
    if (this.isOpen) return;

    this.isOpen = true;

    // Make sure container exists and is in DOM
    if (!this.container || !document.body.contains(this.container)) {
      this.removeExistingPanels();
      this.createContainer();
      this.setupEventListeners();
    }

    // Force a reflow and add transition class
    this.container.classList.add('transition-transform');
    this.container.offsetHeight; // Force reflow

    // Remove the transform in the next frame
    requestAnimationFrame(() => {
      this.container.classList.remove('translate-x-full');
    });

    // Close all schedulers first
    this.closeAllNetworkSchedulers();

    // Then open the requested one
    const scheduler = this.initializeNetworkScheduler(network);
    if (scheduler) {
      scheduler.open();
    }
  }

  getDisplayTime(network) {
    if (this.updatedSchedules.has(network)) {
      return this.updatedSchedules.get(network);
    }

    if (this.currentSchedules.has(network)) {
      return this.currentSchedules.get(network);
    }

    // Default to current time + 5 minutes
    const now = new Date();
    now.setMinutes(now.getMinutes() + 5);

    return now;
  }

  getCanonicalScheduler() {
    if (!this.networkSchedulers.has('canonical')) {
      this.initializeNetworkScheduler('canonical');
    }

    return this.networkSchedulers.get('canonical');
  }

  getScheduler(network) {
    if (!this.networkSchedulers.has(network)) {
      this.initializeNetworkScheduler(network);
    }

    return this.networkSchedulers.get(network);
  }

  setupEventListeners() {
    // Close button
    this.container.querySelector('.close-scheduling').addEventListener('click', () => {
      this.close();
    });

    // Save button
    this.container.querySelector('.save-schedule').addEventListener('click', () => {
      let schedules = [];
      let allSchedule = this.updatedSchedules['canonical']
      let hasOtherSchedules = Object.values(this.updatedSchedules).some(schedule => schedule !== allSchedule)

      // Get canonical time - we'll use this as fallback
      const canonicalTime = this.getCanonicalScheduler().getCurrentTime()

      if (allSchedule && !hasOtherSchedules) {
        // If no time was explicitly selected, use the time shown in the UI
        if (!allSchedule) {
          allSchedule = canonicalTime
        }

        schedules = Array.from(this.post.socialNetworks).map(network => ({
          network,
          scheduled_at: allSchedule
        }))
      } else {
        schedules = Array.from(this.post.socialNetworks).map(network => {
          // First try to get explicitly selected time
          let scheduledTime = this.updatedSchedules.get(network) || this.currentSchedules.get(network)

          // If no time was selected, try network scheduler or fall back to canonical time
          if (!scheduledTime) {
            const networkScheduler = this.getScheduler(network)
            scheduledTime = networkScheduler ? networkScheduler.getCurrentTime() : canonicalTime
          }

          return { network, scheduled_at: scheduledTime }
        })
      }

      // Update currentSchedules to match what we're saving
      schedules.forEach(schedule => {
        this.getScheduler(schedule.network).setTime(schedule.scheduled_at)
        this.currentSchedules.set(schedule.network, schedule.scheduled_at)
      })

      this.events.emit('schedules:set', { schedules })

      this.close()
    })

    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });

    // Close on click outside
    document.addEventListener('click', (e) => {
      if (!this.isOpen || !this.container) return;

      // Check if click is on any scheduling-related element
      const isSchedulingClick = e.target.closest('#scheduling-panel') ||
        e.target.closest('[data-open-scheduling]') ||
        e.target.closest('.calendar-widget') ||
        e.target.closest('.time-picker-container');

      if (!isSchedulingClick) {
        this.close();
      }
    });

    // Update accordion functionality
    this.container.addEventListener('click', (e) => {
      const button = e.target.closest('button[data-network]');
      if (button) {
        e.preventDefault();
        e.stopPropagation();
        this.toggleNetworkScheduler(button.dataset.network);
      }
    });
  }

  toggleNetworkScheduler(network) {
    const scheduler = this.initializeNetworkScheduler(network);
    if (!scheduler) return;

    const section = this.container.querySelector(`.network-schedule[data-network="${network}"]`);
    const chevron = section.querySelector('[data-chevron]');

    if (scheduler.opened) {
      scheduler.close();
      chevron.style.transform = 'rotate(0deg)';
    } else {
      // Close all other schedulers
      this.networkSchedulers.forEach((otherScheduler, otherNetwork) => {
        if (otherNetwork !== network) {
          otherScheduler.close();
          const otherChevron = this.container.querySelector(`.network-schedule[data-network="${otherNetwork}"] [data-chevron]`);
          if (otherChevron) {
            otherChevron.style.transform = 'rotate(0deg)';
          }
        }
      });

      // Open this scheduler
      scheduler.open();
      chevron.style.transform = 'rotate(90deg)';
    }
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.container.classList.add('translate-x-full');

    // Wait for transition to complete before cleanup
    this.container.addEventListener('transitionend', () => {
      if (!this.isOpen) {
        this.networkSchedulers.forEach(scheduler => scheduler.close());
        this.updatedSchedules.clear();
      }
    }, { once: true });
  }

  destroy() {
    this.networkSchedulers.forEach(scheduler => scheduler.destroy());
    this.networkSchedulers.clear();
    this.container.remove();
  }

  initializeFromPostData(schedules) {
    this.currentSchedules = new Map(
      (schedules || []).map(schedule => [schedule.network, schedule.scheduledAt])
    );
    this.updatedSchedules = new Map();
  }

  getScheduledTime() {
    // If user has made changes, use that time
    if (this.updatedTime) {
      return this.updatedTime;
    }

    // If we have existing schedules, use the earliest one
    if (this.currentSchedules.size > 0) {
      const sortedSchedules = [...this.currentSchedules.values()].sort((a, b) =>
        new Date(a) - new Date(b)
      );
      return new Date(sortedSchedules[0]);
    }

    return null;
  }

  createNetworkElement(network) {
    const element = document.createElement('div');
    element.className = 'network-schedule bg-white border rounded-lg shadow-sm';
    element.dataset.network = network;

    // Find network config for styling
    const networkConfig = NETWORKS.find(n => n.id === network);
    const iconAndLabel = network === 'canonical'
      ? `<span class="text-sm font-medium">All Networks</span>`
      : `<div class="flex items-center gap-2">
           ${this.getNetworkIcon(network)}
           <span class="text-sm font-medium">${networkConfig?.name || network}</span>
         </div>`;

    element.innerHTML = `
      <button type="button"
          class="w-full flex items-center justify-between p-4 text-left group"
          data-network="${network}"
          data-testid="schedule-${network}-button">
          ${iconAndLabel}
          <svg class="w-5 h-5 text-gray-400 group-hover:text-gray-500 transition-transform duration-200"
               fill="none"
               stroke="currentColor"
               viewBox="0 0 24 24"
               data-chevron
               style="transform: rotate(0deg);">
              <path stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7" />
          </svg>
      </button>
      <div class="schedule-content hidden p-4 border-t" data-network="${network}">
      </div>
    `;

    return element;
  }

  getNetworkLabel(network) {
    const networkConfig = NETWORKS.find(n => n.id === network);
    return networkConfig ? networkConfig.name : network;
  }
}
