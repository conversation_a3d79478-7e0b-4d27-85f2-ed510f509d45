const JsonEditor = {
  mounted() {
    const container = this.el;
    const options = {
      mode: 'view',
      modes: ['view'],
      enableSort: false,
      enableTransform: false,
      navigationBar: false,
      statusBar: false,
      mainMenuBar: false,
      onError: (err) => {
        console.error('JSON Editor error:', err);
      }
    };

    try {
      const json = JSON.parse(this.el.dataset.json);
      const editor = new JSONEditor(container, options, json);

      // Store the editor instance for cleanup
      this.editor = editor;
    } catch (err) {
      console.error('Failed to initialize JSON editor:', err);
    }
  },

  destroyed() {
    if (this.editor) {
      this.editor.destroy();
    }
  }
};

export default {
  JsonEditor
};
