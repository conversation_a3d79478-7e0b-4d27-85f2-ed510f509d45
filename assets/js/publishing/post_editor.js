import { Post, PostContent } from '../models/post'

export class PostEditor {
  constructor(options = {}) {
    this.element = document.getElementById('tiptap-editor-container')
    this.events = options.events
    this.store = options.store
    this.attachments = options.attachments
    this.network = options.network
    this.selectedNetworks = options.selectedNetworks

    this.contentEditors = []

    this.wrapper = this.createNetworkWrapper()
    this.element.appendChild(this.wrapper)

    this.initializeContentEditors()

    if (this.handlers === undefined) {
      this.handlers = {}
    }
  }

  setup() {
    this.setupEventListeners()
  }

  destroy() {
    this.events.unregister(this)
    this.contentEditors.forEach(editor => editor.destroy())
    this.contentEditors = []
    this.store.removeContent(this.network)
    this.wrapper.remove()
  }

  setupEventListeners() {
    Object.entries(this.handlers).forEach(([event, handler]) => {
      if (Array.isArray(handler)) {
        this.events.topic(handler[0]).on(event, handler[1].bind(this))
      } else {
        this.events.on(event, handler.bind(this))
      }
    })
  }

  createNetworkWrapper() {
    const wrapper = document.createElement('div')

    wrapper.className = 'divide-y divide-gray-600/60'
    wrapper.dataset.network = this.network

    return wrapper
  }

  initializeContentEditors() {
    let content = this.store.getContentForNetwork(this.network)

    if (content.length === 0) {
      if (this.network === 'canonical') {
        content = this.store.addContent(new PostContent({ network: this.network }))
        return this.addContentEditor(content)
      } else {
        content = this.store.getContentForNetwork('canonical')
          .map(c => this.store.addContent(PostContent.fromCanonical(this.network, c), c.order))
      }
    }

    content.forEach(c => this.addContentEditor(c))

    return this
  }

  addContentEditor(content) {
    throw new Error('addContentEditor must be implemented by subclass')
  }

  show() {
    this.wrapper.style.display = 'block'
    this.wrapper.dataset.visible = true
    return this
  }

  hide() {
    this.wrapper.style.display = 'none'
    this.wrapper.dataset.visible = false
    return this
  }

  size() {
    return this.contentEntries().length
  }

  contentEntries() {
    return this.store.getContentForNetwork(this.network)
  }

  hasContent() {
    return this.contentEditors.some(block => {
      const text = block.getText()?.trim() || ""
      const hasAttachments = block.getAttachments().length > 0
      return text.length > 0 || hasAttachments
    })
  }

  hasAttachments() {
    return this.contentEditors.some(block => block.getAttachments().length > 0)
  }

  updatePostData(postData) {
    const post = new Post(postData)

    this.contentEditors.forEach((block, index) => {
      const savedContent = post.getContentForNetwork(this.network, index)
      if (savedContent) {
        block.id = savedContent.id
        if (savedContent.attachments) {
          block.updateAttachments(savedContent.attachments)
        }
      }
    })
  }
}
