import { Mention } from '@tiptap/extension-mention'
import { SocialProfileEditor } from './social_profile_editor'
import { SocialProfileSuggestion } from './social_profile_suggestion'

export const SocialProfile = Mention.extend({
  name: 'socialProfile',

  addNodeView() {
    return ({ editor, node, getPos, HTMLAttributes, decorations, extension }) => {
      const mention = document.createElement('socialProfile')
      const options = extension.options
      const callback = options.command.bind(this)

      mention.setAttribute('data-id', node.attrs.id)
      mention.setAttribute('data-pos', getPos())
      mention.setAttribute('data-label', node.attrs.label)
      mention.setAttribute('data-network', options.HTMLAttributes['data-network'])

      const span = document.createElement('span')
      span.className = options.HTMLAttributes.class
      span.textContent = `@${node.attrs.label}`

      const editButton = document.createElement('button')
      editButton.className = 'edit-button'

      editButton.appendChild(span)
      mention.appendChild(editButton)

      editor.storage.socialProfile.set(node.attrs.id, node.attrs)
      editButton.addEventListener('click', (event) => options.onEdit({ event, editor, callback }))

      return {
        dom: mention
      }
    }
  },

  addStorage() {
    return new Map()
  },

  addOptions() {
    return {
      ...this.parent?.(),

      suggestion: SocialProfileSuggestion,

      onEdit: async ({ event, editor, callback }) => {
        const socialProfiles = JCP.getSocialProfiles()
        const spEditor = new SocialProfileEditor({ socialProfiles })

        const mention = event.target.closest('[data-id]')
        const command = (sp) => callback({ socialProfile: sp, editor: editor })

        const existingProfile = socialProfiles.find(p => p.id === parseInt(mention.dataset.id))
        const props = { isEditing: true, command }

        if (existingProfile) {
          spEditor
            .renderResults(props, existingProfile)
            .showPopup(() => mention.getBoundingClientRect())
        }
      },
      command: ({ socialProfile, editor }) => {
        const { view } = editor

        const el = document.querySelector(`socialprofile[data-id="${socialProfile.id}"]`)
        const pos = parseInt(el.dataset.pos)
        const network = el.dataset.network

        const networkNode = socialProfile.networks.find(n => n.id === network)

        const label = networkNode ? networkNode.handle : socialProfile.name

        view.dispatch(view.state.tr.setNodeMarkup(pos, undefined, {
          id: socialProfile.id,
          label: label
        }))

        editor.emit('social_profile:updated', socialProfile)
      }
    }
  },

  onUpdate({ editor }) {
    const doc = editor.state.doc
    const storedProfiles = editor.storage.socialProfile
    const currentProfileIds = new Set()

    // Find all social profile mentions in the current document
    doc.descendants((node, pos) => {
      if (node.type.name === 'socialProfile') {
        currentProfileIds.add(node.attrs.id)
      }
    })

    // Find profiles that were removed
    const removedProfiles = []
    storedProfiles.forEach((profile, id) => {
      if (!currentProfileIds.has(id)) {
        removedProfiles.push(profile)
        storedProfiles.delete(id)
      }
    })

    // Emit events for removed profiles
    removedProfiles.forEach(profile => {
      editor.emit('social_profile:removed', profile)
    })
  },

  renderText({ options, node }) {
    return `@${node.attrs.label}`
  }
})
