import tippy from 'tippy.js'
import { SocialProfileForm } from './social_profile_form'
import { SocialProfileList } from './social_profile_list'
import JCP from '../../jcp'

export class SocialProfileEditor {
  constructor({ socialProfiles }) {
    this.component = document.createElement('div')
    this.component.className = 'mention-suggestions'
    this.container = document.createElement('div')
    this.container.className = 'bg-white rounded-lg shadow-lg border border-blue-700 overflow-hidden min-w-[300px]'
    this.component.appendChild(this.container)

    this.storedProfiles = socialProfiles

    this.form = new SocialProfileForm(null, (data) => {
      if (this.currentProps) {
        const profile = JCP.addSocialProfile(data)
        this.currentProps.command(profile)
        this.destroy()
      }
    })

    this.list = new SocialProfileList([], {
      onSelect: (profile) => {
        if (this.currentProps) {
          this.currentProps.command(profile)
          this.destroy()
        }
      },
      onEdit: (profile) => {
        if (this.currentProps) {
          this.renderResults(this.currentProps, profile)
        }
      },
      onShowSearch: () => {
        if (this.currentProps) {
          this.renderResults({ ...this.currentProps, isSearchMode: true })
        }
      },
      createNewButtonText: 'Create new profile'
    })

    this.form.component.addEventListener('dismiss', () => this.destroy())
  }

  setPopup(clientRect) {
    this.popup = tippy('body', {
      content: this.component,
      getReferenceClientRect: clientRect,
      appendTo: () => document.body,
      showOnCreate: false,
      interactive: true,
      trigger: 'manual',
      placement: 'bottom-start',
    })[0]

    return this
  }

  destroy() {
    if (this.popup) {
      this.popup.destroy()
    }

    this.container.innerHTML = ''
    this.popup = null
    this.currentProps = null
  }

  isHidden() {
    return (this.popup && this.popup.state.isDestroyed) || !this.popup
  }

  isVisible() {
    return !this.isHidden()
  }

  showPopup(clientRect) {
    if (!this.popup) {
      this.setPopup(clientRect)
    } else {
      this.align(clientRect)
    }

    this.popup.show()
    return this
  }

  align(clientRect) {
    this.popup.setProps({ getReferenceClientRect: clientRect })
  }

  renderResults(props, existingMention = null) {
    if (!this.component) { return this }

    this.currentProps = props
    this.container.innerHTML = ''

    if (existingMention) {
      this.form.updateProfile(existingMention, {
        isSearchMode: true,
        initialQuery: ''
      })
      this.container.appendChild(this.form.render())
    } else if (props.isSearchMode) {
      this.form.updateProfile(null, {
        isSearchMode: true,
        initialQuery: props.query || ''
      })
      this.container.appendChild(this.form.render())
    } else {
      const matchingProfiles = props.query ?
        this.storedProfiles.filter(profile => this.matchesQuery(profile, props.query)) :
        this.storedProfiles

      if (props.query && matchingProfiles.length === 0) {
        this.form.updateProfile(null, {
          isSearchMode: true,
          initialQuery: props.query
        })
        this.container.appendChild(this.form.render())
      } else {
        this.list.updateProfiles(matchingProfiles)
        this.container.appendChild(this.list.render())
      }
    }

    return this
  }

  matchesQuery(profile, query) {
    query = query.toLowerCase()
    return profile.name.toLowerCase().includes(query) ||
      (profile.bskyHandle && profile.bskyHandle.toLowerCase().includes(query)) ||
      (profile.xHandle && profile.xHandle.toLowerCase().includes(query)) ||
      (profile.mastodonHandle && profile.mastodonHandle.toLowerCase().includes(query))
  }
}
