import { icons } from './icons'

const API_ENDPOINTS = {
  PROFILES: '/api/social-profiles'
}

export class SocialProfileList {
  constructor(profiles = [], { onSelect, onEdit, onShowSearch, createNewButtonText = 'Create new profile' } = {}) {
    this.profiles = profiles
    this.onSelect = onSelect
    this.onEdit = onEdit
    this.onShowSearch = onShowSearch
    this.createNewButtonText = createNewButtonText
    this.component = document.createElement('div')

    // Set up event delegation
    this.component.addEventListener('click', this.handleClick.bind(this))
  }

  updateProfiles(profiles = []) {
    this.profiles = profiles
  }

  handleClick(e) {
    // Handle profile selection
    const profileButton = e.target.closest('[data-stored-profile-id]')
    if (profileButton && !e.target.closest('[data-edit-profile-id], [data-delete-profile-id]')) {
      const profileId = parseInt(profileButton.dataset.storedProfileId)
      const profile = this.profiles.find(p => p.id === profileId)
      if (profile && this.onSelect) {
        this.onSelect(profile)
      }
      return
    }

    // Handle profile edit
    const editButton = e.target.closest('[data-edit-profile-id]')
    if (editButton) {
      e.stopPropagation()
      const profileId = parseInt(editButton.dataset.editProfileId)
      const profile = this.profiles.find(p => p.id === profileId)
      if (profile && this.onEdit) {
        this.onEdit(profile)
      }
      return
    }

    // Handle profile deletion
    const deleteButton = e.target.closest('[data-delete-profile-id]')
    if (deleteButton) {
      e.stopPropagation()
      const profileId = parseInt(deleteButton.dataset.deleteProfileId)
      if (confirm('Are you sure you want to delete this profile?')) {
        this.deleteProfile(profileId)
      }
      return
    }

    // Handle show search
    const searchButton = e.target.closest('[data-action="show-search"]')
    if (searchButton && this.onShowSearch) {
      this.onShowSearch()
      return
    }
  }

  async deleteProfile(profileId) {
    try {
      const response = await fetch(`${API_ENDPOINTS.PROFILES}/${profileId}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        this.profiles = this.profiles.filter(p => p.id !== profileId)
        this.render()
      }
    } catch (error) {
      console.error('Error deleting profile:', error)
    }
  }

  render() {
    this.component.innerHTML = `
      <div class="social-profiles-list">
        <div class="p-2 flex items-center justify-between bg-blue-700">
          <h3 class="font-medium text-white">Found ${this.profiles.length} matching profiles</h3>
          <button
            type="button"
            class="text-gray-200 hover:text-gray-100 p-1 rounded"
            data-dismiss
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        ${this.profiles.length > 0 ? `
          <div class="p-2">
            ${this.profiles.map(profile => this.renderProfileItem(profile)).join('')}
          </div>
        ` : ''}
        <div class="p-2">
          <button
            type="button"
            class="w-full px-2 py-1.5 text-sm bg-blue-500 hover:bg-blue-600 rounded flex items-center justify-center gap-2"
            data-action="show-search"
          >
            <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            ${this.createNewButtonText}
          </button>
        </div>
      </div>
    `

    return this.component
  }

  renderProfileItem(profile) {
    return `
      <div class="group relative p-2 mb-2 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700/50 dark:hover:bg-gray-700 rounded flex items-start">
        <button class="w-full text-left" data-stored-profile-id="${profile.id}">
          <div class="flex-1">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${profile.name}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2 mt-1 pr-16">
              ${this.renderNetworkHandles(profile)}
            </div>
          </div>
        </button>
        <div class="absolute right-3 top-3 flex items-center gap-1">
          <button
            type="button"
            class="p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded"
            data-edit-profile-id="${profile.id}"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          </button>
          <button
            type="button"
            class="p-1 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 rounded"
            data-delete-profile-id="${profile.id}"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    `
  }

  renderNetworkHandles(profile) {
    const handles = []

    if (profile.bskyHandle) {
      handles.push(`
        <span class="flex items-center gap-1 text-blue-500">
          <svg class="w-3 h-3" viewBox="${icons.bsky.viewBox}" fill="currentColor">
            <path d="${icons.bsky.path}"/>
          </svg>
          <span class="truncate max-w-[160px]">@${profile.bskyHandle.slice(0, 20)}${profile.bskyHandle.length > 20 ? '...' : ''}</span>
        </span>
      `)
    }

    if (profile.xHandle) {
      handles.push(`
        <span class="flex items-center gap-1">
          <svg class="w-3 h-3" viewBox="${icons.twitter.viewBox}" fill="currentColor">
            <path d="${icons.twitter.path}"/>
          </svg>
          <span class="truncate max-w-[160px]">@${profile.xHandle.slice(0, 20)}${profile.xHandle.length > 20 ? '...' : ''}</span>
        </span>
      `)
    }

    if (profile.mastodonHandle) {
      handles.push(`
        <span class="flex items-center gap-1 text-purple-600">
          <svg class="w-3 h-3" viewBox="${icons.mastodon.viewBox}" fill="currentColor">
            <path d="${icons.mastodon.path}"/>
          </svg>
          <span class="truncate max-w-[160px]">@${profile.mastodonHandle.slice(0, 20)}${profile.mastodonHandle.length > 20 ? '...' : ''}</span>
        </span>
      `)
    }

    return handles.join('')
  }
}
