import { Paragraph } from '@tiptap/extension-paragraph'

export const CustomParagraph = Paragraph.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      order: {
        default: 0,
        parseHTML: element => {
          return parseInt(element.getAttribute('data-order') || 0)
        },
        renderHTML: attributes => {
          return {
            'class': 'mt-4',
            'data-order': attributes.order,
            'data-type': 'paragraph',
            'data-placeholder': 'Write something...'
          }
        }
      }
    }
  },

  addKeyboardShortcuts() {
    return {
      Enter: () => {
        if (this.editor.commands.newlineInCode()) {
          return true
        }

        const { selection } = this.editor.state
        const { nodeBefore, nodeAfter } = selection.$from

        // If we're after a hard break and before another hard break or empty text
        if (nodeBefore?.type.name === 'hardBreak' &&
          (!nodeAfter || nodeAfter.type.name === 'hardBreak' || nodeAfter.text === '')) {
          // Delete the hard break before creating a new paragraph
          return this.editor.chain()
            .command(({ tr }) => {
              // Delete the hard break node
              tr.delete(selection.$from.pos - 1, selection.$from.pos)
              return true
            })
            .createParagraphNear()
            .run()
        }

        return this.editor.commands.setHardBreak()
      }
    }
  }
})
