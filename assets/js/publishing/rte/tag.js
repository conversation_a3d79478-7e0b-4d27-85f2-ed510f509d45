import { Node, mergeAttributes } from '@tiptap/core'
import { Plugin } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import { TextSelection } from '@tiptap/pm/state'

// Add URL regex pattern at the top
const URL_REGEX = /https?:\/\/[^\s]+/g

export const Tag = Node.create({
  name: 'tag',

  group: 'inline',

  content: 'text*',

  inline: true,

  selectable: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: `
          inline-flex items-center
          bg-blue-50 dark:bg-blue-900/30
          text-blue-700 dark:text-blue-400
          rounded-full px-3 py-0.5 mx-0.5
          text-base font-medium
          relative group/tag
        `,
      },
    }
  },

  addCommands() {
    return {
      insertTag: (tagText) => ({ chain, state }) => {
        const text = tagText.startsWith('#') ? tagText.slice(1) : tagText

        return chain()
          .insertContent({
            type: this.name,
            content: [
              {
                type: 'text',
                text: `#${text}`
              }
            ]
          })
          .run()
      }
    }
  },

  parseHTML() {
    return [
      {
        // Parse <span data-type="tag"> tags
        tag: 'span[data-type="tag"]',
      },
      {
        // Also parse #hashtags in text, but not in URLs
        text: /#[\w\u0590-\u05ff]+/g,
        getAttrs: (match, pos, parent) => {
          // Get the full text content
          const text = parent.textContent

          // Find all URLs in the text
          const urlMatches = [...text.matchAll(URL_REGEX)]
          const urlRanges = urlMatches.map(urlMatch => ({
            start: urlMatch.index,
            end: urlMatch.index + urlMatch[0].length
          }))

          // Check if this hashtag is within any URL
          const isInUrl = urlRanges.some(range =>
            pos >= range.start && (pos + match[0].length) <= range.end
          )

          // Only return attributes if not in URL
          return isInUrl ? false : { text: match[0] }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const wrapper = document.createElement('span')
    wrapper.setAttribute('data-type', 'tag')

    // Merge attributes correctly
    const attrs = mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)
    Object.entries(attrs).forEach(([key, value]) => {
      wrapper.setAttribute(key, value)
    })

    // Create content container for the text
    const contentContainer = document.createElement('span')
    contentContainer.className = 'tag-content'
    wrapper.appendChild(contentContainer)

    // Create delete button overlay
    const deleteButton = document.createElement('button')
    deleteButton.setAttribute('data-delete-tag', '')
    deleteButton.className = `
      absolute right-2 top-1/2 -translate-y-1/2
      opacity-0 group-hover/tag:opacity-100
      transition-opacity duration-150
      flex items-center justify-center
      w-3.5 h-3.5 rounded-full
      bg-blue-100
    `
    deleteButton.innerHTML = `
      <svg class="h-2.5 w-2.5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    `
    wrapper.appendChild(deleteButton)

    return {
      dom: wrapper,
      contentDOM: contentContainer
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          handleClick: (view, pos, event) => {
            const deleteButton = event.target.closest('[data-delete-tag]')
            if (!deleteButton) return false

            // Find the tag node
            let start = pos
            let end = pos
            let found = false

            view.state.doc.nodesBetween(0, view.state.doc.content.size, (node, nodePos) => {
              if (found) return false
              if (node.type.name === 'tag' && nodePos <= pos && nodePos + node.nodeSize >= pos) {
                start = nodePos
                end = nodePos + node.nodeSize
                found = true
                return false
              }
            })

            if (found) {
              const tr = view.state.tr.delete(start, end)
              view.dispatch(tr)
              return true
            }

            return false
          },

          handleKeyDown: (view, event) => {
            if (event.key === ' ' || event.key === 'Enter') {
              const { $from } = view.state.selection

              // Check if we're inside a tag node
              const node = $from.node()
              if (node.type.name === 'tag' || $from.parent.type.name === 'tag') {
                // Get the end position of the tag node
                const endPos = $from.end()

                const tr = view.state.tr
                tr.insertText(' ', endPos)
                tr.setSelection(TextSelection.create(tr.doc, endPos + 1))

                view.dispatch(tr)
                return true
              }

              // Handle creating new tag from hashtag
              const textBefore = $from.parent.textBetween(0, $from.parentOffset)

              // Check if the hashtag is part of a URL
              const lastUrl = textBefore.match(URL_REGEX)?.pop()
              if (lastUrl && lastUrl.includes('#')) {
                return false
              }

              const match = textBefore.match(/#[\w\u0590-\u05ff]+$/)
              if (match) {
                const start = $from.pos - match[0].length
                const tr = view.state.tr

                // Delete the hashtag text
                tr.delete(start, $from.pos)

                // Create and insert tag node
                const tagNode = this.type.create(null, [
                  view.state.schema.text(match[0])
                ])
                tr.insert(start, tagNode)

                // Add space after tag
                tr.insertText(' ', start + tagNode.nodeSize)

                view.dispatch(tr)
                return true
              }
            }
            return false
          },

          decorations(state) {
            const decorations = []
            const hashtagRegex = /#[\w\u0590-\u05ff]+/g

            state.doc.descendants((node, pos) => {
              if (node.isText) {
                // First find all URLs in the text
                const urlMatches = [...node.text.matchAll(URL_REGEX)]
                const urlRanges = urlMatches.map(match => ({
                  start: pos + match.index,
                  end: pos + match.index + match[0].length
                }))

                // Then process hashtags
                let match
                while ((match = hashtagRegex.exec(node.text)) !== null) {
                  const from = pos + match.index
                  const to = from + match[0].length

                  // Check if this hashtag is within any URL range
                  const isInUrl = urlRanges.some(range =>
                    from >= range.start && to <= range.end
                  )

                  const $from = state.doc.resolve(from)
                  if (!isInUrl && $from.parent.type.name !== 'tag') {
                    decorations.push(
                      Decoration.inline(from, to, {
                        class: 'text-blue-600 bg-blue-50 rounded-full px-3 py-0.5 text-base font-medium'
                      })
                    )
                  }
                }
              }
            })

            return DecorationSet.create(state.doc, decorations)
          }
        }
      })
    ]
  }
})
