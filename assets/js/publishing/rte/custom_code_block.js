import { CodeBlock } from '@tiptap/extension-code-block'

export const CustomCodeBlock = CodeBlock.extend({
  addNodeView() {
    return ({ node, editor }) => {
      const wrapper = document.createElement('div')
      wrapper.className = 'relative group'

      const pre = document.createElement('pre')
      pre.className = 'rounded-md bg-gray-800 text-gray-100 p-4 font-mono text-sm my-4'

      const code = document.createElement('code')

      const button = document.createElement('button')
      button.type = 'button'
      button.className = `
        px-3 py-1 text-sm bg-indigo-600 text-white rounded-md
        hover:bg-indigo-700 focus:outline-none focus:ring-2
        focus:ring-indigo-500 focus:ring-offset-2 absolute right-2 top-2
        opacity-0 group-hover:opacity-100 transition-opacity duration-150
      `
      button.textContent = 'Snappify'

      button.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()

        const blockId = editor.view.dom.closest('.content-block')?.dataset.blockId

        if (blockId) {
          editor.view.dom.dispatchEvent(new CustomEvent('editor:snappify-code', {
            bubbles: true,
            detail: { blockId, code: pre.textContent }
          }))
        }
      })

      pre.appendChild(code)
      wrapper.appendChild(pre)
      wrapper.appendChild(button)

      return {
        dom: wrapper,
        contentDOM: code,
      }
    }
  }
})
