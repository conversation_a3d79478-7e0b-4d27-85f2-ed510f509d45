export class ContentParser {
  constructor(text, { meta, order, network }) {
    this.text = text
    this.meta = meta
    this.order = order
    this.network = network
  }

  parse() {
    const text = this.text

    if (!text) return { type: 'paragraph', content: [], attrs: { order: this.order } }

    const paragraphs = text.split('\n\n').filter(p => p.length > 0)

    if (paragraphs.length === 1) {
      return this.createParagraphNode(paragraphs[0])
    }

    return paragraphs.map(text => this.createParagraphNode(text))
  }

  createParagraphNode(text) {
    // Split paragraph into lines by single newlines
    const lines = text.split('\n')

    return {
      type: 'paragraph',
      attrs: { order: this.order },
      content: lines.flatMap((line, index) => {
        const nodes = []

        let lastIndex = 0
        const tokens = this.collectTokens(line)

        tokens.forEach(token => {
          // Add text before token if exists
          if (token.index > lastIndex) {
            const textContent = line.slice(lastIndex, token.index)
            if (textContent.length > 0) {
              nodes.push({
                type: 'text',
                text: textContent
              })
            }
          }

          // Add token node
          if (token.type === 'socialProfile') {
            nodes.push({
              type: 'socialProfile',
              attrs: token.data
            })
          }

          lastIndex = token.index + token.length
        })

        // Add remaining text after last token
        if (lastIndex < line.length) {
          const textContent = line.slice(lastIndex)
          if (textContent.length > 0) {
            nodes.push({
              type: 'text',
              text: textContent
            })
          }
        }

        // If empty line or no tokens found, add a single space text node
        if (nodes.length === 0 && line.length === 0) {
          nodes.push({
            type: 'text',
            text: ' '
          })
        } else if (nodes.length === 0) {
          nodes.push({
            type: 'text',
            text: line
          })
        }

        // Add hardbreak after each line except the last one
        if (index < lines.length - 1) {
          nodes.push({ type: 'hardBreak' })
        }

        return nodes
      })
    }
  }

  collectTokens(text) {
    const tokens = []
    const mentionPositions = new Map()

    // Find all URLs first
    const urlMatches = [...text.matchAll(/https?:\/\/[^\s]+/g)]
    const urlRanges = urlMatches.map(match => ({
      start: match.index,
      end: match.index + match[0].length
    }))

    // First pass: collect all possible mention positions
    this.meta.mentions.forEach(mention => {
      const networkMention = mention.networks.find(n => n.id === this.network)
      const shouldUseNetworkHandle = this.network !== 'canonical' && networkMention
      const label = shouldUseNetworkHandle ? networkMention.handle : mention.name

      const originalMentionText = `@${mention.name}`
      const networkMentionText = networkMention ? `@${networkMention.handle}` : null

      // Find all occurrences of the original mention
      if (originalMentionText) {
        // Use regex with word boundary to ensure we match complete mentions
        const regex = new RegExp(`(^|\\s)${this.escapeRegExp(originalMentionText)}(?=\\s|$)`, 'g')
        const matches = [...text.matchAll(regex)]

        matches.forEach(match => {
          const startIndex = match.index + match[1].length // Skip the space or start of line
          mentionPositions.set(startIndex, {
            type: 'socialProfile',
            index: startIndex,
            length: originalMentionText.length,
            data: { id: mention.id, label },
            text: originalMentionText
          })
        })
      }

      // Find all occurrences of the network-specific mention
      if (networkMentionText) {
        const regex = new RegExp(`(^|\\s)${this.escapeRegExp(networkMentionText)}(?=\\s|$)`, 'g')
        const matches = [...text.matchAll(regex)]

        matches.forEach(match => {
          const startIndex = match.index + match[1].length // Skip the space or start of line
          mentionPositions.set(startIndex, {
            type: 'socialProfile',
            index: startIndex,
            length: networkMentionText.length,
            data: { id: mention.id, label },
            text: networkMentionText
          })
        })
      }
    })

    // Add all mention tokens
    tokens.push(...mentionPositions.values())

    // Sort tokens by index
    return tokens.sort((a, b) => a.index - b.index)
  }

  // Helper method to escape special characters in regex
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }
}
