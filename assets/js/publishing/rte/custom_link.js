import { Link } from '@tiptap/extension-link'
import { Plugin } from '@tiptap/pm/state'
import { TextSelection } from '@tiptap/pm/state'

export const URL_REGEX = /https?:\/\/[^\s]+/g

export const CustomLink = Link.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      events: null,
      HTMLAttributes: {
        class: 'text-blue-600 hover:text-blue-800 underline',
        target: '_blank',
        rel: 'noopener noreferrer'
      },
      autolink: true,
      linkOnPaste: true,
      openOnClick: false,
      defaultProtocol: 'https'
    }
  },

  addStorage() {
    return {
      links: new Set()
    }
  },

  onCreate() {
    // Initialize links from existing content by detecting URLs in text
    const links = new Set()
    const tr = this.editor.state.tr

    this.editor.state.doc.descendants((node, pos) => {
      if (node.isText) {
        const matches = Array.from(node.text.matchAll(URL_REGEX))

        // Process matches in reverse to not invalidate positions
        matches.reverse().forEach(match => {
          const start = pos + match.index
          const end = start + match[0].length
          const url = match[0]

          // Add link mark to the URL text
          tr.addMark(
            start,
            end,
            this.editor.schema.marks.link.create({ href: url })
          )

          links.add(url)
        })
      }
    })

    // Apply the transaction if we found any links
    if (tr.steps.length > 0) {
      this.editor.view.dispatch(tr)
    }

    this.storage.links = links

    this.options.events.emit('links:set', { links: links })
  },

  addProseMirrorPlugins() {
    const events = this.options.events
    const storage = this.storage

    const plugins = [
      ...this.parent?.(),
      new Plugin({
        props: {
          handlePaste(view, event) {
            if (!events) return false

            const text = event.clipboardData.getData('text/plain')
            const urlMatch = text.match(URL_REGEX)

            if (urlMatch) {
              const url = urlMatch[0]
              storage.links.add(url)

              events.emit('links:added', url)
            }

            return false
          }
        },
        appendTransaction(transactions, oldState, newState) {
          // Track link changes from other sources (like autolink)
          const links = new Set(storage.links)
          let changed = false

          newState.doc.descendants((node, pos) => {
            if (node.type.name === 'text' && node.marks) {
              const linkMark = node.marks.find(mark => mark.type.name === 'link')
              if (linkMark && !links.has(linkMark.attrs.href)) {
                links.add(linkMark.attrs.href)
                changed = true
              }
            }
          })

          return null
        }
      })
    ]

    return plugins
  },
})
