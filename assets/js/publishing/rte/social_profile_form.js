import { icons } from './icons'

const API_ENDPOINTS = {
  PROFILES: '/api/social-profiles',
  SEARCH: '/api/social-profiles/search'
}

export class SocialProfileForm {
  constructor(profile = null, onSave = () => { }, { isSearchMode = false, initialQuery = '' } = {}) {
    this.profile = profile
    this.onSave = onSave
    this.isSearchMode = isSearchMode
    this.initialQuery = initialQuery
    this.component = document.createElement('div')
    this.searchResults = {
      bsky: [],
      mastodon: []
    }
    this.debounceTimeouts = {
      bsky: null,
      mastodon: null
    }
    this.abortControllers = {
      bsky: null,
      mastodon: null
    }

    // Set up event delegation
    this.component.addEventListener('click', this.handleClick.bind(this))
    this.component.addEventListener('input', this.handleInput.bind(this))
    this.component.addEventListener('submit', this.handleSubmit.bind(this))
  }

  updateProfile(profile = null, { isSearchMode = false, initialQuery = '' } = {}) {
    this.profile = profile
    this.isSearchMode = isSearchMode
    this.initialQuery = initialQuery
    this.searchResults = {
      bsky: [],
      mastodon: []
    }
    // Clear any pending timeouts and requests
    Object.keys(this.debounceTimeouts).forEach(network => {
      if (this.debounceTimeouts[network]) {
        clearTimeout(this.debounceTimeouts[network])
        this.debounceTimeouts[network] = null
      }
    })
    Object.keys(this.abortControllers).forEach(network => {
      if (this.abortControllers[network]) {
        this.abortControllers[network].abort()
        this.abortControllers[network] = null
      }
    })
  }

  handleClick(e) {
    // Handle dismiss button
    if (e.target.closest('[data-dismiss]')) {
      this.component.dispatchEvent(new CustomEvent('dismiss'))
      return
    }

    // Handle Bluesky result selection
    const bskyResult = e.target.closest('[data-bsky-result]')
    if (bskyResult) {
      const { handle } = bskyResult.dataset
      const input = this.component.querySelector('[name="bsky_handle"]')
      if (input) {
        input.value = handle
        this.searchResults.bsky = []
        this.renderSearchResults('bsky')
      }
      return
    }

    // Handle Mastodon result selection
    const mastodonResult = e.target.closest('[data-mastodon-result]')
    if (mastodonResult) {
      const { handle } = mastodonResult.dataset
      const input = this.component.querySelector('[name="mastodon_handle"]')
      if (input) {
        input.value = handle
        this.searchResults.mastodon = []
        this.renderSearchResults('mastodon')
      }
      return
    }
  }

  handleInput(e) {
    const input = e.target
    if (!input.matches('[name="bsky_handle"], [name="mastodon_handle"]')) return

    const network = input.name === 'bsky_handle' ? 'bsky' : 'mastodon'

    if (this.debounceTimeouts[network]) {
      clearTimeout(this.debounceTimeouts[network])
    }

    this.debounceTimeouts[network] = setTimeout(() => {
      this.searchProfiles(network, input.value.trim())
      this.debounceTimeouts[network] = null
    }, 300)
  }

  async handleSubmit(e) {
    e.preventDefault()
    const form = e.target
    if (!form.matches('[data-profile-form]')) return

    const formData = new FormData(form)
    const data = {
      name: formData.get('name'),
      bsky_handle: formData.get('bsky_handle'),
      x_handle: formData.get('x_handle'),
      mastodon_handle: formData.get('mastodon_handle')
    }

    if (!data.name || (!data.bsky_handle && !data.x_handle && !data.mastodon_handle)) {
      return
    }

    const url = this.profile?.id ?
      `${API_ENDPOINTS.PROFILES}/${this.profile.id}` :
      API_ENDPOINTS.PROFILES

    try {
      const response = await fetch(url, {
        method: this.profile?.id ? 'PATCH' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.data) {
        this.onSave(result.data)
      } else {
        console.error('No data returned from server', result)
      }
    } catch (error) {
      console.error('Error saving profile:', error)
    }
  }

  render() {
    const isEditing = !!this.profile

    this.component.innerHTML = `
      <form class="divide-y divide-gray-100" data-profile-form>
        <div class="p-2 flex items-center justify-between bg-blue-700">
          <h3 class="font-medium text-white">Social Profile</h3>
          <button
            type="button"
            class="text-gray-200 hover:text-gray-100 p-1 rounded"
            data-dismiss
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="p-2">
          <h3 class="flex items-center gap-2 text-xs font-medium text-gray-900 mb-2">
            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
            </svg>
            Profile Name
          </h3>
          <input
            type="text"
            name="name"
            class="w-full px-2 py-1.5 text-sm border border-gray-200 rounded"
            placeholder="Enter a name for this profile"
            value="${this.profile?.name || this.initialQuery}"
            required
          >
        </div>

        <div class="p-2">
          <div class="flex items-center gap-2 text-xs font-medium text-blue-500 mb-2">
            <svg class="w-4 h-4" viewBox="${icons.bsky.viewBox}" fill="currentColor">
              <path d="${icons.bsky.path}"/>
            </svg>
            Bluesky
          </div>
          <input
            type="text"
            name="bsky_handle"
            class="w-full px-2 py-1.5 text-sm border border-gray-200 rounded"
            placeholder="Search Bluesky handles..."
            value="${this.profile?.bskyHandle || this.initialQuery}"
            ${!this.profile ? 'autofocus' : ''}
          >
          <div class="mt-2" data-bsky-results></div>
        </div>

        <div class="p-2">
          <div class="flex items-center gap-2 text-xs font-medium text-purple-600 mb-2">
            <svg class="w-4 h-4" viewBox="${icons.mastodon.viewBox}" fill="currentColor">
              <path d="${icons.mastodon.path}"/>
            </svg>
            Mastodon
          </div>
          <input
            type="text"
            name="mastodon_handle"
            class="w-full px-2 py-1.5 text-sm border border-gray-200 rounded"
            placeholder="Search Mastodon handles..."
            value="${this.profile?.mastodonHandle || this.initialQuery}"
          >
          <div class="mt-2" data-mastodon-results></div>
        </div>

        <div class="p-2">
          <div class="flex items-center gap-2 text-xs font-medium text-gray-900 mb-2">
            <svg class="w-4 h-4" viewBox="${icons.twitter.viewBox}" fill="currentColor">
              <path d="${icons.twitter.path}"/>
            </svg>
            X
          </div>
          <input
            type="text"
            name="x_handle"
            class="w-full px-2 py-1.5 text-sm border border-gray-200 rounded"
            placeholder="Enter X handle (without @)"
            value="${this.profile?.xHandle || ''}"
          >
        </div>

        <div class="p-2 bg-gray-50">
          <button
            type="submit"
            class="w-full px-2 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            ${isEditing ? 'Update Profile' : 'Save Profile'}
          </button>
        </div>
      </form>
    `

    if (this.initialQuery) {
      this.searchProfiles('bsky', this.initialQuery)
      this.searchProfiles('mastodon', this.initialQuery)
    }

    return this.component
  }

  renderSearchResults(network) {
    const resultsContainer = this.component.querySelector(`[data-${network}-results]`)
    if (!resultsContainer) return

    const results = this.searchResults[network]
    if (results.length === 0) {
      resultsContainer.innerHTML = ''
      return
    }

    resultsContainer.innerHTML = results.map(result => `
      <button
        type="button"
        class="w-full text-left px-2 py-1.5 hover:bg-gray-50 rounded flex items-center gap-2"
        data-${network}-result
        data-handle="${result.handle || result.acct}"
        data-display-name="${result.display_name}"
        data-avatar="${result.avatar}"
      >
        <img src="${result.avatar}" class="w-5 h-5 rounded-full" onerror="this.src='https://www.gravatar.com/avatar/0?d=mp'" />
        <div>
          <div class="text-sm font-medium">${result.display_name}</div>
          <div class="text-xs text-gray-500">@${result.handle || result.acct}</div>
        </div>
      </button>
    `).join('')
  }

  async searchProfiles(network, query) {
    if (query.length < 2) {
      this.searchResults[network] = []
      this.renderSearchResults(network)
      return
    }

    if (this.abortControllers[network]) {
      this.abortControllers[network].abort()
    }

    const controller = new AbortController()
    this.abortControllers[network] = controller

    try {
      const response = await fetch(
        `${API_ENDPOINTS.SEARCH}?q=${encodeURIComponent(query)}&network=${network}`,
        {
          headers: {
            'Accept': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          },
          signal: controller.signal
        }
      )
      const data = await response.json()
      this.searchResults[network] = data.data
      this.renderSearchResults(network)
    } catch (error) {
      if (error.name === 'AbortError') return
      console.error('Error fetching search results:', error)
    } finally {
      if (this.abortControllers[network] === controller) {
        this.abortControllers[network] = null
      }
    }
  }
}
