import { SocialProfileEditor } from './social_profile_editor'

const MIN_QUERY_LENGTH = 2

class SuggestionRenderer {
  constructor() {
    const socialProfiles = JCP.getSocialProfiles()

    this.editor = new SocialProfileEditor({ socialProfiles })
    this.debounceTimeout = null
  }

  async onStart(props) {
    if (!this.shouldRender(props)) return
    this.debounceTimeout = setTimeout(() => { this.showEditor(props) }, 300)
  }

  async onUpdate(props) {
    if (!this.shouldRender(props)) return

    clearTimeout(this.debounceTimeout)

    this.debounceTimeout = setTimeout(() => { this.showEditor(props) }, 300)
  }

  showEditor(props) {
    const clientRect = props.clientRect()

    if (this.editor.isHidden()) {
      this.editor.showPopup(() => clientRect)
    }
    this.editor.align(() => clientRect)
    this.editor.renderResults(props)
  }

  onKeyDown(props) {
    if (props.event.key === 'Escape') {
      this.destroy()
      return true
    }
    return false
  }

  onExit() {
    this.destroy()
  }

  shouldRender(props) {
    if (!props.query || props.query.length <= MIN_QUERY_LENGTH) {
      return false
    }

    return true
  }

  destroy() {
    this.editor.destroy()
    this.debounceTimeout = null
    this.destroyed = true
  }
}

export const SocialProfileSuggestion = {
  char: '@',
  allowSpaces: false,
  command: ({ editor, range, props }) => {
    const attrs = { id: props.id, label: props.name }

    editor.storage.socialProfile.set(attrs.id, props)
    editor.emit('social_profile:added', props)

    editor
      .chain()
      .focus()
      .deleteRange(range)
      .insertContent([
        {
          type: 'socialProfile',
          attrs: attrs
        },
        {
          type: 'text',
          text: ' '
        }
      ])
      .run()

    return true
  },
  render: () => {
    return new SuggestionRenderer()
  }
}
