import { ContentParser } from '../content_parser'

describe('ContentParser', () => {
  const defaultOpts = { meta: { mentions: [] }, order: 0 }

  it('handles empty text', () => {
    const parser = new ContentParser('', defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      content: [],
      attrs: { order: 0 }
    })
  })

  it('handles single line text', () => {
    const parser = new ContentParser('Hello World', defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello World' }
      ]
    })
  })

  it('handles multiple lines within a paragraph', () => {
    const parser = new ContentParser('First line\nSecond line', defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'First line' },
        { type: 'hardBreak' },
        { type: 'text', text: 'Second line' }
      ]
    })
  })

  it('handles multiple paragraphs', () => {
    const text = 'First paragraph\nwith two lines\n\nSecond paragraph\nwith two lines'
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual([
      {
        type: 'paragraph',
        attrs: { order: 0 },
        content: [
          { type: 'text', text: 'First paragraph' },
          { type: 'hardBreak' },
          { type: 'text', text: 'with two lines' }
        ]
      },
      {
        type: 'paragraph',
        attrs: { order: 0 },
        content: [
          { type: 'text', text: 'Second paragraph' },
          { type: 'hardBreak' },
          { type: 'text', text: 'with two lines' }
        ]
      }
    ])
  })

  it('handles bullet points and links', () => {
    const text = 'Hello World\n\n- New line\n- one more\n\nhttps://elixir-lang.org\nhttps://ruby-lang.org'
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual([
      {
        type: 'paragraph',
        attrs: { order: 0 },
        content: [
          { type: 'text', text: 'Hello World' }
        ]
      },
      {
        type: 'paragraph',
        attrs: { order: 0 },
        content: [
          { type: 'text', text: '- New line' },
          { type: 'hardBreak' },
          { type: 'text', text: '- one more' }
        ]
      },
      {
        type: 'paragraph',
        attrs: { order: 0 },
        content: [
          { type: 'text', text: 'https://elixir-lang.org' },
          { type: 'hardBreak' },
          { type: 'text', text: 'https://ruby-lang.org' }
        ]
      }
    ])
  })

  it('handles mentions', () => {
    const text = 'Hello @user and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          { label: 'user', id: '1' },
          { label: 'another', id: '2' }
        ]
      },
      order: 0
    })
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'user', id: '1' } },
        { type: 'text', text: ' and ' },
        { type: 'socialProfile', attrs: { label: 'another', id: '2' } }
      ]
    })
  })

  it('handles mentions without network-specific handles', () => {
    const text = 'Hello @user and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          { label: 'user', id: '1' },
          { label: 'another', id: '2' }
        ]
      },
      network: 'bsky',
      order: 0
    })
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'user', id: '1' } },
        { type: 'text', text: ' and ' },
        { type: 'socialProfile', attrs: { label: 'another', id: '2' } }
      ]
    })
  })

  it('handles network-specific mentions in canonical editor', () => {
    const text = 'Hello @user and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          {
            label: 'user',
            id: '1',
            networks: [
              { id: 'bsky', handle: 'user.bsky' }
            ]
          }
        ]
      },
      network: 'canonical',
      order: 0
    })
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'user', id: '1' } },
        { type: 'text', text: ' and @another' }
      ]
    })
  })

  it('handles network-specific mentions in network editor with original mention text', () => {
    const text = 'Hello @user and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          {
            label: 'user',
            id: '1',
            networks: [
              { id: 'bsky', handle: 'user.bsky' }
            ]
          }
        ]
      },
      network: 'bsky',
      order: 0
    })
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'user.bsky', id: '1' } },
        { type: 'text', text: ' and @another' }
      ]
    })
  })

  it('handles network-specific mentions in network editor with network handle text', () => {
    const text = 'Hello @user.bsky and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          {
            label: 'user',
            id: '1',
            networks: [
              { id: 'bsky', handle: 'user.bsky' }
            ]
          }
        ]
      },
      network: 'bsky',
      order: 0
    })
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'user.bsky', id: '1' } },
        { type: 'text', text: ' and @another' }
      ]
    })
  })

  it('handles hashtags', () => {
    const text = 'Hello #world and #elixir'
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'tag', content: [{ type: 'text', text: '#world' }] },
        { type: 'text', text: ' and ' },
        { type: 'tag', content: [{ type: 'text', text: '#elixir' }] }
      ]
    })
  })

  it('preserves hashtags in URLs', () => {
    const text = 'Check out https://example.com/#section and #realTag'
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Check out https://example.com/#section and ' },
        { type: 'tag', content: [{ type: 'text', text: '#realTag' }] }
      ]
    })
  })

  it('handles network-specific handles correctly without duplication', () => {
    const text = 'Hello @solnic.dev and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          {
            label: 'solnic',
            id: '1',
            networks: [
              { id: 'bsky', handle: 'solnic.dev' }
            ]
          }
        ]
      },
      network: 'bsky',
      order: 0
    })

    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'solnic.dev', id: '1' } },
        { type: 'text', text: ' and @another' }
      ]
    })
  })

  it('prevents duplicate network suffixes when meta already contains network handle', () => {
    const text = 'Hello @solnic.dev and @another'
    const parser = new ContentParser(text, {
      meta: {
        mentions: [
          {
            label: 'solnic.dev',
            id: '1',
            networks: [
              { id: 'bsky', handle: 'solnic.dev' }
            ]
          }
        ]
      },
      network: 'bsky',
      order: 0
    })

    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'Hello ' },
        { type: 'socialProfile', attrs: { label: 'solnic.dev', id: '1' } },
        { type: 'text', text: ' and @another' }
      ]
    })
  })

  it('handles multiple URLs with hashtags', () => {
    const text = [
      'First URL: https://example.com/#section1',
      'Second URL: https://test.org/page#section2',
      'Regular #hashtag here',
      'Another URL: https://docs.com/api#reference'
    ].join('\n')
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'First URL: https://example.com/#section1' },
        { type: 'hardBreak' },
        { type: 'text', text: 'Second URL: https://test.org/page#section2' },
        { type: 'hardBreak' },
        { type: 'text', text: 'Regular ' },
        { type: 'tag', content: [{ type: 'text', text: '#hashtag' }] },
        { type: 'text', text: ' here' },
        { type: 'hardBreak' },
        { type: 'text', text: 'Another URL: https://docs.com/api#reference' }
      ]
    })
  })

  it('handles URLs with query parameters and hashtags', () => {
    const text = 'URL: https://example.com/search?q=test#results and #tag'
    const parser = new ContentParser(text, defaultOpts)
    expect(parser.parse()).toEqual({
      type: 'paragraph',
      attrs: { order: 0 },
      content: [
        { type: 'text', text: 'URL: https://example.com/search?q=test#results and ' },
        { type: 'tag', content: [{ type: 'text', text: '#tag' }] }
      ]
    })
  })
})
