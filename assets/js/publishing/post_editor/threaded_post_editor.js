import { PostContent } from '../../models/post'
import { PostEditor } from '../post_editor'
import { ThreadedContentEditor } from '../content_editor/threaded_content_editor'

export class ThreadedPostEditor extends PostEditor {
  constructor(element, options = {}) {
    super(element, options)

    this.debouncedRerender = this.debounce(this.rerender, 300)

    this.handlers = {
      ...this.handlers,
      "content:sync_toggled": [this.network, this.onContentSyncToggled],
      "content:remove": [this.network, this.onContentRemove],
      "content:continue": [this.network, this.onContentContinue],
      "content:move": [this.network, this.onContentMove]
    }

    if (this.network !== 'canonical') {
      this.handlers = {
        ...this.handlers,
        "content:edit": ["canonical", this.onCanonicalContentEdit],
        "content:attachment_removed": ["canonical", this.onCanonicalAttachmentRemoved],
        "content:attachments_updated": ["canonical", this.onCanonicalAttachmentsUpdated],
        "content:removed": ["canonical", this.onCanonicalContentRemoved],
        "content:moved": ["canonical", this.onCanonicalContentMoved],
        "content:link_embedded": ["canonical", this.onCanonicalLinkEmbedded]
      }
    }
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func.apply(this, args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later.bind(this), wait)
    }
  }

  setupEventListeners() {
    Object.entries(this.handlers).forEach(([event, [topic, handler]]) => {
      this.events.topic(topic).on(event, handler.bind(this), this)
    })
  }

  show() {
    if (this.network !== 'canonical') {
      this.sync()
    }

    super.show()
  }

  sync() {
    this.contentEditors.forEach(editor => {
      if (editor.isSynced()) {
        const canonicalContent = this.store.getContentForNetwork("canonical", editor.content.order)

        this.store.updateContent(this.network, canonicalContent.order, {
          text: PostContent.processCanonicalText(this.network, canonicalContent),
          meta: canonicalContent.meta
        })
      }
    })

    this.rerender()
  }

  onContentSyncToggled(content) {
    if (content.sync) {
      const canonicalContent = this.store.getContentForNetwork("canonical", content.order)

      if (!canonicalContent) {
        alert("There's no corresponding content to sync with")
        return
      }

      this.store.syncFromCanonical(this.network, content.order)

      this.rerender()
    }
  }

  onContentRemove(content) {
    if (content.order === 0 && this.contentEditors.length == 1) {
      this.contentEditors[0].clear()
    } else {
      this.store.removeContent(this.network, content.order)
      this.rerender()
    }

    this.events.topic(this.network).emit('content:removed', content)
  }

  onContentContinue({ afterIndex, meta }) {
    const content = this.store.addContent({ network: this.network, meta: meta, sync: false }, afterIndex + 1)
    this.rerender()
    this.events.topic(this.network).emit('content:added', content)
  }

  onContentMove({ content, newOrder }) {
    const oldOrder = content.order
    const updatedContent = this.store.reorder(content, newOrder)

    this.rerender()

    const movedContent = updatedContent.find(c => c.order === newOrder)

    this.events.topic(this.network)
      .emit('content:moved', { canonicalContent: movedContent, oldOrder })
  }

  isPreviousEditorSynced(order) {
    return this.contentEditors[order - 1]?.isSynced()
  }

  onCanonicalContentEdit(canonicalContent) {
    let editor = this.contentEditors[canonicalContent.order]

    if (!editor && this.isPreviousEditorSynced(canonicalContent.order)) {
      const content = this.store.addContent(PostContent.fromCanonical(this.network, canonicalContent), canonicalContent.order)
      editor = this.addContentEditor(content)

      this.events.topic(this.network).emit('content:edit', content)
    } else if (editor?.isSynced()) {
      const content = this.store.updateContent(this.network, canonicalContent.order, {
        text: PostContent.processCanonicalText(this.network, canonicalContent),
        meta: canonicalContent.meta
      })

      this.events.topic(this.network).emit('content:edit', content)
    }
  }

  onCanonicalAttachmentsUpdated(content) {
    if (!this.contentEditors[content.order].isSynced()) return

    const editor = this.contentEditors[content.order]
    editor.updateAttachments(content.attachments)
  }

  onCanonicalAttachmentRemoved({ attachment, content }) {
    if (!this.contentEditors[content.order].isSynced()) return

    const editor = this.contentEditors[content.order]

    editor.content.removeAttachment(attachment.id)
    editor.updateAttachments()
  }

  onCanonicalContentAdded(canonicalContent) {
    const isSynced = this.contentEditors[canonicalContent.order - 1]?.isSynced()

    if (!isSynced) return

    let content = PostContent.fromCanonical(this.network, canonicalContent)

    content.sync = isSynced

    content = this.store.addContent(content, canonicalContent.order)

    this.events.topic(this.network).emit('content:sync_toggled', content)

    this.rerender()
  }

  onCanonicalContentRemoved(content) {
    if (!this.contentEditors[content.order]?.isSynced()) return

    this.onContentRemove(this.contentEditors[content.order].content)
  }

  onCanonicalContentMoved({ canonicalContent, oldOrder }) {
    if (!this.contentEditors[oldOrder].isSynced()) return

    const content = this.store.getContentForNetwork(this.network, oldOrder)
    this.onContentMove({ content, newOrder: canonicalContent.order })
  }

  onCanonicalLinkEmbedded({ content, linkAttachment }) {
    if (!this.contentEditors[content.order].isSynced()) return

    const editor = this.contentEditors[content.order]

    if (editor) {
      editor.linkControls.replaceCurrentAttachment(linkAttachment)

      // Update the content text with URL removed if needed
      const processedText = PostContent.processTextForNetwork(
        editor.content.text,
        this.network,
        [linkAttachment]
      )

      const updatedContent = this.store.updateContent(this.network, content.order, {
        text: processedText
      })

      // Emit content:edit to update counters
      this.events.topic(this.network).emit('content:edit', updatedContent)
    }
  }

  addContentEditor(content) {
    const editorContainer = document.createElement('div')

    editorContainer.className = 'post-editor threaded-post-editor group/editor'
    editorContainer.dataset.editorOrder = content.order

    const size = this.size()

    let moveButtons = null

    if (size > 1 && content.order !== size - 1) {
      moveButtons = this.addMoveButtons(content.order, size)
    }

    this.wrapper.appendChild(editorContainer)

    const editorOpts = {
      content,
      selectedNetworks: this.selectedNetworks,
      wrapper: this.wrapper,
      charCounter: this.charCounter,
      events: this.events,
      attachments: this.attachments,
      store: this.store,
      network: this.network
    }

    const contentEditor = new ThreadedContentEditor(editorContainer, editorOpts)

    if (moveButtons) {
      editorContainer.appendChild(moveButtons)
    }

    this.contentEditors[content.order] = contentEditor

    return contentEditor
  }

  addMoveButtons(currentOrder, size) {
    const buttonContainer = document.createElement('div')

    buttonContainer.className = 'relative'
    buttonContainer.dataset.moveButtonsAfter = currentOrder

    buttonContainer.innerHTML = `
      <div class="move-buttons absolute left-1/2 -translate-x-1/2 -top-3 z-20">
        <div class="flex items-center gap-1">
          <button
            type="button"
            class="w-6 h-6 rounded-full flex items-center justify-center bg-gray-100 border border-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-700 transition-colors duration-150 disabled:opacity-40 disabled:hover:bg-gray-100 disabled:hover:text-gray-500 disabled:cursor-not-allowed"
            data-move-up
            data-editor-order="${currentOrder + 1}"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            type="button"
            class="w-6 h-6 rounded-full flex items-center justify-center bg-gray-100 border border-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-700 transition-colors duration-150 disabled:opacity-40 disabled:hover:bg-gray-100 disabled:hover:text-gray-500 disabled:cursor-not-allowed"
            data-move-down
            data-editor-order="${currentOrder === 0 ? 0 : currentOrder - 1}"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    `

    this.setupMoveButtonListeners(buttonContainer)

    return buttonContainer
  }

  setupMoveButtonListeners(container) {
    const moveUpButton = container.querySelector('[data-move-up]')
    const moveDownButton = container.querySelector('[data-move-down]')

    if (moveUpButton) {
      moveUpButton.onclick = (e) => {
        e.preventDefault()
        e.stopPropagation()

        if (moveUpButton.disabled) return

        const order = parseInt(moveUpButton.dataset.editorOrder)
        const content = this.store.getContentForNetwork(this.network, order)
        if (content) {
          this.events.topic(this.network).emit('content:move', { content, newOrder: order - 1 })
        }
      }
    }

    if (moveDownButton) {
      moveDownButton.onclick = (e) => {
        e.preventDefault()
        e.stopPropagation()

        if (moveDownButton.disabled) return

        const order = parseInt(moveDownButton.dataset.editorOrder)
        const content = this.store.getContentForNetwork(this.network, order)
        if (content) {
          this.events.topic(this.network).emit('content:move', { content, newOrder: order + 1 })
        }
      }
    }
  }

  rerender() {
    this.contentEditors.forEach(editor => editor.destroy())

    this.contentEditors = []
    this.wrapper.innerHTML = ''

    this.store.getContentForNetwork(this.network)
      .forEach(content => this.addContentEditor(content))

    return this
  }
}
