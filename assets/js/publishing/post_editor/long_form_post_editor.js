import { PostContent } from '../../models/post'
import { PostEditor } from '../post_editor'
import { LongFormContentEditor } from '../content_editor/long_form_content_editor'
import merge from 'lodash/merge'

export class LongFormPostEditor extends PostEditor {
  constructor(element, options = {}) {
    super(element, options)

    // Create a debounced version of rerender
    this.debouncedRerender = this.debounce(this.rerender, 300)

    if (this.network !== 'canonical') {
      this.handlers = {
        ...this.handlers,
        "content:sync_toggled": [this.network, this.sync],
        "content:edit": ["canonical", this.sync],
        "content:moved": ["canonical", this.sync],
        "content:removed": ["canonical", this.sync],
        "content:attachments_updated": ["canonical", this.onCanonicalAttachmentsUpdated],
        "content:attachment_removed": ["canonical", this.onCanonicalAttachmentRemoved],
        "content:link_embedded": ["canonical", this.onCanonicalLinkEmbedded]
      }
    }

    this.contentEditor = this.contentEditors[0]
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func.apply(this, args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later.bind(this), wait)
    }
  }

  setupEventListeners() {
    Object.entries(this.handlers).forEach(([event, [topic, handler]]) => {
      this.events.topic(topic).on(event, handler.bind(this), this)
    })
  }

  initializeContentEditors() {
    let content = this.store.getContentForNetwork(this.network)

    if (content.length > 0) {
      const processedContent = new PostContent({
        ...content[0],
        text: PostContent.processTextForNetwork(
          content[0].text,
          this.network,
          content[0].attachments
        )
      })

      return this.addContentEditor(processedContent)
    }

    const canonicalContent = this.store.getContentForNetwork('canonical')
    const mergedContent = this.store.addContent(this.mergeContent(canonicalContent))

    return this.addContentEditor(mergedContent)
  }

  sync() {
    if (!this.contentEditor.isSynced()) return

    this.updateContentFromCanonical()
    // Use the debounced version of rerender
    this.debouncedRerender()
  }

  onCanonicalAttachmentsUpdated(content) {
    if (!this.contentEditor.isSynced()) return

    this.updateContentFromCanonical()
    this.contentEditor.updateAttachments()
  }

  onCanonicalAttachmentRemoved({ attachment, _content }) {
    if (!this.contentEditor.isSynced()) return

    this.contentEditor.content.removeAttachment(attachment.id)

    this.updateContentFromCanonical()
    this.contentEditor.updateAttachments()
  }

  onCanonicalLinkEmbedded({ content, linkAttachment }) {
    if (!this.contentEditor.isSynced()) return

    this.contentEditor.linkControls.replaceCurrentAttachment(linkAttachment)

    // Update content from canonical to properly process text with URL removal
    this.updateContentFromCanonical()
  }

  updateContentFromCanonical() {
    const canonicalContent = this.canonicalContent()
    const mergedContent = this.mergeContent(canonicalContent)

    this.contentEditor.content = mergedContent
    this.contentEditor.attachments = mergedContent.attachments

    const content = this.store.updateContent(this.network, 0, mergedContent)

    this.events.topic(this.network).emit('content:edit', content)

    return mergedContent
  }

  mergeContent(content) {
    const networkContent = content.map(c => PostContent.fromCanonical(this.network, c))

    // Process the merged text for network-specific requirements
    const mergedText = networkContent.map(c => c.text).join("\n\n")
    const mergedAttachments = content.flatMap(c => c.attachments)
    const processedText = PostContent.processTextForNetwork(
      mergedText,
      this.network,
      mergedAttachments
    )

    const mergedContent = new PostContent({
      network: this.network,
      text: processedText,
      attachments: mergedAttachments,
      meta: content.map(c => c.meta).reduce((acc, meta) => merge({}, acc, meta), {}),
      sync: this.contentEditor?.content.sync
    })

    return mergedContent
  }

  canonicalContent() {
    return this.store.getContentForNetwork("canonical")
  }

  addContentEditor(content, options = {}) {
    const editorContainer = document.createElement('div')

    editorContainer.className = 'post-editor long-form-post-editor group/editor'

    this.wrapper.appendChild(editorContainer)

    const contentEditor = new LongFormContentEditor(editorContainer, {
      content,
      selectedNetworks: this.selectedNetworks,
      wrapper: this.wrapper,
      charCounter: this.charCounter,
      events: this.events,
      network: this.network,
      attachments: this.attachments,
      store: this.store
    })

    this.contentEditors.push(contentEditor)
    this.contentEditor = contentEditor

    return contentEditor
  }

  rerender() {
    this.contentEditor.destroy()
    this.wrapper.innerHTML = ''

    const content = this.store.getContentForNetwork(this.network)[0]

    this.addContentEditor(content)

    return this
  }
}

