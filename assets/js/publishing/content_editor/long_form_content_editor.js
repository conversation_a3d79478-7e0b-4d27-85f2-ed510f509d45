import { ContentEditor } from '../content_editor'
import { PostContent } from '../../models/post'

export class LongFormContentEditor extends ContentEditor {
  setupDOM() {
    super.setupDOM()

    const editorWrapper = this.container.querySelector('[data-editor-wrapper]')
    if (editorWrapper) {
      editorWrapper.classList.add('long-form-content')

      const continueButton = editorWrapper.querySelector('[data-continue-block]')
      if (continueButton) {
        continueButton.remove()
      }

      const moveButtons = editorWrapper.querySelectorAll('[data-move-up], [data-move-down]')
      moveButtons.forEach(button => button.remove())

      const editorContent = editorWrapper.querySelector('.editor-content')

      if (editorContent) {
        editorContent.classList.remove('min-h-[100px]')
        editorContent.classList.add('min-h-[300px]')
      }

      const proseMirror = editorContent.querySelector('.ProseMirror')
      if (proseMirror) {
        proseMirror.classList.remove('min-h-[100px]')
        proseMirror.classList.add('min-h-[300px]')
      }
    }
  }

  processContent(text, { meta, order }) {
    const processedText = PostContent.processTextForNetwork(text, this.network, this.content.attachments)

    const content = processedText
      .split(/\n\n+/)
      .map((blockText, index) => (super.processContent(blockText, { meta, order: index })))

    return content
  }
}
