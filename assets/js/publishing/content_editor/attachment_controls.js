import { v4 as uuidv4 } from 'uuid'
import Sortable from 'sortablejs'

export class AttachmentControls {
  static SPINNER_SVG = `
    <div class="relative">
      <svg class="h-10 w-10 text-gray-500" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3"></circle>
        <path class="opacity-75 progress-circle" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
          d="M 12 2 A 0 0 0 0 1 12 2">
        </path>
      </svg>
      <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-[9px] font-medium text-gray-500 progress-text">0%</span>
    </div>
  `

  constructor(editorContainer, options = {}) {
    this.container = editorContainer.querySelector('[data-attachment-controls]')
    this.content = options.content
    this.events = options.events
    this.topic = options.topic
    this.attachments = options.attachments
    this.pendingUploads = new Set()
    this.network = this.content.network
    this.attachmentHandler = options.attachmentHandler
    this.uploadProgress = new Map()

    this.content.attachments.forEach(attachment => {
      if (attachment.type !== 'link') {
        this.content.updateAttachment(attachment.id, this.attachments.getAttachment(attachment.id))
      }
    })

    this.setupEventHandlers()
    this.setupEventListeners()
    this.initSortable()
    this.updatePreview()
    this.updateDisabledState()
  }

  destroy() {
    this.events.unregister(this)
    this.topic.unregister(this)
    this.container.remove()
    this.modal?.remove()
  }

  initSortable() {
    const previewContainer = this.container.querySelector('.attachments-preview')
    this.sortable = new Sortable(previewContainer, {
      animation: 150,
      ghostClass: 'opacity-50',
      onEnd: (evt) => {
        const attachments = Array.from(previewContainer.querySelectorAll('[data-attachment-id]'))
          .map((el, index) => {
            const attachment = this.content.attachments.find(a => a.id === el.dataset.attachmentId)
            if (attachment) {
              attachment.order = index
              return attachment
            }
            return null
          })
          .filter(Boolean)

        this.content.withAttachments(attachments)
        this.events.topic(this.network).emit('content:attachments_updated', this.content)

        this.updatePreview()
      }
    })
  }

  setupEventListeners() {
    const uploadButton = this.container.querySelector('.upload-button')
    const previewContainer = this.container.querySelector('.attachments-preview')

    uploadButton.addEventListener('click', () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = 'image/*,video/*'
      input.style.display = 'none'
      document.body.appendChild(input)

      input.addEventListener('change', () => {
        if (input.files.length > 0) {
          const fileData = Array.from(input.files).map(file => {
            const uuid = uuidv4()
            this.pendingUploads.add(uuid)
            return { id: uuid, file }
          })
          this.topic.emit('attachments:upload_requested', fileData)
        }
        input.remove()
      })

      input.click()
    })

    previewContainer.addEventListener('click', (e) => {
      const removeButton = e.target.closest('[data-remove-attachment]')
      const editButton = e.target.closest('[data-edit-attachment]')

      if (removeButton) {
        const attachmentId = removeButton.dataset.removeAttachment
        const attachment = this.content.attachments.find(a => a.id === attachmentId)

        this.content.removeAttachment(attachmentId)
        this.onAttachmentRemoved(attachment)

        this.topic.emit('attachments:remove_requested', attachment.id)
      } else if (editButton) {
        const attachmentId = editButton.dataset.editAttachment
        const attachment = this.content.attachments.find(a => a.id === attachmentId)

        if (attachment) {
          if (!this.modal) {
            this.setupModal()
          }

          this.openModal(attachment)
        }
      }
    })
  }

  updatePreview() {
    const mediaAttachments = this.content.attachments
      .filter(a => a.type !== 'link')
      .sort((a, b) => a.order - b.order)

    const previewContainer = this.container.querySelector('.attachments-preview')
    previewContainer.innerHTML = mediaAttachments
      .map(attachment => this.renderAttachmentPreview(attachment))
      .join('')
  }

  renderAttachmentPreview(attachment) {
    return `
      <div
        class="group/attachment relative w-10 h-10 cursor-move transition-transform duration-200"
        data-attachment-id="${attachment.id}"
        data-attachment-order="${attachment.order}"
      >
        ${this.renderAttachmentContent(attachment)}
        ${this.renderRemoveButton(attachment.id)}
        ${attachment.type === 'image' ? this.renderEditButton(attachment.id) : ''}
      </div>
    `
  }

  setupModal() {
    const modal = document.createElement('div')

    modal.className = 'fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center hidden z-50'
    modal.setAttribute('data-alt-text-modal', '')
    modal.setAttribute('id', 'attachment-editor-modal')
    modal.setAttribute('data-network', this.network)

    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 shadow-xl">
        <div class="mb-4">
          <img class="max-h-64 rounded-lg mx-auto transition-opacity duration-200" src="" alt="" data-modal-image>
        </div>
        <div class="mb-4">
          <textarea
            placeholder="Enter alt text"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent resize-y"
            data-alt-text-input
          ></textarea>
        </div>
        <div class="flex justify-end gap-2">
          <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600" data-modal-cancel>
            Cancel
          </button>
          <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 rounded-md hover:bg-blue-700 dark:hover:bg-blue-400" data-modal-save>
            Save
          </button>
        </div>
      </div>
    `

    document.body.appendChild(modal)
    this.modal = modal

    modal.querySelector('[data-modal-cancel]').addEventListener('click', (e) => {
      e.stopPropagation()
      e.preventDefault()

      this.closeModal()
    })
    modal.querySelector('[data-modal-save]').addEventListener('click', async (e) => {
      e.stopPropagation()
      e.preventDefault()

      await this.saveAltText()
    })

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        e.stopPropagation()
        e.preventDefault()

        this.closeModal()
      }
    })
  }

  openModal(attachment) {
    this.currentAttachment = attachment

    const modal = this.modal
    const image = modal.querySelector('[data-modal-image]')
    const input = modal.querySelector('[data-alt-text-input]')

    // Show loading state
    image.style.opacity = '0'
    image.parentElement.classList.add('relative')
    const loadingSpinner = document.createElement('div')
    loadingSpinner.className = 'absolute inset-0 flex items-center justify-center'
    loadingSpinner.innerHTML = AttachmentControls.SPINNER_SVG
    image.parentElement.appendChild(loadingSpinner)

    // Preload the modal image
    const preloadImage = new Image()
    preloadImage.onload = () => {
      image.src = preloadImage.src
      image.style.opacity = '1'
      loadingSpinner.remove()
    }
    preloadImage.src = this.getModalPreviewUrl(attachment.sourceUrl)

    input.value = attachment.metadata.alt || ''
    modal.classList.remove('hidden')
  }

  closeModal() {
    this.modal.remove()
    this.modal = null
    this.currentAttachment = null
  }

  async saveAltText() {
    const input = this.modal.querySelector('[data-alt-text-input]')
    const alt = input.value.trim()

    if (this.currentAttachment) {
      const updatedAttachment = this.attachments.setMetadata(this.currentAttachment.id, { alt })

      await this.attachmentHandler.updateAttachment(updatedAttachment.id, updatedAttachment.toJSON())

      this.content.updateAttachment(updatedAttachment.id, updatedAttachment)

      this.closeModal()
      this.updatePreview()
    }
  }

  onAttachmentsUpdated(attachments) {
    // Only update if attachments are not being removed
    if (!this._isRemoving) {
      attachments.forEach(attachment => {
        this.content.addAttachment(attachment)
        this.validateMixedAttachments()
        this.updatePreview()
      })
    }
  }

  validateMixedAttachments() {
    const attachments = this.content.attachments.filter(a => a.type !== 'link')
    const hasVideos = attachments.some(a => a.type === 'video')
    const hasImages = attachments.some(a => a.type === 'image')

    if (hasVideos && hasImages) {
      this.topic.emit('editor:show_error', 'You cannot mix videos and images in the same post. Please choose either images or a single video.')
      return false
    }

    return true
  }

  onAttachmentUploaded(attachment) {
    if (this.pendingUploads.delete(attachment.id)) {
      const finalizedAttachment = this.attachments.finalize(attachment)

      // Check size limits first
      const sizeFeature = JCP.user.getFeature(finalizedAttachment.type === 'video' ? 'video_size' : 'image_size')
      const sizeLimit = sizeFeature.limit
      const fileSize = finalizedAttachment.metadata.size

      if (fileSize > sizeLimit) {
        const sizeLimitMB = Math.round(sizeLimit / 10240)

        this.topic.emit('editor:show_info', `The uploaded file exceeds your plan's size limit of ${sizeLimitMB}MB.`)

        // Set flag to prevent attachments:updated from processing
        this._isRemoving = true

        // Clean up all references to the attachment
        this.attachments.removeAttachment(finalizedAttachment.id)
        this.content.removeAttachment(finalizedAttachment.id)
        this.updatePreview() // Force preview update to remove the loading state

        // Request server-side deletion
        this.topic.emit('attachments:remove_requested', finalizedAttachment.id)

        // Emit removal event to ensure all components are notified
        this.events.topic(this.network).emit('content:attachment_removed', {
          attachment: finalizedAttachment,
          content: this.content
        })

        // Reset the flag after a short delay to allow all events to process
        setTimeout(() => {
          this._isRemoving = false
        }, 100)

        return
      }

      // Only update content and UI if size is within limits
      this.content.updateAttachment(finalizedAttachment.id, finalizedAttachment)

      // Validate mixed attachments after adding new one
      this.validateMixedAttachments()

      const element = this.container.querySelector(`[data-attachment-id="${finalizedAttachment.id}"] .loading-spinner`)
      if (element) {
        const progressText = element.querySelector('.progress-text')
        if (progressText) {
          progressText.textContent = 'Loading...'
        }
      }

      this.preloadImage(finalizedAttachment).then(() => {
        this.updatePreview()

        if (this.pendingUploads.size === 0) {
          this.events.topic(this.network).emit('content:attachments_updated', this.content)
        }
      })
    }
  }

  async preloadImage(attachment) {
    // For videos, preload the preview_url directly
    if (attachment.type === 'video') {
      if (!attachment.previewUrl) return Promise.resolve();
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve();
        img.src = attachment.previewUrl;
      });
    }

    // For images, use thumbnail
    const url = this.getThumbnailUrl(attachment.sourceUrl);
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve();
      img.src = url;
    });
  }

  onAttachmentRemoved(attachment) {
    const previewElement = this.container.querySelector(`[data-attachment-id="${attachment.id}"]`)
    if (previewElement) {
      this.events.topic(this.network).emit('content:attachment_removed', { attachment, content: this.content })
      previewElement.remove()
    }
  }

  setupEventHandlers() {
    this.events.off('attachment:uploaded', this.onAttachmentUploaded.bind(this))
    this.events.off('attachment:upload_failed', this.onAttachmentUploadFailed.bind(this))
    this.events.off('attachment:progress', this.onAttachmentProgress.bind(this))

    const boundUploadHandler = this.onAttachmentUploaded.bind(this)
    const boundFailureHandler = this.onAttachmentUploadFailed.bind(this)
    const boundProgressHandler = this.onAttachmentProgress.bind(this)

    this.events.on('attachment:uploaded', boundUploadHandler, this)
    this.events.on('attachment:upload_failed', boundFailureHandler, this)
    this.events.on('attachment:progress', boundProgressHandler, this)

    this.topic.on('attachment:removed', this.onAttachmentRemoved.bind(this), this)
    this.topic.on('attachments:updated', this.onAttachmentsUpdated.bind(this), this)
    this.topic.on('clipboard:image_paste', this.onClipboardImagePaste.bind(this), this)

    this.events.topic(this.network).on('content:sync_toggled', this.onSyncToggled.bind(this), this)
  }

  onClipboardImagePaste({ images, content }) {
    if (this.content.order !== content.order) return

    const fileData = Array.from(images).map(file => {
      const uuid = uuidv4()
      this.pendingUploads.add(uuid)
      return { id: uuid, file }
    })

    if (fileData.length > 0) {
      this.topic.emit('attachments:upload_requested', fileData)
    }
  }

  onAttachmentUploadFailed({ id, message }) {
    const attachment = this.attachments.getAttachment(id)

    if (attachment) {
      this.onAttachmentRemoved(attachment)
      this.topic.emit('editor:show_error', message)
    }
  }

  onSyncToggled(content) {
    if (content.order === this.content.order) {
      this.updateDisabledState()
    }
  }

  updateDisabledState() {
    const isDisabled = this.content.sync && !this.content.isCanonical()
    const uploadButton = this.container.querySelector('.upload-button')
    const previewContainer = this.container.querySelector('.attachments-preview')

    if (isDisabled) {
      uploadButton.setAttribute('disabled', '')
      uploadButton.classList.add('opacity-50', 'cursor-not-allowed')
      previewContainer.classList.add('pointer-events-none', 'opacity-50')
    } else {
      uploadButton.removeAttribute('disabled')
      uploadButton.classList.remove('opacity-50', 'cursor-not-allowed')
      previewContainer.classList.remove('pointer-events-none', 'opacity-50')
    }
  }

  renderEditButton(attachmentId) {
    return `
      <button
        type="button"
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-gray-800 text-white rounded-full p-1.5 opacity-0 group-hover/attachment:opacity-100 transition-opacity"
        data-edit-attachment="${attachmentId}"
      >
        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </button>
    `
  }

  renderAttachmentContent(attachment) {
    if (attachment.isLoading) {
      return this.renderLoadingState()
    }

    const isVideo = attachment.type === 'video'
    const mediaClass = 'w-full h-full object-cover rounded-md'

    if (isVideo) {
      return `
        <div class="w-full h-full bg-gray-50 rounded-md">
          ${attachment.previewUrl ? `
            <img class="${mediaClass}" src="${attachment.previewUrl}" alt="" />
          ` : `
            <div class="${mediaClass} flex items-center justify-center bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
          `}
          <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-md" data-video-overlay>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      `
    }

    return `
      <div class="w-full h-full bg-gray-50 rounded-md">
        <img class="${mediaClass}" src="${this.getThumbnailUrl(attachment.sourceUrl)}" alt="${attachment.metadata?.alt || ''}" />
      </div>
    `
  }

  getThumbnailUrl(url) {
    if (!url || !url.includes('/image/upload/')) return url;

    const matches = url.match(/^(.*\/upload\/)(.+)$/);
    if (!matches) return url;

    const [_, baseUrl, rest] = matches;
    return `${baseUrl}c_thumb,w_80,h_80,g_center/${rest}`;
  }

  getModalPreviewUrl(url) {
    // Skip transformation for non-image URLs
    if (!url.includes('/image/upload/')) return url;

    const matches = url.match(/^(.*\/upload\/)(.+)$/);
    if (!matches) return url;

    const [_, baseUrl, rest] = matches;
    return `${baseUrl}c_scale,w_512/${rest}`;
  }

  renderLoadingState() {
    return `
      <div class="w-full h-full flex items-center justify-center bg-gray-50 rounded-md p-1.5 loading-spinner">
        <div class="relative">
          <svg class="h-10 w-10 text-gray-500" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3"></circle>
            <path class="opacity-75 progress-circle" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
              d="M 12 2 A 0 0 0 0 1 12 2">
            </path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-[9px] font-medium text-gray-500 progress-text">0%</span>
        </div>
      </div>
    `
  }

  renderRemoveButton(attachmentId) {
    return `
      <button
        type="button"
        class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5 opacity-0 group-hover/attachment:opacity-100 transition-opacity"
        data-remove-attachment="${attachmentId}"
      >
        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    `
  }

  getAttachments() {
    return this.content.attachments
  }

  updateAttachments(attachments) {
    this.content.withAttachments(attachments)
    this.updatePreview()
  }

  updateProgress(attachmentId, progress) {
    this.uploadProgress.set(attachmentId, progress)
    const element = this.container.querySelector(`[data-attachment-id="${attachmentId}"] .loading-spinner`)
    if (element) {
      const progressCircle = element.querySelector('.progress-circle')
      const progressText = element.querySelector('.progress-text')
      const svg = element.querySelector('svg')

      if (progressCircle && progressText && svg) {
        if (progress === 100) {
          progressText.textContent = '100%'
          svg.classList.add('animate-pulse')
          svg.classList.remove('text-gray-500')
          svg.classList.add('text-green-500')
        } else {
          svg.classList.remove('animate-pulse')
          svg.classList.remove('text-green-500')
          svg.classList.add('text-gray-500')

          const radius = 10
          const angle = (progress / 100) * 360

          const x = 12 + radius * Math.cos((angle - 90) * Math.PI / 180)
          const y = 12 + radius * Math.sin((angle - 90) * Math.PI / 180)

          const largeArcFlag = angle > 180 ? 1 : 0

          progressCircle.setAttribute('d', `M 12 2 A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x} ${y}`)
          progressText.textContent = `${Math.round(progress)}%`
        }
      }
    }
  }

  onAttachmentProgress({ id, progress }) {
    this.updateProgress(id, progress)
  }
}
