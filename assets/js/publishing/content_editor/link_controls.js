export class LinkControls {
  static SPINNER_SVG = `
    <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  `

  static DEFAULT_THUMBNAIL = `
    <div class="w-24 h-24 bg-gray-100 flex items-center justify-center">
      <svg class="w-12 h-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    </div>
  `

  constructor(editorContainer, options = {}) {
    this.container = editorContainer.querySelector('[data-link-preview]')
    this.content = options.content
    this.events = options.events
    this.topic = options.topic
    this.attachments = options.attachments

    this.linkAttachment = null
    this.network = this.content.network
    this.editorLinks = new Set()
    this.linkAttachmentHistory = new Map()

    this.attachmentHandler = options.attachmentHandler

    this.content.attachments.forEach(attachment => {
      if (attachment.type === 'link') {
        this.editorLinks.add(attachment.metadata.url)
        this.linkAttachmentHistory.set(attachment.metadata.url, attachment)
        this.linkAttachment = attachment

        this.content.updateAttachment(attachment.id, this.attachments.getAttachment(attachment.id))
      }
    })

    this.setupEventHandlers()
    this.setupEventListeners()

    if (this.linkAttachment) {
      this.updatePreview()
    }

    this.updateDisabledState()

    this.topic.on('links:error', this.onLinkError.bind(this), this)
  }

  destroy() {
    this.events.unregister(this)
    this.topic.unregister(this)
    this.attachmentHandler.destroy()
  }

  onLinksAttached(attachment) {
    this.linkAttachmentHistory.set(attachment.metadata.url, attachment)
    this.replaceCurrentAttachment(attachment)

    this.events.topic(this.network).emit('content:link_embedded', {
      content: this.content,
      linkAttachment: this.linkAttachment
    })
  }

  onLinksSet({ links }) {
    this.editorLinks = new Set(links)
  }

  onLinksAdded(url) {
    if (this.linkAttachment?.metadata?.url != url) {
      this.linkAttachment = { metadata: {} }
      this.updatePreview()

      this.attachmentHandler.createLink(url)
    }

    if (!this.editorLinks.has(url)) {
      this.editorLinks.add(url)
    }
  }

  onAttachmentRemoved(attachment) {
    if (attachment.type === 'link') {
      this.linkAttachment = null
      this.container.innerHTML = ''
      this.linkAttachmentHistory.delete(attachment.metadata.url)
      this.content.removeAttachment(attachment.id)
      this.editorLinks.delete(attachment.metadata.url)
    }
  }

  setupEventHandlers() {
    this.topic.on('attachment:removed', this.onAttachmentRemoved.bind(this), this)
    this.topic.on('links:attached', this.onLinksAttached.bind(this), this)
    this.topic.on('links:added', this.onLinksAdded.bind(this), this)
    this.topic.on('links:set', this.onLinksSet.bind(this), this)
    this.events.topic(this.network).on('content:sync_toggled', this.onSyncToggled.bind(this), this)

    if (this.network !== 'canonical') {
      this.events.topic('canonical').on('content:link_removed', this.onCanonicalLinkRemoved.bind(this), this)
    }
  }

  setupEventListeners() {
    this.container.addEventListener('click', (e) => {
      const removeButton = e.target.closest('[data-remove-attachment]')

      e.preventDefault()
      e.stopPropagation()

      if (removeButton) {
        this.clearCurrentAttachment()
      }
    })
  }

  replaceCurrentAttachment(attachment) {
    this.linkAttachment = attachment
    this.content.updateLinkAttachment(attachment)
    this.updatePreview()
  }

  clearCurrentAttachment() {
    const attachmentId = this.linkAttachment.id
    const url = this.linkAttachment.metadata.url
    const attachment = this.linkAttachment

    this.content.updateLinkAttachment(null)
    this.linkAttachment = null
    this.container.innerHTML = ''
    this.linkAttachmentHistory.delete(url)
    this.editorLinks.delete(url)

    this.topic.emit('attachments:remove_requested', attachmentId)
    this.events.topic(this.network).emit('content:link_removed', { content: this.content, linkAttachment: attachment })
  }

  navigateLinks(direction) {
    if (this.editorLinks.size <= 1) return

    const links = Array.from(this.editorLinks)
    const currentUrl = this.linkAttachment?.metadata?.url
    const currentIndex = currentUrl ? links.indexOf(currentUrl) : -1
    let newIndex

    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? links.length - 1 : currentIndex - 1
    } else {
      newIndex = currentIndex === links.length - 1 ? 0 : currentIndex + 1
    }

    const newUrl = links[newIndex]
    const existingAttachment = this.linkAttachmentHistory.get(newUrl)

    if (existingAttachment) {
      this.replaceCurrentAttachment(existingAttachment)

      this.events.topic(this.network).emit('content:link_embedded', {
        content: this.content,
        linkAttachment: this.linkAttachment
      })
    } else {
      this.attachmentHandler.createLink(newUrl)
    }
  }

  updatePreview() {
    if (!this.linkAttachment) {
      this.container.innerHTML = ''
      return
    }

    // Show loading state if metadata is not yet available
    if (!this.linkAttachment.metadata?.title) {
      this.container.innerHTML = `
        <div class="rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div class="px-8 py-4">
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                ${LinkControls.SPINNER_SVG}
              </div>
              <div class="flex-1 min-w-0">
                <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
                <div class="mt-2 space-y-2">
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-full"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-4/5"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `
      return
    }

    this.container.innerHTML = `
      <div class="rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 relative group">
        <button
          type="button"
          class="absolute left-0 inset-y-0 w-8 bg-gray-100/75 dark:bg-gray-700/75 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-l-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          data-prev-link
        >
          <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>

        <button
          type="button"
          class="absolute right-0 inset-y-0 w-8 bg-gray-100/75 dark:bg-gray-700/75 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-r-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          data-next-link
        >
          <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>

        <div class="flex items-start px-8 py-4">
          <div class="flex-shrink-0">
            ${this.linkAttachment.metadata.image_url ? `
              <img
                src="${this.linkAttachment.metadata.image_url}"
                class="w-24 h-24 object-cover rounded"
                alt="${this.linkAttachment.metadata.title}"
              />
            ` : LinkControls.DEFAULT_THUMBNAIL}
          </div>
          <div class="flex-1 pl-4 min-w-0">
            <div class="flex items-start justify-between gap-2">
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-gray-900 dark:text-gray-100 truncate">
                  ${this.linkAttachment.metadata.title}
                </h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                  ${this.linkAttachment.metadata.description}
                </p>
                <p class="mt-1 text-xs text-gray-400 dark:text-gray-500">
                  ${new URL(this.linkAttachment.metadata.url).hostname}
                </p>
              </div>
              <button
                type="button"
                class="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                data-remove-attachment="${this.linkAttachment.id}"
              >
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    `

    // Add navigation event listeners
    const prevButton = this.container.querySelector('[data-prev-link]')
    const nextButton = this.container.querySelector('[data-next-link]')

    if (prevButton) {
      prevButton.addEventListener('click', () => this.navigateLinks('prev'))
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => this.navigateLinks('next'))
    }
  }

  onLinkError({ error }) {
    this.linkAttachment = null
    this.container.innerHTML = ''

    this.topic.emit('editor:show_error', error)
  }

  onSyncToggled(content) {
    if (content.order === this.content.order) {
      this.updateDisabledState()
    }
  }

  updateDisabledState() {
    const isDisabled = this.content.sync && !this.content.isCanonical()

    if (this.container) {
      if (isDisabled) {
        this.container.classList.add('pointer-events-none', 'opacity-50')
      } else {
        this.container.classList.remove('pointer-events-none', 'opacity-50')
      }
    }
  }

  onCanonicalLinkRemoved({ content, linkAttachment }) {
    if (content.order === this.content.order && this.content.sync) {
      this.linkAttachment = null
      this.container.innerHTML = ''
      this.linkAttachmentHistory.delete(linkAttachment.metadata.url)
      this.content.removeAttachment(linkAttachment.id)
      this.editorLinks.delete(linkAttachment.metadata.url)
    }
  }
}
