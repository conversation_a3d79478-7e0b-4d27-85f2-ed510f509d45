export class UpgradeModal {
  constructor() {
    this.modal = null
  }

  formatResetPeriod(period) {
    if (!period) return ''
    return period.toLowerCase().replace('ly', '')
  }

  show(feature) {
    const isDark = document.documentElement.classList.contains('dark')
    const modal = document.createElement('div')
    modal.className = 'fixed inset-0 z-50 overflow-y-auto'

    // Check if this is a usage-based feature
    const hasUsage = feature.limit !== undefined && feature.currentUsage !== undefined

    modal.innerHTML = `
      <div class="fixed inset-0 bg-gray-500 dark:bg-black bg-opacity-75 dark:bg-opacity-80 transition-opacity" aria-hidden="true"></div>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div>
              <div class="mx-auto text-center mb-4">
                <span class="text-5xl" role="img" aria-label="Scream Cat">🙀</span>
              </div>
              <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">
                  ${feature.featureName}
                </h3>
                <div class="mt-2">
                  ${hasUsage ? `
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      You've reached your limit of ${feature.limit} ${feature.featureName.toLowerCase()} per ${this.formatResetPeriod(feature.resetPeriod)}.
                      Upgrade your plan to continue using this feature.
                    </p>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      Current usage: ${feature.currentUsage} / ${feature.limit}
                    </p>
                  ` : `
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      This feature is not available in your current plan. Upgrade to access ${feature.featureName}.
                    </p>
                  `}
                </div>
              </div>
            </div>
            <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
              <a
                href="/plans"
                class="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
              >
                Upgrade Plan
              </a>
              <button
                type="button"
                class="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:col-start-1 sm:mt-0"
                data-close-modal
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(modal)
    this.modal = modal

    // Add event listeners
    const closeButton = modal.querySelector('[data-close-modal]')
    closeButton.addEventListener('click', () => this.close())

    // Close on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.close()
      }
    })
  }

  close() {
    if (this.modal) {
      this.modal.remove()
      this.modal = null
    }
  }
}
