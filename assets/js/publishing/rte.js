import { Editor } from '@tiptap/core'

export class RTE {
  constructor(element, options = {}) {
    this.element = element

    this.content = options.content
    this.onUpdate = options.onUpdate
    this.onPaste = options.onPaste
    this.processContent = options.processContent
    this.extensions = options.extensions
    this.editable = options.editable
    this.network = options.network

    this.initializeEditor()
    this.setupClickListener()
  }

  setupClickListener() {
    this.element.addEventListener('click', (event) => {
      // Only focus if we're clicking the wrapper itself, not its children
      if (event.target === this.element) {
        this.editor.commands.focus()
      }
    })
  }

  initializeEditor() {
    this.editor = new Editor({
      element: this.element,
      editable: this.editable,
      extensions: this.extensions,
      content: this.createDocument(),
      editorProps: {
        attributes: {
          class: 'focus:outline-none max-w-none min-h-[100px] [&[contenteditable="false"]]:text-gray-500',
        },
        handleDOMEvents: {
          focus: (view, event) => {
            const wrapper = event.target.closest('#tiptap-editor-container [data-network] > div')
            wrapper.classList.add('is-focused')
            return false
          },
          blur: (view, event) => {
            const wrapper = event.target.closest('#tiptap-editor-container [data-network] > div')
            wrapper.classList.remove('is-focused')
            return false
          }
        },
        handlePaste: this.onPaste
      },
      onUpdate: this.onUpdate
    })

    this.editor.mentions = this.content.meta.mentions
  }

  getText() {
    let text = this.editor.getText().trim()
    return text
  }

  getMentions() {
    return Array.from(this.editor.storage.mention || new Set())
  }

  updateContent(content) {
    this.content = content
    this.editor.commands.setContent(this.createDocument())
  }

  clearContent() {
    this.editor.commands.clearContent()
    this.editor.mentions = []
    return this
  }

  createDocument() {
    const items = Array.isArray(this.content) ? this.content : [this.content]

    const doc = {
      type: 'doc',
      content: items.flatMap(item =>
        this.processContent(item.text, { meta: item.meta, order: item.order })
      )
    }

    if (doc.content.length === 0) {
      doc.content.push({
        type: 'paragraph',
        content: []
      })
    }

    return doc
  }

  destroy() {
    if (this.editor) {
      this.editor.destroy()
      this.editor = null
    }
  }
}
