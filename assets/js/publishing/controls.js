import { Scheduling } from '../extensions/scheduling'
import { AutoScheduler } from './auto_scheduler'

export class Controls {
  constructor(element, options = {}) {
    this.element = element
    this.post = options.post
    this.events = options.events
    this.hooks = options.hooks
    this.network = 'canonical'
    this.selectedNetworks = options.selectedNetworks
    this.scheduleSlots = options.scheduleSlots || []

    this.buttons = {
      save: this.element.querySelector('[data-save-button]'),
      publish: this.element.querySelector('[data-publish-button]'),
      openScheduling: this.element.querySelector('[data-open-scheduling]'),
      autoSchedule: this.element.querySelector('[data-auto-schedule-button]')
    }

    if (this.buttons.openScheduling) {
      this.scheduling = new Scheduling({ events: this.events, post: this.post })
    }

    if (this.buttons.autoSchedule) {
      this.autoScheduling = new AutoScheduler({
        events: this.events,
        post: this.post,
        suggestedSlots: this.scheduleSlots
      })
    }

    if (this.buttons.autoSchedule && this.post.schedules.length > 0) {
      this.buttons.autoSchedule.textContent = 'Edit Schedule'
    }

    this.post.content.forEach(content => {
      this.events.topic(content.network).emit('content:edit', content)
    })

    this.setupEventHandlers()
    this.setupEventListeners()
    this.updateButtonStates()
  }

  onContentEdit() {
    this.updateButtonStates()
    // Update auto-schedule button text
    if (this.buttons.autoSchedule) {
      this.buttons.autoSchedule.textContent = this.post.schedules.length > 0 ? 'Edit Schedule' : 'Auto-schedule'
    }
  }

  onContentCharCountUpdated({ count, network, isExceeded }) {
    this.updateButtonStates()
  }

  onNetworkChanged({ network }) {
    this.network = network
  }

  onNetworkEnabled({ network }) {
    this.scheduling?.initializeNetworkScheduler(network)
  }

  onNetworkDisabled({ network }) {
    this.scheduling?.removeNetworkScheduler(network)
  }

  setupEventHandlers() {
    this.events.topic('canonical').on('content:edit', this.onContentEdit.bind(this), this)
    this.events.topic('canonical').on('content:char_count_updated', this.onContentCharCountUpdated.bind(this), this)

    this.post.socialNetworks.forEach(network => {
      this.events.topic(network).on('content:edit', this.onContentEdit.bind(this), this)
      this.events.topic(network).on('content:char_count_updated', this.onContentCharCountUpdated.bind(this), this)
    })

    this.events.on('network:enabled', this.onNetworkEnabled.bind(this), this)
    this.events.on('network:disabled', this.onNetworkDisabled.bind(this), this)
    this.events.on('network:changed', this.onNetworkChanged.bind(this), this)
  }

  setupEventListeners() {
    if (this.buttons.save) {
      this.buttons.save.addEventListener('click', () => {
        if (!this.post.isSaveable()) return
        this.post.setSchedules([])
        this.save({ status: 'draft', schedules: [] })
      })
    }

    if (this.buttons.publish) {
      this.buttons.publish.addEventListener('click', () => {
        if (!this.post.isPublishable()) return

        if (confirm('Are you sure you want to publish this post now? This action cannot be undone.')) {
          this.save({ status: 'publishing', publish_now: true })
        }
      })
    }

    if (this.buttons.openScheduling) {
      this.buttons.openScheduling.addEventListener('click', () => {
        this.scheduling.open(this.network)
      })
    }

    if (this.buttons.autoSchedule) {
      this.buttons.autoSchedule.addEventListener('click', () => {
        if (!this.post.isPublishable()) return
        this.autoScheduling.open()
      })
    }

    this.events.on('schedules:set', ({ schedules }) => {
      this.post.setSchedules(schedules)
      // Update auto-schedule button text
      if (this.buttons.autoSchedule) {
        this.buttons.autoSchedule.textContent = schedules.length > 0 ? 'Edit Schedule' : 'Auto-schedule'
      }
      this.updateButtonStates()
      this.save({ schedule: true })
    }, this)

    this.events.on('auto-schedule:confirm', () => {
      this.updateButtonStates()
      this.save({ auto_schedule: true })
    }, this)
  }

  save(params = {}) {
    const payload = { ...this.post.toJSON(), ...params }

    this.hooks.pushEvent('save', { ...payload, ...params }, (response) => {
      this.updateButtonStates()
    })
  }

  validateForm() {
    return this.post.isSaveable()
  }

  getNetworkLabel(networkId) {
    const networks = {
      canonical: 'All',
      bsky: 'Bluesky',
      mastodon: 'Mastodon',
      linkedin: 'LinkedIn',
      x: 'X (Twitter)'
    }
    return networks[networkId] || networkId
  }

  updateButtonStates() {
    this.buttons.save.disabled = !this.post.isSaveable()
    this.buttons.publish.disabled = !this.post.isPublishable()

    if (this.buttons.openScheduling) {
      this.buttons.openScheduling.disabled = !this.post.isSaveable()
    }

    if (this.buttons.autoSchedule) {
      this.buttons.autoSchedule.disabled = !this.post.isPublishable()
    }
  }

  destroy() {
    if (this.scheduling) {
      this.scheduling.destroy()
    }
    if (this.autoScheduling) {
      this.autoScheduling.destroy()
    }
    this.events.unregister(this)
  }
}
