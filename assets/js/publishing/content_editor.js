import { v4 as uuidv4 } from 'uuid'
import { RTE } from './rte'
import { Attachments } from '../extensions/attachments'
import { AttachmentControls } from './content_editor/attachment_controls'
import { LinkControls } from './content_editor/link_controls'
import { XChar<PERSON>ounter } from '../extensions/char_counters/x'
import { LinkedInCharCounter } from '../extensions/char_counters/linkedin'
import { BlueskyCharCounter } from '../extensions/char_counters/bsky'
import { MastodonCharCounter } from '../extensions/char_counters/mastodon'
import { ContentParser } from './rte/content_parser'
import { merge } from 'lodash'

// Tiptap extensions
import { StarterKit } from '@tiptap/starter-kit'
import { Placeholder } from '@tiptap/extension-placeholder'
import { CustomLink } from './rte/custom_link'
import { SocialProfile } from './rte/social_profile'
import { CustomParagraph } from './rte/paragraph'

export class ContentEditor {
  static SPINNER_SVG = `
    <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  `

  constructor(container, options = {}) {
    this.container = container
    this.options = options

    this.store = options.store
    this.content = options.content
    this.selectedNetworks = options.selectedNetworks
    this.wrapper = options.wrapper
    this.charCounter = options.charCounter
    this.network = options.network
    this.attachments = options.attachments

    this.id = `content_editor:${uuidv4()}`
    this.events = options.events
    this.topic = this.events.topic(this.id)

    this.order = this.content.order

    // Initialize counters array
    this.counters = []

    this.setupDOM()

    this.attachmentHandler = new Attachments({
      events: this.events,
      topic: this.topic,
      store: this.attachments,
      postStore: this.store
    })

    this.attachmentControls = new AttachmentControls(this.container, {
      content: this.content,
      events: this.events,
      topic: this.topic,
      attachments: this.attachments,
      attachmentHandler: this.attachmentHandler
    })

    this.linkControls = new LinkControls(this.container, {
      content: this.content,
      events: this.events,
      topic: this.topic,
      attachments: this.attachments,
      attachmentHandler: this.attachmentHandler
    })

    this.extensions = [
      StarterKit.configure({
        bold: false,
        italic: false,
        strike: false,
        underline: false,
        code: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        heading: false,
        blockquote: false,
        horizontalRule: false,
        codeBlock: false,
        paragraph: false,
        hardBreak: { keepMarks: false }
      }),
      Placeholder.configure({
        placeholder: 'Write something...',
        emptyEditorClass: 'is-editor-empty',
        emptyNodeClass: 'is-empty',
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
        includeChildren: true
      }),
      CustomParagraph,
      CustomLink.configure({ events: this.topic }),
      SocialProfile.configure({
        HTMLAttributes: {
          class: `mention inline-flex items-center
          text-blue-600 dark:text-blue-400
          font-semibold`,
          contenteditable: 'false',
          'data-network': this.network
        }
      })
    ]

    this.initializeRTE()

    this.counters = this.initializeCounters()

    if (this.network === 'canonical') {
      this.events.on('network:enabled', this.onNetworkEnabled.bind(this), this)
      this.events.on('network:disabled', this.onNetworkDisabled.bind(this), this)

      this.selectedNetworks.forEach(network => {
        this.events.topic(network).on('content:sync_toggled', this.onSyncToggled.bind(this), this)
      })
    }
  }

  onSyncToggled(content) {
    if (this.network === 'canonical') {
      const network = content.network;
      const contentOrder = content.order;

      const isLinkedIn = network === 'linkedin';
      const orderToUse = isLinkedIn ? 0 : contentOrder;

      if (isLinkedIn || this.order === contentOrder) {
        if (content.sync) {
          const existingCounter = this.counters.find(counter =>
            counter.network === network
          );

          if (!existingCounter) {
            this.createCounter(network, orderToUse);
          }
        } else {
          this.removeCounter(network);
        }
      }
    }
  }

  // Create a counter for a specific network and order
  createCounter(network, order) {
    const CounterClasses = {
      x: XCharCounter,
      linkedin: LinkedInCharCounter,
      bsky: BlueskyCharCounter,
      mastodon: MastodonCharCounter
    };

    if (CounterClasses[network]) {
      const countersContainer = this.container.querySelector('[data-char-counters]');
      const counterContainer = document.createElement('div');

      counterContainer.className = 'counter-container';
      counterContainer.dataset.network = network;
      countersContainer.appendChild(counterContainer);

      const counter = new CounterClasses[network]({
        events: this.events,
        editorId: this.id,
        network: network,
        store: this.store,
        order: order,
        container: counterContainer
      });

      this.counters.push(counter);
      return counter;
    }

    return null;
  }

  // Remove a counter for a specific network
  removeCounter(network) {
    const index = this.counters.findIndex(counter =>
      counter.network === network
    );

    if (index !== -1) {
      this.counters[index].destroy();
      this.counters.splice(index, 1);
    }
  }

  onNetworkEnabled({ network }) {
    if (this.network === 'canonical') {
      const isLinkedIn = network === 'linkedin';
      const order = isLinkedIn ? 0 : this.content.order;
      this.createCounter(network, order);
    }
  }

  onNetworkDisabled({ network }) {
    if (this.network === 'canonical') {
      this.removeCounter(network);
    }
  }

  setupDOM() {
    this.container.dataset.synced = this.content.sync

    this.container.innerHTML = `
      <div class="content-block relative" data-editor-id="${this.id}" data-order="${this.order}">
        <div class="content-block-wrapper relative group">
          <div class="content-block-inner relative group threaded-content transition-colors duration-150" data-editor-wrapper>
            <div class="editor-message hidden absolute inset-x-0 top-0 z-50 rounded-t-lg overflow-hidden">
              <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-white/80 hover:text-white" data-close-message>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              <div class="message-content"></div>
            </div>
            ${this.network !== 'canonical' ? `
            <div class="absolute inset-0 z-10 transition-all duration-150 ${this.content.sync ? 'bg-gray-900/30 backdrop-blur-[2px]' : ''}" data-sync-overlay style="pointer-events: ${this.content.sync ? 'auto' : 'none'}">
              <div class="absolute transition-all duration-150 ${this.content.sync
          ? 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'
          : 'left-2.5 top-2.5 opacity-0 group-hover:opacity-100 transition-opacity duration-150'
        }" data-sync-label style="pointer-events: auto">
                <label class="flex items-center gap-2 cursor-pointer ${this.content.sync
          ? 'px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700'
          : 'flex items-center gap-1.5'
        }">
                  <input
                    type="checkbox"
                    class="${this.content.sync
          ? 'h-4 w-4'
          : 'h-3 w-3'
        } rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    data-sync-toggle
                    ${this.content.sync ? 'checked' : ''}
                  >
                  <span class="${this.content.sync
          ? 'text-sm font-medium text-gray-900 dark:text-gray-100'
          : 'text-xs text-gray-500 dark:text-gray-400'
        }">${this.content.sync ? 'Synced with Main post' : 'Sync'}</span>
                  <div class="sync-tooltip relative inline-block">
                    <div class="cursor-help text-gray-400 hover:text-gray-600 peer">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-3.5 h-3.5">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.061-1.061 3 3 0 112.871 5.026v.345a.75.75 0 01-1.5 0v-.5c0-.72.57-1.172 1.081-1.287A1.5 1.5 0 108.94 6.94zM10 15a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden peer-hover:block w-48 z-20 pointer-events-none">
                      <div class="bg-gray-900 text-white text-xs rounded py-1 px-2 text-center">
                        When checked, this editor's content will be synchronized with the Main editor
                        <div class="absolute left-1/2 -translate-x-1/2 top-full">
                          <div class="w-2 h-2 bg-gray-900 transform rotate-45"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </label>
              </div>
            </div>
            ` : ''}
            <div class="absolute right-2.5 top-2.5">
              <button
                type="button"
                class="w-7 h-7 rounded-md flex items-center justify-center bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-red-500 transition-colors duration-150 opacity-0 group-hover:opacity-100"
                data-remove-block
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>

            <div class="flex flex-col p-4 sm:p-6 min-h-[100px]">
              <div class="flex-1">
                <div class="editor-content"></div>
              </div>

              <div class="link-preview-container my-4" data-link-preview></div>

              <div class="flex flex-col gap-4">
                <div class="flex justify-end" data-char-counters></div>

                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2" data-attachment-controls>
                    <button type="button" class="upload-button w-10 h-10 flex items-center justify-center text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100">
                      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </button>
                    <div class="flex gap-2 attachments-preview"></div>
                  </div>

                  <button
                    type="button"
                    class="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    data-continue-block
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    <span>Continue</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `

    this.editorContent = this.container.querySelector('.editor-content')
    this.messageContainer = this.container.querySelector('.editor-message')
    this.messageContent = this.container.querySelector('.message-content')

    // Add event listeners
    this.setupEventListeners()
  }

  setupEventListeners() {
    // Continue button
    const continueButton = this.container.querySelector('[data-continue-block]')
    if (continueButton && this.network !== 'linkedin') {
      continueButton.addEventListener('click', () => {
        const blockIndex = Array.from(this.wrapper.children).indexOf(this.container)

        this.events.topic(this.network).emit('content:continue', {
          afterIndex: blockIndex,
          meta: this.meta
        })
      })
    }

    // Remove button
    const removeButton = this.container.querySelector('[data-remove-block]')
    removeButton.addEventListener('click', () => {
      this.events.topic(this.network).emit('content:remove', this.content)
    })

    const syncToggle = this.container.querySelector('[data-sync-toggle]')
    if (syncToggle) {
      syncToggle.addEventListener('change', (e) => {
        const syncLabel = this.container.querySelector('[data-sync-label]')
        const syncOverlay = this.container.querySelector('[data-sync-overlay]')
        const isChecked = e.target.checked

        // Update data-synced attribute
        this.container.dataset.synced = isChecked

        // Update checkbox size
        syncToggle.className = `${isChecked ? 'h-4 w-4' : 'h-3 w-3'} rounded border-gray-300 text-blue-600 focus:ring-blue-500`

        // Update label styles and position
        syncLabel.className = `absolute transition-all duration-150 ${isChecked
          ? 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'
          : 'left-2.5 top-2.5'
          }`

        const labelContainer = syncLabel.querySelector('label')
        labelContainer.className = `flex items-center gap-2 cursor-pointer ${isChecked
          ? 'px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700'
          : 'flex items-center gap-1.5'
          }`

        // Update text span
        const textSpan = syncLabel.querySelector('span')
        textSpan.className = isChecked ? 'text-sm font-medium text-gray-900 dark:text-gray-100' : 'text-xs text-gray-500 dark:text-gray-400'
        textSpan.textContent = isChecked ? 'Synced with main post' : 'Sync'

        // Update overlay background
        syncOverlay.className = `absolute inset-0 z-10 transition-all duration-150 ${isChecked ? 'bg-gray-900/30 backdrop-blur-[2px]' : ''}`

        // Add a semi-transparent div to prevent editor interaction when synced
        if (isChecked) {
          syncOverlay.style.pointerEvents = 'auto'
        } else {
          syncOverlay.style.pointerEvents = 'none'
          syncLabel.style.pointerEvents = 'auto' // Ensure the sync button remains clickable
        }

        this.content = this.store.updateContent(
          this.network, this.order, { sync: isChecked }
        )

        // Update editor state
        this.rte.editor.setEditable(!isChecked)

        this.events.topic(this.network).emit('content:sync_toggled', this.content)
      })
    }

    // Add message event handlers
    this.topic.on('editor:show_error', this.showError.bind(this))
    this.topic.on('editor:show_info', this.showInfo.bind(this))
    this.topic.on('editor:clear_message', this.clearMessage.bind(this))

    const closeButton = this.container.querySelector('[data-close-message]')
    if (closeButton) {
      closeButton.addEventListener('click', () => this.clearMessage())
    }
  }

  initializeRTE() {
    this.rte = new RTE(this.editorContent, {
      content: this.content,
      network: this.network,
      extensions: this.extensions,
      onUpdate: this.onUpdate.bind(this),
      onPaste: this.onPaste.bind(this),
      processContent: this.processContent.bind(this),
      editable: this.content.isEditable()
    })

    this.rte.editor.on('social_profile:added', (socialProfile) => {
      const index = this.content.meta.mentions.findIndex(m => m.id === socialProfile.id)

      if (index !== -1) {
        this.content.meta.mentions[index] = socialProfile
      } else {
        this.content.meta.mentions.push(socialProfile)
      }

      this.store.updateContent(this.network, this.order, { text: this.getText() })
    })

    this.rte.editor.on('social_profile:updated', (socialProfile) => {
      const index = this.content.meta.mentions.findIndex(m => m.id === socialProfile.id)

      if (index !== -1) {
        this.content.meta.mentions[index] = socialProfile
      }

      this.store.updateContent(this.network, this.order, { text: this.getText() })
    })

    this.rte.editor.on('social_profile:removed', (socialProfile) => {
      const index = this.content.meta.mentions.findIndex(m => m.id === socialProfile.id)

      if (index !== -1) {
        this.content.meta.mentions.splice(index, 1)
        this.store.updateContent(this.network, this.order, { text: this.getText() })
      }
    })

    this.events.topic(this.network).on('content:sync_toggled', (content) => {
      if (content.order == this.content.order) {
        this.rte.editor.setEditable(!content.sync)
      }
    }, this)

    // Handle content:text_updated event
    this.topic.on('content:text_updated', ({ text }) => {
      this.rte.updateContent({ ...this.content, text })
    }, this)

    this.store.updateContent(this.network, this.order, { text: this.getText() })
  }

  isSynced() {
    return this.content.sync
  }

  onUpdate({ editor, transaction }) {
    if (transaction.updated === 0) return

    const text = this.getText()
    const mentions = this.rte.getMentions()

    this.content = this.store.updateContent(this.network, this.order, {
      text,
      meta: merge(this.content.meta, { mentions })
    })

    this.events.topic(this.network).emit('content:edit', this.content)
  }

  onPaste(view, event, slice) {
    if (event.clipboardData && event.clipboardData.files.length > 0) {
      const images = Array.from(event.clipboardData.files).filter(file =>
        file.type.startsWith('image/')
      );

      if (images.length > 0) {
        event.preventDefault();

        this.topic.emit('clipboard:image_paste', {
          images,
          content: this.content
        });

        return true;
      }
    }
  }

  getText() {
    return this.rte.getText()
  }

  getAttachments() {
    return this.attachmentControls.getAttachments()
  }

  updateAttachments(attachments) {
    if (attachments === undefined) {
      this.attachmentControls.updateAttachments(this.content.attachments)
    } else {
      this.attachmentControls.updateAttachments(attachments)
    }
  }

  clear() {
    this.rte.clearContent()
    this.updateAttachments([])
    this.content.meta = { mentions: [] }
    return this
  }

  destroy() {
    this.events.unregister(this)
    this.rte.destroy()
    this.attachmentControls.destroy()
    this.linkControls.destroy()
    this.container.remove()

    return this
  }

  initializeCounters() {
    const countersContainer = this.container.querySelector('[data-char-counters]');

    countersContainer.innerHTML = '';

    let networksToShow = [];

    if (this.network === 'canonical') {
      networksToShow = Array.from(this.selectedNetworks)
        .filter(network => {
          const content = this.store.getContentForNetwork(network, this.content.order)

          if (network !== 'canonical' && (!content || content.sync)) {
            return true
          } else {
            return false
          }
        })
    } else {
      networksToShow = [this.network];
    }

    return networksToShow
      .map(network => {
        const isLinkedIn = network === 'linkedin';
        const order = isLinkedIn ? 0 : this.content.order;

        return this.createCounter(network, order);
      })
      .filter(counter => !!counter);
  }

  processContent(text, { meta, order }) {
    const parser = new ContentParser(text, { meta, order, network: this.network })
    return parser.parse()
  }

  showError(message) {
    this.messageContainer.className = 'editor-message absolute inset-x-0 top-0 z-50 p-3 bg-red-900/60 text-red-50 font-medium text-center shadow-lg backdrop-blur-sm rounded-t-lg overflow-hidden'
    this.messageContent.textContent = message
    this.messageContainer.classList.remove('hidden')
  }

  showInfo(message) {
    this.messageContainer.className = 'editor-message absolute inset-x-0 top-0 z-50 p-3 bg-blue-900/60 text-blue-50 font-medium text-center shadow-lg backdrop-blur-sm rounded-t-lg overflow-hidden'
    this.messageContent.textContent = message
    this.messageContainer.classList.remove('hidden')
  }

  clearMessage() {
    this.messageContainer.classList.add('hidden')
    this.messageContent.textContent = ''
  }
}
