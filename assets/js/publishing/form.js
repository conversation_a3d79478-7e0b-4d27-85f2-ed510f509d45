import { EventBus } from '../extensions/event_bus'
import { Topbar } from './topbar'
import { Controls } from './controls'
import { PostStore } from '../stores/post_store'
import { AttachmentStore } from '../stores/attachment_store'
import { ThreadedPostEditor } from './post_editor/threaded_post_editor'
import { LongFormPostEditor } from './post_editor/long_form_post_editor'
import JCP from '../jcp'
import equal from 'fast-deep-equal'

const AUTO_SAVE_INTERVAL = 1000

export class Form {
  constructor(element, options = {}) {
    this.options = options
    this.events = new EventBus({ debug: false })

    this.store = new PostStore()
    this.isSaving = false

    const postData = element.dataset.post ? JSON.parse(element.dataset.post) : {}
    const cloudinaryData = element.dataset.cloudinary ? JSON.parse(element.dataset.cloudinary) : {}
    const scheduleSlots = element.dataset.scheduleSlots ? JSON.parse(element.dataset.scheduleSlots) : []
    const defaultNetworks = JCP.user.workspace_settings?.default_networks || []

    this.connections = element.dataset.connections ? JSON.parse(element.dataset.connections) : []

    if (postData.social_networks?.length > 0) {
      this.selectedNetworks = new Set(postData.social_networks)
    } else {
      this.selectedNetworks = new Set(
        defaultNetworks.length > 0 ? defaultNetworks : this.connections.map(c => c.network)
      )
    }

    this.store.loadFromJSON(postData, Array.from(this.selectedNetworks))

    JCP.store = this.store
    JCP.user.connections = this.connections
    JCP.config = {
      ...JCP.config,
      cloudinary: cloudinaryData
    }

    this.lastSavedState = this.normalizeState(this.store.post.toJSON())
    this.scheduleSlots = scheduleSlots

    this.attachments = AttachmentStore.fromPost(this.store.post, this.events)

    this.hooks = options.hooks
    this.editors = new Map()

    this.initializeComponents()
    this.setupEventListeners()
    this.startAutoSaveInterval()
  }

  startAutoSaveInterval() {
    this.autoSaveInterval = setInterval(() => {
      this.saveIfNeeded()
    }, AUTO_SAVE_INTERVAL)
  }

  // Normalize state by converting mentions to a stable format
  normalizeState(state) {
    const normalizedState = JSON.parse(JSON.stringify(state))

    // Normalize mentions in content
    normalizedState.content.forEach(content => {
      if (content.meta?.mentions?.length > 0) {
        // Replace mentions with a simplified version that only contains essential data
        content.meta.mentions = content.meta.mentions.map(mention => {
          // Create a simplified mention object with only the essential properties
          const simplifiedMention = {
            id: mention.id,
            name: mention.name,
            bsky_handle: mention.bsky_handle || null,
            mastodon_handle: mention.mastodon_handle || null,
            x_handle: mention.x_handle || null
          }

          // If networks array exists, normalize it by sorting and simplifying
          if (mention.networks && Array.isArray(mention.networks)) {
            simplifiedMention.networks = mention.networks
              .map(network => ({
                id: network.id,
                handle: network.handle
              }))
              .sort((a, b) => a.id.localeCompare(b.id))
          } else {
            simplifiedMention.networks = []
          }

          return simplifiedMention
        })

        // Sort mentions by ID for consistent ordering
        content.meta.mentions.sort((a, b) => a.id - b.id)
      }
    })

    return normalizedState
  }

  saveIfNeeded() {
    if (this.isSaving || !this.store.post.isSaveable()) {
      return
    }

    const currentState = this.store.post.toJSON()
    const normalizedCurrentState = this.normalizeState(currentState)

    if (equal(normalizedCurrentState, this.lastSavedState)) {
      return
    }

    this.isSaving = true

    try {
      this.hooks.pushEvent("save", { ...currentState, auto_save: true }, (response) => {
        if (response?.id) {
          this.store.post.id = response.id
        }

        this.lastSavedState = this.normalizeState(currentState)
        this.isSaving = false
      })
    } catch (error) {
      console.error("Auto-save failed:", error)
      this.isSaving = false
    }
  }

  initializeComponents() {
    const topbarElement = document.querySelector('#editor-topbar')
    this.topbar = new Topbar(topbarElement, {
      selectedNetworks: this.selectedNetworks,
      connections: this.connections,
      events: this.events
    })

    const controlsElement = document.querySelector('#actions')
    this.controls = new Controls(controlsElement, {
      events: this.events,
      selectedNetworks: this.selectedNetworks,
      post: this.store.post,
      hooks: this.hooks,
      scheduleSlots: this.scheduleSlots
    })

    const initialCanonicalContent = this.store.getContentForNetwork('canonical') || []

    this.initializeCanonicalEditor(initialCanonicalContent)

    for (const network of this.store.post.socialNetworks) {
      if (network === 'canonical') continue

      const editor = this.initializeEditor(network)
      editor.hide()
    }
  }

  setupEventListeners() {
    this.events.on('network:changed', ({ network }) => {
      if (this.currentEditor) {
        this.currentEditor.hide()
      }

      const editor = this.editors.get(network)
      if (editor) {
        editor.show()
        this.currentEditor = editor
      } else {
        console.warn(`No editor found for network: ${network}`)
      }
    })

    this.events.on('network:enabled', ({ network }) => {
      this.selectedNetworks.add(network)
      if (!this.store.post.socialNetworks.includes(network)) {
        this.store.post.socialNetworks.push(network)
      }

      if (!this.editors.has(network)) {
        const editor = this.initializeEditor(network)
        editor.hide()
      }
    })

    this.events.on('network:disabled', ({ network }) => {
      this.selectedNetworks.delete(network)
      this.store.disableNetwork(network)

      const editor = this.editors.get(network)
      if (editor) {
        if (this.currentEditor === editor) {
          this.events.emit('network:changed', { network: 'canonical' })
        }

        editor.destroy()
        this.editors.delete(network)
      }
    })
  }

  initializeCanonicalEditor(content) {
    const editor = this.buildEditor({
      network: 'canonical',
      content,
      selectedNetworks: this.selectedNetworks,
      events: this.events,
      attachments: this.attachments,
      store: this.store
    })

    this.editors.set('canonical', editor)
    this.canonicalEditor = this.currentEditor = editor

    editor.setup()

    return editor
  }

  initializeEditor(network) {
    const editor = this.buildEditor({
      network,
      selectedNetworks: this.selectedNetworks,
      events: this.events,
      attachments: this.attachments,
      store: this.store
    })

    this.editors.set(network, editor)

    editor.setup()

    return editor
  }

  buildEditor(params) {
    const editorClass = params.network === 'linkedin' ? LongFormPostEditor : ThreadedPostEditor
    return new editorClass(params)
  }

  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  destroy() {
    this.events.removeAllListeners()
    this.topbar.destroy()
    for (const editor of this.editors.values()) {
      editor.destroy()
    }
    this.editors.clear()
    this.controls.destroy()

    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
    }
  }
}
