// Date utility functions
const isSameDay = (date1, date2) => {
  return date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
}

// Add isPastDate function to check if a date is in the past
const isPastDate = (date, now = new Date()) => {
  // Only consider times as past if they are actually in the past
  // For future days, no times should be considered past
  // For today, only times before the current hour are past
  
  const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  // If the date is in the future, no times are past
  if (checkDate > currentDate) {
    return false
  }
  
  // If the date is in the past, all times are past
  if (checkDate < currentDate) {
    return true
  }
  
  // If it's today, only hours before the current hour are past
  if (checkDate.getTime() === currentDate.getTime()) {
    return date.getHours() < now.getHours()
  }
  
  return false
}

// Add these helper functions to format time in 12-hour format
const formatHour = (hour) => {
  return (hour % 12 || 12).toString()
}

const getPeriod = (hour) => {
  return hour >= 12 ? 'PM' : 'AM'
}

export class WeekView {
  constructor({ parentElement, onPrevWeek, onNextWeek, daysToShow = 3, stepDays = 3 }) {
    this.parentElement = parentElement
    this.onPrevWeek = onPrevWeek
    this.onNextWeek = onNextWeek
    this.daysToShow = daysToShow
    this.stepDays = stepDays
    this.currentDate = new Date()
    this.currentDate.setHours(0, 0, 0, 0)
    this.days = []
    this.container = null
  }

  // Create and render the week view container
  createContainer() {
    // Create the container if it doesn't exist
    if (!this.container) {
      this.container = document.createElement('div')
      this.container.className = 'week-view-container'

      // Add the container to the parent element
      if (this.parentElement) {
        this.parentElement.querySelector('.p-6').appendChild(this.container)
      }
    }

    // Render the calendar markup
    this.container.innerHTML = this.getCalendarMarkup()

    // Setup event listeners
    this.setupEventListeners()

    return this.container
  }

  // Generate the calendar markup
  getCalendarMarkup() {
    return `
      <!-- Calendar -->
      <div class="bg-white rounded-lg border border-gray-200">
        <!-- Calendar Header -->
        <div class="flex items-center justify-between p-4 border-b">
          <div class="flex items-center gap-2">
            <button type="button" class="p-2 hover:bg-gray-100 rounded-full" data-prev-week>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span class="text-lg font-semibold" data-current-week></span>
            <button type="button" class="p-2 hover:bg-gray-100 rounded-full" data-next-week>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
          <div class="flex items-center gap-2">
            <button type="button" class="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" data-today-button>
              Today
            </button>
            <div class="text-sm text-gray-500">
              ${JCP.user.timezone}
            </div>
          </div>
        </div>

        <!-- Calendar Table -->
        <table class="w-full border-collapse select-none table-fixed" data-calendar-grid>
          <thead class="bg-gray-50">
            <tr>
              <th class="w-[70px] p-2 text-sm font-medium text-gray-900 border-b border-gray-200 text-right pr-4">
                Hour
              </th>
              ${Array(this.daysToShow).fill(0).map((_, index) => {
                // Calculate consistent width for each column
                const widthClass = this.daysToShow === 1 ? 'w-full' : 
                                 this.daysToShow === 2 ? 'w-1/2' :
                                 this.daysToShow === 3 ? 'w-1/3' :
                                 this.daysToShow === 4 ? 'w-1/4' : 
                                 `w-1/${this.daysToShow}`;
                return `
                  <th class="${widthClass} p-2 text-sm font-medium text-gray-900 border-b border-gray-200 text-center min-w-0">
                    <div class="truncate"></div>
                  </th>
                `;
              }).join('')}
            </tr>
          </thead>
          <tbody>
            ${Array(16).fill(0).map((_, hourIndex) => {
      const hour = hourIndex + 9; // Start from 9 AM
      return `
                <tr>
                  <td class="border-b border-r border-gray-200 p-2 text-sm text-gray-500 text-right pr-4 whitespace-nowrap">
                    ${formatHour(hour)}:00 ${getPeriod(hour)}
                  </td>
                  ${Array(this.daysToShow).fill(0).map((_, index) => {
                    // Calculate consistent width for each column
                    const widthClass = this.daysToShow === 1 ? 'w-full' : 
                                     this.daysToShow === 2 ? 'w-1/2' :
                                     this.daysToShow === 3 ? 'w-1/3' :
                                     this.daysToShow === 4 ? 'w-1/4' : 
                                     `w-1/${this.daysToShow}`;
                    return `
                      <td class="${widthClass} border-b border-r border-gray-200 last:border-r-0 h-12 group hover:bg-gray-50 min-w-0" data-slot-cell>
                        <div class="relative w-full h-full"></div>
                      </td>
                    `;
                  }).join('')}
                </tr>
              `;
    }).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  render(options = {}) {
    const {
      occupiedSlots = [],
      suggestedSlots = [],
      selectedSlots = []
    } = options

    // Create the container if it doesn't exist
    if (!this.container) {
      this.createContainer()
    }

    this.renderCalendar()

    // Mark past time slots
    this.markPastTimeSlots()

    // Mark occupied slots
    if (occupiedSlots.length > 0) {
      this.markOccupiedSlots(occupiedSlots)
    }

    // Highlight suggested slots
    if (suggestedSlots.length > 0) {
      this.highlightSuggestedSlots(suggestedSlots)
    }

    // Mark selected slots
    if (selectedSlots.length > 0) {
      this.markSelectedSlots(selectedSlots)
    }

    return this.container
  }

  renderCalendar() {
    // Generate consecutive days starting from currentDate
    this.days = []
    
    for (let i = 0; i < this.daysToShow; i++) {
      const day = new Date(this.currentDate)
      day.setDate(this.currentDate.getDate() + i)
      this.days.push(day)
    }

    // Update date range display
    const formatter = new Intl.DateTimeFormat('en', { month: 'short', day: 'numeric' })
    let rangeDisplay
    if (this.daysToShow === 1) {
      rangeDisplay = formatter.format(this.days[0])
    } else {
      rangeDisplay = `${formatter.format(this.days[0])} - ${formatter.format(this.days[this.daysToShow - 1])}`
    }
    this.container.querySelector('[data-current-week]').textContent = rangeDisplay

    // Update header dates
    const headers = this.container.querySelectorAll('[data-calendar-grid] thead th')
    this.days.forEach((date, i) => {
      const dayFormatter = new Intl.DateTimeFormat('en', { weekday: 'short', month: 'short', day: 'numeric' })
      const headerDiv = headers[i + 1]?.querySelector('div')
      if (headerDiv) {
        headerDiv.textContent = dayFormatter.format(date)
      }
    })

    // Clear all slot highlights and reset cells
    this.resetAllCells()
  }


  resetAllCells() {
    const allSlotCells = this.container.querySelectorAll('[data-slot-cell]')
    allSlotCells.forEach(cell => {
      // Reset cell to default state by removing data attributes
      cell.removeAttribute('data-selected')
      cell.removeAttribute('data-suggested')
      cell.removeAttribute('data-occupied')
      cell.removeAttribute('data-past-time')
      
      // Clear any inline styles that might override CSS
      cell.style.backgroundColor = ''
      cell.style.color = ''
      cell.style.backgroundImage = ''

      // Clear cell content
      const cellContent = cell.querySelector('.relative.w-full.h-full')
      if (cellContent) {
        cellContent.innerHTML = ''
      } else {
        cell.innerHTML = '<div class="relative w-full h-full"></div>'
      }
    })
  }

  markOccupiedSlots(occupiedSlots) {
    this.days.forEach((date, dayIndex) => {
      // Filter schedules for this day
      const daySchedules = occupiedSlots.filter(schedule => {
        const scheduleDate = schedule.scheduled_at
        return scheduleDate.getDate() === date.getDate() &&
          scheduleDate.getMonth() === date.getMonth() &&
          scheduleDate.getFullYear() === date.getFullYear()
      })

      // Group schedules by hour to avoid duplicates
      const hourlySchedules = daySchedules.reduce((acc, schedule) => {
        const hour = schedule.scheduled_at.getHours()
        if (!acc[hour]) {
          acc[hour] = schedule
        }
        return acc
      }, {})

      // Mark occupied slots
      Object.entries(hourlySchedules).forEach(([hour, schedule]) => {
        const hourIndex = parseInt(hour) - 9
        if (hourIndex >= 0 && hourIndex < 16) { // Only show slots between 9 AM and midnight
          const cell = this.getCellAt(hourIndex, dayIndex)
          if (cell) {
            this.markCellAsOccupied(cell, schedule)
          }
        }
      })
    })
  }

  markCellAsOccupied(cell, schedule) {
    // Mark cell as occupied using data attribute (CSS will handle styling)
    cell.setAttribute('data-occupied', 'true')
    cell.setAttribute('data-date', schedule.scheduled_at.toISOString())

    // Add post preview with truncated content
    const previewContainer = document.createElement('div')
    previewContainer.className = 'w-full h-full flex items-center px-2'

    const previewText = document.createElement('div')
    previewText.className = 'text-xs text-gray-300 truncate'

    // Truncate content to 25 characters
    const content = schedule.content || 'No content'
    const truncatedContent = content.length > 25 ? content.substring(0, 25) + '...' : content

    previewText.textContent = truncatedContent

    // Add title attribute to show full content on hover
    previewText.title = content

    previewContainer.appendChild(previewText)
    cell.querySelector('.relative.w-full.h-full').appendChild(previewContainer)
  }

  highlightSuggestedSlots(suggestedSlots) {
    this.days.forEach((date, dayIndex) => {
      suggestedSlots
        .filter(slotDate =>
          slotDate.getDate() === date.getDate() &&
          slotDate.getMonth() === date.getMonth() &&
          slotDate.getFullYear() === date.getFullYear()
        )
        .forEach(slotDate => {
          const hourIndex = slotDate.getHours() - 9
          const cell = this.getCellAt(hourIndex, dayIndex)

          if (cell && !cell.hasAttribute('data-occupied')) {
            this.markCellAsSuggested(cell, slotDate)
          }
        })
    })
  }

  markCellAsSuggested(cell, date) {
    // Mark cell as suggested using data attribute (CSS will handle styling)
    cell.setAttribute('data-suggested', 'true')
    cell.setAttribute('data-date', date.toISOString())
  }

  markSelectedSlots(selectedSlots) {
    selectedSlots.forEach(({ date, networks }) => {
      const dayIndex = this.days.findIndex(d =>
        d.getDate() === date.getDate() &&
        d.getMonth() === date.getMonth() &&
        d.getFullYear() === date.getFullYear()
      )

      if (dayIndex >= 0) {
        const hourIndex = date.getHours() - 9
        const cell = this.getCellAt(hourIndex, dayIndex)

        if (cell && !cell.hasAttribute('data-occupied')) {
          // Mark cell as selected using data attribute (CSS will handle styling)
          cell.setAttribute('data-selected', 'true')
          cell.setAttribute('data-date', date.toISOString())
        }
      }
    })
  }

  getCellAt(hourIndex, dayIndex) {
    return this.container.querySelectorAll('tbody tr')[hourIndex]
      ?.querySelectorAll('[data-slot-cell]')[dayIndex]
  }

  getCellDate(cell) {
    if (!cell) return null

    const dateAttr = cell.getAttribute('data-date')
    if (dateAttr) {
      return new Date(dateAttr)
    }

    // Calculate date from position if not explicitly set
    const rowIndex = Array.from(cell.closest('tbody').children).indexOf(cell.closest('tr'))
    const cellIndex = Array.from(cell.parentElement.children).indexOf(cell) - 1

    if (cellIndex >= 0 && cellIndex < this.days.length) {
      const date = new Date(this.days[cellIndex])
      const hour = rowIndex + 9
      date.setHours(hour, 0, 0, 0)
      return date
    }

    return null
  }

  getAllCells() {
    return this.container.querySelectorAll('[data-slot-cell]')
  }

  getAvailableCells() {
    return Array.from(this.getAllCells()).filter(cell =>
      !cell.hasAttribute('data-occupied') && !cell.hasAttribute('data-past-time')
    )
  }

  getSuggestedCells() {
    return Array.from(this.getAllCells()).filter(cell =>
      cell.hasAttribute('data-suggested')
    )
  }

  getSelectedCells() {
    return Array.from(this.getAllCells()).filter(cell =>
      cell.hasAttribute('data-selected')
    )
  }

  setupEventListeners() {
    if (!this.container) return;

    // Handle week navigation
    const prevWeekBtn = this.container.querySelector('[data-prev-week]')
    const nextWeekBtn = this.container.querySelector('[data-next-week]')
    const todayBtn = this.container.querySelector('[data-today-button]')

    if (prevWeekBtn) {
      prevWeekBtn.addEventListener('click', () => {
        this.currentDate.setDate(this.currentDate.getDate() - this.stepDays)
        this.renderCalendar()
        if (this.onPrevWeek) this.onPrevWeek(new Date(this.currentDate))
      })
    }

    if (nextWeekBtn) {
      nextWeekBtn.addEventListener('click', () => {
        this.currentDate.setDate(this.currentDate.getDate() + this.stepDays)
        this.renderCalendar()
        if (this.onNextWeek) this.onNextWeek(new Date(this.currentDate))
      })
    }

    if (todayBtn) {
      todayBtn.addEventListener('click', () => {
        // Reset to today's date
        this.currentDate = new Date()
        this.currentDate.setHours(0, 0, 0, 0)
        this.renderCalendar()
        
        // Trigger the callback to notify parent components
        if (this.onNextWeek) this.onNextWeek(new Date(this.currentDate))
      })
    }
  }

  // Add new method to mark past time slots
  markPastTimeSlots() {
    const now = new Date()

    this.days.forEach((day, dayIndex) => {
      // For each hour in the day (9 AM to midnight)
      for (let hourIndex = 0; hourIndex < 16; hourIndex++) {
        const cell = this.getCellAt(hourIndex, dayIndex)
        if (!cell) continue

        // Get the date for this cell
        const cellDate = new Date(day)
        cellDate.setHours(hourIndex + 9, 0, 0, 0)

        // More conservative check: only mark as past if it's definitively in the past
        // Use a stricter comparison that accounts for minutes as well
        const isThisPast = this.isDefinitelyPastTime(cellDate, now)
        
        
        if (isThisPast) {
          // Mark as past time using data attribute (CSS will handle all styling)
          cell.setAttribute('data-past-time', 'true')
        }
      }
    })
  }

  // More conservative method to check if a time is definitely in the past
  isDefinitelyPastTime(date, now = new Date()) {
    // Use the original isPastDate logic but be more conservative
    // Only mark as past if it's clearly in the past with a buffer
    
    const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    // If the date is in the future, definitely not past
    if (checkDate > currentDate) {
      return false
    }
    
    // If the date is in the past (previous day), definitely past
    if (checkDate < currentDate) {
      return true
    }
    
    // If it's today, only mark as past if it's more than 1 hour in the past
    // This gives a buffer for the current hour and near-future times
    if (checkDate.getTime() === currentDate.getTime()) {
      return date.getHours() < (now.getHours() - 1)
    }
    
    return false
  }
}
