import tippy from 'tippy.js'
import { NETWORKS } from '../../extensions/networks'

// Add this before the AutoSchedulingCalendar class
const debug = (method, message, data = {}) => {
  console.log(`[AutoScheduler] ${method}:`, message, data)
}

// Add this helper function to format time in 12-hour format
const formatHour = (hour) => {
  return (hour % 12 || 12).toString()
}

const getPeriod = (hour) => {
  return hour >= 12 ? 'PM' : 'AM'
}

// Date utility functions
const isSameDay = (date1, date2) => {
  return date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
}

const isSameHour = (date1, date2) => {
  return date1.getHours() === date2.getHours() && isSameDay(date1, date2)
}

const isPastTime = (date, now = new Date()) => {
  // Check if date and time are in the past (including minutes)
  if (date.getFullYear() < now.getFullYear()) return true
  if (date.getMonth() < now.getMonth() && date.getFullYear() === now.getFullYear()) return true
  if (date.getDate() < now.getDate() && date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear()) return true
  if (date.getHours() < now.getHours() && isSameDay(date, now)) return true
  if (date.getHours() === now.getHours() && date.getMinutes() <= now.getMinutes() && isSameDay(date, now)) return true
  return false
}

const ensureFutureTime = (date, now = new Date(), minutesAhead = 5) => {
  // If it's the current hour, ensure minutes are in the future
  if (isSameHour(date, now) && date.getMinutes() <= now.getMinutes()) {
    date.setMinutes(Math.min(59, now.getMinutes() + minutesAhead))
  }
  return date
}

// Helper function to get network information
export const getNetworkInfo = (network) => {
  const networkData = NETWORKS.find(n => n.id === network)
  return {
    viewBox: networkData?.viewBox || "0 0 24 24",
    icon: networkData?.icon || "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z",
    color: getNetworkColor(network)
  }
}

// Helper function to get network color
export const getNetworkColor = (network) => {
  switch (network) {
    case 'canonical':
      return 'bg-blue-700'
    case 'bsky':
      return 'bg-sky-500'
    case 'mastodon':
      return 'bg-purple-500'
    case 'linkedin':
      return 'bg-blue-600'
    case 'x':
      return 'bg-black'
    default:
      return 'bg-gray-800'
  }
}

// Helper function to get network label
export const getNetworkLabel = (network) => {
  const labels = {
    all: 'All Networks',
    canonical: 'All Networks',
    bsky: 'Bluesky',
    mastodon: 'Mastodon',
    linkedin: 'LinkedIn',
    x: 'X (Twitter)'
  }
  return labels[network] || network
}

export class ScheduleControl {
  constructor({ date, cell, availableNetworks, isSelected = false, isSuggested = false, calendar }) {
    this.date = date

    // If the date is in the current hour, set minutes to at least 5 minutes in the future
    ensureFutureTime(date)

    this.cell = cell
    this.availableNetworks = new Set(availableNetworks)
    this.selectedNetworks = new Set()
    this.isSelected = isSelected
    this.isSuggested = isSuggested
    this.calendar = calendar
    this.isEditingTime = false
    this.tippyInstance = null
    debug('ScheduleControl.constructor', 'Created new schedule control', {
      date,
      cellId: cell?.dataset?.slotId,
      availableNetworks: Array.from(this.availableNetworks)
    })
  }

  addNetwork(network) {
    debug('ScheduleControl.addNetwork', `Adding network ${network}`, {
      cellId: this.cell?.dataset?.slotId,
      before: Array.from(this.selectedNetworks),
      availableBefore: Array.from(this.availableNetworks)
    })
    this.selectedNetworks.add(network)
    this.availableNetworks.delete(network)
    this.render()
    debug('ScheduleControl.addNetwork', `Added network ${network}`, {
      cellId: this.cell?.dataset?.slotId,
      after: Array.from(this.selectedNetworks),
      availableAfter: Array.from(this.availableNetworks)
    })
  }

  removeNetwork(network) {
    debug('ScheduleControl.removeNetwork', `Removing network ${network}`, {
      cellId: this.cell?.dataset?.slotId,
      before: Array.from(this.selectedNetworks),
      availableBefore: Array.from(this.availableNetworks)
    })
    this.selectedNetworks.delete(network)
    this.availableNetworks.add(network)
    this.render()
    debug('ScheduleControl.removeNetwork', `Removed network ${network}`, {
      cellId: this.cell?.dataset?.slotId,
      after: Array.from(this.selectedNetworks),
      availableAfter: Array.from(this.availableNetworks)
    })
  }

  hasNetwork(network) {
    return this.selectedNetworks.has(network)
  }

  isEmpty() {
    return this.selectedNetworks.size === 0
  }

  render() {
    // If cell is null, this is a time-based slot with no visual representation
    if (!this.cell) {
      return;
    }

    // If this is a past time slot, don't render any interactive elements
    if (this.cell.hasAttribute('data-past-time')) {
      return;
    }

    // Clean up any existing event listeners and tippy instances
    this.cleanup()

    // Set up the slot markup if it doesn't exist
    this.setupMarkup()

    // Update visual state
    if (this.isEmpty()) {
      // Hide the slot background
      const slotBg = this.cell.querySelector('.slot-bg')
      if (slotBg) {
        slotBg.classList.add('opacity-0')
      }
      this.cell.removeAttribute('data-selected')

      // Hide time inputs
      const timeInputs = this.cell.querySelector('.time-inputs')
      if (timeInputs) {
        timeInputs.classList.add('hidden')
      }

      // Clear network icons
      const networkIcons = this.cell.querySelector('[data-network-icons]')
      if (networkIcons) {
        networkIcons.innerHTML = ''
      }

      // Hide dropdown trigger
      const dropdownTrigger = this.cell.querySelector('[data-dropdown-trigger]')
      if (dropdownTrigger) {
        dropdownTrigger.classList.add('hidden')
      }

      // Keep the suggested styling if this is a suggested slot
      if (this.isSuggested) {
        this.cell.style.backgroundColor = '#148d391a'
      } else {
        this.cell.style.backgroundColor = ''
      }
    } else {
      // Show the slot background
      const slotBg = this.cell.querySelector('.slot-bg')
      if (slotBg) {
        slotBg.classList.remove('opacity-0')
      }
      this.cell.setAttribute('data-selected', 'true')

      // Remove any background color from the cell itself
      this.cell.style.backgroundColor = ''

      // Show time display or editor
      const timeContainer = this.cell.querySelector('.time-container')
      if (timeContainer) {
        if (this.isEditingTime) {
          timeContainer.innerHTML = `
            <div class="flex items-center dark:text-gray-200">
              <span class="text-sm font-medium w-4 text-right">${formatHour(this.date.getHours())}</span>
              <span class="text-sm font-medium mx-1">:</span>
              <input type="number"
                min="0"
                max="59"
                class="text-sm font-medium !bg-white dark:!bg-gray-800 border border-gray-200 dark:border-gray-700 rounded px-1 text-center appearance-none focus:ring-1 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 dark:text-gray-200 w-8"
                data-minute-input
                value="${this.date.getMinutes().toString().padStart(2, '0')}">
              <span class="text-sm font-medium ml-1">${getPeriod(this.date.getHours())}</span>
            </div>
          `

          // Hide network icons and other controls while editing
          const networkIcons = this.cell.querySelector('[data-network-icons]')
          const dragHandle = this.cell.querySelector('.drag-handle')
          const dropdownTrigger = this.cell.querySelector('[data-dropdown-trigger]')

          if (networkIcons) networkIcons.classList.add('hidden')
          if (dragHandle) dragHandle.classList.add('hidden')
          if (dropdownTrigger) dropdownTrigger.classList.add('hidden')

          // Focus and select the minute input
          const minuteInput = timeContainer.querySelector('[data-minute-input]')
          if (minuteInput) {
            minuteInput.focus()
            minuteInput.select()

            // Add event listeners for the minute input
            this.setupMinuteInputListeners(minuteInput)
          }
        } else {
          timeContainer.innerHTML = `
            <div class="flex items-center dark:text-gray-200">
              <span class="text-sm font-medium w-4 text-right">${formatHour(this.date.getHours())}</span>
              <span class="text-sm font-medium mx-1">:</span>
              <span class="text-sm font-medium cursor-pointer hover:text-gray-100 dark:hover:text-white w-4" data-minute-display>${this.date.getMinutes().toString().padStart(2, '0')}</span>
              <span class="text-sm font-medium ml-1">${getPeriod(this.date.getHours())}</span>
            </div>
          `

          // Add event listener for the minute display
          const minuteDisplay = timeContainer.querySelector('[data-minute-display]')
          if (minuteDisplay) {
            minuteDisplay.addEventListener('click', this.handleMinuteDisplayClick.bind(this))
          }
        }
      }

      // Only show network icons when not editing
      if (!this.isEditingTime) {
        // Update network icons
        const networkIcons = this.cell.querySelector('[data-network-icons]')
        if (networkIcons) {
          networkIcons.classList.remove('hidden')
          // Get all networks and limit display to first 2
          const networks = Array.from(this.selectedNetworks)
          const displayNetworks = networks.slice(0, 2)
          const remainingCount = networks.length - 2

          networkIcons.innerHTML = displayNetworks
            .map(network => {
              const info = getNetworkInfo(network)
              return `
                <div class="w-5 h-5 rounded-full flex items-center justify-center ${info.color} text-white">
                  <svg class="w-3.5 h-3.5" fill="currentColor" viewBox="${info.viewBox}">
                    <path d="${info.icon}" />
                  </svg>
                </div>
              `
            }).join('') +
            (remainingCount > 0 ? `
              <div class="w-5 h-5 rounded-full flex items-center justify-center bg-gray-400 text-white text-xs font-medium">
                +${remainingCount}
              </div>
            ` : '')
        }

        // Show other controls
        const dragHandle = this.cell.querySelector('.drag-handle')
        const dropdownTrigger = this.cell.querySelector('[data-dropdown-trigger]')

        if (dragHandle) dragHandle.classList.remove('hidden')
        if (dropdownTrigger) {
          if (this.selectedNetworks.size > 0 || this.availableNetworks.size > 0) {
            dropdownTrigger.classList.remove('hidden')
            this.setupTippyDropdown(dropdownTrigger)
          } else {
            dropdownTrigger.classList.add('hidden')
          }
        }
      }
    }
  }

  setupMarkup() {
    // Generate a unique ID for the cell if it doesn't have one
    if (!this.cell.dataset.slotId) {
      this.cell.dataset.slotId = `slot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }

    // Check if this is a past time slot
    const isPastTimeSlot = this.cell.hasAttribute('data-past-time')

    // Set up the slot markup
    let container = this.cell.querySelector('.relative.w-full.h-full')
    if (!container) {
      // If container doesn't exist, create it
      this.cell.innerHTML = `<div class="relative w-full h-full"></div>`
      container = this.cell.querySelector('.relative.w-full.h-full')
    }

    // If it's a past time slot, add a simple grayed-out display
    if (isPastTimeSlot) {
      container.innerHTML = `
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="flex items-center gap-1 px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded-md">
            <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Past</span>
          </div>
        </div>
      `
      return
    }

    container.innerHTML = `
      <div class="absolute inset-0 bg-green-100 rounded-sm transition-opacity duration-200 slot-bg"></div>
      <div class="absolute inset-0 p-2 flex items-center gap-1">
        <div class="drag-handle text-white hover:text-gray-100 dark:text-white dark:hover:text-gray-100 transition-colors duration-200 cursor-move select-none px-0.5" data-slot-drag-handle style="touch-action: none;">
          <svg class="w-3.5 h-3.5" viewBox="0 0 16 16" fill="currentColor">
            <circle cx="8" cy="3" r="1.5" />
            <circle cx="8" cy="8" r="1.5" />
            <circle cx="8" cy="13" r="1.5" />
          </svg>
        </div>
        <div class="time-container cursor-pointer"></div>
        <div class="flex-1"></div>
        <div class="flex items-center gap-1 relative">
          <div data-network-icons class="flex gap-0.5 items-center"></div>
          <button type="button" class="p-0.5 bg-gray-50 dark:hover:bg-gray-700 rounded-md" data-dropdown-trigger>
            <svg class="w-3.5 h-3.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
    `
  }

  setupMinuteInputListeners(minuteInput) {
    // Handle blur event
    minuteInput.addEventListener('blur', this.handleMinuteInputBlur.bind(this))

    // Handle keydown event for Enter key
    minuteInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        minuteInput.blur()
      }
    })
  }

  handleMinuteDisplayClick(event) {
    this.isEditingTime = true
    this.render()
  }

  handleMinuteInputBlur(event) {
    const minuteInput = event.target
    let minutes = Math.min(Math.max(0, parseInt(minuteInput.value) || 0), 59)

    // Set the minutes first
    this.date.setMinutes(minutes)

    // Then ensure the time is in the future
    ensureFutureTime(this.date)

    this.isEditingTime = false
    this.render()

    // Update network schedules
    if (this.calendar && this.calendar.networkSchedules) {
      this.selectedNetworks.forEach(network => {
        this.calendar.networkSchedules.set(network, this.date)
      })
    }
  }

  setupTippyDropdown(triggerElement) {
    // Destroy existing tippy instance if it exists
    if (this.tippyInstance) {
      this.tippyInstance.destroy()
    }

    // Create dropdown content
    const dropdownContent = document.createElement('div')
    dropdownContent.className = 'bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 py-1 w-48'
    dropdownContent.setAttribute('data-network-dropdown', 'true')

    // Populate dropdown content
    dropdownContent.innerHTML = Array.from(this.availableNetworks).concat(Array.from(this.selectedNetworks))
      .sort((a, b) => getNetworkLabel(a).localeCompare(getNetworkLabel(b)))
      .map(network => `
        <div class="flex items-center px-3 py-2 hover:bg-gray-50" data-network-item="${network}">
          <label class="flex items-center gap-2 cursor-pointer w-full">
            <input type="checkbox"
              class="rounded border-gray-300 text-green-600 focus:ring-green-500"
              data-network-checkbox="${network}"
              data-slot-id="${this.cell.dataset.slotId}"
              ${this.selectedNetworks.has(network) ? 'checked' : ''}>
            <div class="flex items-center gap-2">
              <div class="w-5 h-5 rounded-full flex items-center justify-center ${getNetworkColor(network)} text-white">
                <svg class="w-3.5 h-3.5" fill="currentColor" viewBox="${NETWORKS.find(n => n.id === network)?.viewBox}">
                  <path d="${NETWORKS.find(n => n.id === network)?.icon}" />
                </svg>
              </div>
              <span class="text-sm text-gray-900">${getNetworkLabel(network)}</span>
            </div>
          </label>
        </div>
      `).join('')

    // Create tippy instance
    this.tippyInstance = tippy(triggerElement, {
      content: dropdownContent,
      interactive: true,
      trigger: 'click',
      placement: 'bottom-end',
      arrow: false,
      theme: 'light',
      appendTo: () => this.cell, // Ensure the dropdown is inside the cell
      onShow: () => {
        // Add event listeners to checkboxes
        const checkboxes = dropdownContent.querySelectorAll('[data-network-checkbox]')
        checkboxes.forEach(checkbox => {
          checkbox.addEventListener('change', this.handleNetworkCheckboxChange.bind(this))
        })
      },
      onHide: () => {
        // Clean up event listeners
        const checkboxes = dropdownContent.querySelectorAll('[data-network-checkbox]')
        checkboxes.forEach(checkbox => {
          checkbox.removeEventListener('change', this.handleNetworkCheckboxChange.bind(this))
        })
      }
    })
  }

  cleanup() {
    // Destroy tippy instance if it exists
    if (this.tippyInstance) {
      this.tippyInstance.destroy()
      this.tippyInstance = null
    }

    // Remove event listeners from minute display if it exists
    const minuteDisplay = this.cell.querySelector('[data-minute-display]')
    if (minuteDisplay) {
      minuteDisplay.removeEventListener('click', this.handleMinuteDisplayClick)
    }

    // Remove event listeners from minute input if it exists
    const minuteInput = this.cell.querySelector('[data-minute-input]')
    if (minuteInput) {
      minuteInput.removeEventListener('blur', this.handleMinuteInputBlur)
      minuteInput.removeEventListener('keydown', null)
    }
  }

  handleNetworkCheckboxChange(event) {
    const checkbox = event.target
    const network = checkbox.dataset.networkCheckbox
    const isChecked = checkbox.checked

    if (isChecked) {
      // Add network to this slot
      this.addNetwork(network)

      // If network exists in another slot, remove it first
      if (this.calendar && this.calendar.slots) {
        this.calendar.slots.forEach((otherSlot, cell) => {
          if (otherSlot !== this && otherSlot.hasNetwork(network)) {
            otherSlot.removeNetwork(network)
            if (otherSlot.isEmpty()) {
              this.calendar.slots.delete(cell)
            }
          }
        })
      }

      // Update network schedule in calendar
      if (this.calendar && this.calendar.networkSchedules) {
        this.calendar.networkSchedules.set(network, this.date)
      }
    } else {
      // Remove network from this slot
      this.removeNetwork(network)

      // If this was the last network, the slot will be removed in the render method

      // Find next available cell for this network if needed
      if (this.calendar) {
        this.calendar.handleNetworkSelection(this, network, true)
      }
    }

    // Update the week view if available
    if (this.calendar && this.calendar.updateWeekView) {
      this.calendar.updateWeekView()
    }
  }
}
