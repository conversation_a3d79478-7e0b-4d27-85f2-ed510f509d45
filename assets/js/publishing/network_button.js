import { NETWORKS } from '../extensions/networks'
import { UpgradeModal } from './upgrade_modal'
import tippy from 'tippy.js'

export class NetworkButton {
  constructor(networkId, options = {}) {
    this.networkId = networkId
    this.connection = options.connection || null
    this.events = options.events || { emit: () => { } }
    this.element = null
    this.upgradeModal = new UpgradeModal()

    // Find network info
    this.network = NETWORKS.find(n => n.id === networkId)
    if (!this.network) {
      console.warn(`Network ${networkId} not found`)
      return null
    }

    // Query connection status
    this.connectionStatus = { enabled: true }
    if (networkId !== 'canonical' && window.JCP) {
      this.connectionStatus = window.JCP.isConnectionAvailable(networkId)
    }

    this.render()
    this.setupEventListeners()
  }

  render() {
    const networkId = this.networkId
    const network = this.network
    const connection = this.connection
    const connectionStatus = this.connectionStatus

    const avatarStyle = connection?.avatar ? `background-image: url('${connection.avatar}'); background-size: cover; background-position: center;` : ''

    const wrapper = document.createElement('div')
    wrapper.className = 'relative group/network-button'
    wrapper.dataset.networkButton = networkId

    wrapper.innerHTML = `
      <div class="relative">
        <button
          type="button"
          class="network-toggle flex items-center transition-all duration-200 overflow-hidden
            ${connectionStatus.enabled ? 'bg-blue-700 text-gray-500 hover:bg-gray-50' : 'bg-gray-300 text-gray-500 hover:bg-gray-200'}
            h-10 rounded-lg
            w-10 data-[selected=true]:w-auto data-[selected=true]:min-w-[120px]
            cursor-pointer
            border border-gray-200
            data-[selected=true]:border-transparent
            data-[selected=true]:bg-${this.getNetworkColor()}
            data-[selected=true]:text-white
            relative
            ${!connectionStatus.enabled ? 'data-connection-disabled opacity-60' : ''}
          "
          data-network="${networkId}"
          data-selected="false"
          data-connection-status="${connectionStatus.enabled ? 'enabled' : connectionStatus.reason}"
          ${!connectionStatus.enabled && connectionStatus.reason === 'feature_disabled' ? `data-feature="crossposting_${networkId}"` : ''}
          ${!connectionStatus.enabled && connectionStatus.reason === 'feature_disabled' ? 'data-feature-enabled="false"' : ''}
          ${!connectionStatus.enabled ? `data-tippy-content="${this.getConnectionStatusMessage(connectionStatus.reason)}"` : ''}
          ${!connectionStatus.enabled && connectionStatus.reason === 'needs_reconnect' ? 'data-needs-reconnect="true"' : ''}
          data-testid="network-button-${networkId}"
        >
          <div class="flex-shrink-0 w-10 h-10 relative">
            ${connection?.avatar ? `
              <div class="absolute inset-0" style="${avatarStyle}"></div>
              <div class="absolute inset-0 ${connectionStatus.enabled ? 'bg-black/75' : 'bg-gray-500/90'}" data-avatar-overlay></div>
            ` : ''}
            <div class="flex items-center justify-center h-full relative z-10">
              <svg class="w-5 h-5 text-gray-500 data-[selected=true]:text-white" fill="currentColor" viewBox="${network.viewBox}">
                <path d="${network.icon}" />
              </svg>
            </div>
          </div>
          <span class="flex-1 px-3 font-medium whitespace-nowrap opacity-0 transition-opacity duration-200 data-[visible=true]:opacity-100 relative z-10"
            data-network-name
          >${network.name}</span>
        </button>
      </div>
      ${networkId !== 'canonical' ? `
        ${connectionStatus.enabled ? `
          <button
            type="button"
            class="absolute -right-2 -top-2 w-5 h-5 flex items-center justify-center rounded-full bg-gray-100 border border-gray-300 shadow-sm opacity-0 group-hover/network-button:opacity-100 transition-opacity duration-200 text-gray-500 hover:text-white hover:border-red-400 hover:bg-red-500 z-20"
            data-remove-network
          >
            <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        ` : `
          <button
            type="button"
            class="absolute -right-2 -top-2 w-5 h-5 flex items-center justify-center rounded-full bg-red-100 border border-red-300 shadow-sm text-red-500 hover:text-white hover:border-blue-400 hover:bg-blue-500 z-20 animate-pulse transition-colors duration-200"
            ${connectionStatus.reason === 'feature_disabled' ? 'data-upgrade-required' : 'data-reconnect-network'}
            data-tippy-content="${this.getConnectionStatusMessage(connectionStatus.reason)}"
          >
            ${connectionStatus.reason === 'feature_disabled' ? `
              <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            ` : `
              <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            `}
          </button>
        `}
      ` : ''}
    `

    this.element = wrapper
    return wrapper
  }

  setupEventListeners() {
    if (!this.element) return

    const button = this.element.querySelector('.network-toggle')
    if (button) {
      button.addEventListener('click', (e) => {
        if (!this.connectionStatus.enabled) {
          if (this.connectionStatus.reason === 'needs_reconnect') {
            const currentPath = window.location.pathname
            window.location.href = `/auth/${this.networkId}?return_to=${encodeURIComponent(currentPath)}`
            return
          }
          // For other disabled reasons, do nothing
          return
        }

        this.events.emit('network:selected', { network: this.networkId })
      })
    }

    // Add remove button event listener
    const removeButton = this.element.querySelector('[data-remove-network]')
    if (removeButton) {
      removeButton.addEventListener('click', (e) => {
        e.stopPropagation()
        this.events.emit('network:removed', { network: this.networkId })
      })
    }

    // Add alert button event listeners (both reconnect and upgrade)
    const alertButtons = [
      this.element.querySelector('[data-reconnect-network]'),
      this.element.querySelector('[data-upgrade-required]')
    ].filter(Boolean)

    alertButtons.forEach(alertButton => {
      if (alertButton) {
        alertButton.addEventListener('click', (e) => {
          e.stopPropagation()

          // Determine where to redirect based on the button type
          if (alertButton.hasAttribute('data-upgrade-required')) {
            window.location.href = '/plans'
          } else {
            // Include return_to parameter when redirecting to auth page
            const currentPath = window.location.pathname
            window.location.href = `/auth/${this.networkId}?return_to=${encodeURIComponent(currentPath)}`
          }
        })

        // Initialize Tippy with styles that work with app.css
        tippy(alertButton, {
          placement: 'top',
          theme: 'light-border',
          zIndex: 9999,
          animation: 'shift-away',
          arrow: true,
          maxWidth: 250,
          content: (reference) => {
            const wrapper = document.createElement('div')
            wrapper.className = 'tippy-box'
            const text = document.createElement('div')
            text.className = 'tippy-content'
            text.textContent = reference.getAttribute('data-tippy-content')
            wrapper.appendChild(text)
            return wrapper
          }
        })
      }
    })
  }

  setSelected(selected) {
    const button = this.element.querySelector('.network-toggle')
    if (button) {
      button.dataset.selected = selected ? 'true' : 'false'
      const nameEl = button.querySelector('[data-network-name]')
      if (nameEl) nameEl.dataset.visible = selected ? 'true' : 'false'
    }
  }

  getNetworkColor() {
    switch (this.networkId) {
      case 'canonical':
        return 'blue-700'
      case 'bsky':
        return 'sky-500'
      case 'mastodon':
        return 'purple-500'
      case 'linkedin':
        return 'blue-600'
      case 'x':
        return 'black'
      default:
        return 'gray-800'
    }
  }

  getConnectionStatusMessage(reason) {
    switch (reason) {
      case 'feature_disabled':
        return 'This network is not available in your current plan. Please upgrade your plan to use this network.'
      case 'not_connected':
        return 'You need to connect this network in your settings.'
      case 'needs_reconnect':
        return 'Click to reconnect - it is required by the changes in the X API.'
      default:
        return 'This network is currently unavailable.'
    }
  }
}
