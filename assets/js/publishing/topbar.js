import { NETWORKS } from '../extensions/networks'
import { NetworkButton } from './network_button'

export class Topbar {
  constructor(element, options = {}) {
    this.element = element

    this.connections = options.connections || []
    this.selectedNetworks = options.selectedNetworks || new Set()
    this.events = options.events
    this.buttons = new Map()
    this.currentNetwork = 'canonical'
    this.user = JCP.user
    this.setupButtons()
    this.updatePreviewButton()
  }

  setupButtons() {
    const togglesContainer = this.element.querySelector('.network-toggles')
    if (!togglesContainer) {
      console.warn('Network toggles container not found')
      return
    }

    // Clear existing buttons
    togglesContainer.innerHTML = ''

    // Add the canonical "All" button first
    const allButton = this.createNetworkButton('canonical')
    if (allButton) {
      togglesContainer.appendChild(allButton.element)
      this.buttons.set('canonical', allButton)

      // Set as selected by default
      allButton.setSelected(true)
    }

    // Create buttons for each available network in alphabetical order
    const sortedNetworks = Array.from(this.selectedNetworks)
      .filter(id => id !== 'canonical')
      .sort()

    sortedNetworks.forEach(networkId => {
      // Create the button regardless of connection status
      const button = this.createNetworkButton(networkId)
      if (button) {
        togglesContainer.appendChild(button.element)
        this.buttons.set(networkId, button)
      }
    })

    // Add the plus button for toggling networks
    this.setupNetworkToggle()
  }

  createNetworkButton(networkId) {
    const connection = networkId === 'canonical' ? null : this.connections.find(c => c.network === networkId)

    // Create a new NetworkButton instance
    const networkButton = new NetworkButton(networkId, {
      connection: connection,
      events: {
        emit: (event, data) => {
          if (event === 'network:selected') {
            // Update selected state for all buttons
            this.buttons.forEach(button => {
              button.setSelected(false)
            })

            // Set this button as selected
            networkButton.setSelected(true)

            // Update current network
            this.currentNetwork = data.network
            this.events.emit('network:changed', { network: data.network })

            // Update preview button
            this.updatePreviewButton()
          } else if (event === 'network:removed') {
            this.removeNetwork(data.network)
          }
        }
      }
    })

    return networkButton
  }

  removeNetwork(networkId) {
    // First switch to canonical if we're on this network
    if (this.currentNetwork === networkId) {
      this.currentNetwork = 'canonical'
      this.events.emit('network:changed', { network: 'canonical' })
    }

    this.selectedNetworks.delete(networkId)
    this.events.emit('network:disabled', { network: networkId })
    this.setupButtons()
  }

  setupNetworkToggle() {
    const togglesContainer = this.element.querySelector('.network-toggles')
    if (!togglesContainer) return

    // Add plus button
    const plusButton = document.createElement('div')
    plusButton.innerHTML = `
      <div class="relative">
        <button
          type="button"
          class="h-10 w-10 flex items-center justify-center rounded-lg border border-gray-200 bg-gray-50 text-gray-500 hover:bg-gray-100"
          data-network-toggle-button
        >
          <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>

        <div class="hidden absolute left-0 z-10 mt-2 w-56 origin-top-left rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          data-network-toggle-menu
          role="menu"
          aria-orientation="vertical"
          tabindex="-1"
        >
          <div class="py-1" role="none">
            ${NETWORKS
        .filter(network => network.id !== 'canonical')
        .filter(network => {
          // Only show networks that the user has connections for
          return this.connections.some(conn => conn.network === network.id)
        })
        .sort((a, b) => a.id.localeCompare(b.id))
        .map(network => {
          // Check if the network is already selected
          const isSelected = this.selectedNetworks.has(network.id)

          return `
                <label class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer relative"
                  role="menuitem"
                >
                  <input
                    type="checkbox"
                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-2"
                    data-network-toggle-checkbox="${network.id}"
                    ${isSelected ? 'checked' : ''}
                  />
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="${network.viewBox}">
                      <path d="${network.icon}" />
                    </svg>
                    ${network.name}
                  </span>
                </label>
              `
        }).join('')}
          </div>
        </div>
      </div>
    `

    togglesContainer.appendChild(plusButton)

    // Add click handlers
    const toggleButton = plusButton.querySelector('[data-network-toggle-button]')
    const toggleMenu = plusButton.querySelector('[data-network-toggle-menu]')

    toggleButton.addEventListener('click', () => {
      toggleMenu.classList.toggle('hidden')
    })

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!plusButton.contains(e.target)) {
        toggleMenu.classList.add('hidden')
      }
    })

    // Add event listeners for checkboxes
    toggleMenu.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const networkId = checkbox.dataset.networkToggleCheckbox

        if (checkbox.checked) {
          this.addNetwork(networkId)
        } else {
          this.removeNetwork(networkId)
        }

        // Close the menu
        toggleMenu.classList.add('hidden')
      })
    })

    // Initialize Tippy.js for disabled checkboxes
    if (window.tippy) {
      toggleMenu.querySelectorAll('label[data-tippy-content]').forEach(label => {
        window.tippy(label, {
          placement: 'right',
          theme: 'light',
          zIndex: 9999
        })
      })
    }
  }

  destroy() {
    this.buttons.clear()
  }

  updatePreviewButton() {
    const previewButton = document.querySelector('[data-preview-button]')
    if (previewButton) {
      previewButton.dataset.network = this.currentNetwork
      previewButton.setAttribute('phx-value-network', this.currentNetwork)
      previewButton.style.display = this.currentNetwork === 'canonical' ? 'none' : ''
    }
  }

  getConnectionStatusMessage(reason) {
    switch (reason) {
      case 'feature_disabled':
        return 'This network is not available in your current plan. Please upgrade your plan to use this network.'
      case 'not_connected':
        return 'You need to connect this network in your settings.'
      case 'needs_reconnect':
        return 'This connection needs to be reconnected. Please go to settings to reconnect.'
      default:
        return 'This network is currently unavailable.'
    }
  }

  addNetwork(networkId) {
    // Check if the connection is available
    if (window.JCP) {
      const connectionStatus = window.JCP.isConnectionAvailable(networkId)
      if (!connectionStatus.enabled) {
        console.warn(`Cannot add network ${networkId}: ${connectionStatus.reason}`)
        return
      }
    }

    this.selectedNetworks.add(networkId)
    this.events.emit('network:enabled', { network: networkId })
    this.setupButtons()
  }
}
