export class EditorControls {
  constructor(options = {}) {
    this.network = options.network
    this.events = options.events
    this.store = options.store

    if (this.network !== 'canonical') {
      this.setupEventListeners()
    }
  }

  setupEventListeners() {
    document.addEventListener('change', (e) => {
      const syncToggle = e.target.closest('[data-sync-toggle]')
      if (!syncToggle) return

      if (syncToggle.checked) {
        const canonicalContent = this.store.getContentForNetwork('canonical')
        this.events.topic(this.network).emit('content:sync', { canonicalContent })
      }
    })
  }

  destroy() {
    // Clean up event listeners if needed
  }
}
