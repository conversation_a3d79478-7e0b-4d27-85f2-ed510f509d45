import { User, SocialProfile } from './models/user'

class JCP {
  constructor() {
    this.user = null
    this.store = null
    this.config = {}
  }

  initialize(userData = {}) {
    this.user = new User(userData)
    this.config = userData.config || {}
  }

  setStore(store) {
    this.store = store
  }

  getConnection(network) {
    return this.user.connections.find(c => c.network === network)
  }

  isConnectionAvailable(network) {
    // Check if the crossposting feature is enabled for this network
    const featureKey = `crossposting_${network}`
    if (!this.user.hasFeature(featureKey)) {
      return { enabled: false, reason: 'feature_disabled' }
    }

    // Check if the connection exists
    const connection = this.getConnection(network)
    if (!connection) {
      return { enabled: false, reason: 'not_connected' }
    }

    // Check if the connection needs to be reconnected
    if (connection.settings && connection.settings.needs_reconnect === true) {
      return { enabled: false, reason: 'needs_reconnect' }
    }

    return { enabled: true }
  }

  getConfig(key) {
    return key.split('.').reduce((obj, k) => obj?.[k], this.config)
  }

  getSocialProfiles() {
    return this.user.socialProfiles
  }

  addSocialProfile(attrs) {
    const profile = new SocialProfile(attrs)
    this.user.socialProfiles.push(profile)
    return profile
  }

  removeSocialProfile(id) {
    this.user.socialProfiles = this.user.socialProfiles.filter(p => p.id !== id)
  }
}

window.JCP = new JCP()

export default window.JCP
