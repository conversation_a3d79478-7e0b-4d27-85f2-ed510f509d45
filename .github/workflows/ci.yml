# See https://fly.io/docs/app-guides/continuous-deployment-with-github-actions/

name: Fly Deploy
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - name: Set up .env files
        run: cp .env.dev.example .env.dev
      - name: Start db service
        run: docker compose up -d postgres
      - name: Run tests with coverage
        run: |
          DOCKER_BUILDKIT=1 docker compose build \
            --build-arg OBAN_LICENSE_KEY=${{ secrets.OBAN_LICENSE_KEY }} \
            --build-arg OBAN_KEY_FINGERPRINT=${{ secrets.OBAN_KEY_FINGERPRINT }} \
            test
          docker compose run --rm test bin/ci
        env:
          OBAN_LICENSE_KEY: ${{ secrets.OBAN_LICENSE_KEY }}
          OBAN_KEY_FINGERPRINT: ${{ secrets.OBAN_KEY_FINGERPRINT }}
