## Platforms

### Bluesky

Info structure

```json
{
  "avatar": "https://cdn.bsky.app/img/avatar/plain/did:plc:6k4tgyg7f2es7tiahpm6ro53/bafkreicnry5xjnfggnmmzzyo527eo7w3ltt3sm3ctf5lr2ri3fe5y3s6ei@jpeg",
  "description": null,
  "display_name": "",
  "followers_count": 4,
  "follows_count": 1,
  "handle": "jc-tester.bsky.social",
  "posts_count": 179,
  "subscription_type": "Basic"
}
```

### Mastodon

Info structure

```json
{
  "birthday": null,
  "description": null,
  "email": null,
  "first_name": null,
  "image": "https://files.mastodon.social/accounts/avatars/113/605/935/015/324/727/original/b246122dd7ab64fb.png",
  "last_name": null,
  "location": null,
  "name": "<PERSON>",
  "nickname": "jc_tester",
  "phone": null,
  "urls": {
    "instance": "https://mastodon.social",
    "profile": "https://mastodon.social/@jc_tester"
  }
}
```

### X

Info structure

```json
{
  "birthday": null,
  "description": null,
  "email": null,
  "first_name": null,
  "image": "https://pbs.twimg.com/profile_images/1833895676238790656/oI9EqzeU_normal.jpg",
  "last_name": null,
  "location": null,
  "name": "Crossposting Testing",
  "nickname": "crosspost_test",
  "phone": null,
  "urls": {}
}
```

### LinkedIn

Info structure

```json
{
  "birthday": null,
  "description": "",
  "email": "<EMAIL>",
  "first_name": null,
  "image": "https://pbs.twimg.com/profile_images/1833895676238790656/oI9EqzeU_normal.jpg",
  "last_name": null,
  "location": "",
  "name": "Crossposting Testing",
  "nickname": "crosspost_test",
  "phone": null,
  "urls": {
    "Twitter": "https://twitter.com/crosspost_test",
    "Website": null
  }
}
```
