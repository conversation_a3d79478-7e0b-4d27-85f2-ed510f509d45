#!/usr/bin/env bash

# Enable stricter error handling
set -o pipefail

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color
BOLD='\033[1m'
CYAN='\033[0;36m'

# Initialize counters
total=0
passed=0
failed=0
failed_tests=()

# Default behavior - don't exit on first failure
exit_on_failure=false

# Parse command line options
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --fail-fast)
      exit_on_failure=true
      shift
      ;;
    *)
      # Save the first non-option argument
      if [ -z "$test_input" ]; then
        test_input="$1"
      fi
      shift
      ;;
  esac
done

# Function to print test results
print_results() {
    echo -e "\n${BOLD}Test Summary${NC}"
    echo -e "Total tests: $total"
    echo -e "${GREEN}Passed: $passed${NC}"
    echo -e "${RED}Failed: $failed${NC}"

    if [ ${#failed_tests[@]} -ne 0 ]; then
        echo -e "\n${RED}Failed Tests:${NC}"
        for test in "${failed_tests[@]}"; do
            echo -e "${RED}- $test${NC}"
        done
    fi
}

# Function to transform test output
transform_output() {
    while IFS= read -r line; do
        if [[ $line =~ test-results/.*\.png ]]; then
            # Extract the screenshot path and transform it
            screenshot_path=$(echo "$line" | grep -o 'test-results/.*\.png')
            echo "${line/test-results/test\/playwright\/test-results}"
        else
            echo "$line"
        fi
    done
}

# Function to run a single test
run_single_test() {
    local test_file=$1
    local line_number=$2
    ((total++))

    # Create a temporary file for the output
    local temp_output=$(mktemp)

    # Run the test and capture both output and exit status
    mix test.e2e "$test_file:$line_number" 2>&1 | transform_output > "$temp_output"
    local test_status=${PIPESTATUS[0]}

    if [ $test_status -eq 0 ]; then
        echo -ne "${GREEN}●${NC}"
        ((passed++))
    else
        echo -ne "${RED}●${NC}"
        ((failed++))
        failed_tests+=("${test_file}:${line_number}")
        echo -e "\n${RED}Error in test at ${test_file}:${line_number}:${NC}"
        cat "$temp_output"

        # Exit immediately if fail-fast is enabled
        if [ "$exit_on_failure" = true ]; then
            print_results
            exit 1
        fi
    fi

    # Clean up
    rm -f "$temp_output"
    return $test_status
}

# Function to run tests for a specific file
run_tests_for_file() {
    local test_file=$1
    local specific_line=$2
    local file_status=0

    # Get relative path for display
    local display_path=${test_file#$TEST_DIR/}
    echo -e "\n${BOLD}Running tests for: ${CYAN}$display_path${NC}"

    if [ -n "$specific_line" ]; then
        run_single_test "$test_file" "$specific_line"
        file_status=$?
    else
        for line_number in $(grep -n "test(" "$test_file" | cut -d : -f 1); do
            run_single_test "$test_file" "$line_number"
            local test_status=$?
            [ $test_status -ne 0 ] && file_status=$test_status

            # Break the loop if a test failed and fail-fast is enabled
            if [ $test_status -ne 0 ] && [ "$exit_on_failure" = true ]; then
                break
            fi
        done
    fi

    return $file_status
}

# Get all test files
TEST_DIR="test/playwright/tests"
overall_status=0

if [ -z "$test_input" ]; then
    # Find and sort all spec files
    echo -e "${BOLD}Found test files:${NC}"
    readarray -d '' spec_files < <(find "$TEST_DIR" -type f -name "*.spec.js" -print0 | sort -z)

    # Print all found spec files
    for spec_file in "${spec_files[@]}"; do
        echo -e "${CYAN}${spec_file#$TEST_DIR/}${NC}"
    done
    echo ""

    # Run all tests
    for spec_file in "${spec_files[@]}"; do
        run_tests_for_file "$spec_file"
        file_status=$?
        [ $file_status -ne 0 ] && overall_status=1

        # Break the loop if a test failed and fail-fast is enabled
        if [ $file_status -ne 0 ] && [ "$exit_on_failure" = true ]; then
            break
        fi
    done
else
    # Parse input for file pattern and line number
    test_pattern="${test_input%:*}"  # Remove line number if present
    line_number=""

    # Extract line number if present
    if [[ "$test_input" == *:* ]]; then
        line_number="${test_input##*:}"
    fi

    # Handle both direct paths and partial matches
    if [ -f "$TEST_DIR/$test_pattern.spec.js" ]; then
        # Direct path provided
        run_tests_for_file "$TEST_DIR/$test_pattern.spec.js" "$line_number"
        overall_status=$?
    else
        # Search for matching files
        readarray -d '' matching_files < <(find "$TEST_DIR" -type f -name "*${test_pattern}*.spec.js" -print0 | sort -z)

        if [ ${#matching_files[@]} -eq 0 ]; then
            echo -e "${RED}No test files found matching: $test_pattern${NC}"
            exit 1
        fi

        for test_file in "${matching_files[@]}"; do
            run_tests_for_file "$test_file" "$line_number"
            file_status=$?
            [ $file_status -ne 0 ] && overall_status=1

            # Break the loop if a test failed and fail-fast is enabled
            if [ $file_status -ne 0 ] && [ "$exit_on_failure" = true ]; then
                break
            fi
        done
    fi
fi

print_results

# Exit with failure if any tests failed
exit $overall_status
