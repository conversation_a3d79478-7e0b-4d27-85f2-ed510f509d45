#!/bin/bash

# Check if DATABASE_URL is set
if [ -z "$DUMP_DATABASE_URL" ]; then
  echo "Error: DUMP_DATABASE_URL environment variable is not set"
  exit 1
fi

# Extract database connection details from DATABASE_URL
# Format: postgres://username:password@hostname:port/database_name
USER=$(echo "$DUMP_DATABASE_URL" | sed -n 's/^postgres:\/\/\([^:]*\):.*/\1/p')
HOST=$(echo "$DUMP_DATABASE_URL" | sed -n 's/^postgres:\/\/[^:]*:[^@]*@\([^:]*\):.*/\1/p')
DATABASE=$(echo "$DUMP_DATABASE_URL" | sed -n 's/^postgres:\/\/[^:]*:[^@]*@[^:]*:[^/]*\/\(.*\)$/\1/p')

echo $HOST

# Run pg_dump with extracted credentials
pg_dump --clean --if-exists --quote-all-identifiers \
-h "$HOST" -U "$USER" -d "$DATABASE" \
--no-owner --no-privileges > dump.sql

echo "Database dump completed successfully: dump.sql"


