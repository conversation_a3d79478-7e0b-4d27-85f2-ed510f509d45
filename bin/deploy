#!/bin/bash

# Exit on error
set -e

# Check if OBAN_KEY_FINGERPRINT is set
if [ -z "$OBAN_KEY_FINGERPRINT" ]; then
    echo "Error: OBAN_KEY_FINGERPRINT environment variable is not set"
    exit 1
fi

# Check if OBAN_LICENSE_KEY is set
if [ -z "$OBAN_LICENSE_KEY" ]; then
    echo "Error: OBAN_LICENSE_KEY environment variable is not set"
    exit 1
fi

echo "Starting deployment..."

# Deploy with Oban secrets
fly deploy \
    --build-secret oban_key_fingerprint="$OBAN_KEY_FINGERPRINT" \
    --build-secret oban_license_key="$OBAN_LICENSE_KEY"

echo "Deployment completed successfully!"
