#!/usr/bin/env bash

[[ -f .env.local ]] && source .env.local

if [[ -e /run/secrets/oban_key_fingerprint ]]; then
  oban_key_fingerprint="$(cat /run/secrets/oban_key_fingerprint)"
else
  oban_key_fingerprint=$OBAN_KEY_FINGERPRINT
fi

if [[ -e /run/secrets/oban_license_key ]]; then
  oban_license_key="$(cat /run/secrets/oban_license_key)"
else
  oban_license_key=$OBAN_LICENSE_KEY
fi

if [[ -z $oban_key_fingerprint || -z $oban_license_key ]]; then
  echo "Missing Oban license key or fingerprint"
  exit 1
fi

mix hex.repo add oban https://getoban.pro/repo \
  --fetch-public-key $oban_key_fingerprint --auth-key $oban_license_key
