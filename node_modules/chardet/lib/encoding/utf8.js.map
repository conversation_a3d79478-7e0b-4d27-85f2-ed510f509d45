{"version": 3, "file": "utf8.js", "sourceRoot": "", "sources": ["../../src/encoding/utf8.ts"], "names": [], "mappings": ";;;;;AACA,qDAAgE;AAEhE,MAAqB,IAAI;IACvB,IAAI;QACF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,GAAY;QAChB,IAAI,MAAM,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,EACZ,UAAU,GAAG,CAAC,EACd,UAAU,GAAG,CAAC,EACd,UAAU,CAAC;QACb,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;QAE3B,IACE,GAAG,CAAC,MAAM,IAAI,CAAC;YACf,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI;YACzB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI;YACzB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EACzB,CAAC;YACD,MAAM,GAAG,IAAI,CAAC;QAChB,CAAC;QAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBAAE,SAAS;YAG9B,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC;gBACzB,UAAU,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC;gBAChC,UAAU,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC/B,UAAU,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,UAAU,EAAE,CAAC;gBACb,IAAI,UAAU,GAAG,CAAC;oBAAE,MAAM;gBAC1B,UAAU,GAAG,CAAC,CAAC;YACjB,CAAC;YAGD,SAAS,CAAC;gBACR,CAAC,EAAE,CAAC;gBACJ,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM;oBAAE,MAAM;gBAE3B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;oBAC/B,UAAU,EAAE,CAAC;oBACb,MAAM;gBACR,CAAC;gBACD,IAAI,EAAE,UAAU,IAAI,CAAC,EAAE,CAAC;oBACtB,QAAQ,EAAE,CAAC;oBACX,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAID,UAAU,GAAG,CAAC,CAAC;QACf,IAAI,MAAM,IAAI,UAAU,IAAI,CAAC;YAAE,UAAU,GAAG,GAAG,CAAC;aAC3C,IAAI,MAAM,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE;YAAE,UAAU,GAAG,EAAE,CAAC;aAC1D,IAAI,QAAQ,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC;YAAE,UAAU,GAAG,GAAG,CAAC;aACtD,IAAI,QAAQ,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC;YAAE,UAAU,GAAG,EAAE,CAAC;aACrD,IAAI,QAAQ,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC;YAEvC,UAAU,GAAG,EAAE,CAAC;aACb,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE;YAEjC,UAAU,GAAG,EAAE,CAAC;;YACb,OAAO,IAAI,CAAC;QAEjB,OAAO,IAAA,eAAK,EAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;CACF;AAzED,uBAyEC"}