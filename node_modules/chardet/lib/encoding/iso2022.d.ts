import type { Context, Recogniser } from '.';
import { type Match, type EncodingName } from '../match';
declare class ISO_2022 implements Recogniser {
    escapeSequences: number[][];
    name(): EncodingName;
    match(det: Context): Match | null;
}
export declare class ISO_2022_JP extends ISO_2022 {
    name(): EncodingName;
    language(): string;
    escapeSequences: number[][];
}
export declare class ISO_2022_KR extends ISO_2022 {
    name(): EncodingName;
    language(): string;
    escapeSequences: number[][];
}
export declare class ISO_2022_CN extends ISO_2022 {
    name(): EncodingName;
    language(): string;
    escapeSequences: number[][];
}
export {};
