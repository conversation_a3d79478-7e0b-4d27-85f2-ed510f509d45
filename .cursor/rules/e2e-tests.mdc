---
description: E2E testing
globs: *.js, *.heex, *.ex, *.exs
alwaysApply: true
---
# When to write e2e tests

- Never, unless I explicitly ask to write an e2e test

# When to run e2e tests

- Whenever UI changes, run related e2e tests

# Running e2e specs

- To run an e2e test use the script bin/e2e
- To run a specific file just do `bin/e2e file_name` - no need for .spec.js suffix
- To run a test from a specific line do `bin/e2e file_name:12` where 12 is the line number with "test('...)" definition
- You MUST use this script as it's the only way that works in this project
