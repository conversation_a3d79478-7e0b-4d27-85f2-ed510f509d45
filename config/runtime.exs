import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/crosspost start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.

if config_env() == :prod do
  database_url =
    System.get_env("DATABASE_URL") ||
      raise """
      environment variable DATABASE_URL is missing.
      For example: ecto://USER:PASS@HOST/DATABASE
      """

  maybe_ipv6 = if System.get_env("ECTO_IPV6") in ~w(true 1), do: [:inet6], else: []

  config :crosspost, Crosspost.Repo,
    url: database_url,
    ssl: false,
    socket_options: maybe_ipv6,
    pool_size: String.to_integer(System.get_env("POOL_SIZE") || "10"),
    migration_lock: :pg_advisory_lock,
    queue_target: 10000,
    timeout: 20_000

  # The secret key base is used to sign/encrypt cookies and other secrets.
  # A default value is used in config/dev.exs and config/test.exs but you
  # want to use a different value for prod and you most likely don't want
  # to check this value into version control, so we use an environment
  # variable instead.
  secret_key_base =
    System.get_env("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host = System.get_env("PHX_HOST") || "example.com"
  port = String.to_integer(System.get_env("PORT") || "4000")

  config :crosspost, :dns_cluster_query, System.get_env("DNS_CLUSTER_QUERY")

  config :crosspost, CrosspostWeb.Endpoint,
    url: [host: host, port: 443, scheme: "https"],
    http: [
      # Enable IPv6 and bind on all interfaces.
      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      # See the documentation on https://hexdocs.pm/bandit/Bandit.html#t:options/0
      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      ip: {0, 0, 0, 0, 0, 0, 0, 0},
      port: port
    ],
    secret_key_base: secret_key_base,
    server: true

  config :swoosh, api_client: Swoosh.ApiClient.Req

  config :crosspost, Crosspost.Mailer,
    adapter: Swoosh.Adapters.Sendgrid,
    api_key: System.get_env("SENDGRID_API_KEY")

  # ## SSL Support
  #
  # To get SSL working, you will need to add the `https` key
  # to your endpoint configuration:
  #
  #     config :crosspost, CrosspostWeb.Endpoint,
  #       https: [
  #         ...,
  #         port: 443,
  #         cipher_suite: :strong,
  #         keyfile: System.get_env("SOME_APP_SSL_KEY_PATH"),
  #         certfile: System.get_env("SOME_APP_SSL_CERT_PATH")
  #       ]
  #
  # The `cipher_suite` is set to `:strong` to support only the
  # latest and more secure SSL ciphers. This means old browsers
  # and clients may not be supported. You can set it to
  # `:compatible` for wider support.
  #
  # `:keyfile` and `:certfile` expect an absolute path to the key
  # and cert in disk or a relative path inside priv, for example
  # "priv/ssl/server.key". For all supported SSL configuration
  # options, see https://hexdocs.pm/plug/Plug.SSL.html#configure/1
  #
  # We also recommend setting `force_ssl` in your config/prod.exs,
  # ensuring no data is ever sent via http, always redirecting to https:
  #
  #     config :crosspost, CrosspostWeb.Endpoint,
  #       force_ssl: [hsts: true]
  #
  # Check `Plug.SSL` for all available options in `force_ssl`.

  # ## Configuring the mailer
  #
  # In production you need to configure the mailer to use a different adapter.
  # Also, you may need to configure the Swoosh API client of your choice if you
  # are not using SMTP. Here is an example of the configuration:
  #
  #     config :crosspost, Crosspost.Mailer,
  #       adapter: Swoosh.Adapters.Mailgun,
  #       api_key: System.get_env("MAILGUN_API_KEY"),
  #       domain: System.get_env("MAILGUN_DOMAIN")
  #
  # For this example you need include a HTTP client required by Swoosh API client.
  # Swoosh supports Hackney and Finch out of the box:
  #
  #     config :swoosh, :api_client, Swoosh.ApiClient.Hackney
  #
  # See https://hexdocs.pm/swoosh/Swoosh.html#module-installation for details.

  # Override admin credentials in production
  config :crosspost, :admin_auth,
    username: System.get_env("ADMIN_USERNAME", "admin"),
    password:
      System.get_env("ADMIN_PASSWORD") || raise("ADMIN_PASSWORD is required for production")

  # Update the Cloudinary config to include all required fields
  config :cloudex,
    api_key: System.get_env("CLOUDINARY_API_KEY") || raise("CLOUDINARY_API_KEY is missing"),
    secret: System.get_env("CLOUDINARY_SECRET") || raise("CLOUDINARY_SECRET is missing"),
    cloud_name:
      System.get_env("CLOUDINARY_CLOUD_NAME") || raise("CLOUDINARY_CLOUD_NAME is missing"),
    enabled: true

  config :crosspost, :billing_provider, Crosspost.Billing.Stripe

  config :crosspost, :publish_post_worker, Crosspost.Workers.PublishPostWorker

  config :crosspost, :mailer_lite_module, Crosspost.Admin.Integrations.MailerLite.Client

  config :logger,
    backends: [{LoggerBackends.Console, [format: {Crosspost.Logger.JSONFormatter, :format}]}],
    level: :info

  config :crosspost, CrosspostWeb.Endpoint,
    url: [host: System.get_env("PHX_HOST") || "localhost"],
    http: [port: String.to_integer(System.get_env("PORT") || "4000")],
    secret_key_base: System.get_env("SECRET_KEY_BASE")
end

config :sentry,
  dsn: System.get_env("SENTRY_DSN"),
  enable_source_code_context: true,
  tracing: true,
  root_source_code_paths: [File.cwd!()],
  capture_log_messages: true,
  integrations: [
    oban: [
      capture_errors: true,
      cron: [enabled: true]
    ]
  ]

config :opentelemetry, span_processor: {Sentry.OpenTelemetry.SpanProcessor, []}

config :opentelemetry,
  sampler:
    {Sentry.OpenTelemetry.Sampler,
     [
       drop: [
         "Elixir.Oban.Stager process",
         "crosspost.repo.query",
         "crosspost.repo.query:oban_peers",
         "crosspost.repo.query:oban_producers",
         "crosspost.repo.query:oban_jobs"
       ]
     ]}

if config_env() == :dev do
  ai_module =
    if System.get_env("AI_ENABLED") == "true",
      do: Crosspost.AI.OpenAIImpl,
      else: Crosspost.AI.Mock

  config :crosspost, :ai_module, ai_module
end

config :ueberauth, Ueberauth.Strategy.LinkedIn.OAuth,
  client_id: System.get_env("LINKEDIN_CLIENT_ID"),
  client_secret: System.get_env("LINKEDIN_CLIENT_SECRET"),
  redirect_uri: System.get_env("LINKEDIN_REDIRECT_URI")

config :ueberauth, Ueberauth.Strategy.X.OAuth,
  client_id: System.get_env("X_CLIENT_ID"),
  client_secret: System.get_env("X_CLIENT_SECRET"),
  redirect_uri: System.get_env("X_REDIRECT_URI")

config :crosspost, Crosspost.Vault,
  ciphers: [
    default:
      {Cloak.Ciphers.AES.GCM,
       tag: "AES.GCM.V1", key: Base.decode64!(System.fetch_env!("CLOAK_KEY"))}
  ]

config :crosspost,
  stripe_webhook_secret: System.get_env("STRIPE_WEBHOOK_SECRET"),
  mailer_lite_api_key: System.get_env("MAILER_LITE_API_KEY")

config :stripity_stripe, api_key: System.get_env("STRIPE_SECRET_KEY")
