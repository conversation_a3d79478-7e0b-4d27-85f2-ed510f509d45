import Config

Code.require_file("lib/crosspost_web.ex")

# Configure your database
config :crosspost, Crosspost.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "publibot_dev",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :crosspost, CrosspostWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "oYSo6arZMJHaJOxzdWEgtNZdtN/biHMcplTsFhesdZ/Sfq4Kv08lfMl+CxK/p3ts",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:crosspost, ~w(--sourcemap=inline --watch)]},
    esbuild: {Esbuild, :install_and_run, [:admin, ~w(--sourcemap=inline --watch)]},
    esbuild: {Esbuild, :install_and_run, [:public, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:crosspost, ~w(--watch)]},
    tailwind: {Tailwind, :install_and_run, [:admin, ~w(--watch)]}
  ],
  plug_static: [
    at: "/",
    from: :crosspost,
    gzip: false,
    # Use the function reference here
    only: &CrosspostWeb.static_paths/0
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :crosspost, CrosspostWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/crosspost_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

config :crosspost, dev_routes: true

config :logger, backends: [:console, {LoggerFileBackend, :json_log}], level: :debug

config :logger, :json_log,
  path: "log/dev.json.log",
  format: {Crosspost.Logger.JSONFormatter, :format},
  metadata: :all

config :phoenix, :stacktrace_depth, 20

config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  debug_heex_annotations: true,
  enable_expensive_runtime_checks: true

if System.get_env("SENDGRID_ENABLED") == "true" do
  config :swoosh, api_client: Swoosh.ApiClient.Req

  config :crosspost, Crosspost.Mailer,
    adapter: Swoosh.Adapters.Sendgrid,
    api_key: System.get_env("SENDGRID_API_KEY")
else
  config :crosspost, Crosspost.Mailer, adapter: Swoosh.Adapters.Local
  config :swoosh, api_client: false
  config :swoosh, local: true
end

config :crosspost, snappify_api_key: System.get_env("SNAPPIFY_API_KEY")
config :crosspost, :mailer_lite_module, Crosspost.Admin.Integrations.MailerLite.Client

config :crosspost,
  cloudinary_cloud_name: "dnl0hikpj",
  cloudinary_upload_preset: "crosspost_preset",
  cloudinary_api_key: System.get_env("CLOUDINARY_API_KEY")

config :crosspost, env: :dev

publish_post_worker =
  if System.get_env("PUBLISHING_ENABLED") == "true",
    do: Crosspost.Workers.PublishPostWorker,
    else: Crosspost.Workers.TestPublishPostWorker

config :crosspost, :publish_post_worker, publish_post_worker

ai_module =
  if System.get_env("AI_ENABLED") == "true",
    do: Crosspost.AI.OpenAIImpl,
    else: Crosspost.AI.Mock

config :crosspost, :ai_module, ai_module

if System.get_env("CLOUDINARY_ENABLED") == "true",
  do: config(:crosspost, :upload_worker, Crosspost.Workers.CloudinaryUploadWorker),
  else: config(:crosspost, :upload_worker, Crosspost.Workers.TestUploadWorker)

if System.get_env("STRIPE_ENABLED") == "true",
  do: config(:crosspost, :billing_provider, Crosspost.Billing.Stripe),
  else: config(:crosspost, :billing_provider, Crosspost.Billing.Mock)
