import Config

config :logger, backends: [{LoggerFileBackend, :json_log}], level: :debug

config :logger, :json_log,
  path: "log/test.json.log",
  format: {Crosspost.Logger.JSONFormatter, :format},
  metadata: :all

# Only in tests, remove the complexity from the password hashing algorithm
config :bcrypt_elixir, :log_rounds, 1

# Configure your database
config :crosspost, Crosspost.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "publibot_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10

server = if System.get_env("E2E_TEST") == "true", do: true, else: false

# Configure the test server
config :crosspost, CrosspostWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "iMgkck41F5wm/aY6cIa7h8LPMjMYzrdElkv3Knk3dyzJVAFIG6Vz8R/lkLq3xN8d",
  server: server,
  debug_errors: true

# In test we don't send emails
config :crosspost, Crosspost.Mailer,
  adapter: Swoosh.Adapters.Test,
  from_name: "JustCrossPost",
  from_email: "<EMAIL>"

# Make sure we disable the API client as it's only needed for production adapters
config :swoosh, :api_client, false

# Add test mode configuration for Swoosh
config :swoosh, local: false

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

# Enable helpful, but potentially expensive runtime checks
config :phoenix_live_view,
  enable_expensive_runtime_checks: true

config :crosspost, http_req_options: [plug: {Req.Test, Crosspost.HTTP}]

config :crosspost, x_req_options: [plug: {Req.Test, Crosspost.Publishing.X}]
config :crosspost, linked_in_req_options: [plug: {Req.Test, Crosspost.Publishing.LinkedIn}]
config :crosspost, bsky_req_options: [plug: {Req.Test, Crosspost.Accounts.Bsky.Client}]

config :crosspost, x_oauth_module: Ueberauth.Strategy.X.OAuthMock

config :crosspost, :bsky_client, Crosspost.Accounts.Bsky.ClientMock
config :crosspost, :linkedin_client, Crosspost.Accounts.LinkedIn.ClientMock
config :crosspost, :accounts, Crosspost.AccountsMock
config :crosspost, :mailer_lite_module, Crosspost.Admin.Integrations.MailerLite.MockImpl

config :crosspost, :mastodon_module, Crosspost.Publishing.MastodonMock
config :crosspost, mastodon_req_options: [plug: {Req.Test, Crosspost.Publishing.Mastodon}]
config :crosspost, :mastodon_client, Crosspost.Accounts.Mastodon.ClientMock

config :crosspost, env: :test

# Add these configurations
config :crosspost,
  mastodon_max_retries: 3,
  mastodon_retry_delay: 100

# Add shorter timeouts for MailerLite tests
config :crosspost,
  mailer_lite_max_import_retries: 3,
  mailer_lite_retry_delay_ms: 100

# Add this line with the other mock configurations
config :crosspost, :ai_module, Crosspost.AI.Mock

# Add these configurations
config :crosspost, :upload_worker, Crosspost.Workers.TestUploadWorker
config :crosspost, :test_files_dir, "priv/static/uploads/test"

if System.get_env("E2E_TEST") == "true" do
  config :crosspost, :publish_post_worker, Crosspost.Workers.TestPublishPostWorker
else
  config :crosspost, :publish_post_worker, Crosspost.Workers.PublishPostWorker
end

config :crosspost, dev_routes: true

if System.get_env("E2E_TEST") == "true" do
  config :crosspost, Oban,
    testing: :inline,
    queues: [default: 10, uploads: 10, media: 10, publishing: 10]
else
  config :crosspost, Oban,
    testing: :manual,
    queues: [default: 10, uploads: 10, media: 10, publishing: 10]
end

config :crosspost, :billing_provider, Crosspost.Billing.Mock
config :crosspost, :x_module, Crosspost.Publishing.XMock

config :crosspost,
  media_helper_req_options: [plug: {Req.Test, Crosspost.Publishing.MediaHelper}]

# Add this to your existing test config
config :crosspost, :publishing_module, Crosspost.PublishingMock

# Add this with the other req test configurations
unless System.get_env("E2E_TEST") == "true" do
  config :crosspost, website_req_options: [plug: {Req.Test, Crosspost.Websites}]
end

config :crosspost,
  x_module: Crosspost.Publishing.X,
  linkedin_module: Crosspost.Publishing.LinkedIn,
  bsky_module: Crosspost.Publishing.Bsky,
  mastodon_module: Crosspost.Publishing.Mastodon,
  storage: Crosspost.Media.StorageMock,
  media_helper: Crosspost.Publishing.MediaHelperMock

config :crosspost, plug_attack_form_limit: 1000, plug_attack_general_limit: 1000
