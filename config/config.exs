# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :crosspost, dev_mode: true

config :crosspost,
  ecto_repos: [Crosspost.Repo],
  generators: [timestamp_type: :utc_datetime]

# Configures the endpoint
config :crosspost, CrosspostWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: CrosspostWeb.ErrorHTML, json: CrosspostWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Crosspost.PubSub,
  live_view: [signing_salt: "H3QQB4Sw"]

# Configures Swoosh API Client
config :swoosh, api_client: false

# Disable Swoosh Local Memory Storage
config :swoosh, local: false

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :crosspost, Crosspost.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  crosspost: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ],
  admin: [
    args:
      ~w(js/admin.js --bundle --target=es2017 --outdir=../priv/static/assets --loader:.svg=dataurl),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ],
  public: [
    args:
      ~w(js/public.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.17",
  crosspost: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ],
  admin: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/admin.css
      --output=../priv/static/assets/admin.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]

# Configures Elixir's Logger
config :logger,
  backends: [:console],
  level: :debug,
  metadata: [
    request_id: nil,
    user_id: nil,
    post_id: nil,
    network: nil,
    event: nil,
    error: nil,
    error_type: nil,
    attempt: nil,
    is_final: nil
  ]

# Configure the console backend
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: :all

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# This is needed for Mastodon to work
config :tesla, adapter: Tesla.Adapter.Hackney

config :ueberauth, Ueberauth,
  providers: [
    linkedin:
      {Ueberauth.Strategy.LinkedIn,
       [
         default_scope:
           "openid profile email w_member_social r_organization_social w_organization_social rw_organization_admin r_organization_admin"
       ]},
    x: {Ueberauth.Strategy.X, []},
    mastodon:
      {Ueberauth.Strategy.Mastodon,
       [
         instance: "https://hachyderm.io",
         client_id: "FKqc3y1S2ZAx3_WQrH5G18RpcqII9aB9CfhS5dR4Wsk",
         client_secret: "3nq7h1tnyg9fWeUUwc20YfGvrd3Uq7hBzGl8E_tF1xs",
         scope: "read write follow"
       ]}
  ]

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"

# Configure Oban
config :crosspost, Oban,
  repo: Crosspost.Repo,
  engine: Oban.Pro.Engines.Smart,
  plugins: [
    Oban.Pro.Plugins.DynamicLifeline,
    {
      Oban.Pro.Plugins.DynamicPruner,
      mode: {:max_age, {7, :days}}, limit: 25_000, timeout: :timer.seconds(60)
    }
  ],
  queues: [
    default: 10,
    uploads: 10,
    publishing: 10,
    media: 10,
    users: 100
  ]

# Admin email configuration
config :crosspost,
  admin_email: "<EMAIL>"

# Configure mocks for Twitter and LinkedIn
config :crosspost, :x_module, Crosspost.Publishing.X
config :crosspost, :linkedin_module, Crosspost.Publishing.LinkedIn

# Add this to your existing config
config :crosspost, :instances_social,
  api_key:
    "OyeKTdZrehMtCopjaHNs2iQR8zlUm4Dcuh5zKQswVOUQaFaDC3Ncgb6rjdBZWghnqHxF8OSTeUA20NKzvwoxEEOYU5x1owzv5XWoLGtkunnESrfK5YB0YG7ZVnGUpCs3"

config :crosspost, :networks,
  bsky: "bsky",
  mastodon: "mastodon",
  linkedin: "linkedin",
  x: "x"

config :cloudex,
  api_key: System.get_env("CLOUDINARY_API_KEY"),
  secret: System.get_env("CLOUDINARY_SECRET"),
  cloud_name: System.get_env("CLOUDINARY_CLOUD_NAME"),
  enabled: System.get_env("CLOUDINARY_ENABLED") == "true"

config :flop, repo: Crosspost.Repo

config :crosspost,
  cloudinary_api_key: System.get_env("CLOUDINARY_API_KEY"),
  cloudinary_api_secret: System.get_env("CLOUDINARY_API_SECRET")

config :domainatrex,
  fetch_latest: true,
  public_suffix_list_url: "https://publicsuffix.org/list/public_suffix_list.dat",
  fallback_local_copy: "priv/public_suffix_list.dat"
